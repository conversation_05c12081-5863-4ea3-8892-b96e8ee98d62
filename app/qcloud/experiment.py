# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/03
# __author:       <PERSON><PERSON><PERSON>

import numpy as np

from pyQCat.analysis.standard_curve_analysis import StandardCurveAnalysis
from pyQCat.experiments.top_experiment_v1 import StandardContext, TopExperimentV1
from pyQCat.qubit import Qubit
from pyQCat.structures import List, MetaData, Options
from pyQCat.tools.utilities import get_multi_readout_channels


class QCloudPlusV1Analysis(StandardCurveAnalysis):
    """QCloudPlusAnalysis class."""

    def _extract_result(self):
        """Extract result."""
        # x_data = self.experiment_data.x_data
        y_data = self.experiment_data.y_data

        for i, tub in enumerate(self.results.items()):
            label, result_obj = tub
            p_label = f"P{label}"
            p_arr = y_data.get(p_label)
            result_obj.value = [round(i, 4) for i in p_arr.tolist()]


class QCloudPlusV1(TopExperimentV1):
    """Q circuit process pulses map."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            xy_pulses_name_dict (dict): XY Line pulse map.
            z_pulses_name_dict (dict): Z Line pulse map.
            m_bits_list (list): Every loop measure bits

        """
        options = super()._default_experiment_options()

        options.set_validator("xy_pulses_name_dict", dict)
        options.set_validator("z_pulses_name_dict", dict)
        options.set_validator("m_bits_list", list)

        options.xy_pulses_name_dict = {}
        options.z_pulses_name_dict = {}
        options.m_bits_list = []

        # modify TopExperiment.experiment_options some default value
        options.enable_one_sweep = True

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default set run options of experiment."""
        options = super()._default_run_options()

        options.rd_qubits = []
        options.rd_channels = []

        options.es_prob_list = []
        options.er_prob_list = []

        options.support_context = [StandardContext.URM.value]

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options."""
        options = super()._default_analysis_options()

        options.is_plot = False
        options.result_parameters = []
        return options

    def get_qubit_str(self):
        """Get qubit str, an args qubit for SaveFile class.

        Returns:
            qubit_str: string, use to create save data path.
        """
        return "q-circuit"

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {}
        return metadata

    def _check_options(self):
        """Before run, some operations."""
        super()._check_options()

        m_bits_list = self.experiment_options.m_bits_list
        online_bits = self.run_options.online_bits

        if online_bits:
            qubit_list = [qubit for qubit in online_bits if isinstance(qubit, Qubit)]
        else:
            qubit_list = self.qubits
        qubit_map = {qubit.name: qubit for qubit in qubit_list}
        multi_readout_channels = get_multi_readout_channels(qubit_list)

        if isinstance(self.discriminator, list):
            dcm_list = self.discriminator
        else:
            dcm_list = [self.discriminator]

        rd_dcms = []
        rd_qubits = []
        rd_channels = []
        m_bits = m_bits_list[0]
        for m_bit in m_bits:
            q_name = f"q{m_bit}"
            qubit = qubit_map.get(q_name)
            rd_qubits.append(qubit)
            rd_channels.append(qubit.readout_channel)

            for dcm_obj in dcm_list:
                if dcm_obj.name == q_name:
                    rd_dcms.append(dcm_obj)
                    break

        label_list = self._get_binary_labels(len(m_bits))

        self.discriminator = rd_dcms
        self.set_experiment_options(
            bind_probe=False,
            multi_readout_channels=multi_readout_channels,
            data_type="I_Q",
            is_dynamic=0,
        )

        self.set_run_options(
            x_data=list(range(len(m_bits_list))),
            analysis_class=QCloudPlusV1Analysis,
            measure_qubits=qubit_list,
            rd_qubits=rd_qubits,
            rd_channels=rd_channels,
        )

        self.set_analysis_options(result_parameters=label_list)

    def _tackle_program(self, program):
        """Overwrite."""
        super()._tackle_program(program)
        self.run_options.measure_qubits = self.run_options.rd_qubits
        self.run_options.sample_channels = self.run_options.rd_channels

    # def _tackle_program(self):
    #     """Overwrite."""
    #     super()._tackle_program()
    #     self.run_options.measure_qubits = self.run_options.rd_qubits
    #     self.run_options.sample_channels = self.run_options.rd_channels

    def _alone_save_result(self):
        """Alone save some special result."""
        is_amend = self.experiment_options.is_amend
        label_list = self.analysis_options.result_parameters
        es_prob_list = self.run_options.es_prob_list
        er_prob_list = self.run_options.er_prob_list

        exp_data = self.analysis.experiment_data
        correct_y_data = exp_data.y_data
        if exp_data.metadata.process_meta:
            origin_y_data = exp_data.metadata.process_meta.get("origin_y_data")
        else:
            origin_y_data = {}

        if not origin_y_data:
            origin_y_data = correct_y_data

        for label in label_list:
            p_label = f"P{label}"
            s_prob = origin_y_data.get(p_label)
            r_prob = correct_y_data.get(p_label)

            es_prob_list.append(s_prob)
            er_prob_list.append(r_prob)

        self.file.save_data(np.array(es_prob_list), name="direct_prob")
        if is_amend is True:
            self.file.save_data(np.array(er_prob_list), name="amend_prob")

    @staticmethod
    def _get_binary_labels(bit_num: int) -> List:
        """Get some bits binary label list."""
        label_list = []
        for i in range(2 ** bit_num):
            label_list.append(np.binary_repr(i, width=bit_num))
        return label_list

    @staticmethod
    def set_xy_pulses(self):
        """Set XY pulse."""
        xy_pulses_name_dict = self.experiment_options.xy_pulses_name_dict

        for qubit in self.qubits:
            xy_pulse_list = xy_pulses_name_dict.get(qubit.name)
            self.play_pulse("XY", qubit, xy_pulse_list)

    @staticmethod
    def set_z_pulses(self):
        """Set Z pulse."""
        z_pulses_name_dict = self.experiment_options.z_pulses_name_dict

        all_base_qubits = [qubit for qubit in self.qubits]
        all_base_qubits.extend(self.couplers)
        for base_qubit in all_base_qubits:
            z_pulse_list = z_pulses_name_dict.get(base_qubit.name)
            self.play_pulse("Z", base_qubit, z_pulse_list)

    @staticmethod
    def update_instrument(self):
        """Update instrument parameters."""
        m_bits_list = self.experiment_options.m_bits_list
        rd_channels = self.experiment_options.multi_readout_channels

        sweep_delay = self._pulse_time_list[: len(m_bits_list)]
        for channel in rd_channels:
            self.sweep_readout_trigger_delay(channel, sweep_delay)

