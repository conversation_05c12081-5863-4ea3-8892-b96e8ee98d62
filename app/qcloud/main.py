# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/03
# __author:       <PERSON><PERSON><PERSON>

from datetime import datetime

from app.config import QDict, init_backend
from app.tool.utils import write_json_file


def extract_quantum_chip_data():
    backend = init_backend()
    data = QDict(
        QubitCount=0,
        CompensateAngle={},
        AvailableQubits=[],
        QGateClock={"CZ": 40, "RPhi": 30, "ECHO": 0, "BARRIER": 0},
        BasicQGate=["RPhi", "CZ"],
        LastUpdateTime=datetime.now().strftime("%Y-%m-%d %H-%M-%S"),
        MaxClockCycle=2000,
        MaxShots=10000,
    )

    for qubit in backend.chip_data.cache_qubit.values():
        if qubit.goodness is True:
            data.AvailableQubits.append(int(qubit.name[1:]))
            data.QubitCount += 1
            data.QubitParams[qubit.name[1:]] = dict(
                Frequency=qubit.drive_freq,
                T1=qubit.T1,
                T2=qubit.T2,
                SingleGateFidelity=qubit.fidelity,
            )
            dcm = backend.chip_data.cache_dcm.get(f"{qubit.name}_01.bin")
            if dcm:
                FidelityMat = [list(v) for v in dcm.fidelity_matrix]
                data.QubitParams[qubit.name[1:]].update(
                    dict(ReadoutFidelity=dcm.fidelity, FidelityMat=FidelityMat)
                )

    pair_list = "q10q16,q14q19,q16q22,q17q22,q1q7,q27q33,q28q33,q28q34,q30q36,q31q38,q33q39,q33q40,q34q40,q34q41,q36q42,q3q9,q40q45,q40q46,q41q46,q41q47,q42q47,q42q48,q45q52,q46q53,q47q53,q47q54,q48q54,q4q9,q50q55,q51q57,q52q57,q52q58,q53q58,q53q59,q54q60,q55q61,q55q62,q57q63,q57q64,q58q64,q58q65,q59q66,q61q67,q62q67,q63q68,q63q69,q64q69,q64q70,q65q70,q66q72,q67q73,q67q74,q68q74,q6q12,q73q79,q74q80,q77q82,q80q87,q81q87,q81q88,q84q90,q90q96,q94q100,q94q101,q95q102,q96q102,q9q15,q9q16".split(
        ","
    )
    for pair in pair_list:
        pair_obj = backend.chip_data.cache_qubit_pair.get(pair)
        _, q1, q2 = pair.split("q")
        data.CompensateAngle[f"CZ({q1},{q2})"] = {
            q1: pair_obj.cz_value(f"q{q1}", "phase"),
            q2: pair_obj.cz_value(f"q{q2}", "phase"),
        }

    write_json_file("quantum_chip_data.json", data)


def circuit_test():
    from app.qcloud.utils import qcloud_plus_test

    backend = init_backend()
    ir_result = """
    RX q[0],(-1.570796)
    RX q[1],(-1.570796)
    CZ q[0],q[1]
    H q[0]
    H q[1]
    MEASURE q[0],c[0]
    MEASURE q[1],c[1]
    """
    qcloud_plus_test(ir_result, ctx_manager=backend.context_manager)


if __name__ == "__main__":
    circuit_test()
    # extract_quantum_chip_data()
