{"QuantumChipArch": {"QubitCount": 72, "HighFrequencyQubit": [1, 10, 12, 17, 5, 14, 13, 7, 15, 16, 19, 3, 8, 26, 29, 25, 32, 27, 33, 34, 30, 35, 36, 31, 39, 38, 37, 46, 41, 42, 48, 43, 45, 53, 49, 50, 51, 55, 58, 63, 62, 61, 68, 72, 70], "CompensateAngle": {"CZ(1,2)": {"1": 4.766435026382899, "2": 1.015149661875704}, "CZ(1,7)": {"1": 5.75, "7": 2.15}, "CZ(10,11)": {"10": 2.313623798732702, "11": 0.7081329185576638}, "CZ(10,16)": {"10": 1.7687499999999998, "16": 1.65}, "CZ(4,10)": {"4": 0.0, "10": 0.0}, "CZ(11,12)": {"11": 1.25, "12": 2.35}, "CZ(11,17)": {"11": 1.35, "17": 5.75}, "CZ(5,11)": {"5": 2.85, "11": 4.45}, "CZ(12,18)": {"12": 2.46875, "18": 1.96875}, "CZ(13,14)": {"13": 0.0, "14": 4.1}, "CZ(13,19)": {"13": 0.0, "19": 0.0}, "CZ(7,13)": {"7": 3.1535288924632168, "13": 4.289122350769232}, "CZ(14,15)": {"14": 2.4, "15": 2.05}, "CZ(14,20)": {"14": 0.6070600229080914, "20": 5.497349128139296}, "CZ(8,14)": {"8": 5.9, "14": 3.05}, "CZ(15,16)": {"15": 2.65, "16": 0.4}, "CZ(9,15)": {"9": -0.05, "15": 5.275}, "CZ(16,17)": {"16": 5.286733606988456, "17": 3.453273680048967}, "CZ(17,18)": {"17": 1.4102284439811192, "18": 1.277035432179727}, "CZ(17,23)": {"17": 5.537989931918172, "23": 5.174709116674274}, "CZ(19,20)": {"19": 0.0, "20": 0.0}, "CZ(19,25)": {"19": 0.0, "25": 0.0}, "CZ(2,3)": {"2": 3.95, "3": 1.5}, "CZ(2,8)": {"2": 1.5, "8": 0.7}, "CZ(20,26)": {"20": 0.4712246452034223, "26": 3.215877173891399}, "CZ(23,29)": {"23": 2.7, "29": 3.7}, "CZ(25,26)": {"25": 4.29921875, "26": 5.10078125}, "CZ(25,31)": {"25": 5.9, "31": 3.25}, "CZ(26,27)": {"26": 4.45, "27": 5.574999999999999}, "CZ(26,32)": {"26": 2.3, "32": 2.3}, "CZ(27,28)": {"27": 3.4, "28": 2.1015625}, "CZ(27,33)": {"27": 0.25, "33": 4.7}, "CZ(28,29)": {"28": 3.65, "29": 4.35}, "CZ(28,34)": {"28": 3.4, "34": 4.25}, "CZ(29,30)": {"29": 5.3, "30": 2.8}, "CZ(29,35)": {"29": 2.45, "35": 5.324999999999999}, "CZ(3,4)": {"3": 0.5, "4": 0.95}, "CZ(3,9)": {"3": 2.55, "9": 3.95}, "CZ(30,36)": {"30": 3.25, "36": 1.6}, "CZ(31,32)": {"31": 5.998131793389733, "32": 3.24910769611349}, "CZ(31,37)": {"31": 4.4, "37": 5.7}, "CZ(32,33)": {"32": 3.40625, "33": 2.125}, "CZ(32,38)": {"32": 0.0, "38": 0.0}, "CZ(33,34)": {"33": 2.72460550529309, "34": 5.777604636360237}, "CZ(33,39)": {"33": 0.0, "39": 0.0}, "CZ(34,35)": {"34": 3.25, "35": 0.4}, "CZ(34,40)": {"34": 1.575, "40": 0.725}, "CZ(35,36)": {"35": 3.35, "36": 2.3}, "CZ(35,41)": {"35": 3.05, "41": 0.85}, "CZ(36,42)": {"36": 0.0, "42": 0.0}, "CZ(37,38)": {"37": 3.15, "38": 0.0}, "CZ(37,43)": {"37": 0.0, "43": 0.0}, "CZ(38,39)": {"38": 0.0, "39": 0.0}, "CZ(39,40)": {"39": 0.47169776056449464, "40": 0.3529696731190569}, "CZ(39,45)": {"39": 5.683626774226508, "45": 1.324781051845663}, "CZ(4,5)": {"4": 2.875, "5": 2.15}, "CZ(40,46)": {"40": 1.4375, "46": 5.775}, "CZ(40,41)": {"40": 2.25, "41": 5.75}, "CZ(41,42)": {"41": 3.238651644618167, "42": 1.0323891576552693}, "CZ(42,48)": {"42": 5.1, "48": 0.6}, "CZ(43,49)": {"43": 0.0, "49": 0.0}, "CZ(45,46)": {"45": 0.0, "46": 0.0}, "CZ(45,51)": {"45": 5.4, "51": 1.75}, "CZ(47,48)": {"47": 2.7090389433891326, "48": 0.8265828114661746}, "CZ(47,53)": {"47": 0.0, "53": 0.0}, "CZ(49,50)": {"49": 0.0, "50": 0.0}, "CZ(49,55)": {"49": 1.75, "55": 1.85}, "CZ(50,51)": {"50": 0.0, "51": 0.0}, "CZ(50,56)": {"50": 0.0, "56": 0.0}, "CZ(51,57)": {"51": 5.05, "57": 0.55}, "CZ(55,61)": {"55": 3.4406250000000003, "61": 0.5}, "CZ(57,58)": {"57": 0.0, "58": 1.75}, "CZ(57,63)": {"57": 5.45, "63": 4.1875}, "CZ(61,62)": {"61": 4.15, "62": 2.55}, "CZ(61,67)": {"61": 2.4, "67": 4.0}, "CZ(62,63)": {"62": 5.7, "63": 0.45}, "CZ(62,68)": {"62": 0.0, "68": 0.0}, "CZ(63,69)": {"63": 0.0, "69": 0.0}, "CZ(66,72)": {"66": 5.90163322343311, "72": 2.8581401011699716}, "CZ(67,68)": {"67": 1.0144510030024239, "68": 4.004984958210452}, "CZ(68,69)": {"68": 0.0, "69": 0.0}, "CZ(69,70)": {"69": 0.0, "70": 0.0}, "CZ(7,8)": {"7": 0.1509422553795739, "8": 0.04786609338454255}, "CZ(71,72)": {"71": -0.12364740602079838, "72": 0.6400860717453604}, "CZ(8,9)": {"8": 0.7, "9": 5.75}}, "AdjMatrix": {"1": [{"v": 2, "w": 0.9}, {"v": 7, "w": 0.9}], "2": [{"v": 1, "w": 0.9}, {"v": 3, "w": 0.9789}, {"v": 8, "w": 0.9682}], "3": [{"v": 2, "w": 0.9789}, {"v": 4, "w": 0.9404}, {"v": 9, "w": 0.9895}], "4": [{"v": 10, "w": 0.9}, {"v": 3, "w": 0.9404}, {"v": 5, "w": 0.9809}], "5": [{"v": 11, "w": 0.983}, {"v": 4, "w": 0.9809}], "7": [{"v": 1, "w": 0.9}, {"v": 13, "w": 0.9}, {"v": 8, "w": 0.9}], "8": [{"v": 14, "w": 0.968}, {"v": 2, "w": 0.9682}, {"v": 7, "w": 0.9}, {"v": 9, "w": 0.9813}], "9": [{"v": 15, "w": 0.9665}, {"v": 3, "w": 0.9895}, {"v": 8, "w": 0.9813}], "10": [{"v": 11, "w": 0.9}, {"v": 16, "w": 0.9727}, {"v": 4, "w": 0.9}], "11": [{"v": 10, "w": 0.9}, {"v": 12, "w": 0.9823}, {"v": 17, "w": 0.9}, {"v": 5, "w": 0.983}], "12": [{"v": 11, "w": 0.9823}, {"v": 18, "w": 0.9798}], "13": [{"v": 14, "w": 0.948}, {"v": 19, "w": 0.9}, {"v": 7, "w": 0.9}], "14": [{"v": 13, "w": 0.948}, {"v": 15, "w": 0.9431}, {"v": 20, "w": 0.9527}, {"v": 8, "w": 0.968}], "15": [{"v": 14, "w": 0.9431}, {"v": 16, "w": 0.9454}, {"v": 9, "w": 0.9665}], "16": [{"v": 10, "w": 0.9727}, {"v": 15, "w": 0.9454}, {"v": 17, "w": 0.9656}], "17": [{"v": 11, "w": 0.9}, {"v": 16, "w": 0.9656}, {"v": 18, "w": 0.9414}, {"v": 23, "w": 0.9}], "18": [{"v": 12, "w": 0.9798}, {"v": 17, "w": 0.9414}], "19": [{"v": 13, "w": 0.9}, {"v": 20, "w": 0.9}, {"v": 25, "w": 0.9}], "20": [{"v": 14, "w": 0.9527}, {"v": 19, "w": 0.9}, {"v": 26, "w": 0.9748}], "23": [{"v": 17, "w": 0.9}, {"v": 29, "w": 0.9797}], "25": [{"v": 19, "w": 0.9}, {"v": 26, "w": 0.9482}, {"v": 31, "w": 0.9523}], "26": [{"v": 20, "w": 0.9748}, {"v": 25, "w": 0.9482}, {"v": 27, "w": 0.9684}, {"v": 32, "w": 0.9561}], "27": [{"v": 26, "w": 0.9684}, {"v": 28, "w": 0.9854}, {"v": 33, "w": 0.9771}], "28": [{"v": 27, "w": 0.9854}, {"v": 29, "w": 0.9573}, {"v": 34, "w": 0.9678}], "29": [{"v": 23, "w": 0.9797}, {"v": 28, "w": 0.9573}, {"v": 30, "w": 0.9687}, {"v": 35, "w": 0.9454}], "30": [{"v": 29, "w": 0.9687}, {"v": 36, "w": 0.9837}], "31": [{"v": 25, "w": 0.9523}, {"v": 32, "w": 0.9}, {"v": 37, "w": 0.9729}], "32": [{"v": 26, "w": 0.9561}, {"v": 31, "w": 0.9}, {"v": 33, "w": 0.9424}, {"v": 38, "w": 0.9}], "33": [{"v": 27, "w": 0.9771}, {"v": 32, "w": 0.9424}, {"v": 34, "w": 0.9506}, {"v": 39, "w": 0.9}], "34": [{"v": 28, "w": 0.9678}, {"v": 33, "w": 0.9506}, {"v": 35, "w": 0.9633}, {"v": 40, "w": 0.9538}], "35": [{"v": 29, "w": 0.9454}, {"v": 34, "w": 0.9633}, {"v": 36, "w": 0.9506}, {"v": 41, "w": 0.9438}], "36": [{"v": 30, "w": 0.9837}, {"v": 35, "w": 0.9506}, {"v": 42, "w": 0.9}], "37": [{"v": 31, "w": 0.9729}, {"v": 38, "w": 0.9522}, {"v": 43, "w": 0.9}], "38": [{"v": 32, "w": 0.9}, {"v": 37, "w": 0.9522}, {"v": 39, "w": 0.9}], "39": [{"v": 33, "w": 0.9}, {"v": 38, "w": 0.9}, {"v": 40, "w": 0.9}, {"v": 45, "w": 0.9}], "40": [{"v": 34, "w": 0.9538}, {"v": 39, "w": 0.9}, {"v": 46, "w": 0.9606}, {"v": 41, "w": 0.9718}], "41": [{"v": 35, "w": 0.9438}, {"v": 40, "w": 0.9718}, {"v": 42, "w": 0.936}], "42": [{"v": 36, "w": 0.9}, {"v": 41, "w": 0.936}, {"v": 48, "w": 0.9466}], "43": [{"v": 37, "w": 0.9}, {"v": 49, "w": 0.9}], "45": [{"v": 39, "w": 0.9}, {"v": 46, "w": 0.9}, {"v": 51, "w": 0.9}], "46": [{"v": 40, "w": 0.9606}, {"v": 45, "w": 0.9}], "47": [{"v": 48, "w": 0.9553}, {"v": 53, "w": 0.9}], "48": [{"v": 42, "w": 0.9466}, {"v": 47, "w": 0.9553}], "49": [{"v": 43, "w": 0.9}, {"v": 50, "w": 0.9}, {"v": 55, "w": 0.9546}], "50": [{"v": 49, "w": 0.9}, {"v": 51, "w": 0.9}, {"v": 56, "w": 0.9}], "51": [{"v": 45, "w": 0.9}, {"v": 50, "w": 0.9}, {"v": 57, "w": 0.9831}], "53": [{"v": 47, "w": 0.9}], "55": [{"v": 49, "w": 0.9546}, {"v": 61, "w": 0.9709}], "56": [{"v": 50, "w": 0.9}], "57": [{"v": 51, "w": 0.9831}, {"v": 58, "w": 0.9439}, {"v": 63, "w": 0.9764}], "58": [{"v": 57, "w": 0.9439}], "61": [{"v": 55, "w": 0.9709}, {"v": 62, "w": 0.9}, {"v": 67, "w": 0.972}], "62": [{"v": 61, "w": 0.9}, {"v": 63, "w": 0.9488}, {"v": 68, "w": 0.9}], "63": [{"v": 57, "w": 0.9764}, {"v": 62, "w": 0.9488}, {"v": 69, "w": 0.9}], "66": [{"v": 72, "w": 0.9}], "67": [{"v": 61, "w": 0.972}, {"v": 68, "w": 0.9453}], "68": [{"v": 62, "w": 0.9}, {"v": 67, "w": 0.9453}, {"v": 69, "w": 0.9}], "69": [{"v": 63, "w": 0.9}, {"v": 68, "w": 0.9}, {"v": 70, "w": 0.9}], "70": [{"v": 69, "w": 0.9}], "71": [{"v": 72, "w": 0.9559}], "72": [{"v": 66, "w": 0.9}, {"v": 71, "w": 0.9559}]}, "QubitParams": {"1": {"Frequency": 4764.0, "T1": 9.254, "T2": 1.128, "ReadoutFidelity": 0.8835, "FidelityMat": [[0.9702, 0.2032], [0.0298, 0.7968]], "SingleGateFidelity": 0.9917}, "2": {"Frequency": 4041.0, "T1": 17.436, "T2": 1.844, "ReadoutFidelity": 0.9487, "FidelityMat": [[0.9838, 0.0864], [0.0162, 0.9136]], "SingleGateFidelity": 0.9963}, "3": {"Frequency": 4239.0, "T1": 13.08, "T2": 1.091, "ReadoutFidelity": 0.8011, "FidelityMat": [[0.9366, 0.3344], [0.0634, 0.6656]], "SingleGateFidelity": 0.9988}, "4": {"Frequency": 3790.0, "T1": 41.965, "T2": 2.773, "ReadoutFidelity": 0.839, "FidelityMat": [[0.9906, 0.3126], [0.0094, 0.6874]], "SingleGateFidelity": 0.9986}, "5": {"Frequency": 4358.0, "T1": 19.928, "T2": 1.266, "ReadoutFidelity": 0.9227, "FidelityMat": [[0.9948, 0.1494], [0.0052, 0.8506]], "SingleGateFidelity": 0.9982}, "7": {"Frequency": 4656, "T1": 4.845, "T2": 10.0, "ReadoutFidelity": 0.8892, "FidelityMat": [[0.9474, 0.169], [0.0526, 0.831]], "SingleGateFidelity": 0.9938}, "8": {"Frequency": 4203, "T1": 20.0, "T2": 10.0, "ReadoutFidelity": 0.847, "FidelityMat": [[0.9474, 0.2534], [0.0526, 0.7466]], "SingleGateFidelity": 0.9984}, "9": {"Frequency": 3915.0, "T1": 15.235, "T2": 1.744, "ReadoutFidelity": 0.8929, "FidelityMat": [[0.984, 0.1982], [0.016, 0.8018]], "SingleGateFidelity": 0.9991}, "10": {"Frequency": 4776.0, "T1": 18.4, "T2": 1.26, "ReadoutFidelity": 0.7621, "FidelityMat": [[0.9592, 0.435], [0.0408, 0.565]], "SingleGateFidelity": 0.9975}, "11": {"Frequency": 3956.0, "T1": 19.648, "T2": 1.327, "ReadoutFidelity": 0.8536, "FidelityMat": [[0.9898, 0.2826], [0.0102, 0.7174]], "SingleGateFidelity": 0.9986}, "12": {"Frequency": 4224.0, "T1": 20.674, "T2": 1.156, "ReadoutFidelity": 0.8368, "FidelityMat": [[0.9956, 0.322], [0.0044, 0.678]], "SingleGateFidelity": 0.9967}, "13": {"Frequency": 4251.0, "T1": 14.819, "T2": 1.315, "ReadoutFidelity": 0.7908, "FidelityMat": [[0.987, 0.4054], [0.013, 0.5946]], "SingleGateFidelity": 0.9955}, "14": {"Frequency": 4530.0, "T1": 19.781, "T2": 1.22, "ReadoutFidelity": 0.9285, "FidelityMat": [[0.9842, 0.1272], [0.0158, 0.8728]], "SingleGateFidelity": 0.9964}, "15": {"Frequency": 4891.0, "T1": 18.681, "T2": 1.073, "ReadoutFidelity": 0.8559, "FidelityMat": [[0.917, 0.2052], [0.083, 0.7948]], "SingleGateFidelity": 0.9971}, "16": {"Frequency": 4245, "T1": 17.21, "T2": 1.264, "ReadoutFidelity": 0.8838, "FidelityMat": [[0.9406, 0.173], [0.0594, 0.827]], "SingleGateFidelity": 0.9954}, "17": {"Frequency": 4139.409, "T1": 11.991, "T2": 2.032, "ReadoutFidelity": 0.8942, "FidelityMat": [[0.9408, 0.1524], [0.0592, 0.8476]], "SingleGateFidelity": 0.9982}, "18": {"Frequency": 3924.0, "T1": 30.324, "T2": 1.934, "ReadoutFidelity": 0.9107, "FidelityMat": [[0.9878, 0.1664], [0.0122, 0.8336]], "SingleGateFidelity": 0.9987}, "19": {"Frequency": 3995.0, "T1": 27.243, "T2": 10.0, "ReadoutFidelity": 0.7599, "FidelityMat": [[0.982, 0.4622], [0.018, 0.5378]], "SingleGateFidelity": 0.9952}, "20": {"Frequency": 4106.0, "T1": 32.023, "T2": 1.919, "ReadoutFidelity": 0.7924, "FidelityMat": [[0.9838, 0.399], [0.0162, 0.601]], "SingleGateFidelity": 0.9985}, "23": {"Frequency": 4013.0, "T1": 18.643, "T2": 0.974, "ReadoutFidelity": 0.8794, "FidelityMat": [[0.9404, 0.1816], [0.0596, 0.8184]], "SingleGateFidelity": 0.998}, "25": {"Frequency": 4939.0, "T1": 7.581, "T2": 1.681, "ReadoutFidelity": 0.9382, "FidelityMat": [[0.9822, 0.1058], [0.0178, 0.8942]], "SingleGateFidelity": 0.9964}, "26": {"Frequency": 4618.0, "T1": 27.94, "T2": 1.464, "ReadoutFidelity": 0.8139, "FidelityMat": [[0.9424, 0.3146], [0.0576, 0.6854]], "SingleGateFidelity": 0.9966}, "27": {"Frequency": 4041.0, "T1": 19.291, "T2": 4.967, "ReadoutFidelity": 0.8499, "FidelityMat": [[0.9794, 0.2796], [0.0206, 0.7204]], "SingleGateFidelity": 0.996}, "28": {"Frequency": 3818.0, "T1": 17.805, "T2": 1.737, "ReadoutFidelity": 0.8879, "FidelityMat": [[0.98, 0.2042], [0.02, 0.7958]], "SingleGateFidelity": 0.9972}, "29": {"Frequency": 4154.0, "T1": 20.273, "T2": 1.999, "ReadoutFidelity": 0.9025, "FidelityMat": [[0.9822, 0.1772], [0.0178, 0.8228]], "SingleGateFidelity": 0.9984}, "30": {"Frequency": 4351, "T1": 16.606, "T2": 1.253, "ReadoutFidelity": 0.9399, "FidelityMat": [[0.996, 0.1162], [0.004, 0.8838]], "SingleGateFidelity": 0.9965}, "31": {"Frequency": 4515.0, "T1": 14.876, "T2": 0.877, "ReadoutFidelity": 0.8642, "FidelityMat": [[0.958, 0.2296], [0.042, 0.7704]], "SingleGateFidelity": 0.9972}, "32": {"Frequency": 4907.0, "T1": 13.618, "T2": 1.432, "ReadoutFidelity": 0.8626, "FidelityMat": [[0.9752, 0.25], [0.0248, 0.75]], "SingleGateFidelity": 0.9972}, "33": {"Frequency": 4454.0, "T1": 17.58, "T2": 1.572, "ReadoutFidelity": 0.8907, "FidelityMat": [[0.9544, 0.173], [0.0456, 0.827]], "SingleGateFidelity": 0.9955}, "34": {"Frequency": 4400.0, "T1": 12.417, "T2": 10.0, "ReadoutFidelity": 0.9188, "FidelityMat": [[0.9568, 0.1192], [0.0432, 0.8808]], "SingleGateFidelity": 0.9954}, "35": {"Frequency": 4305.0, "T1": 15.299, "T2": 2.645, "ReadoutFidelity": 0.873, "FidelityMat": [[0.9856, 0.2396], [0.0144, 0.7604]], "SingleGateFidelity": 0.998}, "36": {"Frequency": 4785.0, "T1": 24.835, "T2": 1.363, "ReadoutFidelity": 0.9237, "FidelityMat": [[0.958, 0.1106], [0.042, 0.8894]], "SingleGateFidelity": 0.9979}, "37": {"Frequency": 4149.0, "T1": 20.0, "T2": 10.0, "ReadoutFidelity": 0.8992, "FidelityMat": [[0.9668, 0.1684], [0.0332, 0.8316]], "SingleGateFidelity": 0.9979}, "38": {"Frequency": 4177.0, "T1": 23.939, "T2": 1.078, "ReadoutFidelity": 0.9146, "FidelityMat": [[0.9968, 0.1676], [0.0032, 0.8324]], "SingleGateFidelity": 0.997}, "39": {"Frequency": 4868.0, "T1": 21.524, "T2": 1.057, "ReadoutFidelity": 0.9059, "FidelityMat": [[0.98, 0.1682], [0.02, 0.8318]], "SingleGateFidelity": 0.9971}, "40": {"Frequency": 3955.0, "T1": 22.461, "T2": 1.131, "ReadoutFidelity": 0.9174, "FidelityMat": [[0.9576, 0.1228], [0.0424, 0.8772]], "SingleGateFidelity": 0.9982}, "41": {"Frequency": 4115.0, "T1": 18.581, "T2": 3.845, "ReadoutFidelity": 0.893, "FidelityMat": [[0.9462, 0.1602], [0.0538, 0.8398]], "SingleGateFidelity": 0.9985}, "42": {"Frequency": 4231.0, "T1": 10.611, "T2": 2.136, "ReadoutFidelity": 0.7641, "FidelityMat": [[0.9554, 0.4272], [0.0446, 0.5728]], "SingleGateFidelity": 0.9966}, "43": {"Frequency": 4425.478, "T1": 20.0, "T2": 10.0, "ReadoutFidelity": 0.8872, "FidelityMat": [[0.9618, 0.1874], [0.0382, 0.8126]], "SingleGateFidelity": 0.9527}, "45": {"Frequency": 4353.0, "T1": 21.4, "T2": 1.814, "ReadoutFidelity": 0.8806, "FidelityMat": [[0.9196, 0.1584], [0.0804, 0.8416]], "SingleGateFidelity": 0.9979}, "46": {"Frequency": 3990.0, "T1": 20.787, "T2": 1.324, "ReadoutFidelity": 0.9223, "FidelityMat": [[0.928, 0.0834], [0.072, 0.9166]], "SingleGateFidelity": 0.9978}, "47": {"Frequency": 3824.0, "T1": 28.148, "T2": 1.123, "ReadoutFidelity": 0.8547, "FidelityMat": [[0.9902, 0.2808], [0.0098, 0.7192]], "SingleGateFidelity": 0.999}, "48": {"Frequency": 4746, "T1": 19.055, "T2": 0.686, "ReadoutFidelity": 0.9087, "FidelityMat": [[0.9894, 0.172], [0.0106, 0.828]], "SingleGateFidelity": 0.9971}, "49": {"Frequency": 4955, "T1": 19.759, "T2": 0.468, "ReadoutFidelity": 0.9406, "FidelityMat": [[0.9922, 0.111], [0.0078, 0.889]], "SingleGateFidelity": 0.9964}, "50": {"Frequency": 3934.0, "T1": 12.68, "T2": 0.7, "ReadoutFidelity": 0.895, "FidelityMat": [[0.9912, 0.2012], [0.0088, 0.7988]], "SingleGateFidelity": 0.9971}, "51": {"Frequency": 4200.0, "T1": 18.688, "T2": 0.58, "ReadoutFidelity": 0.859, "FidelityMat": [[0.903, 0.185], [0.097, 0.815]], "SingleGateFidelity": 0.9969}, "53": {"Frequency": 4851.0, "T1": 15.665, "T2": 0.77, "ReadoutFidelity": 0.8874, "FidelityMat": [[0.9786, 0.2038], [0.0214, 0.7962]], "SingleGateFidelity": 0.9981}, "55": {"Frequency": 4541.0, "T1": 14.033, "T2": 0.789, "ReadoutFidelity": 0.8907, "FidelityMat": [[0.9716, 0.1902], [0.0284, 0.8098]], "SingleGateFidelity": 0.9954}, "56": {"Frequency": 4894.0, "T1": 10.289, "T2": 1.029, "ReadoutFidelity": 0.9378, "FidelityMat": [[0.989, 0.1134], [0.011, 0.8866]], "SingleGateFidelity": 0.9963}, "57": {"Frequency": 3804.0, "T1": 16.266, "T2": 0.624, "ReadoutFidelity": 0.8739, "FidelityMat": [[0.9688, 0.221], [0.0312, 0.779]], "SingleGateFidelity": 0.9984}, "58": {"Frequency": 4720.0, "T1": 15.408, "T2": 0.61, "ReadoutFidelity": 0.9275, "FidelityMat": [[0.9884, 0.1334], [0.0116, 0.8666]], "SingleGateFidelity": 0.9985}, "61": {"Frequency": 4104.0, "T1": 27.662, "T2": 0.86, "ReadoutFidelity": 0.9192, "FidelityMat": [[0.9764, 0.138], [0.0236, 0.862]], "SingleGateFidelity": 0.9981}, "62": {"Frequency": 4393.0, "T1": 16.348, "T2": 0.983, "ReadoutFidelity": 0.8413, "FidelityMat": [[0.9208, 0.2382], [0.0792, 0.7618]], "SingleGateFidelity": 0.9975}, "63": {"Frequency": 4521.0, "T1": 10.81, "T2": 0.712, "ReadoutFidelity": 0.9104, "FidelityMat": [[0.9822, 0.1614], [0.0178, 0.8386]], "SingleGateFidelity": 0.9957}, "66": {"Frequency": 4333.0, "T1": 16.04, "T2": 0.522, "ReadoutFidelity": 0.9267, "FidelityMat": [[0.9814, 0.128], [0.0186, 0.872]], "SingleGateFidelity": 0.9951}, "67": {"Frequency": 3793.0, "T1": 25.834, "T2": 0.631, "ReadoutFidelity": 0.9143, "FidelityMat": [[0.9682, 0.1396], [0.0318, 0.8604]], "SingleGateFidelity": 0.998}, "68": {"Frequency": 4616.0, "T1": 18.417, "T2": 0.572, "ReadoutFidelity": 0.8443, "FidelityMat": [[0.965, 0.2764], [0.035, 0.7236]], "SingleGateFidelity": 0.9962}, "69": {"Frequency": 4345.0, "T1": 14.643, "T2": 0.744, "ReadoutFidelity": 0.5322, "FidelityMat": [[0.7606, 0.6962], [0.2394, 0.3038]], "SingleGateFidelity": 0.9951}, "70": {"Frequency": 4395.0, "T1": 30.684, "T2": 0.502, "ReadoutFidelity": 0.9226, "FidelityMat": [[0.9708, 0.1256], [0.0292, 0.8744]], "SingleGateFidelity": 0.9952}, "71": {"Frequency": 4260.0, "T1": 28.34, "T2": 0.525, "ReadoutFidelity": 0.9112, "FidelityMat": [[0.9768, 0.1544], [0.0232, 0.8456]], "SingleGateFidelity": 0.9955}, "72": {"Frequency": 4676.0, "T1": 15.387, "T2": 0.645, "ReadoutFidelity": 0.8956, "FidelityMat": [[0.9766, 0.1854], [0.0234, 0.8146]], "SingleGateFidelity": 0.996}}, "AvailableQubits": [1, 2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 45, 46, 47, 48, 49, 50, 51, 53, 55, 56, 57, 58, 61, 62, 63, 66, 67, 68, 69, 70, 71, 72], "QGateClock": {"CZ": 40, "RPhi": 30, "ECHO": 0, "BARRIER": 0}, "BasicQGate": ["RPhi", "CZ"], "LastUpdateTime": 1734423794107, "MaxClockCycle": 2000, "MaxShots": 10000}}