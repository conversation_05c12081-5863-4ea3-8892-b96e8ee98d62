# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/03
# __author:       <PERSON><PERSON><PERSON>


import asyncio
import json
import re
from typing import Dict, List, Union

import numpy as np

from pyQCat.errors import PyQCatError
from pyQCat.executor import MonsterContextManager
from pyQCat.gate import Rphi_gate as RPhiGate
from pyQCat.pulse import Constant, PulseComponent
from pyQCat.pulse_adjust import params_to_pulse
from pyQCat.qaio_property import QAIO
from pyQCat.qubit import Qubit, QubitPair

from .experiment import QCloudPlusV1

QAIO.type = 72
PulseComponent.fake = True

with open(r"app/qcloud/quantum_chip_data.json", "r") as f:
    QuantumChipArch = json.load(f)

q_bits = QuantumChipArch["QuantumChipArch"]["AvailableQubits"]
pairs = []
rad2angle = 1 / np.pi * 180

# 可用的门长
time_map = {"I": 40, "RX": 40, "U3": 40, "CZ": 60, "H": 40, "CNOT": 60}

i_gate_width = 10

qgate_clock_map = {"RPhi": 4, "CZ": 6}

CompensateAngle = QuantumChipArch["QuantumChipArch"]["CompensateAngle"]
CZ_PHASE = {}
for qubit_pair_str in CompensateAngle.keys():
    [q1, q2] = CompensateAngle[qubit_pair_str].keys()
    pairs.append(f"q{q1}q{q2}")
    CZ_PHASE[q1 + "-" + q2] = {
        q1: CompensateAngle[qubit_pair_str][q1],
        q2: CompensateAngle[qubit_pair_str][q2],
    }
    CZ_PHASE[q2 + "-" + q1] = {
        q1: CompensateAngle[qubit_pair_str][q1],
        q2: CompensateAngle[qubit_pair_str][q2],
    }

print_flag = False
map_flag = True

# 可直接使用的门
gate_list = ["RX", "U3", "H", "CZ", "CNOT", "BARRIER", "MEASURE"]
# 给物理比特排序，用于实验使用，本次是排了8个比特，实验也只能调用这8个比特
qubit_map = {
    "0": 1,
    "1": 7,
    "2": 7,
    "3": 4,
    "4": 5,
    "5": 6,
    "6": 7,
    "7": 8,
    "8": 9,
    "9": 10,
    "10": 11,
    "11": 12,
    "12": 13,
    "13": 14,
    "14": 15,
    "15": 16,
    "16": 17,
    "17": 18,
    "18": 20,
    "19": 21,
    "20": 22,
    "21": 23,
    "22": 24,
    "23": 25,
    "24": 26,
    "25": 27,
    "26": 28,
    "27": 29,
    "28": 30,
    "29": 31,
    "30": 32,
    "31": 33,
    "32": 34,
    "33": 35,
    "34": 36,
    "35": 37,
    "36": 38,
    "37": 39,
    "38": 40,
    "39": 41,
    "40": 42,
    "41": 44,
    "42": 45,
    "43": 46,
    "44": 47,
    "45": 48,
    "46": 49,
    "47": 51,
    "48": 52,
    "49": 53,
    "50": 54,
    "51": 55,
    "52": 56,
    "53": 57,
    "54": 58,
    "55": 59,
    "56": 60,
    "57": 61,
    "58": 62,
    "59": 63,
    "60": 64,
    "61": 65,
    "62": 66,
    "63": 67,
    "64": 68,
    "65": 69,
    "66": 70,
    "67": 71,
    "68": 72,
    "-1": None,
}


class CircleParamError(PyQCatError):
    """Quantum Circle parameters error."""

    def __init__(self, err_msg):
        self.err = err_msg


def ir_translation(ir, q_list):
    _time_map = time_map
    str1 = ir.split("\n")
    str2_list = []
    # 初始化
    for i, gate in enumerate(str1):
        str2_list.append([v.strip() for v in gate.split(",")])

    _clock_list = np.zeros_like(q_list)
    _phase_list = [0.0 for i in range(0, len(q_list))]
    qprog_list = []  # 储存翻译后的脉冲？
    measure_qubit = []
    for i, gate in enumerate(str2_list):
        if len(gate) == 1 and gate[0] == "":
            continue

        _gate_name = gate[0].split()[0]

        if print_flag is True:
            print(_gate_name)
        ##RX门
        if _gate_name == "RX":
            _qubit = int(re.findall(r"[[](.*?)[]]", gate[0].split()[1])[0])
            if map_flag is True:
                _qubit = qubit_map[str(_qubit)]
            _qubit_index = q_list.index(_qubit)

            if print_flag is True:
                print(_gate_name, _qubit)
                print("phase:", _phase_list)
                print("clock:", _clock_list)
            _phi1 = 90
            _phase_list[_qubit_index] += _phi1
            _phase = _phase_list[_qubit_index]
            _clock = _clock_list[_qubit_index]

            if print_flag is True:
                print("phase:", _phase_list)
                print("stride RZ(-pi/2)")
            qprog_list.append({"RPhi": [_qubit, _phase, _clock]})
            _clock_list[_qubit_index] += _time_map[_gate_name]
            if print_flag is True:
                print("clock:", _clock_list)
                print("stride RX(pi/2)")

            _phi2 = (float(gate[1][1:-1])) * rad2angle - 180
            _phase_list[_qubit_index] += _phi2
            _phase = _phase_list[_qubit_index]
            _clock = _clock_list[_qubit_index]

            if print_flag is True:
                print("phase:", _phase_list)
                print("stride RZ(pi-theta)")
            qprog_list.append({"RPhi": [_qubit, _phase, _clock]})
            _phi3 = 90
            _phase_list[_qubit_index] += _phi3
            _clock_list[_qubit_index] += _time_map[_gate_name]
            if print_flag is True:
                print("clock:", _clock_list)
                print("stride RX(pi/2)")
                print("phase:", _phase_list)
                print("stride RZ(-pi/2)")
                print("\n")
            _qubit_index = None
            _qubit = None
        ##U3门
        if _gate_name == "U3":
            _qubit = int(re.findall(r"[[](.*?)[]]", gate[0].split()[1])[0])
            if map_flag is True:
                _qubit = qubit_map[str(_qubit)]
            if print_flag is True:
                print(_gate_name, _qubit)
            _qubit_index = q_list.index(_qubit)

            if print_flag is True:
                print("phase:", _phase_list)
                print("clock:", _clock_list)
            _phi1 = float(gate[3][:-1]) * rad2angle * -1
            _phase_list[_qubit_index] += _phi1
            _phase = _phase_list[_qubit_index]
            _clock = _clock_list[_qubit_index]

            if print_flag is True:
                print("phase:", _phase_list)
                print("stride RZ(lambda)")

            qprog_list.append({"RPhi": [_qubit, _phase, _clock]})
            _clock_list[_qubit_index] += _time_map[_gate_name]
            if print_flag is True:
                print("clock:", _clock_list)
                print("stride RX(pi/2)")

            _phi2 = float(gate[1][1:]) * rad2angle * -1
            _phase_list[_qubit_index] += _phi2
            _phase = _phase_list[_qubit_index]
            _clock = _clock_list[_qubit_index]

            if print_flag is True:
                print("phase:", _phase_list)
                print("stride RZ(theta)")

            qprog_list.append({"RPhi": [_qubit, 180 + _phase, _clock]})
            _phi3 = float(gate[2]) * rad2angle * -1
            _phase_list[_qubit_index] += _phi3
            _clock_list[_qubit_index] += _time_map[_gate_name]

            if print_flag is True:
                print("clock:", _clock_list)
                print("stride RX(-pi/2)")
                print("phase:", _phase_list)
                print("stride RZ(phi)")
                print("\n")
            _qubit_index = None
            _qubit = None
        ##H门
        if _gate_name == "H":
            _qubit = int(re.findall(r"[[](.*?)[]]", gate[0].split()[1])[0])
            if map_flag is True:
                _qubit = qubit_map[str(_qubit)]
            if print_flag is True:
                print(_gate_name, _qubit)
            _qubit_index = q_list.index(_qubit)

            if print_flag is True:
                print("phase:", _phase_list)
                print("clock:", _clock_list)
            _phi1 = -90
            _phase_list[_qubit_index] += _phi1
            _phase = _phase_list[_qubit_index]
            _clock = _clock_list[_qubit_index]

            if print_flag is True:
                print("phase:", _phase_list)
                print("stride RZ(pi/2)")
            qprog_list.append({"RPhi": [_qubit, _phase, _clock]})
            _clock_list[_qubit_index] += _time_map[_gate_name]
            _phi2 = -90
            _phase_list[_qubit_index] += _phi2

            if print_flag is True:
                print("clock:", _clock_list)
                print("stride RX(pi/2)")
                print("phase:", _phase_list)
                print("stride RZ(pi/2)")
                print("\n")
            _qubit_index = None
            _qubit = None
        ##I门？
        if _gate_name == "I":
            _qubit = int(re.findall(r"[[](.*?)[]]", gate[0].split()[1])[0])
            if map_flag is True:
                _qubit = qubit_map[str(_qubit)]
            _qubit_index = q_list.index(_qubit)

            if print_flag is True:
                print(_gate_name, _qubit)
                print("phase:", _phase_list)
                print("clock:", _clock_list)
            _clock = _clock_list[_qubit_index]
            qprog_list.append({"I": [_qubit, _time_map[_gate_name], _clock]})
            _clock_list[_qubit_index] += _time_map[_gate_name]
            if print_flag is True:
                print("stride I")
                print("clock:", _clock_list)
                print("\n")

            _qubit_index = None
            _qubit = None
        ##CZ门
        if _gate_name == "CZ":
            _qc_name = int(re.findall(r"[[](.*?)[]]", gate[0].split()[1])[0])
            _qt_name = int(re.findall(r"[[](.*?)[]]", gate[1])[0])
            if map_flag is True:
                _qc_name = qubit_map[str(_qc_name)]
                _qt_name = qubit_map[str(_qt_name)]
            _qc_index = q_list.index(_qc_name)
            _qt_index = q_list.index(_qt_name)
            # print(_qc_index)
            # print(_qt_index)

            _clock = max(_clock_list[_qc_index], _clock_list[_qt_index])
            if print_flag is True:
                print(_gate_name, _qc_name, _qt_name)
                print("phase:", _phase_list)
                print("clock:", _clock_list)

            qprog_list.append({"CZ": [_qc_name, _qt_name, _clock]})
            _phase_list[_qc_index] -= (
                CZ_PHASE["{}-{}".format(_qc_name, _qt_name)]["{}".format(_qc_name)]
                * rad2angle
            )
            _phase_list[_qt_index] -= (
                CZ_PHASE["{}-{}".format(_qc_name, _qt_name)]["{}".format(_qt_name)]
                * rad2angle
            )
            _clock_list[_qc_index] = _time_map[_gate_name] + _clock
            _clock_list[_qt_index] = _time_map[_gate_name] + _clock

            if print_flag is True:
                print("clock:", _clock_list)
                print("stride CZ")
                print("phase:", _phase_list)
                print("stride phase compensation")
                print("\n")
            _qc_name = None
            _qt_name = None
            _qc_index = None
            _qt_index = None
        ##CNOT门
        if _gate_name == "CNOT":
            _qc_name = int(re.findall(r"[[](.*?)[]]", gate[0].split()[1])[0])
            _qt_name = int(re.findall(r"[[](.*?)[]]", gate[1])[0])
            if map_flag is True:
                _qc_name = qubit_map[str(_qc_name)]
                _qt_name = qubit_map[str(_qt_name)]
            _qc_index = q_list.index(_qc_name)
            _qt_index = q_list.index(_qt_name)

            _clock = max(_clock_list[_qc_index], _clock_list[_qt_index])
            if print_flag is True:
                print(_gate_name, _qc_name, _qt_name)
                print("phase:", _phase_list)
                print("clock:", _clock_list)

            # H
            _phi1 = -90
            _phase_list[_qt_index] += _phi1
            _phase = _phase_list[_qt_index]

            if print_flag is True:
                print("phase:", _phase_list)
                print("stride RZ(pi/2)")
            qprog_list.append({"RPhi": [_qt_name, _phase, _clock]})
            _clock_list[_qt_index] = _time_map["H"] + _clock

            if print_flag is True:
                print("clock:", _clock_list)
                print("stride RX(pi/2)")

            _phi2 = -90
            _phase_list[_qt_index] += _phi2
            if print_flag is True:
                print("phase:", _phase_list)
                print("stride RZ(pi/2)")

            # CZ
            _clock = max(_clock_list[_qc_index], _clock_list[_qt_index])
            qprog_list.append({"CZ": [_qc_name, _qt_name, _clock]})

            _phase_list[_qc_index] -= (
                CZ_PHASE["{}-{}".format(_qc_name, _qt_name)]["{}".format(_qc_name)]
                * rad2angle
            )
            _phase_list[_qt_index] -= (
                CZ_PHASE["{}-{}".format(_qc_name, _qt_name)]["{}".format(_qt_name)]
                * rad2angle
            )
            _clock_list[_qc_index] = _time_map[_gate_name] + _clock
            _clock_list[_qt_index] = _time_map[_gate_name] + _clock
            if print_flag is True:
                print("clock:", _clock_list)
                print("stride CZ")
                print("phase:", _phase_list)
                print("stride phase compensation")

            # H
            _phi1 = -90
            _phase_list[_qt_index] += _phi1
            _phase = _phase_list[_qt_index]
            _clock = _clock_list[_qt_index]

            if print_flag is True:
                print("phase:", _phase_list)
                print("stride RZ(pi/2)")
            qprog_list.append({"RPhi": [_qt_name, _phase, _clock]})
            _clock_list[_qt_index] += _time_map["H"]
            _phi2 = -90
            _phase_list[_qt_index] += _phi2
            if print_flag is True:
                print("clock:", _clock_list)
                print("stride RX(pi/2)")
                print("phase:", _phase_list)
                print("stride RZ(pi/2)")
                print("\n")
            _qc_name = None
            _qt_name = None
            _qc_index = None
            _qt_index = None
        ##BARRIER门
        if _gate_name == "BARRIER":
            _barrier_qubit_name = []
            for i in range(0, len(gate)):
                if i == 0:
                    _barrier_qubit_name.append(
                        int(re.findall(r"[[](.*?)[]]", gate[i].split()[1])[0])
                    )
                else:
                    _barrier_qubit_name.append(
                        int(re.findall(r"[[](.*?)[]]", gate[i].split()[0])[0])
                    )
            if print_flag is True:
                print("barrier qubit: {}".format(_barrier_qubit_name))
                print("clock:", _clock_list)
                print("stride BARRIER")

            _barrier_qubit_index = []
            for _name in _barrier_qubit_name:
                if map_flag is True:
                    _name = qubit_map[str(_name)]
                _barrier_qubit_index.append(q_list.index(_name))

            _max_clock = 0
            for _index in _barrier_qubit_index:
                if _clock_list[_index] > _max_clock:
                    _max_clock = _clock_list[_index]

            for _index in _barrier_qubit_index:
                _clock_list[_index] = _max_clock
            if print_flag is True:
                print("clock:", _clock_list)
                print("\n")
        ##测量
        if _gate_name == "MEASURE":
            _qubit = int(re.findall(r"[[](.*?)[]]", gate[0].split()[1])[0])
            if map_flag is True:
                _qubit = qubit_map[str(_qubit)]
            _qubit_index = q_list.index(_qubit)

            _clock = _clock_list[_qubit_index]
            if print_flag is True:
                print(_gate_name, _qubit)
                print("clock:", _clock_list)

            qprog_list.append({"Measure": [[_qubit], _clock]})
            measure_qubit.append(_qubit)
            _qubit_index = None
            _qubit = None

        elif _gate_name not in gate_list:
            print(_gate_name)
            # pass
            return []

    #     print(qprog_list)
    #     print(measure_qubit)
    qprog_list = qprog_list[: (len(measure_qubit) * -1)]
    #     print(qprog_list)
    qprog_list.append({"Measure": [measure_qubit, max(_clock_list)]})
    return qprog_list


def special_add_pulse(
    old_pulse: Union[None, PulseComponent],
    s_time: Union[int, float],
    line_type: str,
    add_pulse: PulseComponent = None,
) -> PulseComponent:
    """Add pulse object."""
    if old_pulse is None:
        if s_time > 0:
            old_pulse = Constant(s_time, 0, line_type)()
    elif isinstance(old_pulse, PulseComponent) and old_pulse.width < s_time:
        of_time = round(s_time - old_pulse.width, 3)
        old_pulse = old_pulse + Constant(of_time, 0, line_type)()

    if add_pulse is not None:
        if old_pulse is not None:
            new_pulse = old_pulse + add_pulse
        else:
            new_pulse = add_pulse
    else:
        new_pulse = old_pulse
    return new_pulse


def append_pulses(
    pulse_obj: Union[None, PulseComponent],
    pulse_list: Union[None, List[PulseComponent]],
    i_gate_width: float,
    p_idx: int,
    m_time_list: List[int],
    line_type: str,
) -> List[PulseComponent]:
    """Append pulse list."""
    m_time = m_time_list[-1]
    if m_time == 0:
        new_pulse = Constant(i_gate_width, 0, line_type)()
    else:
        new_pulse = special_add_pulse(pulse_obj, m_time, line_type)

    if p_idx == 0:
        pulse_list = []
    elif pulse_list is None:
        pulse_list = [Constant(width, 0, line_type)() for width in m_time_list[:p_idx]]
    pulse_list.append(new_pulse)
    return pulse_list


def parse_prog_once(
    p_idx: int,
    prog: List[Dict],
    qubit_map: Dict[str, Qubit],
    qubit_pair_map: Dict[str, QubitPair],
    available_q_bits: List[int],
    cz_key_list: List[str],
    available_cz_keys: List[str],
    rphi_width: float = 30.0,
    cz_width: float = 40.0,
):
    """Parse once prog information."""
    s_xy_pulses_map = {}
    s_z_pulses_map = {}
    xy_names = []
    z_names = []
    m_bits = []
    m_time = 0
    measure_exist = False

    # prog: [{'RPhi': [0, 0.0, 0]}, {"CZ": [1, 0, 30]}, {'Measure': [[0, 1], 90]}]
    for q_gate_dict in prog:
        # q_gate_dict, one gate information.
        for gate_name, value in q_gate_dict.items():
            if gate_name == "RPhi":
                if value[0] not in available_q_bits:
                    raise CircleParamError(
                        f"QProg index {p_idx}, {q_gate_dict}, "
                        f"Qubit {value[0]} is not available! "
                        f"Available Qubits: {available_q_bits}"
                    )
                if len(value) == 3:
                    bit, phase, st_time = value
                    theta = np.pi / 2
                elif len(value) == 4:
                    bit, phase, note_theta, st_time = value
                    # if note_theta == 90:
                    if np.isclose(note_theta, 90):
                        theta = np.pi / 2
                    # elif note_theta == 180:
                    elif np.isclose(note_theta, 180):
                        theta = np.pi
                    else:
                        raise CircleParamError(
                            f"QProg index {p_idx}, {q_gate_dict}, "
                            f"RPhi parameter `theta` {note_theta} error!"
                        )
                else:
                    raise CircleParamError(
                        f"QProg index {p_idx}, {q_gate_dict}, RPhi args is not 3 or 4!"
                    )
                phase = (phase / 180) * np.pi

                qubit = qubit_map.get(f"q{bit}")  # Qubit object
                # x_width = qubit.XYwave.time + 2 * qubit.XYwave.offset
                # if x_width < rphi_width:
                #     new_offset = round((rphi_width - qubit.XYwave.time) / 2, 3)
                # else:
                #     new_offset = qubit.XYwave.offset
                new_offset = qubit.XYwave.offset
                t_pulse = RPhiGate(phase, theta=theta).to_pulse(qubit)()

                name = qubit.name
                xy_pulse = s_xy_pulses_map.get(name)
                new_xy_pulse = special_add_pulse(
                    xy_pulse,
                    st_time,
                    "XY",
                    t_pulse,
                )
                s_xy_pulses_map.update({name: new_xy_pulse})

                if name not in xy_names:
                    xy_names.append(name)
                if name not in z_names:
                    z_names.append(name)

            elif gate_name == "CZ":
                bit_1, bit_2, st_time = value
                cz_key = "".join([f"q{q_bit}" for q_bit in sorted([bit_1, bit_2])])

                if bit_1 not in available_q_bits or bit_2 not in available_q_bits:
                    raise CircleParamError(
                        f"Qubit {bit_1} or {bit_2} is not available! "
                        f"Available Qubits: {available_q_bits}"
                    )
                if cz_key not in cz_key_list:
                    raise CircleParamError(
                        f"CZ({bit_1},{bit_2}) is not available! "
                        f"Available CZ Gates: {available_cz_keys}"
                    )

                # adjust monster new qubit_pair structure
                qubit_pair_obj = qubit_pair_map.get(cz_key)  # QubitPair object
                qubit_pair_params = qubit_pair_obj.metadata.std  # QDict object

                ql_name = qubit_pair_params.get("ql")
                qh_name = qubit_pair_params.get("qh")
                parking_qubit_names = qubit_pair_params.get("parking_bits")
                pulses_params = qubit_pair_params.get("cz").get("params")
                width = qubit_pair_params.get("cz").get("width")

                base_qubit_names = [qh_name, ql_name]
                base_qubit_names.extend(parking_qubit_names)

                for name in base_qubit_names:
                    single_pulse_params = pulses_params.get(name, {})
                    amp = single_pulse_params.get("amp") or 0.0
                    single_pulse_params.update({"time": width, "amp": amp})

                    # if width < cz_width:
                    #     offset_width = round((cz_width - width) / 2, 3)
                    #     f_pulse = Constant(offset_width, 0, name="Z")()
                    #     m_pulse = params_to_pulse(**single_pulse_params)()
                    #     e_pulse = Constant(offset_width, 0, name="Z")()
                    #     t_pulse = f_pulse + m_pulse + e_pulse
                    # else:
                    #     t_pulse = params_to_pulse(**single_pulse_params)()
                    t_pulse = params_to_pulse(**single_pulse_params)()

                    z_pulse = s_z_pulses_map.get(name)
                    new_z_pulse = special_add_pulse(z_pulse, st_time, "Z", t_pulse)
                    s_z_pulses_map.update({name: new_z_pulse})

                    if name not in z_names:
                        z_names.append(name)

            elif gate_name == "Measure":
                measure_exist = True
                m_bits, m_time = value

                for bit in m_bits:
                    if bit not in available_q_bits:
                        raise CircleParamError(
                            f"QProg index {p_idx}, Measure bit {bit} is not available! "
                            f"Available Qubits: {available_q_bits}"
                        )
                    q_name = f"q{bit}"
                    if q_name not in xy_names:
                        xy_names.append(q_name)
                    if q_name not in z_names:
                        z_names.append(q_name)

    if measure_exist is False:
        raise CircleParamError(f"QProg index {p_idx} error, no `Measure` gate!")

    once_prog_pulse_map = {
        "xy_pulses_map": s_xy_pulses_map,
        "z_pulses_map": s_z_pulses_map,
        "xy_names": xy_names,
        "z_names": z_names,
        "m_bits": m_bits,
        "m_time": m_time,
    }
    return {p_idx: once_prog_pulse_map}


def parse_prog_list(
    prog_list: List[List[Dict]],
    qubit_map: Dict[str, Qubit],
    qubit_pair_map: Dict[str, QubitPair],
    i_gate_width: float,
    q_gate_clock_map: Dict[str, int],
    available_q_bits: List[int],
) -> Dict:
    """Parse quantum circles."""
    rphi_width = q_gate_clock_map.get("RPhi") * i_gate_width
    cz_width = q_gate_clock_map.get("CZ") * i_gate_width
    cz_key_list = list(qubit_pair_map.keys())
    available_cz_keys = [
        f"CZ({','.join(new_cz_key.split('q')[1:])})" for new_cz_key in cz_key_list
    ]

    q_pattern = re.compile(r"q\d+")
    for qp_name, qp_obj in qubit_pair_map.items():
        flag_list = [
            True if int(lh_name[1:]) in available_q_bits else False
            for lh_name in q_pattern.findall(qp_name)
        ]
        if all(flag_list):
            try:
                ql_obj = qubit_map.get(qp_obj.ql)
                qh_obj = qubit_map.get(qp_obj.qh)
                qp_obj.validate(ql=ql_obj, qh=qh_obj)
            except Exception as err:
                pass

    xy_names = []
    z_names = []
    xy_pulses_name_dict = {}
    z_pulses_name_dict = {}

    m_bits_list = []
    m_time_list = []
    em_bits_list = []

    p_loop = len(prog_list)
    # logger.info(f"Normal Parse prog_list loop: {p_loop}, start ...")
    for p_idx, prog in enumerate(prog_list):
        # prog: [{'RPhi': [0, 0.0, 0]}, {"CZ": [1, 0, 30]}, {'Measure': [[0, 1], 90]}],

        once_prog_pulse_map = parse_prog_once(
            p_idx,
            prog,
            qubit_map,
            qubit_pair_map,
            available_q_bits,
            cz_key_list,
            available_cz_keys,
            rphi_width,
            cz_width,
        )

        s_prog_pulse_map = once_prog_pulse_map.get(p_idx, {})
        s_xy_pulses_map = s_prog_pulse_map.get("xy_pulses_map", {})
        s_z_pulses_map = s_prog_pulse_map.get("z_pulses_map", {})
        s_xy_names = s_prog_pulse_map.get("xy_names", [])
        s_z_names = s_prog_pulse_map.get("z_names", [])
        m_bits = s_prog_pulse_map.get("m_bits", [])
        m_time = s_prog_pulse_map.get("m_time", 0.0)

        for s_xy_name in s_xy_names:
            if s_xy_name not in xy_names:
                xy_names.append(s_xy_name)
        for s_z_name in s_z_names:
            if s_z_name not in z_names:
                z_names.append(s_z_name)
        m_bits_list.append(m_bits)
        m_time_list.append(m_time)
        if m_bits not in em_bits_list:
            em_bits_list.append(m_bits)

        for xy_name in xy_names:
            xy_pulse = s_xy_pulses_map.get(xy_name)
            xy_pulse_list = xy_pulses_name_dict.get(xy_name)
            xy_pulse_list = append_pulses(
                xy_pulse, xy_pulse_list, i_gate_width, p_idx, m_time_list, "XY"
            )
            xy_pulses_name_dict[xy_name] = xy_pulse_list
        for z_name in z_names:
            z_pulse = s_z_pulses_map.get(z_name)
            z_pulse_list = z_pulses_name_dict.get(z_name)
            z_pulse_list = append_pulses(
                z_pulse, z_pulse_list, i_gate_width, p_idx, m_time_list, "Z"
            )
            z_pulses_name_dict[z_name] = z_pulse_list

    if len(em_bits_list) > 1:
        same_measure = False
    else:
        same_measure = True

    c_names = []
    for z_name in z_names:
        if z_name.startswith("c"):
            c_names.append(z_name)

    prog_pulse_map = {
        "xy_pulses_map": xy_pulses_name_dict,
        "z_pulses_map": z_pulses_name_dict,
        "m_bits_list": m_bits_list,
        "m_time_list": m_time_list,
        "same_measure": same_measure,
        "q_names": xy_names,
        "c_names": c_names,
    }
    # logger.info(f"Normal Parse prog_list loop: {p_loop}, end .")
    return prog_pulse_map


def format_result(er_prob_list):
    er_prob_list = np.array(er_prob_list).T
    measure_result_single = {}
    measure_result = []
    for i, _prob in enumerate(er_prob_list):
        for j in range(0, len(_prob)):
            _bit_str = str(bin(j)[2:])
            while (len(_bit_str)) < np.log2(len(_prob)):
                _bit_str = "0" + _bit_str

            measure_result_single[_bit_str] = _prob[j]
        measure_result.append(measure_result_single)
        measure_result_single = {}
    return measure_result


def qcloud_plus_test(ir_result: str, ctx_manager: MonsterContextManager):
    """QCloudPlus test api."""

    qubit_dict = {}
    qubit_pair_dict = {}
    for q_bit in q_bits:
        q_name = f"q{q_bit}"
        qubit_dict[q_name] = ctx_manager.chip_data.cache_qubit.get(q_name)
    for pair_name in pairs:
        qubit_pair_dict[pair_name] = ctx_manager.chip_data.cache_qubit_pair.get(
            pair_name
        )

    qprog_list = [ir_translation(ir_result, q_bits)]
    print(f"线路解析结果:\n{qprog_list}")

    pulse_data_dict = parse_prog_list(
        qprog_list,
        qubit_dict,
        qubit_pair_dict,
        i_gate_width,
        qgate_clock_map,
        q_bits,
    )
    bc_names = []
    bc_names.extend(pulse_data_dict.get("q_names", []))
    bc_names.extend(pulse_data_dict.get("c_names", []))
    context_obj = ctx_manager.generate_context(
        name="union_read_measure",
        physical_unit=bc_names,
        readout_type="01",
        use_parallel=False,
    )

    qcloud_plus_exp = QCloudPlusV1.from_experiment_context(context_obj)
    qcloud_plus_exp.set_experiment_options(
        xy_pulses_name_dict=pulse_data_dict.get("xy_pulses_map", {}),
        z_pulses_name_dict=pulse_data_dict.get("z_pulses_map", {}),
        m_bits_list=pulse_data_dict.get("m_bits_list", []),
        is_amend=True,
        fidelity_correct_type="ibu",
        repeat=5000,
        fake_pulse=False,
        plot_iq=False,
    )
    asyncio.run(qcloud_plus_exp.run_experiment())
    er_prob_list = qcloud_plus_exp.run_options.get("er_prob_list")
    measure_result = format_result(er_prob_list)
    print(measure_result)


if __name__ == "__main__":
    ir = """
    H q[1]
    H q[2]
    MEASURE q[1],c[0]
    MEASURE q[2],c[1]
    """
    print(ir_translation(ir, q_bits))
