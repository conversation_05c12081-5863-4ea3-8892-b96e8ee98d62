# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/12
# __author:       <PERSON><PERSON><PERSON>

from app.config import init_backend
from app.automation.executor import AutomationExecutor, QDict


def main():
    executor = AutomationExecutor(init_backend())
    executor.experiment_options.config = (
        r"D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\app\automation\conf\auto.json"
    )
    executor.experiment_options.nodes = [
        # "BatchBusS21",
        # "BatchCavityQ",
        # "BatchIMPAControl",
        # "BatchCavityPowerScan_V0",
        # "BatchCouplerTunable",
        # "BatchQubitTunable",
        # "BatchCavityPowerScan_VMax",
        # "BatchQubitSpectrumZAmp",
        "BatchSearchIdlePoint",
        "BatchReadout",
        # "BatchSearchF12",
        # "BatchXYCrossRabiWidth",
        "BatchXZTiming",
        "BatchACT1Spectrum",
        "BatchRBSpectrum"
    ]
    executor.experiment_options.physical_unit_map = QDict(
        qubit=[f"q{i + 1}" for i in range(102)],
        # qubit=['q8', 'q7', 'q80', 'q59', 'q93'],
        coupler="c9-16,c19-25,c68-74,c8-15,c15-21,c76-81,c77-83,c46-52,c70-76,c49-55,c89-95,c85-91,c39-45,c15-20,c82-88,c20-27,c66-71,c52-57,c59-66,c96-102,c34-41,c56-63,c80-87,c30-35,c52-58,c83-90,c56-62,c68-75,c100-105,c26-32,c6-12,c79-85,c74-79,c100-106,c34-40,c61-67,c43-49,c60-66,c35-41,c7-14,c40-45,c23-30,c95-102,c30-36,c62-67,c73-79,c36-42,c35-42,c86-92,c41-47,c80-86,c33-40,c32-38,c32-39,c9-15,c75-81,c62-68,c90-96,c5-11,c76-82,c65-70,c53-59,c51-57,c22-28,c14-19,c23-29,c20-26,c11-17,c67-73,c28-34,c83-89,c25-31,c17-23,c10-17,c94-101,c8-14,c38-43,c13-19,c81-88,c53-58,c70-77,c51-56,c79-86,c81-87,c4-10,c74-80,c21-27,c14-20,c27-32,c48-54,c63-68,c42-48,c55-61,c3-8,c69-76,c72-78,c86-91,c71-78,c101-107,c59-65,c99-104,c98-103,c91-97,c54-60,c88-94,c1-7,c64-69,c29-35,c69-75,c19-26,c88-93,c77-82,c2-8,c91-98,c84-90,c4-9,c66-72,c2-7,c58-64,c47-54,c46-53,c10-16,c90-95,c42-47,c39-44,c12-18,c92-99,c24-30,c93-99,c78-84,c40-46,c50-56,c33-39,c38-44,c45-52,c82-89,c97-103,c31-37,c11-18,c63-69,c95-101,c28-33,c87-93,c78-83,c67-74,c92-98,c26-31,c98-104,c43-50,c54-59,c58-65,c55-62,c99-105,c45-51,c27-33,c44-51,c18-24,c16-22,c21-28,c29-34,c71-77,c101-106,c6-11,c102-107,c57-64,c64-70,c16-21,c47-53,c94-100,c31-38,c17-22,c44-50,c7-13,c41-46,c3-9,c57-63,c37-43,c22-29,c65-71,c87-92,c50-55,c93-100,c89-94,c18-23,c75-80,c5-10".split(','), 
        qubit_pair=[], 
        bus=[i + 1 for i in range(17)]
    )
    executor.run()


if __name__ == "__main__":
    main()
