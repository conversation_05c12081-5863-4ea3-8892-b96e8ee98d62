# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/17
# __author:       <PERSON><PERSON><PERSON>


"""
流程作用
将低功率腔频更新至各个比特
将高低功率的下 q_int、q_ext 保存到各自的 point_label 中

流程描述
创建低功率 point_label (lp), 并切换；
使用拨码开关控制网分作用的 BUS
使用 FindBusCavityFreq 进行长范围粗扫，采集到 6 个腔后，按照 chip_line_connect.json 中的 bus_cavity_freq_sort_temp 配置的映射关系更新每个比特的腔频；
使用 FindBusCavityFreq 的 segm_scan 开启分段扫描，并将 analysis 中 fit_q 设置为True, 计算出每个比特的 q_int、q_ext, 更新数据库
每根 BUS 重复上述流程
创建高功率 point_label (hb), 并切换，重复上述 2 ~ 5 步
"""

import time

from pyQCat.experiments.batch_experiment import BatchExperiment, BatchPhysicalUnitType
from pyQCat.instrument.socket_service.tcp_client import MicroSwitch
from pyQCat.invoker import Invoker


class BatchCavityQ(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.ip = None
        options.port = None
        options.flows = ["FindBusCavityFreq_rough", "FindBusCavityFreq_segma"]
        options.physical_unit_type = BatchPhysicalUnitType.BUS
        options.low_point_label = "lp"
        options.high_point_label = "hp"
        options.high_net_power = -15
        options.low_net_power = -45
        options.affect_next_node = False
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.micro_switch = None
        options.init_point_label = None
        options.cur_point_label = None
        options.records = {}
        return options

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "FindBusCavityFreq_segma" == exp_name:
            for unit in record.analysis_data.keys():
                pass_bits = (
                    record.analysis_data.get(unit).get("result").get("pass_bits")
                )
                fail_bits = (
                    record.analysis_data.get(unit).get("result").get("fail_bits")
                )
                if pass_bits == "None":
                    pass_bits = None
                if fail_bits == "None":
                    fail_bits = None
                if pass_bits:
                    self.run_options.records[self.run_options.cur_point_label][
                        "pass_bits"
                    ].extend(pass_bits)
                    self.run_options.records[self.run_options.cur_point_label][
                        "fail_bits"
                    ].extend(fail_bits)

        return record

    def _check_options(self):
        env = Invoker.get_env()
        self.run_options.init_point_label = env.point_label
        self.run_options.micro_switch = MicroSwitch(
            self.experiment_options.ip, self.experiment_options.port
        )
        self.run_options.records = {
            self.experiment_options.low_point_label: dict(pass_bits=[], fail_bits=[]),
            self.experiment_options.high_point_label: dict(pass_bits=[], fail_bits=[]),
        }
        super()._check_options()

    def _open_bus(self, bus: int):
        connect = self.run_options.micro_switch

        connect.close_sw()
        time.sleep(2)

        connect.bus = bus
        if 1 <= bus <= 6:
            connect.open_sw1()
        elif 7 <= bus <= 12:
            connect.open_sw2()
        elif 13 <= bus <= 18:
            connect.open_sw3()

        time.sleep(2)
        connect.open_bw()
        time.sleep(1)

    def _change_point_label(self, point_label: str):
        self.run_options.cur_point_label = point_label
        self.backend.change_point_label(point_label)

    def _get_qubits_from_bus(self, bus):
        goal_qubits = []
        for qubit in self.backend.chip_data.cache_qubit.values():
            if str(qubit.inst.bus) == str(bus):
                goal_qubits.append(qubit.name)
        return goal_qubits

    def _config_file_path(self, unit: str, exp):
        """Configure the parent directory for each experiment executed in the BatchExperiment.

        Args:
            unit (str): Physical working unit.
            exp (BaseExperiment) :
        """
        qubit = self.backend.chip_data.cache_qubit.get(unit)
        super()._config_file_path(
            f"Bus-{qubit.inst.bus}-{self.run_options.cur_point_label}", exp
        )

    def _run_batch(self):
        all_qubits = [qubit.name for qubit in self.backend.chip_data.cache_qubit.values() if qubit.goodness is True]
        for idx, point_label in enumerate(
            [
                self.experiment_options.low_point_label,
                self.experiment_options.high_point_label,
            ]
        ):
            self._change_point_label(point_label)
            net_power = (
                self.experiment_options.low_net_power
                if idx == 0
                else self.experiment_options.high_net_power
            )
            for exp in self.experiment_options.flows:
                self.change_regular_exec_exp_options(exp, net_power=net_power)
            for bus in self.experiment_options.physical_units:
                for exp in self.experiment_options.flows:
                    self.change_regular_exec_exp_options(exp_name=exp, bus=int(bus))
                self._open_bus(int(bus))
                qubits = self._get_qubits_from_bus(bus)
                self._run_flow(
                    self.experiment_options.flows,
                    physical_units=qubits,
                    name=f"BUS-{bus}",
                )
            self.readout_baseband_freq_allocator(all_qubits)
            self.backend.save_chip_data_to_db(all_qubits)

        self._save_data_to_json(self.run_options.records, "state_details")
        self.bind_pass_units(self.experiment_options.physical_units)
        self.record_meta.execute_meta.result.hot_data = self.run_options.records

    def _batch_down(self):
        self.backend._set_env()
        self.backend.refresh()
        return super()._batch_down()
