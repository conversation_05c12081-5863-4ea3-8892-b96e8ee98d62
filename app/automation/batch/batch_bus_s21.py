# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/10
# __author:       <PERSON><PERSON><PERSON>

"""
节点作用：
1. 获取全部 BUS 的 4-8.5 GHz 高功率 S21 数据
2. 获取全部 BUS 的腔频范围高（-15dBm) 低（-40dBm) 功率S21数据
3. 期望的测试结果详见: BUS S21

节点流程：

1. 使用拨码开关控制网分作用的 BUS
2. 使用网络分析仪获取 S21 信号，截图保存，并保存 S2P 文件
3. 每根 BUS 重复上述流程
"""

import time

import matplotlib.pyplot as plt

from pyQCat.experiments.batch_experiment import (
    BatchExperiment,
    BatchPhysicalUnitType,
    Path,
)
from pyQCat.instrument.socket_service.tcp_client import MicroSwitch


class BatchBusS21(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.ip = None
        options.port = None
        options.flows = ["BusS21Collector"]
        options.physical_unit_type = BatchPhysicalUnitType.BUS
        options.affect_next_node = False
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.micro_switch = None
        options.s21_data = {}
        return options

    def _check_options(self):
        self.run_options.micro_switch = MicroSwitch(
            self.experiment_options.ip, self.experiment_options.port
        )
        return super()._check_options()

    def _open_bus(self, bus: int):
        connect = self.run_options.micro_switch

        connect.close_sw()
        time.sleep(2)

        connect.bus = bus
        if 1 <= bus <= 6:
            connect.open_sw1()
        elif 7 <= bus <= 12:
            connect.open_sw2()
        elif 13 <= bus <= 18:
            connect.open_sw3()

        time.sleep(2)
        connect.open_bw()
        time.sleep(1)

    def _batch_down(self):
        super()._batch_down()
        self._plot_s21()
        self.run_options.micro_switch.close_sw()

    def _plot_s21(self):
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle("All BUS21", fontsize=16)
        for k, v in self.run_options.s21_data.items():
            ax1.plot(v["hp_wide"]["freq"], v["hp_wide"]["amp"], label=k)
            ax2.plot(v["hp_small"]["freq"], v["hp_small"]["amp"], label=k)

        ax1.set_xlabel("Frequency (MHz)")
        ax1.set_ylabel("Amp")
        ax1.legend()
        ax2.set_xlabel("Frequency (MHz)")
        ax2.set_ylabel("Amp")
        ax2.legend()

        save_path = str(Path(Path(self.run_options.record_path).parent, "s21.png"))
        fig.savefig(save_path)
        plt.tight_layout()
        plt.close()

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        experiment_data = exp.experiment_data

        self.run_options.s21_data[physical_units[0]] = dict(
            hp_wide=dict(
                freq=experiment_data.replace_x_data.get("hp_wide_amp"),
                amp=experiment_data.y_data.get("hp_wide_amp"),
            ),
            hp_small=dict(
                freq=experiment_data.replace_x_data.get("hp_small_amp"),
                amp=experiment_data.y_data.get("hp_small_amp"),
            ),
        )

        return record

    def _run_batch(self):
        for bus in self.experiment_options.physical_units:
            self._open_bus(int(bus))
            self.change_regular_exec_exp_options(
                self.experiment_options.flows[0], bus=bus
            )
            self._run_flow(
                flows=self.experiment_options.flows,
                physical_units=[f"Bus-{bus}"],
                name=f"Bus-{bus}",
            )
        # todo: quality evaluated
        self.bind_pass_units(self.experiment_options.physical_units)
