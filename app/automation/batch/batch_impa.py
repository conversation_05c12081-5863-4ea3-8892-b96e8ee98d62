# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/17
# __author:       <PERSON><PERSON><PERSON>

import time

from pyQCat.experiments.batch_experiment import BatchExperiment, BatchPhysicalUnitType
from pyQCat.instrument.socket_service.tcp_client import MicroSwitch


class BatchImpa(BatchExperiment):

    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.ip = None
        options.port = None
        options.flux_scan_flows = ["ImpaCavityFluxScan"]
        options.impa_optimize_flows = ["ImpaOptiParams", "ImpaGain"]
        options.physical_unit_type = BatchPhysicalUnitType.BUS
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.micro_switch = None
        options.flux_points = {}
        options.current_bus = None
        return options

    def _check_options(self):
        self.run_options.micro_switch = MicroSwitch(
            self.experiment_options.ip, self.experiment_options.port
        )
        super()._check_options()

    def _open_bus(self, bus: int):
        connect = self.run_options.micro_switch
        connect.bus = bus
        sw1_flag = connect.sw1_opened
        sw2_flag = connect.sw2_opened
        sw3_flag = connect.sw3_opened
        connect.close_sw()
        time.sleep(2)
        if 1 <= bus <= 6 and not sw1_flag:
            if sw2_flag or sw3_flag:
                connect.close_sw()
            connect.open_sw1()
        elif 7 <= bus <= 12 and not sw2_flag:
            if sw1_flag or sw3_flag:
                connect.close_sw()
            connect.open_sw2()
        elif 13 <= bus <= 18 and not sw3_flag:
            if sw1_flag or sw2_flag:
                connect.close_sw()
            connect.open_sw3()
        connect.open_bw()
        time.sleep(2)

    def _run_batch(self):
        for bus in self.experiment_options.physical_units:
            self._open_bus(int(bus))
            self.run_options.current_bus = bus

            # todo: update experiment options
            self._run_flow(
                self.experiment_options.flux_scan_flows,
                physical_units=[f"BUS-{bus}"],
                name=f"BUS-{bus} Flux Scan",
            )

            flux_points = self.run_options.flux_points.get(bus)
            for flux in flux_points:
                self._run_flow(
                    flows=self.experiment_options.impa_optimize_flows,
                    physical_units=[f"Bus-{bus}"],
                    name=f"Bus-{bus}",
                )

            self.run_options.micro_switch.close_sw()
            time.sleep(2)
