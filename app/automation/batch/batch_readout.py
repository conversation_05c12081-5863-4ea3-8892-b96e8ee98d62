# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/04
# __author:       <PERSON><PERSON><PERSON>

from pyQCat.experiments.batch_experiment import BatchExperiment


class BatchReadout(BatchExperiment):

    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.flows = [
            "ReadoutFreqCalibrate_01",
            "ReadoutPowerCalibrate_01"
        ]
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        return options

    def _run_batch(self):
        group_map = self.parallel_allocator_for_qc(self.experiment_options.physical_units)
        pass_units = []
        for group_name, group in group_map.items():
            pass_units.extend(
                self._run_flow(
                    flows=self.experiment_options.flows,
                    physical_units=group,
                    name=f"{group_name} Calibrate Readout"
                )
            )
        self.bind_pass_units(pass_units)
        qubits = list(self.backend.chip_data.cache_qubit.keys())
        self.readout_amp_allocator(qubits)
        self.backend.save_chip_data_to_db(qubits)
