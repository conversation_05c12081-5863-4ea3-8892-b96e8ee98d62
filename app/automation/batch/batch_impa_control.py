# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/01
# __author:       <PERSON><PERSON><PERSON>

import time
import json
from pyQCat.experiments.batch_experiment import (
    BatchExperiment,
    BatchPhysicalUnitType,
    pyqlog,
)
from pyQCat.instrument.socket_service.tcp_client import MicroSwitch


class BatchIMPAControl(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.set_validator("mode", ["open", "close"])
        options.ip = None
        options.port = None
        options.flows = ["ImpaGain"]
        options.physical_unit_type = BatchPhysicalUnitType.BUS
        options.mode = "open"
        options.impa_params_path = ""
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.micro_switch = None
        options.impa_params = None
        return options

    def _check_options(self):
        self.run_options.micro_switch = MicroSwitch(
            self.experiment_options.ip, self.experiment_options.port
        )
        return super()._check_options()

    def _open_bus(self, bus: int):
        connect = self.run_options.micro_switch

        connect.close_sw()
        time.sleep(2)

        connect.bus = bus
        if 1 <= bus <= 6:
            connect.open_sw1()
        elif 7 <= bus <= 12:
            connect.open_sw2()
        elif 13 <= bus <= 18:
            connect.open_sw3()

        time.sleep(2)
        connect.open_bw()
        time.sleep(1)

    def _batch_down(self):
        super()._batch_down()
        self.run_options.micro_switch.close_sw()

    def _run_batch(self):
        with open(self.experiment_options.impa_params_path, mode="r", encoding="utf-8") as fp:
            data = json.load(fp)
        self.run_options.impa_params = {k.upper(): v for k, v in data.items()}

        self.change_regular_exec_exp_options(
            self.experiment_options.flows[0], mode=self.experiment_options.mode
        )
        for bus in self.experiment_options.physical_units:
            ffp_list = self.run_options.impa_params.get(f"BUS{bus}")
            if not ffp_list:
                pyqlog.warning(f"No find bus-{bus} impa params!")
                continue

            count = 0
            while bus and count < 3:
                self._open_bus(int(bus))
                self.change_regular_exec_exp_options(
                    self.experiment_options.flows[0],
                    bus=bus,
                    ffp_list=self.run_options.impa_params.get(f"BUS{bus}")
                )
                pass_bus = self._run_flow(
                    flows=self.experiment_options.flows,
                    physical_units=[f"Bus-{bus}"],
                    name=f"Bus-{bus}",
                )
                if pass_bus:
                    pyqlog.info(
                        f"Bus-{bus} {self.experiment_options.mode} Count-{count} suc!"
                    )
                    break
                count += 1
                pyqlog.warning(
                    f"Bus-{bus} {self.experiment_options.mode} Count-{count} fail, retry!"
                )

        self.bind_pass_units(self.experiment_options.physical_units)
