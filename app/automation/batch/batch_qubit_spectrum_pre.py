# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/10
# __author:       <PERSON><PERSON><PERSON>

"""
节点作用：
节点流程：

1. 在 dc_max 处变化不同的 drive_power 执行 QubitSpectrum 实验，从实验结果中提取最佳 drive_power
2. 在合适的 drive_power 下，测出 dc_max、dc_min 处的比特频率，确定后续扫描二维能谱所需的 freq_list
3. 执行 QubitSpectrumZAmp, 寻找粗测 AC 谱
"""

from pyQCat.experiments.batch_experiment import (
    BatchExperiment,
    List,
    defaultdict,
    np,
    pyqlog,
)
from pyQCat.tools import qarange


class BatchQubitSpectrumZAmp(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.power_scan_flow = ["QubitSpectrumScanPower"]
        options.max_f01_flow = ["QubitSpectrumVMax"]
        options.min_f01_flow = ["QubitSpectrumVMin"]
        options.amp_scan_flow = ["QubitSpectrumZAmp"]
        options.v_min_f01_limit = 3750
        options.v_max_f01_limit = 5000
        options.scan_amp_step = 0.01
        options.affect_next_node = False
        return options

    @classmethod
    def _default_analysis_options(cls):
        options = super()._default_analysis_options()
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.qubit_idle_map = {}
        options.f01_scope = {}
        options.best_power_map = {}
        return options

    def _record_idle_cache(self):
        for unit in self.experiment_options.physical_units:
            qubit = self.backend.chip_data.cache_qubit.get(unit)
            self.run_options.qubit_idle_map[unit] = qubit.idle_point
            qubit.XYwave.baseband_freq = 850
            self.run_options.f01_scope[unit] = [
                self.experiment_options.v_min_f01_limit,
                self.experiment_options.v_max_f01_limit,
            ]

    def _reset_idle_point(self):
        for unit, idle_point in self.run_options.qubit_idle_map.items():
            qubit = self.backend.chip_data.cache_qubit.get(unit)
            qubit.idle_point = idle_point

    def _set_to_max_point(self, physical_units: List[str]):
        for unit in physical_units:
            qubit = self.backend.chip_data.cache_qubit.get(unit)
            qubit.idle_point = 0

    def _set_to_min_point(self, physical_units: List[str]):
        for unit in physical_units:
            qubit = self.backend.chip_data.cache_qubit.get(unit)
            qubit.idle_point = qubit.dc_min - qubit.dc_max

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "QubitSpectrumScanPower" == exp_name:
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    drive_power = (
                        record.analysis_data.get(unit).get("result").get("best_power")
                    )
                    self.run_options.best_power_map[unit] = float(drive_power)
        elif "QubitSpectrumVMax" == exp_name:
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    peaks = record.analysis_data.get(unit).get("result").get("peaks")
                    if peaks:
                        self.run_options.f01_scope[unit][1] = min(
                            max(peaks) + 50, self.experiment_options.v_max_f01_limit
                        )
        elif "QubitSpectrumVMin" == exp_name:
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    peaks = record.analysis_data.get(unit).get("result").get("peaks")
                    self.run_options.f01_scope[unit][0] = max(
                        min(peaks) - 50, self.experiment_options.v_min_f01_limit
                    )
        return record

    def _set_amp_scan_options(self, physical_units: List[str]):
        scan_amp_step = self.experiment_options.scan_amp_step
        z_amp_map = {}
        same_xy_lo_map = defaultdict(list)
        max_point = 0

        for unit in physical_units:
            qubit = self.backend.chip_data.cache_qubit.get(unit)
            amp_sign = 1 if qubit.dc_max - qubit.dc_min > 0 else -1
            min_amp = qubit.dc_min - (scan_amp_step * amp_sign * 2)
            max_amp = qubit.dc_max + (scan_amp_step * amp_sign * 2)
            point_list = qarange(min_amp, max_amp, scan_amp_step * amp_sign)
            point_list = [amp for amp in point_list if abs(amp) <= 0.48]
            z_amp_list = [amp - qubit.dc_max for amp in point_list]
            pyqlog.log(
                "FLOW", f"{unit} set amp from `{z_amp_list[0]}` to `{z_amp_list[-1]}`"
            )
            z_amp_map[unit] = z_amp_list
            xy_lo = qubit.inst.xy_lo
            same_xy_lo_map[xy_lo].append(unit)
            fq_min, fq_max = self.run_options.f01_scope[unit]
            if fq_max - fq_min < 100:
                pyqlog.warning(
                    f"{unit} fq scope `{fq_min}` to `{fq_max}`, lower 100 MHz, auto reset!"
                )
                fq_min = self.experiment_options.v_min_f01_limit
                fq_max = self.experiment_options.v_max_f01_limit
                self.run_options.f01_scope[unit] = [fq_min, fq_max]
                max_point = max(max_point, 250)

            elif fq_max - fq_min < 500:
                max_point = max(max_point, 100)
            else:
                freq_list = qarange(fq_min, fq_max, 5)
                max_point = max(max_point, len(freq_list))

        for xy_lo, units in same_xy_lo_map.items():
            fq_min_side = self.experiment_options.v_max_f01_limit
            fq_max_side = self.experiment_options.v_min_f01_limit
            for unit in units:
                fq_min, fq_max = self.run_options.f01_scope[unit]
                fq_min_side = min(fq_min_side, fq_min)
                fq_max_side = max(fq_max_side, fq_max)
            freq_list = list(
                np.round(np.linspace(fq_min_side, fq_max_side, max_point), 3)
            )
            for unit in units:
                pyqlog.log(
                    "FLOW",
                    f"{unit} set freq list from `{fq_min_side}` to `{fq_max_side}`",
                )
                self.change_parallel_exec_exp_options(
                    "QubitSpectrumZAmp",
                    unit,
                    options={
                        "child_exp_options.freq_list": freq_list,
                        "z_amp_list": z_amp_map[unit],
                    },
                )

    def _set_drive_power(self):
        if not self.run_options.best_power_map:
            pyqlog.warning("No find any best drive power!")
            return

        for unit in self.experiment_options.physical_units:
            base_unit = unit
            power = self.run_options.best_power_map.get(unit)
            distance = 1
            while not power and distance < 5:
                neighbor_bits = self.backend.chip_data.topology.bit_neighbor(unit, 1)
                for nb in neighbor_bits:
                    power = self.run_options.best_power_map.get(nb)
                    if power:
                        base_unit = nb
                        break
                distance += 1
            if power:
                pyqlog.info(
                    f"Set {unit} drive power to {power}dB, base from {base_unit}"
                )

    def _run_batch(self):
        self._record_idle_cache()

        group_map = self.parallel_allocator_for_qc(
            self.experiment_options.physical_units
        )

        all_pass_units = []

        if self.experiment_options.power_scan_flow:
            for group_name, group_units in group_map.items():
                self._set_to_max_point(group_units)
                pass_units = self._run_flow(
                    self.experiment_options.power_scan_flow,
                    group_units,
                    name=f"{group_name}-MaxPoint PowerScan",
                )
                all_pass_units.extend(pass_units)
            self._set_drive_power()

        if (
            self.experiment_options.max_f01_flow
            and self.experiment_options.min_f01_flow
            and self.experiment_options.amp_scan_flow
        ):
            all_pass_units = []
            for group_name, group_units in group_map.items():
                self._set_to_max_point(group_units)
                self._run_flow(
                    self.experiment_options.max_f01_flow,
                    group_units,
                    name=f"{group_name}-MaxPoint F01",
                )
                self._set_to_min_point(group_units)
                self._run_flow(
                    self.experiment_options.min_f01_flow,
                    group_units,
                    name=f"{group_name}-MinPoint F01",
                )
                self._set_amp_scan_options(group_units)
                pass_units = self._run_flow(
                    self.experiment_options.amp_scan_flow,
                    group_units,
                    name=f"{group_name}-ZAmpScan",
                )
                self._reset_idle_point()
                all_pass_units.extend(pass_units)

        self.bind_pass_units(all_pass_units)
        self.backend.save_chip_data_to_db(self.experiment_options.physical_units)
