# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/10
# __author:       <PERSON><PERSON><PERSON>

"""
节点作用：全局搜索 ZZ 相对延时
"""

import json
from pathlib import Path
from typing import Dict, List

import numpy as np

from pyQCat.executor.batch import divide_cz_parallel_group
from pyQCat.experiments.batch_experiment import (
    Backend,
    BatchExperiment,
    BatchPhysicalUnitType,
    QDict,
)
from pyQCat.log import pyqlog
from pyQCat.processor.chip_data import ChipConfigField
from pyQCat.processor.hardware_manager import HardwareOffsetManager
from pyQCat.qubit import Qubit, QubitPair
from pyQCat.tools.find import ResonanceFrequency, get_bound_ac_spectrum
from pyQCat.tools.zz_check import <PERSON><PERSON><PERSON><PERSON><PERSON>
from pyQCat.errors import ExperimentFieldError


class BatchZZTimingV2(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.point_map = {}
        options.iter_swap_freq = None
        options.qubit_check_flows = [
            "SingleShot",
            "VoltageDriftGradientCalibration",
            "RabiScanAmp",
        ]
        options.fix_flows = [
            "FixedPointCalibration_for_qh",
            "FixedPointCalibration_for_ql",
        ]
        options.time_flows = [
            "FixedSwapFreqCaliCoupler",
            "Swap",
            "SwapQC",
            "ZZTimingComposite",
        ]
        options.swap_state = "01"

        # Added option whether to calculate resonance frequency automatically
        options.auto_calc_freq = True
        options.step = 30
        options.threshold = 100

        # from https://document.qpanda.cn/docs/5rk9dzPeOVUMElqx
        options.sigma = 0.01
        options.buffer = 0.03
        options.amp = 0.23
        options.differ = 120
        options.physical_unit_type = BatchPhysicalUnitType.PAIR
        options.sweep_point_limit = 5

        return options

    @classmethod
    def _default_analysis_options(cls):
        options = super()._default_analysis_options()
        options.set_validator("zz_check_mode", ["close", "find_fault", "tolerance"])
        options.zz_check_mode = "close"
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.fidelity_record = {}
        options.pair_qubit_map = {}
        options.zz_result = {}
        options.next_pairs = None
        return options

    def _check_options(self):
        if not self.experiment_options.physical_units:
            raise ExperimentFieldError(self, "no define physical units")
        super()._check_options()

    def _batch_up(self):
        super()._batch_up()

        sigma = self.experiment_options.sigma
        buffer = self.experiment_options.buffer
        amp = self.experiment_options.amp
        pair_qubit_map = self.run_options.pair_qubit_map

        swap_state = self.experiment_options.swap_state
        label = "cz" if swap_state == "11" else "zz"
        remove_pairs = []

        for pair_name in self.experiment_options.physical_units:
            pair_obj = self.backend.chip_data.get_physical_unit(pair_name)
            pair_qubit_map[pair_name] = [pair_obj.qh, pair_obj.ql]
            qc = self.backend.chip_data.get_physical_unit(pair_obj.qc)
            qc_idle = qc.idle_point
            qc_amp = amp if qc_idle > 0 else -amp
            if abs(qc.dc_max + qc_idle + qc_amp) > 0.5:
                remove_pairs.append(pair_name)
                pyqlog.warning(
                    f"{pair_name} qc amp set error, max({qc.dc_max}), idle({qc.idle_point})"
                )
                continue
            params = {
                pair_obj.qh: dict(sigma=sigma, buffer=buffer),
                pair_obj.ql: dict(sigma=sigma, buffer=buffer),
                pair_obj.qc: dict(sigma=sigma, buffer=buffer, amp=qc_amp),
            }
            pair_obj.update_cz_gate_params(key=label, params=params)

            qc_amp_list = list(
                np.linspace(0, qc.dc_min - qc.dc_max - qc.idle_point, 20)
            )
            self.change_parallel_exec_exp_options(
                "SwapQC", pair_name, z_amp_list=qc_amp_list
            )

        for pair in remove_pairs:
            self.experiment_options.physical_units.remove(pair)

    def _count_max_sweep_count(self, pair_collections: List[str]):
        return max(
            [
                len(self.experiment_options.point_map.get(unit, []))
                for unit in pair_collections
            ]
        )

    def _change_work_point(self, i: int, pair_collections: list):
        working_units = []
        swap_state = self.experiment_options.swap_state
        for pair in pair_collections:
            if len(self.experiment_options.point_map.get(pair)) > i:
                working_units.append(pair)
                freq = self.experiment_options.point_map[pair][i]
                pair_obj: QubitPair = (
                    self.context_manager.chip_data.cache_qubit_pair.get(pair)
                )
                qh: Qubit = self.context_manager.chip_data.cache_qubit.get(pair_obj.qh)
                gate_params = pair_obj.gate_params("zz")
                if swap_state in ["01", "10"]:
                    gate_params.get(pair_obj.qh).freq = freq
                    gate_params.get(pair_obj.ql).freq = freq
                elif swap_state == "11":
                    gate_params.get(pair_obj.qh).freq = freq - qh.anharmonicity
                    gate_params.get(pair_obj.ql).freq = freq
                else:
                    raise NameError(f"swap_sate({swap_state}) error!")
                pyqlog.log("EXP", f"{pair_obj} swap {swap_state} zz point ({freq})MHz")

        return working_units

    def _run_batch(self):
        # cz parallel group allocate
        pair_groups = divide_cz_parallel_group(
            self.experiment_options.physical_units, self.context_manager.chip_data
        )

        self._check_experiment_options(pair_groups)

        fail_units = []

        # loop group
        for group_name, pair_collections in pair_groups.items():
            sweep_count = self._count_max_sweep_count(pair_collections)
            if sweep_count > self.experiment_options.sweep_point_limit:
                sweep_count = self.experiment_options.sweep_point_limit

            # check every swap point
            for i in range(sweep_count):
                describe = f"{group_name}-Point-{i}"

                if pair_collections:
                    # change working point
                    working_units = self._change_work_point(i, pair_collections)
                    
                    if not working_units:
                        continue

                    if not working_units:
                        continue

                    # run qubit_check_flows flow to check best readout qubit
                    self._check_best_readout_qubit(working_units, describe)

                    # calibration swap point
                    if self.experiment_options.fix_flows:
                        working_units = self._run_flow(
                            flows=self.experiment_options.fix_flows,
                            physical_units=working_units,
                            name=f"{describe} Fix Swap Point",
                        )

                    # run exp flow
                    for swap_freq in self.experiment_options.iter_swap_freq:
                        if working_units:
                            # update qc z amp for FixedSwapFreqCaliCoupler experiment
                            fsf = self.params_manager.exp_map.get(
                                "FixedSwapFreqCaliCoupler"
                            )
                            fsf.options_for_regular_exec["experiment_options"][
                                "frequency"
                            ] = swap_freq
                            pyqlog.log("FLOW", f"Change swap point {swap_freq}")

                            # run zz timing flow
                            pass_units = self._run_flow(
                                flows=self.experiment_options.time_flows,
                                physical_units=working_units,
                                name=f"{describe} SF={swap_freq}MHz Check ZZTiming",
                            )

                            # filter unit
                            if pass_units:
                                for unit in pass_units:
                                    working_units.remove(unit)
                                    pair_collections.remove(unit)

            fail_units.extend(pair_collections)

        pass_units = [
            unit
            for unit in self.experiment_options.physical_units
            if unit not in fail_units
        ]
        self.bind_pass_units(pass_units)
        self.record_meta.execute_meta.result.hot_data = self.run_options.zz_result

        zz_check_mode = self.analysis_options.zz_check_mode
        if zz_check_mode == "close":
            self.run_options.next_pairs = fail_units
            return
        else:
            fault_list = self.zz_check(
                self.run_options.zz_result,
                str(Path(self.run_options.record_path).parent),
                mode="find_fault",
            )
            if fault_list:
                new_pair = ["q".join([""] + unit[1:].split("-")) for unit in fault_list]
                pyqlog.warning(f"ZZ Check fail pair: {new_pair}")
                if zz_check_mode == "tolerance":
                    pyqlog.warning(f"Remove ZZ Check fail unit: {new_pair}")
                    for unit in fault_list:
                        self.run_options.zz_result.pop(unit, None)
                    fault_list = []
                    new_pair = []
                self.run_options.next_pairs = new_pair

            if not fault_list:
                new_hd = self.zz_check(
                    self.run_options.zz_result,
                    str(Path(self.run_options.record_path).parent),
                    mode="tolerance",
                )
                hardware_offset_data = self.backend.chip_data.cache_config.get(
                    ChipConfigField.hardware_offset.value
                )
                chip_data = self.backend.chip_data.cache_config.get(
                    ChipConfigField.chip_line.value
                )
                offset_manager = HardwareOffsetManager.from_data(hardware_offset_data)
                origin_data = offset_manager.to_chip_offset_data(chip_data)
                for unit in list(origin_data.keys()):
                    if unit.startswith("c"):
                        origin_data.pop(unit)
                origin_data.update(new_hd)
                save_path = self._save_data_to_json(origin_data, "final_global_line_offset.json")
                if save_path:
                    offset_manager.from_chip_offset_data(save_path)
                # self.backend.save_chip_data_to_db(ChipConfigField.hardware_offset.value)
                self.run_options.next_pairs = None

            # self._save_data_to_json(self.run_options.zz_result, "zz_delay")

    def _check_best_readout_qubit(self, working_units: List[str], describe: str):
        pair_qubit_map = self.run_options.pair_qubit_map
        qubits = []

        for unit in working_units:
            qubits.extend(pair_qubit_map[unit])

        if qubits:
            self._run_flow(
                flows=self.experiment_options.qubit_check_flows,
                physical_units=qubits,
                name=f"{describe} Check best readout qubit",
            )

            for unit in working_units:
                qh, ql = pair_qubit_map[unit]
                f_qh, f_ql = (
                    self.run_options.fidelity_record.get(qh, 0),
                    self.run_options.fidelity_record.get(ql, 0),
                )
                readout_type = "qh-01" if f_qh > f_ql else "ql-01"
                pyqlog.log(
                    "FLOW",
                    f"{unit} qh({qh})({f_qh}) ql({ql})({f_ql}), set readout type {readout_type}",
                )
                # for exp in self.experiment_options.fix_flows:
                #     self.params_manager.update_exp_context_options(
                #         exp_name=exp, readout_type=readout_type
                #     )
                for exp in self.experiment_options.time_flows:
                    self.params_manager.update_exp_context_options(
                        exp_name=exp, readout_type=readout_type
                    )
        return working_units

    def _run_analysis(self):
        super()._run_analysis()
        # self.collection_line_offset(
        #     self.run_options.record_path, self.backend, self.experiment_options.save_db
        # )

    @staticmethod
    def zz_check(
        zz_result,
        save_path,
        mode="tolerance",
        fault_threshold=2.5,
        tolerance_threshold=0.833,
    ):
        zz_check_obj = ZZCheck()
        zz_check_obj.set_options(
            mode=mode,
            hd_json=zz_result,
            save_path=save_path,
            fault_threshold=fault_threshold,
            tolerance_threshold=tolerance_threshold,
        )
        zz_check_obj.run()
        if mode == "tolerance":
            return zz_check_obj.run_options.new_hd_dict
        else:
            return zz_check_obj.run_options.fault_list

    @staticmethod
    def collection_line_offset(path: str, backend: Backend, save_db: bool = False):
        with open(path, encoding="utf-8") as fs:
            records = json.load(fs)

        chip_data = backend.context_manager.chip_data
        offset_data = {}
        hardware_manager = HardwareOffsetManager.from_data(
            chip_data.cache_config.get(ChipConfigField.hardware_offset)
        )

        for key, record in records.items():
            if "ZZTimingComposite" in key:
                analysis_data = record.get("analysis_data")
                for unit in record.get("pass_units"):
                    result = analysis_data.get(unit).get("result")
                    pair = chip_data.cache_qubit_pair.get(unit)
                    c_split = unit.split("q")
                    c_name = f"c{c_split[1]}-{c_split[2]}"
                    coupler = chip_data.cache_coupler.get(c_name)
                    if f"q{coupler.probe_bit}" == pair.qh:
                        pq, dq = pair.qh, pair.ql
                        rp, rd = "qh", "ql"
                    else:
                        pq, dq = pair.ql, pair.qh
                        rp, rd = "ql", "qh"

                    data = {
                        "zc": {
                            "channel": coupler.z_flux_channel,
                            "delay": result.get("qc")[1],
                        },
                        "zp": {
                            "channel": chip_data.cache_qubit.get(pq).z_flux_channel,
                            "delay": result.get(rp)[1],
                        },
                        "zd": {
                            "channel": chip_data.cache_qubit.get(dq).z_flux_channel,
                            "delay": result.get(rd)[1],
                        },
                    }

                    args = [
                        data.get("zc").get("channel"),
                        data.get("zp").get("channel"),
                        data.get("zd").get("channel"),
                        data.get("zc").get("delay"),
                        data.get("zp").get("delay"),
                        data.get("zd").get("delay"),
                    ]
                    offset_data[c_name] = data
                    hardware_manager.insert_zz_timing(*args)

        if save_db:
            chip_data.cache_config[ChipConfigField.hardware_offset] = (
                hardware_manager.to_origin_data()
            )
            backend.save_chip_data_to_db(ChipConfigField.hardware_offset)

        collection_path = str(Path(str(Path(path).parent), "hd.json"))

        with open(collection_path, mode="w", encoding="utf-8") as f:
            json.dump(offset_data, f, indent=4, ensure_ascii=False)

    def _check_experiment_options(self, pair_groups: Dict):
        if self.experiment_options.auto_calc_freq:
            res_req = ResonanceFrequency(filter_zero_amp=True)
            point_map = {}
            for group_name, group_pair in pair_groups.items():
                for pair in group_pair:
                    if self.experiment_options.swap_state in ["01", "10"]:
                        freq_point = res_req.get_intersect_freq01(
                            pair,
                            self.context_manager.chip_data,
                            step=self.experiment_options.step,
                            threshold=self.experiment_options.threshold,
                        )
                    # swap_state is 11
                    elif self.experiment_options.swap_state == "11":
                        freq_point = res_req.get_intersect_freq11(
                            pair,
                            self.context_manager.chip_data,
                            step=self.experiment_options.step,
                            threshold=self.experiment_options.threshold,
                        )
                    else:
                        raise NameError(
                            f"swap_sate({self.experiment_options.swap_state}) error!"
                        )
                    point_map[pair] = freq_point
            self.experiment_options.point_map = point_map
        elif not self.experiment_options.point_map:
            if self.experiment_options.swap_state != "11":
                differ = self.experiment_options.differ
                for pair_name in self.experiment_options.physical_units:
                    pair_obj = self.backend.chip_data.get_physical_unit(pair_name)
                    qh = self.backend.chip_data.get_physical_unit(pair_obj.qh)
                    ql = self.backend.chip_data.get_physical_unit(pair_obj.ql)
                    qh_max, qh_min = get_bound_ac_spectrum(qh)
                    ql_max, ql_min = get_bound_ac_spectrum(ql)
                    qh_freq = qh.drive_freq
                    ql_freq = ql.drive_freq
                    if abs(qh_freq - ql_freq) < differ:
                        q = (qh_freq + ql_freq) / 2 + 150
                        if not ((qh_min <= q <= qh_max) and (ql_min <= q <= ql_max)):
                            q = (qh_freq + ql_freq) / 2 - 150
                    else:
                        q = (qh_freq + ql_freq) / 2
                    q = round(q, 3)
                    pyqlog.info(f"update pair name point to {q} MHz")
                    pair_obj.update_cz_gate_params(
                        key="zz",
                        params={
                            pair_obj.qh: {"freq": q},
                            pair_obj.ql: {"freq": q},
                        },
                    )
                    self.experiment_options.point_map[pair_name] = [q]

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "Swap" == exp_name:
            label = "cz" if self.experiment_options.swap_state == "11" else "zz"
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    g = record.analysis_data.get(unit).get("result").get("g")
                    zz_width = (500 / g / 4) + 2
                    pair = self.backend.chip_data.get_physical_unit(unit)
                    pair.metadata.std[label].width = zz_width

        elif "SingleShot" == exp_name:
            for unit in record.analysis_data.keys():
                fidelity = (
                    record.analysis_data.get(unit)
                    .get("result", {})
                    .get("dcm", {})
                    .get("fidelity")
                )
                if fidelity:
                    self.run_options.fidelity_record[unit] = np.mean(np.array(fidelity))

        elif "ZZTimingComposite" == exp_name:
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    link = record.analysis_data.get(unit).get("result").get("link")
                    print(unit, link)
                    qh_channel, ql_channel, qc_channel, qh_value, ql_value, qc_value = (
                        link
                    )
                    pair = self.backend.chip_data.get_physical_unit(unit)
                    qc = self.backend.chip_data.get_physical_unit(pair.qc)
                    zd = pair.qh if str(qc.drive_bit) == pair.qh[1:] else pair.ql
                    zp = pair.qh if zd == pair.ql else pair.ql
                    zd_channel, zd_value = (
                        (qh_channel, qh_value)
                        if zd == pair.qh
                        else (ql_channel, ql_value)
                    )
                    zp_channel, zp_value = (
                        (qh_channel, qh_value)
                        if zp == pair.qh
                        else (ql_channel, ql_value)
                    )

                    self.run_options.zz_result[pair.qc] = {
                        "zc": {"channel": qc_channel, "delay": qc_value},
                        "zd": {"channel": zd_channel, "delay": zd_value},
                        "zp": {"channel": zp_channel, "delay": zp_value},
                    }
            self._save_data_to_json(self.run_options.zz_result, "zz_delay")
        return record

    def bind_process_data(self, process_data: QDict):
        process_data[self.__class__.__name__] = dict(
            experiment_options=dict(physical_units=self.run_options.next_pairs),
            analysis_options={},
            run_options=dict(zz_result=self.run_options.zz_result),
        )
