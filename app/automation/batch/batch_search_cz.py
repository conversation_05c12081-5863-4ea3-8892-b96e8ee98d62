# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/19
# __author:       <PERSON><PERSON><PERSON>


"""
节点作用：
1. 全局标定 CZ 门

节点流程: https://document.qpanda.cn/docs/ZzkLVJy6PLhV0E3Q
"""

from collections import defaultdict
from dataclasses import asdict, dataclass

from pyQCat.experiments.batch_experiment import BatchExperiment, List, pyqlog, BatchPhysicalUnitType
from pyQCat.processor.chip_data import ChipData
from pyQCat.qubit import Qubit, QubitPair
from pyQCat.tools.utilities import display_dict_as_table, get_bound_ac_spectrum


@dataclass
class CZPoint:
    name: str
    qh: str
    ql: str
    freq: float  # 低频比特位置， ql_freq = freq, qh_freq = freq - qh_非谐
    scan_bit: str  # 扫描比特，一般固定比特的频率已知可用，扫描比特用来精确标定，初始化节点默认使用离边界较远的比特作为扫描比特
    is_pass: bool = False

    def __eq__(self, other):
        if not isinstance(other, CZPoint):
            return False
        return (
            self.name == other.name
            and self.qh == other.qh
            and self.ql == other.ql
            and self.scan_bit == other.scan_bit
            and abs(self.freq - other.freq) <= 1
        )

    @classmethod
    def from_qubit_pair(cls, pair: QubitPair, is_pass: bool = False):
        return CZPoint(
            name=pair.name,
            qh=pair.qh,
            ql=pair.ql,
            scan_bit=pair.qh,
            freq=round(pair.cz_value(pair.ql, "freq"), 3),
            is_pass=is_pass,
        )

    def get_work_freq(self, qubit: Qubit):
        if qubit.name == self.ql:
            return self.freq
        if qubit.name == self.qh:
            return round(self.freq - qubit.anharmonicity, 3)
        raise ValueError(f"{qubit} not in {self}")


class CZPointSearcher:
    def __init__(self, chip_data: ChipData):
        self.chip_data = chip_data
        self.qubit_pair_cache = defaultdict(list)
        self.ready_points = {}
        self.pass_points = {}
        self.available_points = defaultdict(list)
        self.history_points = defaultdict(list)
        self.working_units = set()
        self.freq_bounds_cache = {}
        self.count = 0

    def _validate_cz_point(self, point: CZPoint, check_scan_bit: bool = False) -> bool:
        qh = self.chip_data.cache_qubit.get(point.qh)
        ql = self.chip_data.cache_qubit.get(point.ql)

        ql_freq = point.freq
        qh_freq = point.freq - qh.anharmonicity

        if qh.name not in self.freq_bounds_cache:
            self.freq_bounds_cache[qh.name] = get_bound_ac_spectrum(qh)

        if ql.name not in self.freq_bounds_cache:
            self.freq_bounds_cache[ql.name] = get_bound_ac_spectrum(ql)

        fh_max, fh_min = self.freq_bounds_cache.get(qh.name)
        fl_max, fl_min = self.freq_bounds_cache.get(ql.name)

        if check_scan_bit is True:
            qh_freq_map = max(abs(qh_freq - fh_max), abs(qh_freq - fh_min))
            ql_freq_map = max(abs(ql_freq - fl_max), abs(ql_freq - fl_min))
            point.scan_bit = point.qh if qh_freq_map > ql_freq_map else point.ql

        is_ok = fl_min <= ql_freq <= fl_max and fh_min <= qh_freq <= fh_max
        if is_ok is False:
            pyqlog.warning(f"Invalid point {point}")
        return is_ok

    def _extract_available_point(self):
        for unit, points in self.available_points.items():
            if points:
                self.ready_points[unit] = points.pop(0)
        if self.ready_points:
            self.count += 1

    def _build_available_points(
        self, qubit_name: str, goal_pair: QubitPair, base_point: CZPoint
    ):
        qubit = self.chip_data.cache_qubit.get(qubit_name)
        base_freq = base_point.get_work_freq(qubit)

        qh = qubit_name
        ql = goal_pair.ql if goal_pair.qh == qubit_name else goal_pair.qh

        points = [
            CZPoint(
                name=goal_pair.name,
                qh=qh,
                ql=ql,
                scan_bit=ql,
                freq=round(qubit.drive_freq + qubit.anharmonicity, 3),
            ),
            CZPoint(
                name=goal_pair.name, qh=ql, ql=qh, scan_bit=ql, freq=qubit.drive_freq
            ),
            CZPoint(
                name=goal_pair.name,
                qh=qh,
                ql=ql,
                scan_bit=ql,
                freq=round(base_freq + qubit.anharmonicity, 3),
            ),
            CZPoint(name=goal_pair.name, qh=ql, ql=qh, scan_bit=ql, freq=base_freq),
        ]

        for point in points:
            self._add_available_points(point)

    def _add_available_points(self, point: CZPoint, check_scan_bit: bool = False):
        if point not in self.history_points[point.name] and self._validate_cz_point(
            point, check_scan_bit
        ):
            pyqlog.info(f"add available point {point}")
            self.history_points[point.name].append(point)
            self.available_points[point.name].append(point)

    def _search_points(self):
        for pair_name, point in self.pass_points.items():
            pair = self.chip_data.cache_qubit_pair.get(pair_name)
            for side_pair in self.qubit_pair_cache.get(pair.qh, []):
                if side_pair.name in self.working_units:
                    self._build_available_points(pair.qh, side_pair, point)
            for side_pair in self.qubit_pair_cache.get(pair.ql, []):
                if side_pair.name in self.working_units:
                    self._build_available_points(pair.ql, side_pair, point)

    @staticmethod
    def point_map_to_table(point_map: dict):
        if point_map:
            return display_dict_as_table([asdict(v) for v in point_map.values()])

    def bind_init_point(self, physical_units: List[str]):
        """绑定初始工作点

        测试前, 可以提前给一些默认通过的 Pair, 基于这些 Pair 自动搜索工作点

        Args:
            physical_units (List[str]): Qubit Pair 名称
        """
        for unit in physical_units:
            pair = self.chip_data.cache_qubit_pair.get(unit)
            point = CZPoint.from_qubit_pair(pair, is_pass=True)
            if self._validate_cz_point(point):
                self.pass_points[pair.name] = point

    def bind_search_pair(self, physical_units: List[str], include_point: bool = True):
        """绑定需要测试的 CZ 门

        Args:
            physical_units (List[str]): Qubit Pair 名称
            include_point (bool, optional): Qubit Pair 自身的初始工作点是否进入搜索流程. Defaults to True.
        """
        for unit in physical_units:
            if unit not in self.pass_points:
                pair = self.chip_data.cache_qubit_pair.get(unit)

                if include_point is True:
                    point = CZPoint.from_qubit_pair(pair)
                    self._add_available_points(point, check_scan_bit=True)

                self.working_units.add(unit)
                self.qubit_pair_cache[pair.qh].append(pair)
                self.qubit_pair_cache[pair.ql].append(pair)

    def bind_pass_pair(self, physical_units: List[str]):
        """前一轮待测 Qubit Pair 结束后，统计成功的 CZ 门

        Args:
            physical_units (List[str]): Qubit Pair 名称
        """
        cur_pass_points = {}
        for unit in physical_units:
            point: CZPoint = self.ready_points.pop(unit)
            point.is_pass = True
            cur_pass_points[unit] = point
            self.pass_points[unit] = point
            self.working_units.discard(unit)
            self.available_points.pop(unit)
        table = self.point_map_to_table(cur_pass_points)
        pyqlog.log("FLOW", f"Count-{self.count} Pass Points:\n{table}")
        self.ready_points.clear()

    def next_check_point(self):
        """
        自动获取下一轮要测试的工作点
        """
        self._extract_available_point()
        if not self.ready_points:
            self._search_points()
            self._extract_available_point()

        if self.ready_points:
            table = self.point_map_to_table(self.ready_points)
            pyqlog.log("FLOW", f"Count-{self.count} Ready Points:\n{table}")


class BatchSearchCZ(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.include_self_point = True
        options.start_pairs = []
        options.cz_freq_cali_flows = [
            "FixedPointCalibration_cz_qh",
            "FixedPointCalibration_cz_ql",
            "FixedSwapFreqCaliCoupler2_cz",
        ]
        options.cz_flows = [
            "Swap",
            "LeakagePre",
            "LeakageAmp",
            "CPhaseTMSE",
            "SQPhaseTMSE",
            "XEBMultiple_1",
            "NMXEBMultiple",
            "XEBMultiple_2",
        ]
        options.set_scan_name_exp = [
            "Swap",
            "LeakageAmp",
            "CPhaseTMSE",
        ]
        options.physical_unit_type = BatchPhysicalUnitType.PAIR
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.cz_fl_map = {}
        options.cz_fh_map = {}
        options.fail_units = {}
        options.searcher = None
        return options

    def _batch_up(self):
        super()._batch_up()
        searcher = CZPointSearcher(self.backend.chip_data)
        if self.experiment_options.start_pairs:
            searcher.bind_init_point(self.experiment_options.start_pairs)
        if self.experiment_options.physical_units:
            searcher.bind_search_pair(
                self.experiment_options.physical_units,
                self.experiment_options.include_self_point,
            )
        searcher.next_check_point()
        self.run_options.searcher = searcher

    def _set_cz_point(self, point: CZPoint):
        pair = self.backend.chip_data.cache_qubit_pair.get(point.name)
        qh_obj = self.backend.chip_data.cache_qubit.get(point.qh)
        pair.metadata.std.ql = point.ql
        pair.metadata.std.qh = point.qh
        pair.set_cz_value(point.qh, field="freq", value=point.freq - qh_obj.anharmonicity)
        pair.set_cz_value(point.ql, field="freq", value=point.freq)
        for exp_name in self.experiment_options.set_scan_name_exp:
            self.change_parallel_exec_exp_options(
                exp_name=exp_name, unit=point.name, scan_name=point.scan_bit
            )

    def _run_cz_flows(self, physical_units: List[str], group_info: str) -> List[str]:
        for unit, point in self.run_options.searcher.ready_points.items():
            self._set_cz_point(point)

        pass_units = physical_units
        if self.experiment_options.cz_freq_cali_flows:
            pass_units = self._run_flow(
                flows=self.experiment_options.cz_freq_cali_flows,
                physical_units=pass_units,
                name=f"{group_info}: CZ Point Calibration",
            )

        if pass_units:
            pass_units = self._run_flow(
                flows=self.experiment_options.cz_flows,
                physical_units=pass_units,
                name=f"{group_info}: CZ Calibration",
            )

        for unit in pass_units:
            pair = self.backend.chip_data.cache_qubit_pair.get(unit)
            pair.save_data()

        return pass_units

    def _run_once_batch(self):
        searcher: CZPointSearcher = self.run_options.searcher
        physical_units = list(searcher.ready_points.keys())
        group_map = self.parallel_allocator_for_cgc(physical_units)
        pass_units = []
        for name, group in group_map.items():
            group_info = f"Iter-{searcher.count} | {name}/{len(group_map)}"
            group_pass_units = self._run_cz_flows(group, group_info)
            pass_units.extend(group_pass_units)
        return pass_units

    def _run_batch(self):
        searcher: CZPointSearcher = self.run_options.searcher

        while searcher.ready_points:
            pass_units = self._run_once_batch()
            searcher.bind_pass_pair(pass_units)
            searcher.next_check_point()

        self.bind_pass_or_fail_units(list(searcher.pass_points.keys()))


if __name__ == "__main__":
    import random

    from app.config import init_backend

    backend = init_backend()

    searcher = CZPointSearcher(backend.chip_data)

    # 绑定初始成功的基准 CZ 门，可以不设
    searcher.bind_init_point([])

    # 绑定期望测试的 CZ 门
    searcher.bind_search_pair(
        [
            "q93q100",
            "q45q52",
            "q23q30",
            "q42q48",
            "q19q26",
            "q75q81",
            "q82q89",
            "q1q7",
            "q90q96",
            "q8q15",
            "q27q33",
            "q16q22",
            "q34q41",
            "q67q74",
            "q86q92",
            "q64q70",
            "q56q63",
            "q5q11",
            "q71q78",
            "q53q59",
            "q93q99",
            "q63q68",
            "q54q59",
            "q19q25",
            "q36q42",
            "q6q11",
            "q2q7",
            "q80q86",
            "q50q55",
            "q32q38",
            "q15q20",
            "q41q46",
            "q10q16",
            "q84q90",
            "q28q33",
            "q58q64",
            "q89q94",
            "q67q73",
            "q71q77",
            "q76q81",
            "q57q64",
            "q46q53",
            "q2q8",
            "q83q90",
            "q28q34",
            "q87q93",
            "q94q101",
            "q50q56",
            "q79q86",
            "q17q23",
            "q39q45",
            "q6q12",
            "q61q67",
            "q76q82",
            "q9q16",
            "q68q75",
            "q20q27",
            "q31q38",
            "q54q60",
            "q65q71",
            "q64q69",
            "q55q61",
            "q59q65",
            "q11q17",
            "q94q100",
            "q86q91",
            "q46q52",
            "q20q26",
            "q42q47",
            "q3q8",
            "q29q34",
            "q68q74",
            "q24q30",
            "q51q56",
            "q90q95",
            "q16q21",
            "q81q87",
            "q33q39",
            "q77q82",
            "q72q78",
            "q38q43",
            "q91q98",
            "q77q83",
            "q40q46",
            "q58q65",
            "q69q76",
            "q62q68",
            "q73q79",
            "q95q102",
            "q47q54",
            "q32q39",
            "q51q57",
            "q25q31",
            "q3q9",
            "q21q28",
            "q43q50",
            "q66q72",
            "q10q17",
            "q14q20",
            "q80q87",
            "q18q24",
            "q88q94",
            "q17q22",
            "q78q83",
            "q69q75",
            "q91q97",
            "q47q53",
            "q74q79",
            "q21q27",
            "q82q88",
            "q65q70",
            "q56q62",
            "q26q31",
            "q95q101",
            "q60q66",
            "q52q57",
            "q12q18",
            "q87q92",
            "q34q40",
            "q4q9",
            "q8q14",
            "q70q77",
            "q89q95",
            "q26q32",
            "q63q69",
            "q37q43",
            "q7q14",
            "q85q91",
            "q92q99",
            "q15q21",
            "q81q88",
            "q41q47",
            "q11q18",
            "q74q80",
            "q59q66",
            "q30q36",
            "q52q58",
            "q4q10",
            "q55q62",
            "q78q84",
            "q22q29",
            "q33q40",
            "q88q93",
            "q27q32",
            "q40q45",
            "q18q23",
            "q92q98",
            "q62q67",
            "q22q28",
            "q5q10",
            "q75q80",
            "q57q63",
            "q96q102",
            "q53q58",
            "q48q54",
            "q9q15",
            "q14q19",
            "q31q37",
            "q79q85",
            "q70q76",
            "q66q71",
            "q83q89",
        ]
    )

    # 初始化待测 CZ 工作点
    searcher.next_check_point()

    while searcher.ready_points:
        # 模拟待测工作点通过情况，50% 通过率
        pass_units = []
        for unit in searcher.ready_points:
            if random.random() < 0.5:
                pass_units.append(unit)

        # 绑定测试成功的 CZ 门
        searcher.bind_pass_pair(pass_units)

        # 下轮测试工作点继续走
        searcher.next_check_point()

    pyqlog.log(
        "RESULT",
        f"流程结束, 成功工作点：\n{searcher.point_map_to_table(searcher.pass_points)}\n失败 Pair: {searcher.working_units}",
    )
