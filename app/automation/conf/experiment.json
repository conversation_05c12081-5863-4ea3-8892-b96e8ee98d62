{"BusS21Collector": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "BusS21Collector", "export_datetime": "2023-12-22 17:41:32", "description": null}, "context_options": {"name": "sqmc_context", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"high_power": -15, "low_power": -45, "wide_scope_freq": "Points(25) | qarange | (4000, 8500, 1)", "small_scope_freq": "Points(25) | qarange | (6800, 7300, 1)"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "FindBusCavityFreq_rough": {"meta": {"username": "D9_super", "visage_version": "0.23.2", "monster_version": "********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "coupler_calibration"}, "exp_class_name": "FindBusCavityFreq", "export_datetime": "2025-06-25 20:13:46", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "", "physical_unit": "q1,q2,q3,q4,q5,q6"}, "options_for_regular_exec": {"experiment_options": {"device_conf_path": null, "chip_type": "102bit-v2.0", "bus": 1, "segm_scan": false, "freq_list": "Points(501) | qarange | (6800, 7300, 0.5)", "scope": {"l": 2, "r": 2, "p": 401}, "network_analyzer": "E5071C", "net_power": -30, "ATT": 0, "net_IFBW": 1000}, "analysis_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "cavity_count": 6, "fit_q": false, "chi_square": 0.01, "distance": 20}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "FindBusCavityFreq_segma": {"meta": {"username": "D9_super", "visage_version": "0.23.2", "monster_version": "********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "coupler_calibration"}, "exp_class_name": "FindBusCavityFreq", "export_datetime": "2025-06-25 20:16:24", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "", "physical_unit": "q1,q2,q3,q4,q5,q6"}, "options_for_regular_exec": {"experiment_options": {"device_conf_path": null, "chip_type": "102bit-v2.0", "bus": 1, "segm_scan": true, "freq_list": "Points(501) | qarange | (6800, 7300, 0.5)", "scope": {"l": 2, "r": 2, "p": 401}, "network_analyzer": "E5071C", "net_power": -30, "ATT": 0, "net_IFBW": 1000}, "analysis_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "cavity_count": 6, "fit_q": true, "chi_square": 0.01, "distance": 20}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ImpaGain": {"meta": {"username": "<PERSON><PERSON><PERSON><PERSON>", "visage_version": "0.23.2", "monster_version": "*********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "sweetpoint_cavityq"}, "exp_class_name": "ImpaGain", "export_datetime": "2025-07-02 10:09:29", "description": null}, "context_options": {"name": "sqmc_context", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"device_conf_path": null, "network_analyzer": "E5071C", "freq_list": "Points(501) | qarange | (6800, 7300, 1)", "net_IFBW": 1000, "net_power": -40, "ATT": -100, "ffp_list": "Points(3) | normal | [4.18497, 14.6742, 15]", "mic_source": "byfs", "dc_source": "qaio", "total_time": 2, "interval_time": 20, "stab_flag": false, "bus": 1, "mode": "open"}, "analysis_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "threshold": 8}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityFreqSpectrum": {"meta": {"username": "D9_super", "visage_version": "0.23.2", "monster_version": "********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "coupler_calibration"}, "exp_class_name": "CavityFreqSpectrum", "export_datetime": "2025-06-18 15:52:02", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"period": 100, "acq_index": "Points(0) | normal | None", "resource_filter": true, "open_half_pi": false, "union_mode": "union", "fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "extend_f12": false, "scope": 3, "z_amp": null, "mode": "IF"}, "analysis_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "quality_bounds": [0.98, 0.95, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityPowerScan": {"meta": {"username": "D9_super", "visage_version": "0.23.2", "monster_version": "********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "coupler_calibration"}, "exp_class_name": "CavityPowerScan", "export_datetime": "2025-06-18 15:52:09", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"period": 100, "acq_index": "Points(0) | normal | None", "resource_filter": true, "open_half_pi": false, "union_mode": "union", "fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "extend_f12": false, "scope": 3, "z_amp": null, "mode": "IF"}, "run_mode": "sync", "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "power_list": "Points(0) | qarange | (-40, -10, 1)"}, "analysis_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "child_ana_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "quality_bounds": [0.98, 0.95, 0.85]}, "quality_bounds": [0.98, 0.95, 0.85], "window_size": 5, "f_threshold": 0.1, "p_threshold": 3}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityTunable_for_coupler": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CavityTunable", "export_datetime": "2024-01-24 11:13:04", "description": null}, "context_options": {"name": "coupler_calibration", "physical_unit": "c1-2", "readout_type": "probeQ"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 2, "z_amp": null, "mode": "IF"}, "fc_list": "Points(0) | normal | None", "readout_power": null, "flux_list": "Points(0) | qarange | (-0.48, 0.48, 0.03)", "scan_name": "ac_bias"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.85, 0.75, 0.7]}, "diff_threshold": 0.01, "tackle_type": "<PERSON><PERSON><PERSON>", "quality_bounds": [0.98, 0.95, 0.9]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityTunable_for_qubit": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CavityTunable", "export_datetime": "2024-01-24 11:13:04", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 3, "z_amp": null, "mode": "IF"}, "fc_list": "Points(0) | normal | None", "readout_power": null, "flux_list": "Points(0) | qarange | (-0.48, 0.48, 0.03)", "scan_name": "ac_bias"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.85, 0.75, 0.65]}, "diff_threshold": 0.1, "tackle_type": "<PERSON><PERSON><PERSON>", "quality_bounds": [0.98, 0.95, 0.9]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitSpectrumScanPower": {"meta": {"username": "yuchengfeng", "visage_version": "0.23.1", "monster_version": "*********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "zidonghua_test"}, "exp_class_name": "QubitSpectrumScanPower", "export_datetime": "2025-06-16 17:01:38", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q21"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"period": 20, "acq_index": "Points(0) | normal | None", "resource_filter": true, "open_half_pi": false, "freq_list": "Points(81) | qarange | (4200, 5200, 10)", "drive_power": null, "z_amp": null, "use_square": true, "band_width": 50.0, "fine_flag": false, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70.0, "fine_window_length": 11, "fine_freq_distance": 80.0}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 1.0, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0.0, "sigma": 5.0, "fast_m": false}, "scope": {"l": 400, "r": 200, "s": 5}, "f02_scope": {"l": 150, "r": -50, "s": 2}, "mode": "IF", "f_type": "f01"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "drive_power_list": "Points(16) | qarange | (-40, -10, 1)"}, "analysis_options": {"pure_exp_mode": false, "save_s3": false, "child_ana_options": {"pure_exp_mode": false, "save_s3": false, "quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5}}}, "options_for_parallel_exec": {}}, "QubitSpectrumVMax": {"meta": {"username": "yuchengfeng", "visage_version": "0.23.1", "monster_version": "*********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "zidonghua_test"}, "exp_class_name": "QubitSpectrum", "export_datetime": "2025-06-16 17:23:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "file_flag": 0, "repeat": 1000, "period": 20, "register_pulse_save": false, "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "ibu", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "prepare_measure_offset": 300, "acq_index": "Points(0) | normal | None", "resource_filter": true, "open_half_pi": false, "freq_list": "Points(161) | qarange | (4200, 5100, 10)", "drive_power": null, "z_amp": null, "use_square": true, "band_width": 50.0, "fine_flag": false, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70.0, "fine_window_length": 11, "fine_freq_distance": 80.0}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 0.5, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0.0, "sigma": 5.0, "fast_m": false}, "scope": {"l": 400, "r": 200, "s": 5}, "f02_scope": {"l": 150, "r": -50, "s": 2}, "mode": "IF", "f_type": "f01"}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "pure_exp_mode": false, "save_s3": false, "quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitSpectrumVMin": {"meta": {"username": "yuchengfeng", "visage_version": "0.23.1", "monster_version": "*********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "zidonghua_test"}, "exp_class_name": "QubitSpectrum", "export_datetime": "2025-06-16 17:23:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "file_flag": 0, "repeat": 1000, "period": 20, "register_pulse_save": false, "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "ibu", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "prepare_measure_offset": 300, "acq_index": "Points(0) | normal | None", "resource_filter": true, "open_half_pi": false, "freq_list": "Points(161) | qarange | (3800, 4500, 10)", "drive_power": null, "z_amp": null, "use_square": true, "band_width": 50.0, "fine_flag": false, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70.0, "fine_window_length": 11, "fine_freq_distance": 80.0}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 0.5, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0.0, "sigma": 5.0, "fast_m": false}, "scope": {"l": 400, "r": 200, "s": 5}, "f02_scope": {"l": 150, "r": -50, "s": 2}, "mode": "IF", "f_type": "f01"}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "pure_exp_mode": false, "save_s3": false, "quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitSpectrumZAmp": {"meta": {"username": "yuchengfeng", "visage_version": "0.23.1", "monster_version": "*********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "zidonghua_test"}, "exp_class_name": "QubitSpectrumZAmp", "export_datetime": "2025-06-16 17:27:11", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "child_exp_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "file_flag": 0, "repeat": 1000, "period": 20, "register_pulse_save": false, "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "ibu", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "prepare_measure_offset": 300, "acq_index": "Points(0) | normal | None", "resource_filter": true, "open_half_pi": false, "freq_list": "Points(121) | qarange | (3800, 5000, 5)", "drive_power": null, "z_amp": null, "use_square": true, "band_width": 50.0, "fine_flag": false, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70.0, "fine_window_length": 11, "fine_freq_distance": 80.0}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 0.5, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0.0, "sigma": 5.0, "fast_m": false}, "scope": {"l": 400, "r": 200, "s": 5}, "f02_scope": {"l": 150, "r": -50, "s": 2}, "mode": "IF", "f_type": "f01"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "z_amp_list": "Points(0) | normal | None"}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "pure_exp_mode": false, "save_s3": false, "child_ana_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "pure_exp_mode": false, "save_s3": false, "quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5}, "clear_base": false, "diff_ratio": 0.5, "point_nums": 5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityFreqSpectrum_BSZ": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CavityFreqSpectrum", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q5"}, "options_for_regular_exec": {"experiment_options": {"fc_list": "Points(0) | normal | None", "points": 101, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "period": 20, "repeat": 300, "scope": 5, "z_amp": null, "mode": "IF"}, "analysis_options": {"quality_bounds": [0.95, 0.5, 0.3]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityFreqSpectrum_BSI": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CavityFreqSpectrum", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q5"}, "options_for_regular_exec": {"experiment_options": {"fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": true, "scope": 3, "z_amp": null, "mode": "IF"}, "analysis_options": {"quality_bounds": [0.8, 0.6, 0.5]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitSpectrum_BSI": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "QubitSpectrum", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"freq_list": "Points(141) | qarange | (3800, 5200, 10)", "drive_power": null, "z_amp": null, "use_square": true, "band_width": 50.0, "fine_flag": false, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70.0, "fine_window_length": 11, "fine_freq_distance": 80.0}, "xpulse_params": {"time": 4000, "offset": 15, "amp": 1.0, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5000, "amp": 0.0, "sigma": 5.0, "fast_m": false}, "scope": {"l": 400, "r": 200, "s": 5}, "f02_scope": {"l": 150, "r": -50, "s": 2}, "mode": "IF", "f_type": "f01", "is_dynamic": 0}, "analysis_options": {"quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "XpiDetection", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null, "is_dynamic": 0}, "expect_value": 0.7, "scope": 0.1, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.9, 0.8, 0.7]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanWidth": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "RabiScanWidth", "export_datetime": "2023-11-29 19:58:37", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1,q2,q3,q4,q5,q6"}, "options_for_regular_exec": {"experiment_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "period": 200, "repeat": 300, "drive_power": null, "drive_freq": null, "adjust_power_flag": true}, "analysis_options": {"quality_bounds": [0.98, 0.75, 0.65]}}, "options_for_parallel_exec": {}}, "SingleShot_BSI": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"quality_bounds": [2, 0.5, 0.5, 0.1], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitFreqCalibration": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null, "is_dynamic": 0}, "fringes": "Points(2) | qarange | (41, -41, -82)", "delays": "Points(71) | qarange | (50, 150, 2.5)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.7, 0.5], "factor": 3.5}, "subplots": [2, 2], "freq_gap_threshold": 2}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ReadoutFreqCalibrate_01": {"meta": {"username": "llguoY4", "visage_version": "0.23.2", "monster_version": "0.23.2", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "1q_point_102_readout02"}, "exp_class_name": "ReadoutFreqCalibrate", "export_datetime": "2025-06-18 16:22:53", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q5"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"acq_index": "Points(0) | normal | None", "resource_filter": true, "open_half_pi": false, "union_mode": "union", "fc_list": "Points(0) | normal | None", "points": 101, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "extend_f12": false, "scope": 2, "z_amp": null, "mode": "IF"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "fc_list": "Points(0) | normal | None", "readout_power": null, "readout_type": "01"}, "analysis_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "child_ana_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "quality_bounds": [0.98, 0.95, 0.85]}, "save_mode": "mean_point", "diff_threshold": 0.02}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ReadoutPowerCalibrate_01": {"meta": {"username": "llguoY4", "visage_version": "0.23.2", "monster_version": "0.23.2", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "1q_point_102_readout02"}, "exp_class_name": "ReadoutPowerCalibrate", "export_datetime": "2025-06-19 11:31:05", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q5"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"acq_index": "Points(0) | normal | None", "resource_filter": true, "open_half_pi": false, "union_mode": "union", "level_str": "01"}, "run_mode": "sync", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "sweep_list": "Points(21) | qarange | (-40, -20, 1)"}, "analysis_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "child_ana_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "quality_bounds": [2, 0.85, 0.6, 0.015], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false, "plot_circle": false, "remove_outlier": false}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitFreqCalibration_XC": {"meta": {"username": "why_y4_debug", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2024-08-20 09:53:26", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "fringes": "Points(2) | qarange | (50, -50, -100)", "delays": "Points(41) | qarange | (20, 120, 2.5)"}, "analysis_options": {"pure_exp_mode": false, "child_ana_options": {"pure_exp_mode": false, "quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}, "subplots": [2, 2], "freq_gap_threshold": 0.1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanWidth_XC": {"meta": {"username": "why_y4_debug", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "RabiScanWidth", "export_datetime": "2024-08-21 18:17:01", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false}, "analysis_options": {"pure_exp_mode": false, "quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XYCrossRabiWidthOnce_XC": {"meta": {"username": "why_y4_debug", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "XYCrossRabiWidthOnce", "export_datetime": "2024-08-20 17:50:11", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"period": 200, "acq_index": "Points(0) | normal | None", "resource_filter": true, "open_half_pi": false, "union_mode": "union", "widths": "Points(40) | qarange | (5, 300, 7.5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "xy_name": "", "rd_name": "", "direct_execute": false}, "analysis_options": {"pure_exp_mode": false, "quality_bounds": [0.9, 0.8, 0.7], "loga_fit": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitSpectrumF12": {"meta": {"username": "zyc", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "QubitSpectrumF12", "export_datetime": "2024-08-15 15:25:45", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"f12_list": "Points(0) | normal | None", "f12_xpi": 0.02, "f12_width": 500, "scope": {"l": 300, "r": -180, "s": 1}, "z_amp": 0, "delay": 0}, "analysis_options": {"pure_exp_mode": false, "quality_bounds": [0.8, 0.6, 0.5], "window_length": 11, "freq_distance": 80}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanWidthF12": {"meta": {"username": "yuchengfeng", "visage_version": "0.23.1", "monster_version": "*********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "batch_searchidle"}, "exp_class_name": "RabiScanWidthF12", "export_datetime": "2025-06-30 16:11:39", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q16"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "file_flag": 0, "repeat": 1000, "period": 100, "register_pulse_save": false, "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "ibu", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "prepare_measure_offset": 300, "acq_index": "Points(0) | normal | None", "resource_filter": true, "open_half_pi": false, "widths": "Points(41) | qarange | (100, 300, 5)", "f12_xpi": null, "f12_freq": null}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "pure_exp_mode": false, "save_s3": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "F12Calibration": {"meta": {"username": "yuchengfeng", "visage_version": "0.23.1", "monster_version": "*********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "batch_searchidle"}, "exp_class_name": "F12Calibration", "export_datetime": "2025-06-30 16:11:25", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q16"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "child_exp_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "file_flag": 0, "repeat": 1000, "period": 100, "register_pulse_save": false, "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "ibu", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "prepare_measure_offset": 300, "acq_index": "Points(0) | normal | None", "resource_filter": true, "open_half_pi": false, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "fringes": "Points(2) | qarange | (20, -20, -40)", "delays": "Points(81) | qarange | (50, 250, 2.5)"}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "pure_exp_mode": false, "save_s3": false, "child_ana_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "pure_exp_mode": false, "save_s3": false, "quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}, "subplots": [2, 2], "freq_gap_threshold": 1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityFreqSpectrum_XT": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CavityFreqSpectrum", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q5"}, "options_for_regular_exec": {"experiment_options": {"fc_list": "Points(0) | normal | None", "points": 51, "readout_power": null, "pi_amp": null, "add_pi_pulse": true, "scope": 5, "z_amp": null, "mode": "IF"}, "analysis_options": {"quality_bounds": [0.9, 0.8, 0.5]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShot_XT": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q4,q10"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01", "is_dynamic": 0}, "analysis_options": {"quality_bounds": [2, 0.6, 0.3, 0.05], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {}}, "SingleShot_XT1": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q4,q10"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01", "is_dynamic": 0}, "analysis_options": {"quality_bounds": [2, 0.6, 0.1, 0.05], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {}}, "QubitFreqCalibration_XT": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (20, 120, 2.5)", "fringe": 250, "z_amp": null, "frequency": null, "ac_branch": null, "is_dynamic": 0}, "fringes": "Points(2) | qarange | (50, -50, -100)", "delays": "Points(71) | qarange | (20, 120, 2.5)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.9, 0.6, 0.5], "factor": 3.5}, "subplots": [2, 2], "freq_gap_threshold": 2}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection_XT": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "XpiDetection", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null, "is_dynamic": 0}, "expect_value": 0.7, "scope": 0.2, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.9, 0.8, 0.5]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XYZTimingComposite": {"meta": {"username": "dpY2", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "batch_test"}, "exp_class_name": "XYZTimingComposite", "export_datetime": "2024-01-15 16:45:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"is_dynamic": 0, "time_perform": true, "delays": "Points(321) | qarange | (30, 150, 1.66666666)", "const_delay": 100, "z_pulse_params": {"time": 15, "amp": 0.2, "sigma": 0.2, "buffer": 2.5}}, "max_count": 2, "policy": "normal"}, "analysis_options": {"child_ana_options": {"peak_limit": 0.3, "extract_mode": "fit_params", "quality_bounds": [0.95, 0.93, 0.75], "fit_model_name": "gauss_lo<PERSON>zian"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SweetPointCalibration": {"meta": {"username": "zyc", "visage_version": "********", "monster_version": "********", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "SweetPointCalibration", "export_datetime": "2024-02-26 17:47:12", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "iteration": 5, "threshold": 0.01, "guess_step": 0.02, "cali_point": "sweet_point", "z_amp": null, "frequency": null, "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ACSpectrum": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "ACSpectrum", "export_datetime": "2024-01-16 09:44:24", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "child_exp_options": {"time_perform": false, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "z_amps": "Points(22) | qarange | (-0.02, 0.4, 0.02)", "delays": "Points(61) | qarange | (200, 800, 10)", "freq_bound": 800, "osc_freq_limit": 2.5, "init_fringe": 10, "spectrum_type": "standard"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}, "quality_bounds": [0.995, 0.992, 0.99], "fit_model_name": "amp2freq_formula"}}, "options_for_parallel_exec": {}}, "SingleShot_RBS": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q4,q10"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"quality_bounds": [2, 0.5, 0.05, 0.05]}}, "options_for_parallel_exec": {}}, "QubitFreqCalibration_RBS_0": {"meta": {"username": "BY170003", "visage_version": "0.11.1", "monster_version": "0.11.1", "chip": {"sample": "240720-工艺验证-102bit-V1.1-Base-RD2400-Demo-2#（V1.1-Flip-NB+Ta-22#-B1）", "env_name": "Y6", "point_label": "sweetpoint0918"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2024-09-19 18:14:27", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q18"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "fringes": "Points(2) | qarange | (60, -60, -120)", "delays": "Points(57) | qarange | (10, 150, 2.5)"}, "analysis_options": {"pure_exp_mode": false, "child_ana_options": {"pure_exp_mode": false, "quality_bounds": [0.7, 0.5, 0.4], "factor": 3.5, "fit_type": "osc"}, "subplots": [2, 2], "freq_gap_threshold": 1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitFreqCalibration_RBS_1": {"context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "experiment_options": {"fringes": [50, -50], "delays*qarange": [20, 140, 2.5], "child_exp_options": {"is_dynamic": 0}}, "analysis_options": {"freq_gap_threshold": 0.5, "child_ana_options": {"quality_bounds": [0.9, 0.6, 0.5]}}}, "DetuneCalibration_RBS": {"context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "experiment_options": {"detune_list*qarange": [-28, 28, 1], "rough_n_list": [6, 7, 8], "fine_n_list": [12, 13], "theta_type": "Xpi", "fine_precision": 0.15, "child_exp_options": {"child_exp_options": {"is_dynamic": 0}}}, "analysis_options": {"child_ana_options": {"diff_threshold": 1}}}, "RabiScanAmp_RBS": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "RabiScanAmp", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "options_for_regular_exec": {"experiment_options": {"amps*qarange": [0, 0.9, 0.02], "name": "Xpi", "is_dynamic": 0}, "analysis_options": {"quality_bounds": [0.95, 0.8, 0.7]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "AmpComposite_RBS": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "AmpComposite", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "options_for_regular_exec": {"experiment_options": {"n_list": [16, 20], "theta_type": "Xpi/2", "child_exp_options": {"points": 81, "threshold_left": 0.9, "threshold_right": 1.1, "is_dynamic": 0, "plot_iq": false}}, "analysis_options": {"diff_threshold": 0.05}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "T1": {"meta": {"username": "BY170003", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "240708-工艺验证-102bit-V1.1-Base-DT(Nb+Ta)-1#(V1.1-Flip-NB+Ta-6#-A1)", "env_name": "Y3", "point_label": "sweetpoint"}, "exp_class_name": "T1", "export_datetime": "2024-08-11 13:02:30", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q93"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "least-sq", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "prepare_measure_offset": 300, "delays": "Points(34) | qarange | (1000, 63000, 1000)", "z_amp": null, "frequency": null, "ac_branch": "right"}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "pure_exp_mode": false, "quality_bounds": [0.8, 0.7, 0.5]}}}, "T2Ramsey": {"meta": {"username": "BY170003", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "240708-工艺验证-102bit-V1.1-Base-DT(Nb+Ta)-1#(V1.1-Flip-NB+Ta-6#-A1)", "env_name": "Y3", "point_label": "sweetpoint"}, "exp_class_name": "T2Ramsey", "export_datetime": "2024-08-11 13:02:38", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q93"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "child_exp_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "least-sq", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "prepare_measure_offset": 300, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "run_mode": "sync", "async_time_out": 10, "quality_filter": false, "delays": "Points(250) | qarange | (200, 12000, 100)", "fringe": 1, "z_amp": null, "rate_down": 0.3, "rate_up": 0.5, "max_loops": 3, "frequency": null, "ac_branch": "right"}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "pure_exp_mode": false, "child_ana_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "pure_exp_mode": false, "quality_bounds": [0.9, 0.6, 0.5], "factor": 3.5, "fit_type": "osc"}, "fit_type": "osc"}}}, "SpinEcho": {"meta": {"username": "BY170003", "visage_version": "0.10.2", "monster_version": "0.10.2", "chip": {"sample": "240720-工艺验证-102bit-V1.1-Base-RD2400-Demo-2#（V1.1-Flip-NB+Ta-22#-B1）", "env_name": "Y6", "point_label": "sweetpoint"}, "exp_class_name": "SpinEcho", "export_datetime": "2024-08-31 15:49:20", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "least-sq", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "prepare_measure_offset": 300, "delays": "Points(201) | qarange | (250, 30000, 250)", "fringe": 0.4, "z_amp": null, "frequency": null, "ac_branch": "right"}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "pure_exp_mode": false, "quality_bounds": [0.95, 0.6, 0.5], "factor": 3.5, "fit_type": "osc"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RBSingle": {"meta": {"username": "BY170003", "visage_version": "0.10.2", "monster_version": "0.10.2", "chip": {"sample": "240720-工艺验证-102bit-V1.1-Base-RD2400-Demo-2#（V1.1-Flip-NB+Ta-22#-B1）", "env_name": "Y6", "point_label": "sweetpoint"}, "exp_class_name": "RBSingle", "export_datetime": "2024-08-31 15:49:20", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"times": 15, "depth1*qarange": [2, 10, 2], "depth2*qarange": [15, 50, 5], "depth3*qarange": [60, 100, 20], "depth4*qarange": [150, 500, 50], "gate_split": true, "mode": "cache", "is_dynamic": 0, "plot_iq": false, "use_simulator": false}, "analysis_options": {"quality_bounds": [0.6, 0.55, 0.5], "std_bound": 0.08, "fidelity_threshold": 0.992}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection_RBS": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "XpiDetection", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null, "is_dynamic": 0}, "expect_value": 0.7, "scope": 0.2, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.9, 0.8, 0.5]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}