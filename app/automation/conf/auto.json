{"meta": {"username": "zyc", "monster_version": "0.23.2", "sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2"}, "nodes": {"BatchBusS21": {"meta": {"exp_class_name": "BatchBusS21", "description": "粗采 BUS S21 数据"}, "experiment_options": {"param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "flows": ["BusS21Collector"], "ip": "*************", "port": 10001}, "analysis_options": {}}, "BatchCavityQ": {"meta": {"exp_class_name": "BatchCavityQ", "description": "高低功率Q值采集"}, "experiment_options": {"param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "flows": ["FindBusCavityFreq_rough", "FindBusCavityFreq_segma"], "low_point_label": "init_point", "high_point_label": "high_point", "high_net_power": -15, "low_net_power": -45, "ip": "*************", "port": 10001, "save_db": true}, "analysis_options": {}}, "BatchIMPAControl": {"meta": {"exp_class_name": "BatchIMPAControl", "description": "IMPA 一键设置及关闭"}, "experiment_options": {"param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "flows": ["ImpaGain"], "mode": "open", "impa_params_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\impa_params.json", "ip": "*************", "port": 10001}, "analysis_options": {}}, "BatchCavityPowerScan_V0": {"meta": {"exp_class_name": "BatchCavityPowerScan", "description": "V0 位置腔功率谱"}, "experiment_options": {"param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "flows": ["CavityFreqSpectrum", "CavityPowerScan"], "point_mode": "zero"}, "analysis_options": {}}, "BatchCouplerTunable": {"meta": {"exp_class_name": "BatchCouplerTunable", "description": "<PERSON>upler 调制谱"}, "experiment_options": {"flows": ["CavityTunable_for_coupler"], "mode": "line", "param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "refresh_context": false}, "analysis_options": {}}, "BatchQubitTunable": {"meta": {"exp_class_name": "BatchQubitTunable", "description": "Qubit 调制谱"}, "experiment_options": {"flows": ["CavityTunable_for_qubit"], "param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "refresh_context": true}, "analysis_options": {}}, "BatchCavityPowerScan_VMax": {"meta": {"exp_class_name": "BatchCavityPowerScan", "description": "V Max 位置腔功率谱"}, "experiment_options": {"param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "flows": ["CavityFreqSpectrum", "CavityPowerScan"], "point_mode": "max"}, "analysis_options": {}}, "BatchQubitSpectrumZAmp": {"meta": {"exp_class_name": "BatchQubitSpectrumZAmp", "description": "初测比特二维能谱"}, "experiment_options": {"param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "power_scan_flow": ["CavityFreqSpectrum_BSZ", "QubitSpectrumScanPower"], "max_f01_flow": ["QubitSpectrumVMax"], "min_f01_flow": ["QubitSpectrumVMin"], "amp_scan_flow": ["QubitSpectrumZAmp"], "v_min_f01_limit": 3750, "v_max_f01_limit": 5000, "scan_amp_step": 0.01}, "analysis_options": {}}, "BatchSearchIdlePoint": {"meta": {"exp_class_name": "BatchSearchIdlePoint", "description": "找 idle"}, "experiment_options": {"param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "qs_flows": ["CavityFreqSpectrum_BSI", "QubitSpectrum_BSI"], "idle_flows": ["XpiDetection", "QubitFreqCalibration", "XpiDetection"], "idle_step": 0.01, "idle_points": 20, "pass_to_db": true, "is_qs_divide": true}, "analysis_options": {}}, "BatchReadout": {"meta": {"exp_class_name": "BatchReadout", "description": "找读取"}, "experiment_options": {"param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "flows": ["ReadoutFreqCalibrate_01", "ReadoutPowerCalibrate_01"]}, "analysis_options": {}}, "BatchSearchF12": {"meta": {"exp_class_name": "BatchSearchF12", "description": "F12 搜索"}, "experiment_options": {"param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "flows": ["QubitSpectrumF12", "RabiScanWidthF12", "F12Calibration"]}, "analysis_options": {}}, "BatchXYCrossRabiWidth": {"meta": {"exp_class_name": "BatchXYCrossRabiWidth", "description": "xy 串扰"}, "experiment_options": {"param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "cali_freq_flag": true, "cali_freq_flows": ["QubitFreqCalibration_XC"], "strength_flows": ["RabiScanWidth_XC"], "xy_cross_flows": ["XYCrossRabiWidthOnce_XC"]}, "analysis_options": {}}, "BatchXZTiming": {"meta": {"exp_class_name": "BatchXZTiming", "description": "全局寻找 XYZTiming "}, "experiment_options": {"param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "z_amp_list": [0.2, -0.2, 0.25, -0.25], "exp_retry": 0, "quality_block_exp": ["SingleShot_XT", "SingleShot_XT1"], "flows": ["CavityFreqSpectrum_XT", "SingleShot_XT", "QubitFreqCalibration_XT", "XpiDetection_XT", "SingleShot_XT1"], "timing_flows": ["XYZTimingComposite"], "save_db": true}, "analysis_options": {}}, "BatchACT1Spectrum": {"meta": {"exp_class_name": "BatchACT1Spectrum", "description": "AC T1 谱搜索 "}, "experiment_options": {"param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "max_cali_flows": [], "ac_spec_flows": ["ACSpectrum"], "t1_spec_flows": [], "gap_points": 30, "band_points": 5, "t1_step": 20, "t1_points": 20}, "analysis_options": {}}, "BatchRBSpectrum": {"meta": {"exp_class_name": "BatchRBSpectrum", "description": "RB 谱搜索 "}, "experiment_options": {"max_batch_count": 1, "param_path": "D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\app\\automation\\conf\\experiment.json", "flows": ["SingleShot_RBS", "QubitFreqCalibration_RBS_0", "XpiDetection_RBS", "QubitFreqCalibration_RBS_1", "RabiScanAmp_RBS", "SingleShot_RBS", "DetuneCalibration_RBS", "AmpComposite_RBS", "SingleShot_RBS", "T1", "T2Ramsey", "SpinEcho", "RBSingle"], "quality_block_exp": ["DetuneCalibration_RBS", "T1", "T2Ramsey", "SpinEcho", "RBSingle"]}, "analysis_options": {}}}}