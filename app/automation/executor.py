# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/12
# __author:       <PERSON><PERSON><PERSON>

from collections import OrderedDict
from copy import deepcopy

import app.automation.nodes as batch_nodes
from app.tool.utils import read_json_file, write_json_file
from pyQCat.errors import ExperimentOptionsError
from pyQCat.executor import Backend
from pyQCat.log import pyqlog
from pyQCat.structures import Options, QDict


class AutomationExecutor:
    def __init__(self, backend: Backend):
        self.backend = backend
        self._batch_config = {}
        self._batch_records = OrderedDict()
        self._experiment_options = self._default_experiment_options()
        self._run_options = self._default_run_options()

    @property
    def experiment_options(self):
        return self._experiment_options

    @property
    def run_options(self):
        return self._run_options

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default kwarg options for experiment."""
        options = Options()

        options.set_validator("config", str)
        options.config = ""

        options.set_validator("nodes", list)
        options.nodes = []

        options.set_validator("_physical_unit_map", QDict)
        options.physical_unit_map = QDict(qubit=[], coupler=[], qubit_pair=[], bus=[])

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default kwarg options for experiment."""
        options = Options()
        options.process_data = QDict()
        options.running_physical_unit_map = QDict()
        return options

    def set_experiment_options(self, **fields):
        for field in fields:
            if field not in self._experiment_options:
                raise ExperimentOptionsError(
                    self._label,
                    key=field,
                    value=fields.get(field),
                    msg=f"field {field} option must be defined in advance!",
                )
        self._experiment_options.update(**fields)

    def _check_options(self):
        self._batch_config = read_json_file(self.experiment_options.config)
        self.run_options.running_physical_unit_map = deepcopy(
            self.experiment_options.physical_unit_map
        )

    def _build_batch(self, node: str):
        batch_class = getattr(
            batch_nodes, self._batch_config["nodes"][node]["meta"]["exp_class_name"]
        )
        batch_obj = batch_class(self.backend)
        batch_obj.set_experiment_options(
            **self._batch_config["nodes"][node]["experiment_options"]
        )
        batch_obj.set_analysis_options(
            **self._batch_config["nodes"][node].get("analysis_options", {})
        )
        return batch_obj

    def _run_node(self, node: str):
        if node not in self._batch_config["nodes"]:
            pyqlog.error(f"No find node: {node}")
        else:
            batch = self._build_batch(node)
            print(f"{batch} start ...")
            batch.bind_current_node(
                self.run_options.running_physical_unit_map,
                self.run_options.process_data,
            )
            batch.run()
            # batch.bind_pass_units(batch.experiment_options.physical_units)
            batch.callback_next_node(
                self.run_options.running_physical_unit_map,
                self.run_options.process_data,
            )
            self._batch_records[str(batch.record_meta)] = (
                batch.record_meta.execute_meta.result.to_dict()
            )
            print(f"{batch} end ...")
            write_json_file("record.json", self._batch_records)

    def run(self):
        self._check_options()

        for node in self.experiment_options.nodes:
            self._run_node(node)
