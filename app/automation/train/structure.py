# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/09
# __author:       <PERSON><PERSON><PERSON>

from enum import Enum
from dataclasses import dataclass, field
from typing import Any, List

from pyQCat.structures import ExperimentData
from pyQCat.experiments.base_experiment import BaseExperiment


class AutoNodeEnum(str, Enum):
    """定义自动化节点名称

    Args:
        str (_type_): _description_
        Enum (_type_): _description_
    """

    SEARCH_IDLE_POINT = "search_idle_point"


class ActionTypeEnum(str, Enum):
    """_summary_

    Args:
        Enum (_type_): _description_

        - OPTIONS: 表示需要修改实验选项重新进行实验
        - UNIT: 表示需要修改比特参数重新进行实验
        - FAIL: 表示当前比特迭代失败, 无法找到有效策略，流程终止
        - SUC: 表示当前比特迭代成功, 流程终止
        - NEXT: 表示前置实验结果正常, 流转至下一实验
    """

    OPTIONS = "options"
    UNIT = "unit"
    FAIL = "fail"
    SUC = "suc"
    NEXT = "next"


@dataclass
class ExperimentResult:
    """实验执行结果

    Args:
        unit: 工作单元
        experiment: 实验名称
        options: 实验选项
        unit_map: 实验比特环境
        quality: monster 分析质量
    """

    unit: str
    experiment: str
    options: dict
    unit_map: dict
    experiment_data: ExperimentData
    quality: dict

    @classmethod
    def from_experiment(cls, exp: BaseExperiment):
        return cls(
            unit=exp.record_meta.execute_meta.physical_units,
            experiment=exp.record_meta.execute_meta.exp_class,
            options=exp.record_meta.context_meta.experiment_options,
            unit_map=exp.record_meta.context_meta.unit_map,
            experiment_data=exp.experiment_data,
            quality=exp.record_meta.execute_meta.quality,
        )


@dataclass
class Input:
    """输入

    Args:
        name: 自动化节点名称
        physical_units: 执行的比特
        data: 给入一系列实验的执行结果, 首次输入 data 为空列表
    """

    name: AutoNodeEnum
    physical_units: list = field(default_factory=list)
    data: List[ExperimentResult] = field(default_factory=list)


@dataclass
class Action:
    """反馈策略

    Args:
        unit: 工作单元
        experiment: 实验名称
        type: 策略类型，详见 ActionTypeEnum
        key: 策略中需要修改的键
        value: 策略中需要修改的值
    """

    unit: str
    experiment: str
    type: str
    key: str = ""
    value: Any = None

    def __repr__(self):
        return f"{self.experiment}<{self.unit}0>"
