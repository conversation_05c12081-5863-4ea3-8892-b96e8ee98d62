# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/09
# __author:       <PERSON><PERSON><PERSON>


from app.automation.train.structure import Input, Action, List


def ask_actions(input: Input) -> List[Action]:
    """_summary_

    Args:
        input (Input): _description_

    Returns:
        List[Action]: _description_
    """
    # todo
    return []
