# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/09
# __author:       <PERSON><PERSON><PERSON>


import asyncio
from typing import List, Union

from app.automation.train.action_model.interface import ask_actions
from app.automation.train.structure import (
    Action,
    ActionTypeEnum,
    AutoNodeEnum,
    ExperimentResult,
    Input,
)
from pyQCat.experiments.batch_experiment import (
    BaseExperiment,
    BatchExperiment,
    ParallelExperiment,
    check_and_start_merge_service,
    check_process_broken_state,
)
from pyQCat.log import pyqlog


class AutoExperiment(BatchExperiment):
    node = None

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.unit_exp_map = {}
        return options

    def _iterate(self, physical_units: List[str]):
        actions = ask_actions(Input(name=self.node, physical_units=physical_units))
        all_pass_units = []
        all_fail_units = []
        while True:
            results, pass_units, fail_units = self._run_actions(actions)
            if not results:
                break
            all_pass_units.extend(pass_units)
            all_fail_units.extend(fail_units)
            actions = ask_actions(
                Input(name=AutoNodeEnum.SEARCH_IDLE_POINT, data=results)
            )
        return all_pass_units, all_fail_units

    def _run_actions(self, actions: List[Action]):
        pass_units = []
        fail_units = []
        experiment_results = []
        for action in actions:
            if action.type == ActionTypeEnum.FAIL:
                pyqlog.warning(f"{action} fail ...")
                fail_units.append(action.unit)
                continue
            elif action.type == ActionTypeEnum.SUC:
                pyqlog.info(f"{action} suc ...")
                pass_units.append(action.unit)
                self._refresh_context_from_analysis(
                    self.run_options.unit_exp_map.pop(action.unit).analysis
                )
                continue
            elif action.type == ActionTypeEnum.UNIT:
                pyqlog.info(
                    f"{action} need change unit field `{action.key}`<{action.value}>"
                )
                unit_obj = self.backend.chip_data.get_physical_unit(action.unit)
                setattr(unit_obj, action.key, action.value)
            elif action.type == ActionTypeEnum.OPTIONS:
                pyqlog.info(
                    f"{action} need change experiment options `{action.key}`<{action.value}>"
                )
                self.change_parallel_exec_exp_options(
                    exp_name=action.experiment,
                    unit=action.unit,
                    options={action.key, action.value},
                )
            elif action.type == ActionTypeEnum.NEXT:
                pyqlog.info(f"{action} next ...")
                self._refresh_context_from_analysis(
                    self.run_options.unit_exp_map.pop(action.unit).analysis
                )
            else:
                raise ValueError(f"No support {action} | {action.type}")

            if action.experiment and action.unit:
                results, cur_fail_units = self._run_simple_exp(
                    exp_name=action.experiment, physical_units=actions.unit
                )
                experiment_results.extend(results)
                fail_units.extend(cur_fail_units)
        return (
            experiment_results,
            pass_units,
            fail_units,
        )

    def _run_simple_exp(
        self,
        exp_name: str,
        physical_units: Union[List[str], str],
    ):
        results = []
        fail_units = []

        if isinstance(physical_units, str):
            physical_units = [physical_units]

        exp, parallel_units, _ = self.params_manager.get(exp_name).to_exp(
            self.context_manager,
            physical_unit=physical_units,
            use_simulator=self.experiment_options.use_simulator,
        )

        if isinstance(exp, ParallelExperiment):
            exp.topology = self.backend.chip_data.topology
            exp.allocation_options = self.backend.system.parallel_divide
            check_and_start_merge_service(self.backend.context_manager.config)
            for index, child_exp in enumerate(exp.experiments):
                unit = parallel_units[index]
                self._config_file_path(unit, child_exp)
        elif isinstance(exp, BaseExperiment):
            unit = (
                parallel_units[0]
                if isinstance(parallel_units, list)
                else parallel_units
            )
            self._config_file_path(unit, exp)
        else:
            error, exp = exp, None
            pyqlog.error(f"{exp_name} error: {error}")
            fail_units.extend(physical_units)

        if exp:
            exp.record_meta.execute_meta.batch_id = self.record_id
            asyncio.run(exp.run_experiment())

            # Asynchronous process pool broken detection, and re pull the process pool
            if check_process_broken_state() is True:
                return self._run_simple_exp(exp_name, physical_units)

            if isinstance(exp, ParallelExperiment):
                for ce in exp.experiments:
                    unit = ce.record_meta.execute_meta.physical_units
                    if ce.status.is_done():
                        self.run_options.unit_exp_map[unit] = exp
                        results.append(ExperimentResult.from_experiment(ce))
                    else:
                        fail_units.append(unit)
            elif exp.status.is_done():
                results.append(ExperimentResult.from_experiment(exp))
            else:
                fail_units.append(exp.record_meta.execute_meta.physical_units)

        return results, fail_units
