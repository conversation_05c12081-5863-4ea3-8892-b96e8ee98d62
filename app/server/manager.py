# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/07
# __author:       <PERSON><PERSON><PERSON>


import asyncio

import psutil
import zmq
from loguru import logger
from zmq.asyncio import Con<PERSON>, Poller, Socket

from app.config import SYSTEM_PARA
from app.server.conf import (
    MS_ADDR,
    NAGA_API_ADDR,
    MonsterServerState,
    TaskExecuteState,
    TaskType,
    decode_msg,
    encode_msg,
)
from app.server.task import MonsterTask
from pyQCat.config import PyqcatConfig
from pyQCat.data_transfer.util import build_ipc_file, generate_unique_identity
from pyQCat.types import MonsterServerOp


class MonsterServer:
    def __init__(self, role: str = ""):
        self.api_addr = NAGA_API_ADDR
        self.context = Context.instance()
        self.socket: Socket = self.context.socket(zmq.DEALER)
        self.identity = generate_unique_identity(f"MS-{role}")
        self.socket.setsockopt(zmq.LINGER, 0)
        self.socket.setsockopt(zmq.IDENTITY, self.identity)
        self.socket.connect(self.api_addr)

        self.execute_socket = Context.instance().socket(zmq.DEALER)
        self.execute_socket.setsockopt(zmq.IDENTITY, self.identity)
        self.execute_socket.connect(build_ipc_file(MS_ADDR))

        self.poller = Poller()
        self.poller.register(self.socket, zmq.POLLIN)
        self.poller.register(self.execute_socket, zmq.POLLIN)

        self._recv_link_map = {
            MonsterServerOp.START_TASK.value: self._start_task,
            MonsterServerOp.STOP_TASK.value: self._stop_task,
            MonsterServerOp.CLOSE_MS.value: self._close_server,
            MonsterServerOp.ASK_ENV.value: self._ask_env,
            MonsterServerOp.DATA_ANALYSIS.value: self._data_analysis,
        }

        self._next_heartbeat = None
        self._state = MonsterServerState.INIT

        self._process_map = {}

        self._config = PyqcatConfig(SYSTEM_PARA.backend.config_file, init_log=False)
        self._context_info = encode_msg(
            dict(
                username=SYSTEM_PARA.backend.username,
                sample=self._config.sample,
                env=self._config.env_name,
                point_label=self._config.point_label,
            )
        )

    def __repr__(self):
        return f"MonsterServer-{self.identity}"

    async def _sync_ms_heart(self):
        if self._process_map:
            self._state = MonsterServerState.RUNNING
        else:
            self._state = MonsterServerState.IDLE
        await self.socket.send_multipart(
            [MonsterServerOp.HEART_MS.value, self._state.value]
        )

    async def ms_heart(self, internal: float = 5.0):
        while self._state != MonsterServerState.CLOSE:
            logger.debug("Monster server send heart to api")
            await self._sync_ms_heart()
            await asyncio.sleep(internal)

    async def _ask_env(self, *buffer):
        task_ids = list(self._process_map.keys())
        task_data = {}
        for tid in task_ids:
            task = self._process_map.get(tid)
            if task:
                task_data[tid] = task.to_dict()
        await self.socket.send_multipart(
            [MonsterServerOp.ASK_ENV.value, self._context_info, encode_msg(task_data)]
        )

    async def _start_task(self, task: bytes):
        task_data = decode_msg(task, parse_json=True)
        logger.info(f"Receive monitor task | {task_data}")
        if task_data["tid"] in self._process_map:
            logger.warning(f"Task {self._process_map[task_data['tid']]} is running!")
        else:
            task = MonsterTask(**task_data)
            logger.info(f"Prepare start task `{task}`")
            task.run()
            self._process_map[task_data["tid"]] = task
            await self._sync_ms_heart()

    async def _stop_task(self, task: bytes):
        task_id = task.decode("utf-8")
        task = self._process_map.get(task_id, None)
        if task and task.process:
            logger.info(f"Prepare stop task {task}")
            task.process.terminate()
            task.wait_terminal()
            logger.info(f"{task} stop success!")
            await self._sync_ms_heart()

    async def _close_server(self):
        self._state = MonsterServerState.CLOSE

    async def _data_analysis(self, msg_id: bytes, task: bytes):
        logger.info("receive data analysis task")
        await self.execute_socket.send_multipart([msg_id, TaskType.ANA.value, task])
        await self.socket.send_multipart(
            [
                MonsterServerOp.DATA_ANALYSIS.value,
                msg_id,
                TaskExecuteState.RECEIVE.value,
                b"",
            ]
        )

    async def listening_task(self):
        while self._state != MonsterServerState.CLOSE:
            res_ = dict(await self.poller.poll(100))

            if self.socket in res_:
                code, *buffer = await self.socket.recv_multipart()
                if code in self._recv_link_map:
                    await self._recv_link_map[code](*buffer)

            if self.execute_socket in res_:
                buffer = await self.execute_socket.recv_multipart()
                logger.info(f"receive executor msg {buffer}")
                await self.socket.send_multipart(
                    [MonsterServerOp.DATA_ANALYSIS.value, *buffer]
                )

    async def listening_task_heart(self):
        while self._state != MonsterServerState.CLOSE:
            task_names = list(self._process_map.keys())
            for name in task_names:
                task = self._process_map.get(name)
                if task and task.process:
                    if not task.process.is_alive():
                        await self.socket.send_multipart(
                            [MonsterServerOp.FINISH_TS.value, task.tid.encode("utf-8")]
                        )
                        logger.warning(f"Task {name} is finish ...")
                        self._process_map.pop(name)
                    else:
                        child_process = psutil.Process(task.process.pid)
                        memory_info = child_process.memory_info()
                        logger.info(
                            f"{task.tid} RSS: {memory_info.rss / 1024 / 1024:.2f} MB | VMS: {memory_info.vms / 1024 / 1024:.2f} MB"
                        )

            await asyncio.sleep(5)

    async def run(self):
        try:
            logger.info(f"{self} start ...")
            await asyncio.gather(
                self.ms_heart(),
                self.listening_task(),
                self.listening_task_heart(),
            )
            self._state = MonsterServerState.IDLE
        except Exception:
            import traceback

            error_msg = traceback.format_exc()
            logger.error(f"{self} crash. {error_msg}")
        finally:
            self._state = MonsterServerState.CLOSE
            self.socket.close()
            logger.info(f"{self} close ...")
