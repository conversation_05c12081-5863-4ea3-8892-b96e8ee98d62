# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/07
# __author:       <PERSON><PERSON><PERSON>

import multiprocessing
import os
from datetime import datetime
from enum import Enum
from typing import List

from loguru import logger

from app.config import SYSTEM_PARA, config_manager, init_backend
from app.server.conf import COURIER_PUB_ADDR, NAGA_API_ADDR
from pyQCat.concurrent.batch_heart import RecordThread
from pyQCat.concurrent.concurrent import CONCURRENT_CACHE, cleanup_resources
from pyQCat.config import LogFlag, PyqcatConfig
from pyQCat.experiments.batch import BatchStabilityV2
from pyQCat.log import pyqlog
from pyQCat.types import LogFormat


class StandardMonitorType(str, Enum):
    PLM = "PopulationLossMonitor"
    CTM = "CoherentTimeMonitor"
    RM = "ReadoutMonitor"
    CGM = "CZGateMonitor"


def monitor_task(name, flows, tid, physical_units, loop, interval, auto_refresh):
    config_manager()
    config = PyqcatConfig(filename=SYSTEM_PARA.backend.config_file, init_log=False)
    LogFlag().flag += 1
    logger.remove()
    log_path = f"{config.log_path}/MS/Task/{name}/{tid}"
    log_path = os.path.join(
        log_path, f"monster-{datetime.now().strftime('%Y-%m-%d')}.log"
    )
    pyqlog.add(log_path, format=LogFormat.detail, level="DEBUG", rotation="00:00")
    pyqlog.info(f"Monster Log save in {log_path}")

    try:
        param_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "batch_test",
            "json_data",
            "simulator.json",
        )
        CONCURRENT_CACHE["open_monitor"] = True

        topic = f"{config.sample}_|_{config.env_name}"
        courier_addr = COURIER_PUB_ADDR if auto_refresh is True else ""
        RecordThread(
            task_id=tid,
            api_addr=NAGA_API_ADDR,
            courier_addr=courier_addr,
            topic=topic,
        ).start()
        backend = init_backend()
        batch = BatchStabilityV2(backend, task_id=tid)
        batch._label = name

        if name == StandardMonitorType.CGM:
            filter_params = {"XEBMultiple": ["f_xeb", "r2", "f_spb_purity"]}
            record_experiment_data = False
        else:
            filter_params = {}
            record_experiment_data = True

        batch.set_experiment_options(
            # Experiment options available for all batch experiments
            param_path=param_path,
            flows=flows,
            filter_params=filter_params,
            exp_retry=0,
            refresh_context=False,
            record_experiment_data=record_experiment_data,
            physical_units=physical_units,
            loops=loop,
            interval=interval,
            is_statistics=False,
            record_length_limit=500,
        )
        # batch.set_analysis_options(interval_of_analysis=1)

        batch.run()
    except Exception as e:
        import traceback

        logger.error(f"{flows} error: {e} \n{traceback.format_exc()}")
    finally:
        cleanup_resources(close_transfer=True)


class MonsterTask:
    def __init__(
        self,
        name: str,
        tid: str,
        units: List[str],
        loop: int,
        interval: int,
        auto_refresh: bool = False,
    ):
        self.name = name
        self.tid = tid
        self.physical_units = units
        self.loop = loop
        self.interval = interval
        self.auto_refresh = auto_refresh
        self.process = None

    def __repr__(self):
        return f"{self.name} | {self.tid} | Loop-{self.loop} | Interval-{self.interval}"

    def to_dict(self):
        return dict(
            name=self.name,
            physical_units=self.physical_units,
            loop=self.loop,
            interval=self.interval,
            task_id=self.tid,
        )

    def _build_monster_process(self):
        task_map = {
            StandardMonitorType.PLM: ["PopulationLossOnce"],
            StandardMonitorType.CTM: ["T1", "Ramsey"],
            StandardMonitorType.RM: ["SingleShot"],
            StandardMonitorType.CGM: ["XEBMultiple"],
        }
        if self.name in task_map:
            self.process = multiprocessing.Process(
                target=monitor_task,
                args=(
                    self.name,
                    task_map[self.name],
                    self.tid,
                    self.physical_units,
                    self.loop,
                    self.interval,
                    self.auto_refresh,
                ),
            )
            self.process.start()
        else:
            logger.error(f"No find task {self.name}")

    def run(self):
        self._build_monster_process()

    def wait_terminal(self):
        self.process.join(timeout=5)
        if self.process.is_alive():
            logger.warning("Subprocess did not exit, sending SIGKILL.")
            self.process.kill()
            self.process.join()
