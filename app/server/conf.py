# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/07
# __author:       <PERSON><PERSON><PERSON>

import json
from enum import Enum
from typing import Union

NAGA_API_ADDR = "tcp://***********:12125"
COURIER_PUB_ADDR = "tcp://localhost:8089"
MS_ADDR = "monster_server"


def encode_msg(msg: Union[str, dict], mode: str = "utf-8"):
    if isinstance(msg, dict):
        msg = json.dumps(msg)
    return msg.encode(mode)


def decode_msg(msg, mode: str = "utf-8", parse_json: bool = False):
    msg_str = msg.decode(mode)
    if parse_json is True:
        return json.loads(msg_str)
    return msg_str


class MonsterServerState(Enum):
    INIT = b"0x01"
    IDLE = b"0x02"
    RUNNING = b"0x03"
    STOP = b"0x04"
    CLOSE = b"0x05"


class OperateCode(Enum):
    SUC = b"0x01"
    FAIL = b"0x02"


class TaskType(Enum):
    EXP = b"0x01"
    ANA = b"0x02"
    TOOL = b"0x03"


class TaskExecuteState(Enum):
    FAIL = b"0x01"
    SUC = b"0x02"
    RECEIVE = b"0x03"
    RUNNING = b"0x04"
    PROCESS_BAR = b"0x05"


class MSRole(str, Enum):
    ROOT = "root"
    USER = "user"
