# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 20)21-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/11/16
# __author:       <PERSON><PERSON><PERSON>

import logging
import os
import sys
from typing import Union

import zmq
from loguru import logger
from zmq.log.handlers import PUBHandler

TRANSFER_LOG_ADDR = "ipc://log"
VISAGE_EXIST = False


class LogLevel:
    """"""
    # DISPATCH = "DISPATCH"
    # TRANSFER = "TRANSFER"
    # SYSTEM = "SYSTEM"


class Logger:
    def __init__(
        self,
        process_name: str,
        log_path: str,
        record_format: str = "detail",
        record_level: Union[int, str] = "DEBUG",
        process_safe: bool = False,
        print_terminal: bool = True,
        print_level: Union[int, str] = "INFO",
    ):
        self._format = dict(
            simple="<yellow>{time:YYYY-MM-DD HH:mm:ss.SSS}</yellow> | "
            "<level>{message}</level>",
            detail="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: ^8}</level> | "
            "<cyan>{name}</cyan>:"
            "<cyan>{function}</cyan>:"
            "<cyan>{line}</cyan> - <level>{message}</level>",
        )
        logger.remove(handler_id=None)
        self._record_format = record_format
        self._record_level = record_level
        self._log_path = os.path.join(log_path, "MS", "Server", str(process_name) + "-{time:YYYY-MM-DD}.log")
        self._process_safe = process_safe
        self._print_terminal = print_terminal
        self._print_level = print_level
        self._log_url = TRANSFER_LOG_ADDR + str(process_name)
        self.set_log_handler()

    @property
    def log_path(self):
        return self._log_path

    @staticmethod
    def set_log_level():
        """"""
        # logger.level(LogLevel.DISPATCH, no=25, color="<cyan><bold>", icon="🐍")
        # logger.level(LogLevel.TRANSFER, no=26, color="<blue><bold>", icon="🐍")
        # logger.level(LogLevel.SYSTEM, no=27, color="<light-green><bold>", icon="🐍")

    def save_log(self):
        if self._print_terminal is True:
            logger.add(
                sys.stdout,
                format=self._format["simple"],
                level=self._print_level,
                colorize=True,
                enqueue=self._process_safe,
            )
        logger.add(
            self._log_path,
            format=self._format[self._record_format],
            level=self._record_level,
            rotation="00:00",
            enqueue=self._process_safe,
        )

    def set_log_handler(self):
        if VISAGE_EXIST:
            print(f"handle transfer log to visage, {self._log_url}")
            ctx = zmq.Context.instance()
            socket = ctx.socket(zmq.PUB)
            socket.bind(self._log_url)
            pub_handler = PUBHandler(socket, ctx)
            pub_handler.formatters = {
                logging.DEBUG: logging.Formatter("%(message)s\n"),
                logging.INFO: logging.Formatter("%(message)s\n"),
                logging.WARN: logging.Formatter("%(message)s\n"),
                logging.ERROR: logging.Formatter("%(message)s\n"),
                logging.CRITICAL: logging.Formatter("%(message)s\n"),
                25: logging.Formatter("%(message)s\n"),
                26: logging.Formatter("%(message)s\n"),
                27: logging.Formatter("%(message)s\n"),
            }
            logger.add(
                pub_handler,
                format=self._format["simple"],
                level=10,
                colorize=True,
            )
