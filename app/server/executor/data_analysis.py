# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/25
# __author:       <PERSON><PERSON><PERSON>

import asyncio
import os
import time
from typing import Any, Dict, List, Optional
from uuid import uuid4

from loguru import logger

import pyQCat.analysis as ana_libs
from pyQCat import __version__
from pyQCat.concurrent.concurrent import ConcurrentCR
from pyQCat.concurrent.worker.analysis_interface import run_analysis_process
from pyQCat.structures import Experiment<PERSON><PERSON>, QDict
from pyQCat.tools.s3storage import S3Storage
from pyQCat.tools.serialization import from_pick_binary_data


class BatchOfflineAnalysis:
    """Container for data analysis metadata with batch result.

    Offline analysis of batch test results, record the result graph to S3.

    Attributes:
        batch_id: Unique identifier for the analysis batch.
        env_id: Environment identifier where analysis was performed.
        exp_results: List of Experimental Data Binary (EPD) identifiers.
        version: Version string of the analysis format.
    """

    def __init__(
        self,
        batch_id: str,
        env_id: str,
        exp_results: List[str],
        version: str,
        date: str,
    ) -> None:
        """Initializes a new DataAnalysis instance.

        Args:
            batch_id: Unique identifier for the analysis batch.
            env_id: Environment identifier (e.g., 'PROD', 'STAGING').
            exp_results: List of experimental data IDs.
            version: Analysis format version string.

        Raises:
            ValueError: If batch_id or env_id are empty strings.
        """
        if not env_id:
            raise ValueError("batch_id and env_id cannot be empty")

        self._batch_id = batch_id
        self._env_id = env_id
        self._exp_results = exp_results
        self._version = version
        self._date = date

        self._s3_database = S3Storage()
        self._bucket = "data"

        # process bar
        self.process_bar = {"state": ""}
        self._total = 0
        self._count = 0

    @property
    def batch_id(self) -> str:
        """Returns the batch identifier."""
        return self._batch_id

    @property
    def env_id(self) -> str:
        """Returns the environment identifier."""
        return self._env_id

    @property
    def exp_results(self) -> List[Dict]:
        """Returns the list of EPD identifiers."""
        return self._exp_results

    @property
    def version(self) -> str:
        """Returns the analysis format version."""
        return self._version

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "BatchOfflineAnalysis":
        """Creates instance from dictionary data.

        Args:
            data: Dictionary containing:
                - batch_id: String identifier
                - env_id: String environment identifier
                - exp_results: List of string EPD IDs
                - version: String version identifier

        Returns:
            New DataAnalysis instance.

        Raises:
            TypeError: If input is not a dictionary.
            ValueError: If required keys are missing or invalid.
        """
        if not isinstance(data, dict):
            raise TypeError(f"Expected dict, got {type(data).__name__}")

        try:
            return cls(
                batch_id=str(data["batch_id"]),
                env_id=str(data["env_id"]),
                exp_results=data["exp_results"],
                version=str(data["version"]),
                date=data["date"],
            )
        except KeyError as e:
            raise ValueError(f"Missing required key: {e}") from None
        except (TypeError, ValueError) as e:
            raise ValueError(f"Invalid data format: {e}") from None

    def standard_result_data(self) -> Dict[str, Any]:
        """Generates standardized result data structure.

        Returns:
            Dictionary containing:
            - batch_id: String identifier
            - env_id: String environment identifier
            - exp_results: List of string EPD IDs
            - analysis_result: List of analysis results
            - version: String version identifier
            - warning: Version mismatch warning if applicable
        """
        return {
            "batch_id": self.batch_id,
            "env_id": self.env_id,
            "exp_results": self.exp_results,
            "date": self._date,
            "version": __version__,
            "warning": ""
            if __version__ == self.version
            else f"version mismatch, use `{__version__}` analysis",
        }

    def __repr__(self) -> str:
        """Generates official string representation.

        Returns:
            String representation that can be used with eval().
        """
        return (
            f"DataAnalysis(batch_id='{self._batch_id}', "
            f"env_id='{self._env_id}', "
            f"exp_results({len(self._exp_results)}), "
            f"version='{self._version}')"
        )

    async def run(self) -> None:
        """Executes analysis for all EPD IDs."""
        start = time.perf_counter()
        self._total = len(self.exp_results)
        tasks = []
        for index, exp in enumerate(self.exp_results):
            logger.info(f"Experiment data transfer {index + 1}/{self._total}")
            task = asyncio.create_task(
                self._analysis_from_exp(
                    exp=exp, process_bar=f"{index + 1}/{self._total}"
                )
            )
            tasks.append(task)
        await asyncio.gather(*tasks)
        end = time.perf_counter()
        logger.warning(f"Cost time: {end - start} s")

    async def _analysis_from_exp(self, exp: Dict, process_bar: str):
        """Performs analysis for single EPD ID."""
        try:
            origin_png_results = exp.get("origin_png_results")
            epd_id = exp.get("epd_id")
            epd_path = "/".join([self.env_id, "epd", epd_id])
            result, transfer_mode = await ConcurrentCR().run_concurrent_job(
                analysis_and_save_to_s3, epd_path, self._date, self._bucket, origin_png_results
            )
            exp.update(result)
        except Exception:
            import traceback

            exp["message"] = str(traceback.format_exc())
        finally:
            self._count += 1
            self.process_bar["state"] = f"{self._count}/{self._total}"
            logger.info(
                f"{transfer_mode} | {self.process_bar['state']} result | {exp['message'] or exp['png_ids']}"
            )


def analysis_and_save_to_s3(epd_path: str, date: str, bucket: str, origin_png_results: Optional[str] = None):
    result_data = dict(png_ids=[], message="")
    s3_database = S3Storage()
    transfer_mode = ""

    def _save_figure_to_s3(fig, count_limit: int = 2) -> tuple[bool, str]:
        """Saves figure to S3 storage.

        Args:
            fig: Figure object to save.
            count_limit: Retry attempts remaining.

        Returns:
            Tuple containing:
            - success: Boolean indicating save status
            - message: Success ID or error message
        """
        nonlocal transfer_mode
        error_msg = ""
        png_id = f"{uuid4()}.png"
        png_path = "/".join(["hot_data", date, png_id])
        count = 0

        while count < count_limit:
            try:
                logger.debug(f"save {png_path} to s3")
                if isinstance(fig, str):
                    s3_database.upload_to_minio(
                        bucket,
                        png_path,
                        fig,
                    )
                    transfer_mode = "local file"
                else:
                    s3_database.put_figure(bucket, png_path, fig)
                    transfer_mode = "offline analysis"
                return True, png_id
            except Exception as e:
                import traceback

                error_msg = f"S3 save error | {e} | {traceback.format_exc()}"
                count += 1
        logger.error(error_msg)
        return False, error_msg

    try:
        if check_picture_is_exist(origin_png_results):
            picture_results = origin_png_results
        else:
            epd_data = s3_database.get_object(bucket, epd_path)
            experiment_data: ExperimentData = from_pick_binary_data(epd_data)
            analysis_options = experiment_data.metadata.process_meta.get(
                "analysis_options", QDict()
            )
            ana_cls_name = experiment_data.metadata.process_meta["ana_class_name"]
            ana_cls = getattr(ana_libs, ana_cls_name)
            ana_obj = run_analysis_process(
                analysis_class=ana_cls,
                analysis_options=analysis_options,
                experiment_data=experiment_data,
            )
            picture_results = ana_obj.drawer.figure
            
        if picture_results:
            figure_list = (
                picture_results
                if isinstance(picture_results, list)
                else [picture_results]
            )
            for fig in figure_list:
                is_save, result = _save_figure_to_s3(fig)
                if is_save is True:
                    result_data["png_ids"].append(result)
                else:
                    result_data["message"] = result
        else:
            result_data["message"] = "no figures generated"
    except Exception:
        import traceback

        result_data["message"] = str(traceback.format_exc())
    finally:
        return result_data, transfer_mode


def check_picture_is_exist(picture_paths: List[str]) -> bool:
    if not picture_paths:
        return False

    for pp in picture_paths:
        if not os.path.exists(pp):
            return False

    return True


def experiment_offline_analysis(data: dict, extra: dict):
    logger.debug(f"start tackle analysis task {data['batch_id']}")
    analysis = BatchOfflineAnalysis.from_dict(data)
    analysis.process_bar = extra
    analysis.process_bar["state"] = ""
    asyncio.run(analysis.run())
    logger.debug(f"end tackle analysis task {data['batch_id']}")
    return analysis.standard_result_data()
