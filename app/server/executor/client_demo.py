# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/27
# __author:       <PERSON><PERSON><PERSON>

import zmq
from zmq import Context, Socket

from app.server.conf import MS_ADDR, TaskType, encode_msg
from pyQCat.data_transfer.util import build_ipc_file, generate_unique_identity


class ExecutorClient:
    def __init__(self):
        self.socket: Socket = Context.instance().socket(zmq.DEALER)
        self.identity = generate_unique_identity("Client-Executor")
        self.socket.setsockopt(zmq.LINGER, 0)
        self.socket.setsockopt(zmq.IDENTITY, self.identity)
        self.socket.connect(build_ipc_file(MS_ADDR))

    def send_task(self, task: dict):
        self.socket.send_multipart(
            [
                encode_msg("67e6459359683a4fe3879a4b"),
                TaskType.ANA.value,
                encode_msg(task),
            ]
        )
        result = self.socket.recv_multipart()
        print(result)


if __name__ == "__main__":
    client = ExecutorClient()
    client.send_task(
        {
            "batch_id": "67e614eb1f04b528adbd7d25",
            "env_id": "677bccb253d8026d1209ad4c",
            "version": "0.23.0",
            "date": "2025-03-28",
            "exp_results": [
                {
                    "exp_id": "67e616970576d21f9bf9c2da",
                    "epd_id": "c41d8b4e-31b2-4cbc-bef8-038602676a17.epd",
                    "png_ids": [],
                    "message": "",
                },
                {
                    "exp_id": "67e616970576d21f9bf9c2da",
                    "epd_id": "c41d8b4e-31b2-4cbc-bef8-038602676a17.epd",
                    "png_ids": [],
                    "message": "",
                },
            ],
        }
    )
