# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/27
# __author:       <PERSON><PERSON><PERSON>

import asyncio
import threading
from typing import Any, Callable, List, Optional

import zmq
from loguru import logger
from zmq.asyncio import Context, Poller, Socket

from app.server.conf import MS_ADDR, TaskExecuteState, TaskType, decode_msg, encode_msg
from app.server.executor.data_analysis import experiment_offline_analysis
from pyQCat.data_transfer.util import build_ipc_file, generate_unique_identity


class Worker(threading.Thread):
    """A thread worker that executes a task and stores the result.

    Attributes:
        result: The output of the task execution (None if not yet completed or failed).
        exception: Any exception raised during task execution (None if successful).
    """

    def __init__(
        self,
        client: bytes,
        msg_id: bytes,
        target: Optional[Callable[..., Any]] = None,
        args: tuple = (),
        kwargs: Optional[dict] = None,
        name: Optional[str] = None,
    ) -> None:
        """Initializes the Worker thread.

        Args:
            target: The callable object to be executed by the worker.
            args: Argument tuple for the target invocation (default: ()).
            kwargs: Keyword arguments for the target invocation (default: None).
            name: Thread name (default: None).
        """
        super().__init__(name=name, daemon=True)
        self._target = target
        self._args = args
        self._kwargs = kwargs or {}
        self.result: Optional[Any] = None
        self.exception: Optional[Exception] = None
        self._completed = threading.Event()
        self.client = client
        self.msg_id = msg_id

        self.is_start = False
        self.thread_state = {}

    def __repr__(self):
        return f"Worker-{self.msg_id}"

    def run(self) -> None:
        """Executes the target function and stores the result or exception."""
        try:
            self.result = self._target(
                *self._args, **self._kwargs, extra=self.thread_state
            )
        except Exception as e:
            logger.error(f"Worker error, {e}")
            self.exception = e
        finally:
            self._completed.set()

    def set_target(self, func: Callable[..., Any]):
        self._target = func

    def join(self, timeout: Optional[float] = None) -> None:
        """Waits for the thread to complete.

        Args:
            timeout: Maximum time to wait in seconds (None for no timeout).
        """
        super().join(timeout=timeout)

    def is_ready(self) -> bool:
        """Checks if the task has completed.

        Returns:
            True if the task has completed (successfully or with error), False otherwise.
        """
        return self._completed.is_set()

    def get_result(self, block: bool = True, timeout: Optional[float] = None) -> Any:
        """Retrieves the task result.

        Args:
            block: If True, blocks until the task completes.
            timeout: Maximum time to wait if blocking (None for no timeout).

        Returns:
            The result of the task execution.

        Raises:
            RuntimeError: If the task is not completed and block=False.
            Exception: Any exception raised by the task (if failed).
        """
        if block and not self._completed.is_set():
            self._completed.wait(timeout=timeout)

        if not self._completed.is_set():
            raise RuntimeError("Task not yet completed")

        if self.exception is not None:
            raise self.exception

        return self.result


class MonsterExecutor:
    def __init__(self):
        self.socket: Socket = Context.instance().socket(zmq.ROUTER)
        self.identity = generate_unique_identity("MS-Executor")
        self.socket.setsockopt(zmq.LINGER, 0)
        self.socket.setsockopt(zmq.IDENTITY, self.identity)
        self.socket.bind(build_ipc_file(MS_ADDR))
        self.poller = Poller()
        self.poller.register(self.socket, zmq.POLLIN)

        self._running = True
        self._task_func_map = {
            TaskType.EXP.value: experiment_offline_analysis,
            TaskType.ANA.value: experiment_offline_analysis,
            TaskType.TOOL.value: experiment_offline_analysis,
        }

        self._worker: List[Worker] = []
        self._worker_limit = 5
        self._alive_worker = 0

    def _task_full_check(self):
        return self._alive_worker < self._worker_limit

    def _check_msg_is_exits(self, msg_id: bytes):
        for worker in self._worker:
            if msg_id == worker.msg_id:
                return worker

    async def _add_worker(self, client: bytes, msg_id: bytes, code: bytes, data: bytes):
        worker = self._check_msg_is_exits(msg_id)
        if worker:
            await self.socket.send_multipart(
                [
                    client,
                    msg_id,
                    TaskExecuteState.RUNNING.value,
                    encode_msg(f"task is running, {worker.thread_state}"),
                ]
            )
        else:
            worker = Worker(
                client=client,
                msg_id=msg_id,
                target=self._task_func_map[code],
                args=(decode_msg(data, parse_json=True),),
            )
            self._worker.append(worker)
            logger.debug(f"build {worker} ...\n{worker._args}")

    async def _handle_message(self):
        while self._running:
            res_ = dict(await self.poller.poll(100))
            if self.socket in res_:
                buffer = await self.socket.recv_multipart()
                await self._add_worker(*buffer)

    async def _monitor_worker(self):
        while self._running:
            remove_worker = []
            for worker in self._worker:
                if not worker.is_start and self._task_full_check():
                    worker.start()
                    worker.is_start = True
                    self._alive_worker += 1
                    logger.debug(f"start {worker} ...")
                    await asyncio.sleep(0.1)

                if worker.exception:
                    await self.socket.send_multipart(
                        [
                            worker.client,
                            worker.msg_id,
                            TaskExecuteState.FAIL.value,
                            encode_msg(str(worker.exception)),
                        ]
                    )
                    remove_worker.append(worker)
                    logger.debug(f"fail end {worker} ...")
                elif worker.result is None:
                    state = worker.thread_state.get("state")
                    if not state:
                        continue
                    count, total = [int(v) for v in state.split("/")]
                    await self.socket.send_multipart(
                        [
                            worker.client,
                            worker.msg_id,
                            TaskExecuteState.PROCESS_BAR.value,
                            encode_msg(dict(count=count, total=total)),
                        ]
                    )
                else:
                    await self.socket.send_multipart(
                        [
                            worker.client,
                            worker.msg_id,
                            TaskExecuteState.SUC.value,
                            encode_msg(worker.result),
                        ]
                    )
                    remove_worker.append(worker)
                    logger.debug(f"suc end {worker} ...")

                for rw in remove_worker:
                    self._worker.remove(rw)
                    self._alive_worker -= 1
                    logger.debug(f"remove {rw} ...")

            await asyncio.sleep(1)

    async def run(self):
        try:
            logger.info(f"{self} start ...")
            await asyncio.gather(self._handle_message(), self._monitor_worker())
        except Exception:
            import traceback

            error_msg = traceback.format_exc()
            logger.error(f"{self} crash. {error_msg}")
        finally:
            for worker in self._worker:
                await self.socket.send_multipart(
                    [
                        worker.client,
                        worker.msg_id,
                        TaskExecuteState.FAIL.value,
                        encode_msg("process crash.."),
                    ]
                )
            self._worker.clear()
            self.socket.close()
            logger.info(f"{self} close ...")


def run():
    from app.config import config_manager
    from app.server.main import _set_up_logger
    from pyQCat.concurrent.concurrent import ConcurrentCR

    config_manager()
    _set_up_logger("Executor")
    ConcurrentCR(link_transfer=False, open_s3=True)
    executor = MonsterExecutor()
    asyncio.run(executor.run())


if __name__ == "__main__":
    run()
