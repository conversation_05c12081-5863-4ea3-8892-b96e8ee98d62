# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/07
# __author:       <PERSON><PERSON><PERSON>

import argparse

import zmq
from bson import ObjectId
from loguru import logger

from app.server.conf import NAGA_API_ADDR, encode_msg
from pyQCat.types import MonsterServerOp


class APIClient:
    def __init__(self):
        self.api_addr = NAGA_API_ADDR
        self.context = zmq.Context.instance()
        self.socket = self.context.socket(zmq.DEALER)
        self.socket.setsockopt(zmq.IDENTITY, b"APIClient")
        self.socket.connect(self.api_addr)

    def start_task(self, name: str, loop: int, interval: int, units: str):
        data = dict(
            tid=str(ObjectId()),
            name=name,
            loop=loop,
            interval=interval,
            units=list(units.split(",")),
        )
        self.socket.send_multipart([MonsterServerOp.START_TASK.value, encode_msg(data)])
        logger.info(f"Register task {name} | {data['tid']}")

    def stop_task(self, tid: str):
        self.socket.send_multipa1rt(
            [MonsterServerOp.STOP_TASK.value, tid.encode("utf-8")]
        )

    def stop_monster_server(self, name: str = "PS"):
        self.socket.send_multipart(
            [MonsterServerOp.CLOSE_MS.value, name.encode("utf-8")]
        )


def main():
    parser = argparse.ArgumentParser(description="Control tasks on the Monster server.")
    parser.add_argument(
        "command",
        choices=["o", "c"],
        help="Command to execute: 'o' for start_task, 'c' for stop_task.",
    )
    parser.add_argument(
        "-t",
        "--task",
        default="PS",
        type=str,
        help="Name of the task to start or stop.",
    )
    parser.add_argument(
        "-l", "--loop", default=5, type=int, help="Number of task cycles."
    )
    parser.add_argument(
        "-i", "--interval", default=5, type=int, help="Task interval time, in seconds."
    )
    parser.add_argument(
        "-u",
        "--units",
        default="q1,q2,q3,q4,q5,q6",
        type=str,
        help="Bit of Task Function.",
    )

    args = parser.parse_args()

    client = APIClient()

    if args.command == "o":
        client.start_task(args.task, args.loop, args.interval, args.units)
    elif args.command == "c":
        client.stop_task(args.task)


if __name__ == "__main__":
    main()
