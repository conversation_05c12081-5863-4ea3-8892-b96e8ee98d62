# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/07
# __author:       <PERSON><PERSON><PERSON>

import asyncio
from datetime import datetime

import zmq
from loguru import logger
from zmq.asyncio import Context, Poller, Socket

from app.server.conf import (
    NAGA_API_ADDR,
    COURIER_PUB_ADDR,
    MonsterServerState,
    decode_msg,
)
from pyQCat.types import MonsterServerOp


class APIServer:
    def __init__(self):
        self.api_addr = NAGA_API_ADDR
        self.context = Context.instance()
        self.socket: Socket = self.context.socket(zmq.ROUTER)
        self.identity = b"APIServer"
        self.socket.setsockopt(zmq.IDENTITY, self.identity)
        self.socket.bind(self.api_addr)
        self.poller = Poller()
        self.poller.register(self.socket, zmq.POLLIN)

        self._recv_link_map = {
            MonsterServerOp.HEART_MS.value: self._heart_ms,
            MonsterServerOp.START_TASK.value: self._start_task,
            MonsterServerOp.STOP_TASK.value: self._stop_task,
            MonsterServerOp.HEART_TS.value: self._heart_ts,
            MonsterServerOp.FINISH_TS.value: self._finish_ts,
            MonsterServerOp.CLOSE_MS.value: self._close_ms,
        }

        self._ms_identity = None
        self._ms_state = MonsterServerState.IDLE
        self._ts_map = {}

    async def _start_task(self, client, task: bytes):
        if self._ms_identity:
            await self.socket.send_multipart(
                [self._ms_identity, MonsterServerOp.START_TASK.value, task]
            )

    async def _stop_task(self, client, task: bytes):
        if self._ms_identity:
            await self.socket.send_multipart(
                [self._ms_identity, MonsterServerOp.STOP_TASK.value, task]
            )

    async def _heart_ms(self, client: bytes, state: bytes):
        self._ms_identity = client
        self._ms_state = MonsterServerState._value2member_map_.get(state)
        logger.info(f"MS {client} state is `{self._ms_state}`")

    async def _heart_ts(self, client: bytes, task_id):
        task_id = decode_msg(task_id)
        logger.info(f"Task {client} `{task_id}` is alive")
        self._ts_map[task_id] = datetime.now()

    async def _finish_ts(self, client: bytes, task_id):
        task_id = decode_msg(task_id)
        logger.info(f"Task {client} `{task_id}` is finish")
        self._ts_map.pop(task_id)

    async def _close_ms(self, client, *buffer):
        if self._ms_identity:
            await self.socket.send_multipart(
                [self._ms_identity, MonsterServerOp.CLOSE_MS.value]
            )

    async def recv_message(self):
        client_id, code, *buffer = await self.socket.recv_multipart()
        if code in self._recv_link_map:
            await self._recv_link_map[code](client_id, *buffer)

    async def listening_ms(self):
        while True:
            res_ = dict(await self.poller.poll(100))
            if self.socket in res_:
                await self.recv_message()

    async def monitor_task(self):
        while True:
            if self._ts_map:
                cur_time = datetime.now()
                keys = list(self._ts_map.keys())
                for tid in keys:
                    if (cur_time - self._ts_map[tid]).total_seconds() > 300:
                        self._ts_map.pop(tid)
                        logger.warning(f"任务 {tid} 心跳断开, 任务退出！")

            await asyncio.sleep(10)

    async def run(self):
        try:
            logger.info(f"{self} start ...")
            await asyncio.gather(
                self.listening_ms(),
                self.monitor_task(),
            )
        except Exception:
            import traceback

            error_msg = traceback.format_exc()
            logger.error(f"{self} crash. {error_msg}")
        finally:
            self.socket.close()


class CourierPubDemo:
    def __init__(self):
        self.courier_addr = COURIER_PUB_ADDR
        self.context = zmq.Context.instance()
        self.socket: Socket = self.context.socket(zmq.PUB)

        self.identity = b"CourierDemo"
        self.socket.setsockopt(zmq.IDENTITY, self.identity)
        self.socket.bind(self.courier_addr)

    def send_update(self):
        import time
        while True:
            self.socket.send_multipart(["Fake-48bit_|_S251".encode("utf-8"), b"0x01"])
            print("发送刷新信号")
            time.sleep(30)


if __name__ == "__main__":
    # server = APIServer()
    # asyncio.run(server.run())

    pub = CourierPubDemo()
    pub.send_update()
