# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/10/19
# __author:       <PERSON><PERSON><PERSON>

import os
import time
from multiprocessing import Process

from app.config import SYSTEM_PARA
from pyQCat.log import pyqlog
from pyQCat.parallel import parallel_utils


def start_alone_parallel_server():
    """Start zmq parallel server."""
    pyqlog.info(f"New Parallel Address: {parallel_utils.PARALLEL_ADDR}")
    p_process = Process(
        target=parallel_utils.run_parallel_server,
        args=(SYSTEM_PARA.backend.config_file,),
        daemon=True,
    )
    p_process.start()
    time.sleep(10.0)


def format_file(path: str):

    def sort_key(_fp: str):
        return os.path.getctime(_fp)

    filelist = []
    for file in os.listdir(path):
        fp = os.path.join(path, file)
        if os.path.isfile(fp) and fp.endswith(".dat"):
            filelist.append(fp)

    filelist = sorted(filelist, key=sort_key)

    for i, of in enumerate(filelist):
        nf = of.replace(".dat", f"-{i}.dat")
        os.rename(of, nf)


if __name__ == "__main__":
    format_file(r"F:\data\SimulatorData\QubitSpectrumZAmp\14.35.50\child\QubitSpectrum")
