from pyQCat.experiments.composite.quantum_circuit_exp import circuit_compile


if __name__ == '__main__':

    circuit = """
        RX q[0],(-1.570796)
        RX q[1],(-1.570796)
        RX q[2],(-1.570796)
        H q[1]
        CZ q[0],q[1]
        RX q[1],(-0.235619)
        CZ q[0],q[1]
        H q[1]
        H q[2]
        CZ q[1],q[2]
        RX q[2],(-0.235619)
        CZ q[1],q[2]
        RX q[0],(-1.570796)
        RX q[1],(-1.570796)
        H q[2]
        U3 q[0],(1.570796,4.712389,1.570796)
        RX q[2],(-1.570796)

        MEASURE q[0],c[0]
        MEASURE q[1],c[1]
        MEASURE q[2],c[2]
    """

    (
        circuit_map,
        qubit_set,
        coupler_set,
        qubit_pair_set,
        measure_qubit_set,
    ) = circuit_compile(circuit)
