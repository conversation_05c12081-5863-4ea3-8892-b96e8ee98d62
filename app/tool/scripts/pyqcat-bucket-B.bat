@echo off

net session >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo.
) else (
    echo Error: Please run this script as an administrator!
    pause
    exit /b 1
)

set WORK_DIR=%~dp0
set APPS=release/0.20.9
set MONSTER=release/b/0.22.5
set INVOKER=naga
set VISAGE=release/0.22.5

echo ****** Start auto build pyqcat bucket ******
cd /d %WORK_DIR%
echo.

echo ****** LOG: delete history directory ... ******
rmdir /s /q pyqcat-apps
rmdir /s /q pyqcat-monster
rmdir /s /q pyqcat-invoker
rmdir /s /q pyqcat-visage
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Cloning pyqcat-apps repository ... ******
git clone https://gitlab.qpanda.cn/spirit/pyqcat-apps.git
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing directory to pyqcat-apps ... ******
cd pyqcat-apps
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Checking out %APPS% branch ... ******
git checkout -b %APPS% origin/%APPS%
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing back to the parent directory ... ******
cd ..
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Cloning pyqcat-invoker repository ... ******
git clone https://gitlab.qpanda.cn/spirit/pyqcat-invoker.git
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing directory to pyqcat-invoker ... ******
cd pyqcat-invoker
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Checking out %INVOKER% branch ... ******
git checkout -b %INVOKER% origin/%INVOKER%
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing back to the parent directory ... ******
cd ..
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Cloning pyqcat-monster repository ... ******
git clone https://gitlab.qpanda.cn/spirit/pyqcat-monster.git
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing directory to pyqcat-monster ... ******
cd pyqcat-monster
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Checking out %MONSTER% branch ... ******
git checkout -b %MONSTER% origin/%MONSTER%
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing back to the parent directory ... ******
cd ..
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Cloning pyqcat-visage repository ... ******
git clone https://gitlab.qpanda.cn/spirit/pyqcat-visage.git
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing directory to pyqcat-visage ... ******
cd pyqcat-visage
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Checking out %VISAGE% branch ... ******
git checkout -b %VISAGE% origin/%VISAGE%
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing back to the parent directory ... ******
cd ..
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Link invoker to monster ... ******
mklink /d %WORK_DIR%pyqcat-monster\pyQCat\invoker\qmq %WORK_DIR%pyqcat-invoker\pyQCat\invoker\qmq
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** LOG: Link monster to apps ... ******
mklink /d %WORK_DIR%pyqcat-apps\pyQCat %WORK_DIR%pyqcat-monster\pyQCat
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** LOG: Link monster to visage ... ******
mklink /d %WORK_DIR%pyqcat-visage\pyQCat %WORK_DIR%pyqcat-monster\pyQCat
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** All commands executed successfully. ******
pause
