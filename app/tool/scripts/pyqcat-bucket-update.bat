@echo off

set WORK_DIR=%~dp0
set APPS=pyqcat-apps
set MONSTER=pyqcat-monster
set INVOKER=pyqcat-invoker
set VISAGE=pyqcat-visage

echo ****** Start Git pull pyqcat bucket ******

echo ****** Git Pull %APPS% ******
set GITAPPS=%WORK_DIR%%APPS%
cd /d %GITAPPS%
set GITAPPS=%GITAPPS:\=/%
git config --global --add safe.directory %GITAPPS%
git status
git stash
git pull
git stash pop
cd ..
echo.

echo ****** Git Pull %MONSTER% ******
set GITMONSTER=%WORK_DIR%%MONSTER%
cd /d %GITMONSTER%
set GITMONSTER=%GITMONSTER:\=/%
git config --global --add safe.directory %GITMONSTER%
git status
git stash
git pull
git stash pop
cd ..
echo.

echo ****** Git Pull %INVOKER% ******
set GITINVOKER=%WORK_DIR%%INVOKER%
cd /d %GITINVOKER%
set GITINVOKER=%GITINVOKER:\=/%
git config --global --add safe.directory %GITINVOKER%
git status
git stash
git pull
git stash pop
cd ..
echo.

echo ****** Git Pull %VISAGE% ******
set GITVISAGE=%WORK_DIR%%VISAGE%
cd /d %GITVISAGE%
set GITVISAGE=%GITVISAGE:\=/%
git config --global --add safe.directory %GITVISAGE%
git status
git stash
git pull
git stash pop
cd ..
echo.

echo ****** All commands executed successfully. ******
pause
