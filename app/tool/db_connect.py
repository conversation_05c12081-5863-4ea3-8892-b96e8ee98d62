# -*- coding: utf-8 -*-

# This code is part of pyqcat-courier.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/09/04
# __author:       Xw

from datetime import datetime

from pymongo import MongoClient

"""
use PyMongo
"""

MONGO_DB = "UserData"
MONGO_ALIAS = "user_data_store"
MONGO_USER = "bylz"
MONGO_PWD = "fjsaoJOIjiojj28hjj"
MAX_DB_POOL_SIZE = 100


class DBPyMongo():

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._db = None
        self._client = None

    def create_connect(
            self,
            ip: str = "***********",
            port: int = 27017,
            auth: bool = True,
            db: str = MONGO_DB,
            max_connections: int = MAX_DB_POOL_SIZE,
    ):
        if auth:
            client = MongoClient(
                f"mongodb://{MONGO_USER}:{MONGO_PWD}@{ip}:{port}/",
                maxPoolSize=max_connections,
            )
        else:
            client = MongoClient(f"mongodb://{ip}:{port}/", maxPoolSize=max_connections)
        self._client = client
        self._db = self._client[db]

    def get_connect(self, collection: str):
        return self._db[collection]


def migrate_config_files(
        db_instance: DBPyMongo,
        old_collection: str,
        new_collection: str,
        old_user: str,
        old_sample: str,
        old_env_name: str,
        new_user: str,
        new_sample: str,
        new_env_name: str,
        new_point_label: str
):
    # 连接旧的数据库
    db_instance.create_connect()
    old_config_doc = db_instance.get_connect(old_collection)
    filter_kwargs = {
        "username": old_user,
        "sample": old_sample,
        "env_name": old_env_name,
        # "filename": {"$regex": r"\.bin$"}
        "filename": {"$regex": r"\.bin$", "$options": "i"}
    }

    config_cursor = old_config_doc.find(filter_kwargs)
    #
    # # 检查是否有匹配的文档
    # if config_cursor.retrieved == 0:
    #     print("No matching documents found.")
    #     return None

    # 连接新的数据库
    new_config_doc = db_instance.get_connect(new_collection)

    for conf in config_cursor:
        # 定义新的查询条件
        query_dict = {
            "username": new_user,
            "sample": new_sample,
            "env_name": new_env_name,
            "filename": conf["filename"],
        }

        new_data = {
            "username": new_user,
            "sample": new_sample,
            "env_name": new_env_name,
            "filename": conf.get("filename", ""),
            "bin": conf.get("bin", ""),
            "bin_abbr": conf.get("bin_abbr", {}),
            "point_label": new_point_label,
            "update_time": conf.get("update_time", datetime.now())
        }

        # 查找新集合中是否已存在相同条件的文档
        conf_origin = new_config_doc.find_one(query_dict, {"_id": 1})

        if conf_origin:
            # 如果存在，更新文档
            new_config_doc.update_one({"_id": conf_origin["_id"]}, {"$set": new_data})
            print(f"Updated document: {conf_origin['_id']}")
        else:
            # 如果不存在，插入新的文档
            new_config_doc.insert_one(new_data)
            print(f"Inserted new document with filename: {conf['filename']}")

    print("Migration completed.")
    return True


DBM = DBPyMongo()

if __name__ == "__main__":
    old_collection = "ConfigStore"
    new_collection = "ConfigStoreV1"
    old_user = "tuple_01"
    old_sample = "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#"
    old_env_name = "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#"
    new_user = "tuple_01"
    new_sample = "NewMultiPoint"
    new_env_name = "new_mpl"
    new_point_label = "test_point"

    # 迁移所有符合条件的文件
    migrate_config_files(
        db_instance=DBM,
        old_collection=old_collection,
        new_collection=new_collection,
        old_user=old_user,
        old_sample=old_sample,
        old_env_name=old_env_name,
        new_user=new_user,
        new_sample=new_sample,
        new_env_name=new_env_name,
        new_point_label=new_point_label
    )
