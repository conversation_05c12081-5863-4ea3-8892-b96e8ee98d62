# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/10/12
# __author:       <PERSON><PERSON><PERSON>


import json
import math
from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger


def read_json_file(file_path):
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            data = json.load(file)
            return data
    except FileNotFoundError:
        logger.error(f"file {file_path} no find!")
    except json.JSONDecodeError:
        logger.error(f"file {file_path} is not a valid JSON format!")


def write_json_file(file_path, data):
    try:
        with open(file_path, "w", encoding="utf-8") as file:
            json.dump(data, file, ensure_ascii=False, indent=4)
        logger.success(f"data has been written to {file_path}")
    except TypeError:
        import traceback
        logger.error(
            f"The provided data is not in a format that can be serialized as JSON\n{traceback.format_exc()}"
        )


class DictComparator:
    """A utility class for comparing nested dictionaries with customizable float tolerance.

    Provides both boolean equality checks and detailed difference reports with
    human-readable formatting.
    """

    def __init__(self, float_tol: float = 1e-6, output_file: str = None):
        """Initializes the comparator with specified float tolerance.

        Args:
            float_tol: Absolute tolerance for float comparisons. Defaults to 1e-6.
            output_file: Path to a file where output should be logged. If None, defaults to console output.
        """
        self.float_tol = float_tol
        self.output_file = output_file

    def are_equal(self, dict1: Dict[str, Any], dict2: Dict[str, Any]) -> bool:
        """Checks if two dictionaries are equal within the specified tolerance.

        Args:
            dict1: First dictionary to compare
            dict2: Second dictionary to compare

        Returns:
            bool: True if dictionaries match within tolerance, False otherwise
        """
        return not self.get_differences(dict1, dict2)

    def get_differences(
        self, dict1: Dict[str, Any], dict2: Dict[str, Any], path: str = ""
    ) -> List[Dict[str, Any]]:
        """Identifies all differences between two dictionaries.

        Args:
            dict1: First dictionary to compare
            dict2: Second dictionary to compare
            path: Current path in nested structure (used internally)

        Returns:
            List of difference dictionaries with detailed information
        """
        differences = []

        # Handle missing keys in either dictionary
        all_keys = set(dict1.keys()).union(set(dict2.keys()))
        for key in all_keys:
            current_path = f"{path}.{key}" if path else key

            if key not in dict1:
                differences.append(
                    self._format_diff(
                        path=current_path,
                        type="MISSING_IN_FIRST",
                        value1=None,
                        value2=dict2[key],
                    )
                )
                continue

            if key not in dict2:
                differences.append(
                    self._format_diff(
                        path=current_path,
                        type="MISSING_IN_SECOND",
                        value1=dict1[key],
                        value2=None,
                    )
                )
                continue

            val1, val2 = dict1[key], dict2[key]

            # Recursively handle nested dictionaries
            if isinstance(val1, dict) and isinstance(val2, dict):
                differences.extend(self.get_differences(val1, val2, current_path))
                continue

            # Handle lists/tuples
            if isinstance(val1, (list, tuple)) and isinstance(val2, (list, tuple)):
                if len(val1) != len(val2):
                    differences.append(
                        self._format_diff(
                            path=current_path,
                            type="LENGTH_MISMATCH",
                            value1=len(val1),
                            value2=len(val2),
                        )
                    )
                else:
                    for i, (v1, v2) in enumerate(zip(val1, val2)):
                        if not self._compare_values(v1, v2):
                            if isinstance(v1, (dict, list, tuple)) or isinstance(
                                v2, (dict, list, tuple)
                            ):
                                nested_path = f"{current_path}[{i}]"
                                if isinstance(v1, dict) and isinstance(v2, dict):
                                    differences.extend(
                                        self.get_differences(v1, v2, nested_path)
                                    )
                                else:
                                    differences.append(
                                        self._format_diff(
                                            path=nested_path,
                                            type="VALUE_MISMATCH",
                                            value1=v1,
                                            value2=v2,
                                        )
                                    )
                            else:
                                differences.append(
                                    self._format_diff(
                                        path=f"{current_path}[{i}]",
                                        type="VALUE_MISMATCH",
                                        value1=v1,
                                        value2=v2,
                                    )
                                )
                continue

            # Compare simple values
            if not self._compare_values(val1, val2):
                differences.append(
                    self._format_diff(
                        path=current_path,
                        type="VALUE_MISMATCH",
                        value1=val1,
                        value2=val2,
                    )
                )

        return differences

    def _compare_values(self, val1: Any, val2: Any) -> bool:
        """Internal method to compare individual values with float tolerance."""
        # 处理浮点数比较
        if isinstance(val1, float) and isinstance(val2, float):
            return math.isclose(val1, val2, abs_tol=self.float_tol)

        # 处理列表/元组比较
        if isinstance(val1, (list, tuple)) and isinstance(val2, (list, tuple)):
            if len(val1) != len(val2):
                return False
            return all(self._compare_values(x, y) for x, y in zip(val1, val2))

        # 其他类型直接比较
        return val1 == val2

    def _format_diff(
        self, path: str, type: str, value1: Any, value2: Any
    ) -> Dict[str, Any]:
        """Formats a difference into a standardized dictionary."""
        return {
            "path": path,
            "type": type,
            "value1": value1,
            "value2": value2,
            "message": self._generate_diff_message(path, type, value1, value2),
        }

    def _generate_diff_message(
        self, path: str, type: str, value1: Any, value2: Any
    ) -> str:
        """Generates human-readable difference messages."""
        messages = {
            "MISSING_IN_FIRST": f"Field '{path}' is missing in first dictionary (found in second: {value2})",
            "MISSING_IN_SECOND": f"Field '{path}' is missing in second dictionary (found in first: {value1})",
            "LENGTH_MISMATCH": f"Array length mismatch at '{path}' (first: {value1}, second: {value2})",
            "VALUE_MISMATCH": (
                f"Value mismatch at '{path}'\n"
                f"- First value:  {value1}\n"
                f"- Second value: {value2}"
            ),
        }
        return messages.get(type, f"Difference at '{path}'")

    def print_differences(
        self, dict1: Optional[Dict[str, Any]] = None, dict2: Optional[Dict[str, Any]] = None, name: str = ""
    ):
        """Prints a formatted report of all differences between dictionaries."""
        dict1 = dict1 or {}
        dict2 = dict2 or {}
        if not dict1 and not dict2:
            logger.warning("empty compare data ...")
            return False

        differences = self.get_differences(dict1, dict2)
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if self.output_file:
            self._write_to_file(differences, name, timestamp)
        else:
            self._print_to_console(differences, name, timestamp)

        return bool(differences)

    def _print_to_console(
        self, differences: List[Dict[str, Any]], name: str, timestamp: str
    ):
        """Prints differences to the console."""

        if not differences:
            print(
                f"[{timestamp}] | 👌 The dictionaries are identical within the specified tolerance <{name}>"
            )
            return

        print(
            f"[{timestamp}] {name} | 🔍 Found {len(differences)} differences <{name}>:"
        )
        print("-" * 50)

        for i, diff in enumerate(differences, 1):
            print(f"\nDifference #{i}:")
            print(f"Location: {diff['path']}")
            print(f"Type: {diff['type']}")
            print(diff["message"])

            if isinstance(diff["value1"], float) and isinstance(diff["value2"], float):
                print(
                    f"Absolute difference: {abs(diff['value1'] - diff['value2']):.2e}"
                )

        print("\n" + "-" * 50)
        # print("Comparison complete.")

    def _write_to_file(
        self, differences: List[Dict[str, Any]], name: str, timestamp: str
    ):
        """Writes differences to the specified output file."""
        with open(self.output_file, "a", encoding="utf-8") as f:
            if not differences:
                f.write(
                    f"[{timestamp}] 👌 The dictionaries are identical within the specified tolerance <{name}>\n"
                )
                return

            f.write(
                f"[{timestamp}] 🔍 Found {len(differences)} differences <{name}>:\n"
            )
            f.write("-" * 50 + "\n")

            for i, diff in enumerate(differences, 1):
                f.write(f"\nDifference #{i}:\n")
                f.write(f"Location: {diff['path']}\n")
                f.write(f"Type: {diff['type']}\n")
                f.write(diff["message"] + "\n")

                if isinstance(diff["value1"], float) and isinstance(
                    diff["value2"], float
                ):
                    f.write(
                        f"Absolute difference: {abs(diff['value1'] - diff['value2']):.2e}\n"
                    )

            f.write("\n" + "-" * 50 + "\n")
            # f.write("Comparison complete.\n\n")
