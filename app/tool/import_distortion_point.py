# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/09/20
# __author:       <PERSON><PERSON><PERSON>


def import_distortion_point(rb_spectrum_path: str, distortion_point_path: str):
    import numpy as np
    import json
    from app.config import init_backend

    _backend = init_backend()

    distortion_point_data = np.loadtxt(distortion_point_path)
    with open(rb_spectrum_path, encoding="utf-8") as f:
        rb_spectrum_data = json.load(f)

    for distortion in distortion_point_data:
        bit, _, distortion_point, _ = distortion
        qubit_name = f"q{int(bit)}"

        qubit_data = None
        for freq, point_data in rb_spectrum_data.get(qubit_name, {}).items():
            if round(float(distortion_point), 3) == round(float(freq), 3):
                qubit_data = point_data.get("params")
                break

        if qubit_data:
            qubit_data.pop("bit", None)
            qubit_obj = _backend.context_manager.chip_data.get_physical_unit(qubit_name)
            qubit_obj.update(qubit_obj, qubit_data)
            qubit_obj.save_data()
            print(f"Update {qubit_name} distortion point {distortion_point}!")
        else:
            print(f"---- Error ---- | import {qubit_name} distortion point {distortion_point} error!")


if __name__ == '__main__':
    import_distortion_point(
        rb_spectrum_path=r"C:\Users\<USER>\Downloads\Y4_RB_Spectrum.json",
        distortion_point_path=r"C:\Users\<USER>\Downloads\Y4_RB_Spectrum.json_flux_sensitive_working_point.dat"
    )
