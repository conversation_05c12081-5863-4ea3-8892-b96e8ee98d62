# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/31
# __author:       <PERSON><PERSON><PERSON>

import numpy as np
import pandas as pd

from app.config import init_backend


def import_z_crosstalk(backend, path_list, refresh: bool = False, save_db: bool = False):
    crosstalk = backend.context_manager.chip_data.cache_config.get("crosstalk.json")

    if refresh is True:
        length = len(crosstalk.get("infos"))
        for i in range(length):
            for j in range(length):
                crosstalk["ac_crosstalk"][i][j] = int(i == j)
    else:
        for data_path in path_list:
            data = pd.read_csv(data_path)
            for i, bq in enumerate(data.iloc[:, 0].tolist()):
                for j in range(72):
                    v = data.iloc[i, j + 1]
                    if not np.isnan(v):
                        if isinstance(bq, int):
                            bq = f"q{bq}"
                        tq = f"q{j + 1}"
                        v = 1 if tq == bq else data.iloc[i, j + 1]
                        tq_index = crosstalk.get("infos").index(tq)
                        bq_index = crosstalk.get("infos").index(bq)
                        crosstalk["ac_crosstalk"][tq_index][bq_index] = v
                        print(f"Set tq({tq}) bq({bq}) coe({v})")
    if save_db is True:
        backend.save_chip_data_to_db("crosstalk.json")


if __name__ == '__main__':
    import_z_crosstalk(
        init_backend(),
        path_list=[
            r"F:\临时文件\zcrosstalk\zcrosstalk\qubit-qubit ZCrosstalk.csv",
            r"F:\临时文件\zcrosstalk\zcrosstalk\横coupler-qubit ZCrosstalk.csv",
            r"F:\临时文件\zcrosstalk\zcrosstalk\纵coupler to qubit ZCrosstalk.csv"
        ],
        refresh=False,
        save_db=True
    )
