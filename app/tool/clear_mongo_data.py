# -*- coding: utf-8 -*-

# This code is part of pyqcat-storm.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/21
# __author:       SS Fang

"""
Clear MongoDB history data.

"""

from datetime import datetime

from bson import ObjectId
from pymongo import MongoClient
from gridfs import GridFS


def get_mg_client(
        user: str = "",
        password: str = "",
        host: str = "localhost",
        port: int = 27017,
        max_connections: int = 100,
):
    """Get mongodb client object."""
    if user and password:
        mg_url = f"mongodb://{user}:{password}@{host}:{port}/"
    else:
        mg_url = f"mongodb://{host}:{port}/"
    mg_client = MongoClient(mg_url, maxPoolSize=max_connections)
    return mg_client


def clear_history_data(mg_client: "MongoClient", split_date: str = "2024-06-01"):
    """Clear mongo data."""
    database = "measuresystem"
    exp_collection = "Experiment"
    aio_collection = "MeasureAIO"
    data_collection = "MeasureData"
    sweep_collection = "Sweep"

    db_obj = mg_client[database]
    fs_obj = GridFS(db_obj)
    exp_collect_obj = db_obj[exp_collection]
    aio_collect_obj = db_obj[aio_collection]
    data_collect_obj = db_obj[data_collection]
    sweep_collect_obj = db_obj[sweep_collection]

    try:
        print(f"split_date: {split_date}")
        old_query_cursor = exp_collect_obj.find(
            {"date_modified": {"$lt": datetime.strptime(split_date, "%Y-%m-%d")}}
        )
        new_query_cursor = exp_collect_obj.find({"date_modified": {"$lt": split_date}})

        exp_ids = []
        ms_aio_ids = []
        ms_data_ids = []
        sweep_ids = []
        for res_cursor in [old_query_cursor, new_query_cursor]:
            for doc_dict in res_cursor:
                date_modified = str(doc_dict.get("date_modified", ""))
                if date_modified < split_date:
                    doc_id = doc_dict.get("_id")
                    program_id = doc_dict.get("program", "")
                    measure_data = doc_dict.get("measure_data", {})

                    exp_ids.append(doc_id)
                    if "measure_aio" in doc_dict:
                        ms_aio_ids.append(doc_dict.get("measure_aio"))
                    if "sweep_control" in doc_dict:
                        sweep_ids.extend(doc_dict.get("sweep_control"))
                    if program_id:
                        fs_obj.delete(ObjectId(program_id))
                    for channel, ids in measure_data.items():
                        try:
                            if isinstance(ids[0], ObjectId):
                                new_ids = ids
                            else:
                                new_ids = [ObjectId(id_str) for id_str in ids]
                        except Exception as err:
                            print(f"Exp id: {doc_id}, measure_data ids: {ids}, error: {err}")
                            new_ids = ids
                        ms_data_ids.extend(new_ids)

                if len(exp_ids) > 200:
                    print("Once delete many start ...")
                    exp_collect_obj.delete_many({"_id": {"$in": exp_ids}})
                    aio_collect_obj.delete_many({"_id": {"$in": ms_aio_ids}})
                    data_collect_obj.delete_many({"_id": {"$in": ms_data_ids}})
                    sweep_collect_obj.delete_many({"_id": {"$in": sweep_ids}})
                    exp_ids.clear()
                    ms_aio_ids.clear()
                    ms_data_ids.clear()
                    sweep_ids.clear()

        print("Finally delete many start ...")
        exp_collect_obj.delete_many({"_id": {"$in": exp_ids}})
        aio_collect_obj.delete_many({"_id": {"$in": ms_aio_ids}})
        data_collect_obj.delete_many({"_id": {"$in": ms_data_ids}})
        sweep_collect_obj.delete_many({"_id": {"$in": sweep_ids}})

    except Exception as error:
        raise ValueError(f"An error occurred: {str(error)}") from error


def delete_collection_doc(mg_client: "MongoClient", collection: str, split_idx: int = 1000):
    """Delete some collection documents."""
    try:
        database = "measuresystem"
        db_obj = mg_client[database]
        collect_obj = db_obj[collection]
        res_cursor = collect_obj.find().sort({"_id": -1})

        print(f"Delete {database}.{collection} split_idx: {split_idx}")
        doc_ids = []
        for idx, doc_dict in enumerate(res_cursor):
            if idx > split_idx:
                doc_ids.append(doc_dict.get("_id"))
            if len(doc_ids) > 300:
                print(f"Index: {idx}, Once delete many start ...")
                collect_obj.delete_many({"_id": {"$in": doc_ids}})
                doc_ids.clear()

        print("Finally delete many start ...")
        collect_obj.delete_many({"_id": {"$in": doc_ids}})
    except Exception as error:
        raise ValueError(f"An error occurred: {str(error)}") from error


if __name__ == '__main__':
    mg_params = {
        "user": "bylz",
        "password": "fjsaoJOIjiojj28hjj",
        "host": "127.0.0.1",
        "port": 27027,
    }

    client_obj = get_mg_client(**mg_params)

    # clear_history_data(client_obj, split_date="2024-06-10")
    delete_collection_doc(client_obj, "MeasureData", split_idx=5000)
