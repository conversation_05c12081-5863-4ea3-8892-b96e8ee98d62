import pyQCat.experiments as monster_exp_library
import pyQCat.preliminary as preliminary_exp_library
from app.tool.utils import write_json_file


def generate_options_json(exp_class_names: list, file_name: str = "test.json"):
    options = {}

    for exp_name in exp_class_names:
        if isinstance(exp_name, str):
            exp_cls = getattr(monster_exp_library, exp_name, None) or getattr(
                preliminary_exp_library, exp_name, None
            )
        else:
            exp_cls = exp_name

        if exp_cls:
            options[exp_cls.__name__] = exp_cls.template_options().to_dict()

    write_json_file(file_name, options)


if __name__ == "__main__":
    generate_options_json(exp_class_names=["CPMGExperiment"])
