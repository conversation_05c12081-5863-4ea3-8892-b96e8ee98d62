# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/18
# __author:       <PERSON><PERSON><PERSON><PERSON>


from __future__ import annotations

from app.config import init_backend


def multiple_attribute_to_xlsx(
    attribute_dict: dict[str: str | tuple | list],
    path_of_file: str,
    exist_ok: bool = False,
):
    # Valid values of `name_of_data`: "Qubit", "Coupler", "QubitPair"
    # Valid values of `color_scheme`: "primary", "success", "danger", "warning", "info"
    for name_of_data, attributes in attribute_dict.items():
        for attribute in attributes:
            if isinstance(attribute, str):
                name_of_attribute = attribute
                color_scheme = "primary"
            elif isinstance(attribute, (tuple, list)):
                name_of_attribute = attribute[0]
                color_scheme = attribute[1]
            else:
                raise ValueError(
                    f"The type of attribute item is {type(attribute)} (not in str, tuple, list)."
                )

            returned_path_of_file = backend.context_manager.chip_data.to_xlsx(
                name_of_data,
                name_of_attribute,
                color_scheme,
                f"{path_of_file}/{name_of_data}/{name_of_attribute}.xlsx",
                exist_ok,
            )

            print(
                f"The heat map ({name_of_data} {name_of_attribute}) is exported to {returned_path_of_file}"
            )


if __name__ == "__main__":
    backend = init_backend()

    attribute_dict = {
        "Qubit": ["sample_delay", "XYwave.Xpi2"],
        "Coupler": ["z_dc_channel", "readout_point.sigma", "drive_bit"],
        "QubitPair": ["name", "metadata.std.qh"],
    }
    path_of_file = "D:/Test"
    multiple_attribute_to_xlsx(attribute_dict, path_of_file, False)
