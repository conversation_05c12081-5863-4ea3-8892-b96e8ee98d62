# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/15
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.executor.batch.tools import *

    backend = init_backend()

    # import_idle_point(
    #     backend,
    #     idle_point_path=r"D:\MonsterData\BatchSearchIdlePoint\2024-01-12_20.01.46"
    # )

    import_readout_point(
        backend,
        readout_point_path=r"D:\MonsterData\BatchSearchReadoutPoint\2024-01-17_14.12.24",
    )
