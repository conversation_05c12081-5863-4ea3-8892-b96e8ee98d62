# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/19
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

import json
import os
import time
from copy import deepcopy

import matplotlib.pyplot as plt
import numpy as np


def merge_all_json_data(file_path: str):
    file_list = os.listdir(file_path)
    json_list = [file for file in file_list if file.endswith(".json")]
    print(list(json_list))
    dicta = {}
    for file_name in json_list:
        with open(file_path + "\\" + file_name, "r", encoding="utf-8") as f1:
            content = f1.read()
            d = json.loads(content)

        for qubit, record in d.items():
            if qubit not in dicta.keys():
                dicta.update({qubit: record})

            for freq, result in record.items():
                if (
                    (freq not in dicta[qubit].keys())
                    or (result["is_pass"] and not dicta[qubit][freq]["is_pass"])
                    or (
                        result["is_pass"]
                        and dicta[qubit][freq]["is_pass"]
                        and result["rb_fidelity"] > dicta[qubit][freq]["rb_fidelity"]
                    )
                ):
                    dicta[qubit].update({freq: result})

    data = dict(sorted(dicta.items(), key=lambda d: int(d[0][1:])))
    with open(file_path + "\\" + "chip_rb_data.json", "w", encoding="utf-8") as file:
        json.dump(data, file, indent=2)
        file.close()

    print(data.keys(), "len", len(data.keys()))

    return data


def rb_pass(
    data: dict,
    file_path: str,
    rb_fidelity: float = 0.995,
    rb_std: float = 0.2,
    assignment: str = "name",
):
    dicta = {}
    for qubit, record in data.items():
        pass_freq_list = []
        pass_drive_freq_list = []
        pass_fidelity_list = []
        pass_std_list = []
        for freq, result in record.items():
            if (
                result["is_pass"]
                and float(result["rb_fidelity"]) >= rb_fidelity
                and float(result["rb_std"]) <= rb_std
            ):
                pass_freq_list.append(float(freq))
                pass_drive_freq_list.append(result["params"]["drive_freq"])
                pass_fidelity_list.append(result["rb_fidelity"])
                pass_std_list.append(result["rb_std"])
        dicta.update(
            {
                qubit: {
                    "freq": pass_freq_list,
                    "drive_freq": pass_drive_freq_list,
                    "fidelity": pass_fidelity_list,
                    "std": pass_std_list,
                }
            }
        )

    def assign(d):
        if d[1]["drive_freq"]:
            return np.max(d[1]["drive_freq"])
        else:
            return 0

    if assignment == "frequency":
        dicta_sort = dict(sorted(dicta.items(), key=lambda d: assign(d)))

    elif assignment == "name":
        dicta_sort = dicta

    qubit_name_list = dicta_sort.keys()
    #     # scatter plot
    xlist = []
    ylist = []
    for index, value in enumerate(dicta_sort.values()):
        freq_list = value["drive_freq"]
        xlist.extend([index] * len(freq_list))
        ylist.extend(freq_list)

    plt.style.use("ggplot")
    fig, ax = plt.subplots(figsize=(14, 8))
    c = ax.scatter(xlist, ylist, c=xlist, marker="s")
    ax.set_xlabel(r"qubit", size=14)
    ax.set_xticks(
        ticks=range(len(qubit_name_list)), labels=qubit_name_list, rotation=90
    )
    ax.set_ylabel(r"Frequence", size=14)
    ax.set_title("RB Pass,threshold:fidelity:%.3f,std:%.2f" % (rb_fidelity, rb_std))

    date_str = time.strftime("%m-%d_%H-%M-%S", time.localtime())

    rb_pass_file_path = file_path + "\\rb_pass"

    if not os.path.exists(rb_pass_file_path):
        os.makedirs(rb_pass_file_path)

    with open(
        rb_pass_file_path + "\\" + date_str + "_rb_pass.json", "w", encoding="utf-8"
    ) as file:
        json.dump(dicta, file, indent=2)
        file.close()

    plt.savefig(
        file_path + "\\rb_pass" + "\\" + date_str + "_" + assignment + ".png", dpi=1024
    )

    return dicta


def rb_fidelity_std(
    data: dict, file_path: str, rb_fidelity: float = 0.995, rb_std: float = 0.2
):
    dicta = {}
    for qubit, record in data.items():
        pass_freq_list = []
        pass_drive_freq_list = []
        pass_fidelity_list = []
        pass_std_list = []
        for freq, result in record.items():
            if (
                result["is_pass"]
                and float(result["rb_fidelity"]) > 0.995
                and float(result["rb_std"]) < 0.2
            ):
                pass_freq_list.append(float(freq))
                pass_drive_freq_list.append(result["params"]["drive_freq"])
                pass_fidelity_list.append(result["rb_fidelity"])
                pass_std_list.append(result["rb_std"])
        dicta.update(
            {
                qubit: {
                    "freq": pass_freq_list,
                    "drive_freq": pass_drive_freq_list,
                    "fidelity": pass_fidelity_list,
                    "std": pass_std_list,
                }
            }
        )

    plt.style.use("ggplot")
    fig, axs = plt.subplots(2, 1, figsize=(10, 4), sharex=True)
    lbs = []
    i = 0
    for index, (qubit, val) in enumerate(list(dicta.items())):
        if val["freq"]:
            axs[0].plot(val["drive_freq"], val["fidelity"], "-o", markersize=5)
            axs[1].plot(val["drive_freq"], val["std"], "-o", markersize=5)
            lbs.append(qubit)
            i += 1
            if np.mod(i, 6) == 0 or index == len(list(dicta.items())) - 1:
                axs[0].set_ylabel("RB fidelity", size=14)

                fig.legend(lbs, loc="upper right", bbox_to_anchor=(1.01, 0.9))
                axs[0].set_title(
                    "RB Pass,threshold:fidelity:%.3f,std:%.2f" % (rb_fidelity, rb_std),
                    size=14,
                )
                axs[1].set_xlabel("Frequency", size=14)
                axs[1].set_ylabel("RB std", size=14)
                plt.tight_layout()

                rb_fidelity_img_path = file_path + "\\qubit_fidelity"

                if not os.path.exists(rb_fidelity_img_path):
                    os.makedirs(rb_fidelity_img_path)

                plt.savefig(
                    rb_fidelity_img_path
                    + "\\"
                    + "rb_fidelity_std%1.f.png" % (int(np.ceil(i / 6))),
                    dpi=1024,
                )
                plt.close()
                if not index == len(list(dicta.items())) - 1:
                    fig, axs = plt.subplots(2, 1, figsize=(10, 4), sharex=True)
                    lbs = []

    return dicta


def all_qubit_error_count(data: dict, file_path: str, rb_fidelity: float = 0.995):
    dicta = {}
    error_type = {
        "QubitFreqCalibration": 0,
        "XpiDetection": 0,
        "DetuneCalibration": 0,
        "RabiScanAmp": 0,
        "AmpComposite": 0,
        "SingleShot": 0,
        "RBSingle": 0,
        "Completion": 0,
    }
    for qubit, record in data.items():
        error_counts = deepcopy(error_type)
        for freq, result in record.items():
            if not result["is_pass"]:
                for error in error_counts.keys():
                    if result["error_exp"] and error in result["error_exp"]:
                        error_counts[error] += 1

            elif float(result["rb_fidelity"]) < rb_fidelity:
                error_counts["RBSingle"] += 1

            else:
                error_counts["Completion"] += 1
        dicta.update({qubit: error_counts})

    error_counts = deepcopy(error_type)
    for val in dicta.values():
        for key, count in val.items():
            error_counts[key] += count

    xlist = []
    for index, num in enumerate(error_counts.values()):
        xlist.extend([index] * num)
    plt.style.use("ggplot")
    fig, ax = plt.subplots(figsize=(5, 6))
    counts_list, bis, _ = ax.hist(
        x=xlist,
        bins=len(error_counts),
        range=[0, len(error_counts)],
        color="steelblue",
        edgecolor="black",
        rwidth=0.9,
    )
    ax.set_ylabel("Counts", size=14)
    ax.set_xticks(ticks=bis[:-1] + 0.5, labels=error_counts.keys(), rotation=-80)
    ax.set_title("Error Reason Count")
    for index, y in enumerate(counts_list):
        ax.text(bis[index] + 0.35, y + 0.2, int(y), fontsize=12)
    plt.tight_layout()

    date_str = time.strftime("%m-%d_%H-%M-%S", time.localtime())
    qubit_error_path = file_path + "\\qubit_error" + "\\" + date_str
    if not os.path.exists(qubit_error_path):
        os.makedirs(qubit_error_path)

    with open(qubit_error_path + "_error_counts.json", "w", encoding="utf-8") as file:
        json.dump(dicta, file, indent=2)
        file.close()

    with open(
        qubit_error_path + "_all_error_counts.json", "w", encoding="utf-8"
    ) as file:
        json.dump(error_counts, file, indent=2)
        file.close()

    plt.savefig(qubit_error_path + "_ErrorReason.png", dpi=1024)

    return dicta


def single_qubit_error(error_counts: dict, file_path: str):
    for qubit_name in error_counts.keys():
        xlist = []
        error_type = {
            "QubitFreqCalibration": 0,
            "XpiDetection": 0,
            "DetuneCalibration": 0,
            "RabiScanAmp": 0,
            "AmpComposite": 0,
            "SingleShot": 0,
            "RBSingle": 0,
            "Completion": 0,
        }
        for index, num in enumerate(error_counts[qubit_name].values()):
            xlist.extend([index] * num)
        plt.style.use("ggplot")
        fig, ax = plt.subplots()
        counts_list, bis, _ = ax.hist(
            x=xlist,
            bins=len(error_type),
            range=[0, len(error_type)],
            color="steelblue",
            edgecolor="black",
            rwidth=0.9,
        )
        ax.set_ylabel("Counts", size=12)
        ax.set_xticks(ticks=bis[:-1] + 0.5, labels=error_type.keys(), rotation=-80)
        ax.set_title(qubit_name + ",Error Reason Count")
        for index, y in enumerate(counts_list):
            ax.text(bis[index] + 0.35, y + 0.2, int(y), fontsize=12)
        plt.tight_layout()

        plt.savefig(
            file_path + "\\qubit_error" + "\\" + qubit_name + "ErrorReason.png", dpi=100
        )
        plt.close()


if __name__ == "__main__":
    file_path = r"D:\MonsterData\Y2 RB Spectrum 数据"

    data = merge_all_json_data(file_path)

    rb_pass(data, file_path)

    rb_fidelity_std(data, file_path)

    error_counts = all_qubit_error_count(data, file_path)

    single_qubit_error(error_counts, file_path)
