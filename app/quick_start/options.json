{"CavityTunable_for_qubit": {"meta": {"username": "Miracle", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CavityTunable", "export_datetime": "2024-01-24 11:13:04", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "fc_list": "Points(0) | normal | None", "points": 101, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 3, "z_amp": null, "mode": "IF", "fake_pulse": false}, "fc_list": "Points(0) | normal | None", "readout_power": null, "flux_list": "Points(0) | qarange | (-0.48, 0.48, 0.06)", "scan_name": "ac_bias", "run_mode": "async"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "diff_threshold": 0.1, "tackle_type": "<PERSON><PERSON><PERSON>", "quality_bounds": [0.98, 0.95, 0.9]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "AmpComposite": {"meta": {"username": "Miracle", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "AmpComposite", "export_datetime": "2023-12-30 12:55:01", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"theta_type": "Xpi", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 7, "threshold_left": 0.95, "threshold_right": 1.05, "f12_opt": false}, "n_list": "Points(3) | normal | [10, 19, 30]", "theta_type": "Xpi", "f12_opt": false, "run_mode": "async"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "diff_threshold": 0.05}}, "options_for_parallel_exec": {}}, "RBSingle": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "RBSingle", "export_datetime": "2023-12-30 12:56:08", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"times": 30, "depth1": "Points(3) | qarange | (2, 10, 2)", "depth2": "Points(9) | qarange | (15, 50, 5)", "depth3": "Points(6) | qarange | (60, 100, 10)", "depth4": "Points(0) | qarange | (120, 380, 20)", "interleaved_gate": null, "gate_split": false, "mode": "cpp", "open_seed": false, "seed": null, "fake_pulse": true}, "analysis_options": {"quality_bounds": [0.9, 0.85, 0.77], "std_bound": 0.05}}, "options_for_parallel_exec": {}}, "AmpOptimize": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "AmpOptimize", "export_datetime": "2023-12-30 12:54:39", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"theta_type": "Xpi", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 100, "threshold_left": 0.7, "threshold_right": 1.1, "f12_opt": false}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.85]}}, "options_for_parallel_exec": {}}, "RabiScanAmp": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "0.5.6", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "test"}, "exp_class_name": "RabiScanAmp", "export_datetime": "2023-12-21 17:03:44", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q3,q29"}, "options_for_regular_exec": {"experiment_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": -30}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RBMultiple": {"meta": {"username": "zyc", "visage_version": "0.4.6", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "RBMultiple", "export_datetime": "2023-12-19 13:42:10", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q45q46"}, "options_for_regular_exec": {"experiment_options": {"depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(0) | normal | None", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 30, "interleaved_gate": null, "gate_split": false, "open_seed": false, "seed": null, "mode": "cpp", "check_matrix": false}, "analysis_options": {"quality_bounds": [0.9, 0.85, 0.77]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "Ramsey": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion"}, "exp_class_name": "<PERSON>", "export_datetime": "2024-01-03 20:34:11", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"delays": "Points(49) | qarange | (20, 140, 2.5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null}, "analysis_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanWidth": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "RabiScanWidth", "export_datetime": "2023-11-29 19:58:37", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1,q2,q3,q4,q5,q6"}, "options_for_regular_exec": {"experiment_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {}}, "CheckFreqRabiWidth": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "CheckFreqRabiWidth", "export_datetime": "2023-11-29 19:58:37", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "is_dynamic": 0}, "simulator_data_path": null, "freq_list": "Points(2) | normal | [4500, 4502, 4503, 4504]", "run_mode": "async"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}}}, "QubitFreqCalibration": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null, "is_dynamic": 0}, "fringes": "Points(2) | qarange | (10, -10, -20)", "delays": "Points(71) | qarange | (100, 800, 10)", "run_mode": "async"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}, "subplots": [2, 2], "freq_gap_threshold": 0.1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "XpiDetection", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null, "is_dynamic": 0}, "expect_value": 0.7, "scope": 0.1, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "DetuneCalibration": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "DetuneCalibration", "export_datetime": "2023-12-30 12:54:48", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"detune_list": "Points(36) | qarange | (-35, 25, 1)", "rough_n_list": "Points(3) | qarange | (6, 8, 1)", "fine_n_list": "Points(2) | qarange | (11, 13, 2)", "theta_type": "Xpi", "fine_precision": 0.15, "f12_opt": false, "run_mode": "sync"}, "analysis_options": {"child_ana_options": {"child_ana_options": {"quality_bounds": [0.95, 0.9, 0.8], "filter": {"window_length": 5, "polyorder": 3}, "fine": false, "prominence_divisor": 4.0}, "diff_threshold": 0.25}}}, "options_for_parallel_exec": {}}, "XYZTimingComposite": {"meta": {"username": "dpY2", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "batch_test"}, "exp_class_name": "XYZTimingComposite", "export_datetime": "2024-01-15 16:45:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"is_dynamic": 0, "time_perform": true, "delays": "Points(161) | qarange | (0.0, 100, 1.25)", "const_delay": 80, "z_pulse_params": {"time": 15, "amp": 0.1, "sigma": 0.1, "buffer": 5}}, "max_count": 2}, "analysis_options": {"child_ana_options": {"extract_mode": "fit_params", "quality_bounds": [0.95, 0.9, 0.75], "fit_model_name": "gauss_lo<PERSON>zian"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}