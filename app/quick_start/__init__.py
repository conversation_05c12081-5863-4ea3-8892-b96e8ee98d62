# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/10
# __author:       HanQ<PERSON> Shi

import os
import pathlib
import sys
import time
from typing import Union

from pyQCat.config import PyqcatConfig
from pyQCat.executor import Backend
from pyQCat.log import pyqlog
# from pyQCat.parallel.parallel_utils import run_parallel_backend
from pyQCat.structures import QDict
from pyQCat.concurrent.concurrent import start_merge_service


def get_user_file_path(
    username: str,
    default_path: str = ".pyqcat/.user",
    file_name: str = "config.conf",
    multiple_user_num: int = 0,
) -> Union[pathlib.Path, None]:
    """Get the relevant files in the visage user directory.

    Args:
        username (str): <PERSON><PERSON><PERSON> which used to load account.
        default_path (str): Default path.
        file_name (str): File name.
        multiple_user_num(int): Number of users to load.

    Returns:
        Path object.
    """
    if multiple_user_num:
        pos = default_path.find("/")
        default_path = default_path[:pos] + str(multiple_user_num) + default_path[pos:]
    config = pathlib.Path.home() / default_path / username / file_name
    if config.is_file():
        return config
    else:
        return None


def generate_system_params(
    username: str,
    password: str,
    multiple_user_num: int = 0,
    use_cache: bool = False,
    config_file: str = None,
) -> QDict:
    backend = QDict(
        username=username,
        password=password,
        config_file=config_file
        or get_user_file_path(username, multiple_user_num=multiple_user_num),
        use_cache=use_cache,
    )
    if not use_cache:
        env_qubits = [f"q{i}" for i in range(1, 73, 1)]
        env_coupler = [f"c{i}-{i + 1}" for i in range(1, 72, 1) if (i % 6) != 0] + [
            f"c{i}-{i + 6}" for i in range(1, 67, 1)
        ]
        env_bits = env_coupler + env_qubits
        max_point_unit = []
        return QDict(
            backend=backend,
            context=QDict(
                env_bits=env_bits,
                working_type="awg_bias",
                divide_type="character_idle_point",
                max_point_unit=max_point_unit,
                online_unit=[],
                f12_opt_bits=[],
                online=False,
                crosstalk=True,
                xy_crosstalk=True,
            ),
            use_simulator=True,
            auto_update=False,
            parallel_divide=QDict(
                freq_delta=10,
                freq_split=50,
                anharmonic=240,
                neighbor_limit=300,
                max_limit=400,
                xy_cross_threshold_low=0.01,
                xy_cross_threshold_high=0.05,
                check_xy_lo=True,
                mode="sub",
            ),
            xy_bf_divide=QDict(),
            m_bf_divide=QDict(),
        )
    else:
        return QDict(backend=backend)


def init_backend_v1(
    username: str,
    password: str,
    open_parallel: bool = False,
    multiple_user_num: int = 0,
    use_cache: bool = False,
) -> Backend:
    system = generate_system_params(username, password, multiple_user_num, use_cache)
    if len(sys.argv) > 1:
        for v in sys.argv[1:]:
            if v.startswith("log"):
                new_log_path = v.split("-")[-1]
                config = PyqcatConfig(
                    filename=system.backend.config_file, init_log=False
                )
                pre_log_path = (
                    config.settings["system"]["log_path"]
                    or config.settings["system"]["local_root"]
                )
                new_path = os.path.join(pre_log_path, new_log_path)
                config.settings["system"]["log_path"] = new_path
                new_file = os.path.join(
                    os.path.dirname(system.backend.config_file),
                    f"temp-{new_log_path}.conf",
                )
                config.to_file(new_file)
                system.backend.config_file = new_file

    backend = Backend(**system.backend)

    if open_parallel:
        # run_parallel_backend(conf_file=backend.context_manager.config)
        # time.sleep(1)
        start_merge_service(conf_file=backend.context_manager.config)

    backend.refresh()
    backend.system = system

    if not system.backend.use_cache:
        backend.context_manager.set_global_options(**system.context)
        pyqlog.info("Start Backend Success | Use Local Config!")
    else:
        pyqlog.info("Start Backend Success | Use Cache Config!")

    backend.infos()
    return backend
