# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/10
# __author:       <PERSON><PERSON><PERSON> <PERSON>


from pyQCat.experiments.batch import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.quick_start import init_backend_v1 as init_backend


if __name__ == "__main__":
    backend = init_backend("Miracle", "pz12138", open_parallel=True)
    batch = BatchRunner(backend)
    batch.set_experiment_options(
        param_path=r"E:\project\pyqcat-apps\app\quick_start\options.json",
        exp_retry=0,
        use_simulator=True,
        record_batch=True,
        quality_filter=False,
        use_config_unit=True,
        unified_dir=False,
        refresh_context=False,
        flows=["RBSingle"],
        physical_units=[
                        "q1",
                        "q2", "q3",
                        "q4",
                        'q5',
                        'q7', 'q8', 'q9', 'q10', 'q11', 'q12',
                        # 'q13', 'q14', 'q15', 'q16', 'q17', 'q18',
                        # 'q19', 'q20', 'q21', 
                        # 'q22', 
                        # 'q23', 'q24',
                        # 'q25', 'q26', 'q27', 'q28', 'q29',
                        # 'q30', 'q31', 'q32', 'q33', 'q34',
                        # 'q35', 'q36', 'q37', 'q38', 'q39',
                        # 'q40', 'q41', 'q42', 'q43', 'q44',
                        # 'q45', 'q46', 'q47', 'q48', 'q49',
                        # 'q50', 'q51', 'q52', 'q53', 'q54',
                        # 'q55', 'q56', 'q57', 'q58', 'q59',
                        # 'q60', 'q61', 'q62', 'q63', 'q64',
                        # 'q65', 'q66', 'q67', 'q68', 'q69',
                        # 'q70', 'q71', 'q72'
                        ],
    )
    batch.run()
