{"context_params": {"ac_switch": false, "crosstalk": true, "divide_type": "character_idle_point", "env_bits": ["q1", "q2", "q3", "q4", "q5", "q7", "q8", "q9", "q10", "q11", "q12", "q13", "q14", "q15", "q16", "q17", "q18", "q19", "q20", "q21", "q22", "q23", "q24", "q25", "q26", "q27", "q28", "q29", "q30", "q31", "q32", "q33", "q34", "q35", "q36", "q37", "q38", "q39", "q40", "q41", "q42", "q43", "q45", "q46", "q47", "q48", "q49", "q50", "q51", "q53", "q55", "q56", "q57", "q58", "q60", "q61", "q62", "q63", "q65", "q66", "q67", "q68", "q69", "q70", "q71", "q72"], "env_couplers": ["c1-2", "c2-3", "c3-4", "c4-5", "c5-6", "c7-8", "c8-9", "c10-11", "c11-12", "c13-14", "c14-15", "c15-16", "c16-17", "c17-18", "c19-20", "c20-21", "c21-22", "c22-23", "c23-24", "c25-26", "c26-27", "c27-28", "c28-29", "c29-30", "c31-32", "c32-33", "c33-34", "c34-35", "c35-36", "c37-38", "c38-39", "c39-40", "c40-41", "c41-42", "c43-44", "c44-45", "c45-46", "c47-48", "c49-50", "c50-51", "c51-52", "c52-53", "c53-54", "c55-56", "c56-57", "c57-58", "c58-59", "c59-60", "c61-62", "c62-63", "c63-64", "c64-65", "c65-66", "c67-68", "c68-69", "c69-70", "c70-71", "c71-72", "c1-7", "c2-8", "c3-9", "c4-10", "c5-11", "c6-12", "c7-13", "c8-14", "c9-15", "c10-16", "c11-17", "c12-18", "c13-19", "c14-20", "c15-21", "c16-22", "c17-23", "c18-24", "c19-25", "c20-26", "c21-27", "c22-28", "c23-29", "c24-30", "c25-31", "c26-32", "c27-33", "c28-34", "c29-35", "c30-36", "c31-37", "c32-38", "c33-39", "c34-40", "c35-41", "c36-42", "c37-43", "c38-44", "c39-45", "c40-46", "c41-47", "c42-48", "c43-49", "c44-50", "c45-51", "c46-52", "c47-53", "c48-54", "c49-55", "c50-56", "c51-57", "c52-58", "c53-59", "c55-61", "c56-62", "c57-63", "c58-64", "c60-66", "c61-67", "c62-68", "c63-69", "c64-70", "c65-71", "c66-72"], "f12_opt_bits": ["q1", "q4", "q5", "q7", "q9", "q10", "q12", "q16", "q18", "q20", "q25", "q26", "q28", "q29", "q30", "q31", "q32", "q33", "q35", "q36", "q38", "q39", "q42", "q46", "q48", "q49", "q50", "q53", "q55", "q57", "q60", "q63", "q66", "q69", "q70"], "max_point_units": ["q1", "q2", "q3", "q4", "q5", "q7", "q8", "q9", "q10", "q11", "q12", "q13", "q14", "q15", "q16", "q17", "q18", "q19", "q20", "q23", "q25", "q26", "q27", "q28", "q29", "q30", "q31", "q32", "q33", "q34", "q35", "q36", "q37", "q38", "q39", "q40", "q41", "q42", "q43", "q45", "q46", "q47", "q48", "q49", "q50", "q51", "q53", "q55", "q56", "q57", "q58", "q61", "q62", "q63", "q66", "q67", "q68", "q69", "q70", "q71", "q72", "q21", "q24", "q60", "q65"], "online": false, "online_bits": ["q1", "q2", "q3", "q4", "q5", "q7", "q8", "q9", "q10", "q11", "q12", "q13", "q14", "q15", "q16", "q17", "q18", "q19", "q20", "q21", "q22", "q23", "q24", "q25", "q26", "q27", "q28", "q29", "q30", "q31", "q32", "q33", "q34", "q35", "q36", "q37", "q38", "q39", "q40", "q41", "q42", "q43", "q45", "q46", "q47", "q48", "q49", "q50", "q51", "q53", "q55", "q56", "q57", "q58", "q60", "q61", "q62", "q63", "q65", "q66", "q67", "q68", "q69", "q70", "q71", "q72"], "online_couplers": ["c1-2", "c1-7", "c10-11", "c10-16", "c11-12", "c11-17", "c12-18", "c13-14", "c13-19", "c14-15", "c14-20", "c15-16", "c15-21", "c16-17", "c17-18", "c17-23", "c19-20", "c19-25", "c2-3", "c2-8", "c20-21", "c20-26", "c21-27", "c23-29", "c25-26", "c25-31", "c26-27", "c26-32", "c27-28", "c27-33", "c28-29", "c28-34", "c29-30", "c29-35", "c3-4", "c3-9", "c30-36", "c31-32", "c31-37", "c32-33", "c32-38", "c33-34", "c33-39", "c34-35", "c34-40", "c35-36", "c35-41", "c36-42", "c37-38", "c37-43", "c38-39", "c39-40", "c39-45", "c4-10", "c4-5", "c40-41", "c40-46", "c41-42", "c41-47", "c42-48", "c43-49", "c45-46", "c45-51", "c47-48", "c47-53", "c49-50", "c49-55", "c5-11", "c50-51", "c50-56", "c51-57", "c55-61", "c57-58", "c57-63", "c61-62", "c61-67", "c62-63", "c62-68", "c63-69", "c66-72", "c67-68", "c68-69", "c69-70", "c7-13", "c7-8", "c71-72", "c8-14", "c8-9", "c9-15"], "physical_units": [], "readout_type": "01", "working_type": "awg_bias", "xy_crosstalk": true}, "exp_options": {"ac_prepare_time": 0, "fidelity_correct_type": "least-sq", "register_pulse_save": false, "schedule_flag": false, "sparse_flag": false}}