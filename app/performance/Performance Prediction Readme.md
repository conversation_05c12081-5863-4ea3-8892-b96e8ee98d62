# Performance Prediction Tools

性能预测请使用deal_data.py.脚本基于实验测试汇总数据生成的success_task.json文件。

## 加载测试数据

1. 加载测试数据首先需要配置测试数据的读取路径,``file_path``, 该路径为全局变量.
2. 配置需要加载的文件名称,注意和文件路径匹配，默认的是指定了文件类型，填入的时候仅仅填入文件名即可。

```python
file_path = "/home/<USER>/demo/{}.json"
file_name = "old_sync_ol"
```

## 测试数据转表格

测试数据汇总的时候默认存储是以json的形式存储，保存原始数据，是一种便于程序读写的形式，不利于人员直观获取数据。所以增加了json数据转excel表格，将重要的数据直接计算出结果保存下来，便于后续横向比较和阅读。

```python
#调用转换函数，将文件保存标志置位True,会将数据保存至当前文件夹下。
trans_data(file_name,True)
```

## 数据拟合预测性能数据

目前在项目中采用了多元线性拟合了``bit``, ``loop``,``deep``三个参数对于当前``Qstream``, ``storm``的性能影响。

使用方式如下：

```python
# 设置需要拟合的类型， 拟合类型为Key， value包含两项， 前面是用于从task类中提取参数的lambda表达式， 后面表示测试数据单位，用于结果展示。
    fit_dict = {
        "qstream编译组包大小": [lambda x: x.mem["q_compiler"], MEM_],
        "qstream解析后大小": [lambda x: x.mem["q_parser"], MEM_],
        "qstream解析耗时": [lambda x: x.time["q_parser"], TIME_],
        "qstream编译耗时": [lambda x: x.time["q_compiler"], TIME_],
        "qstream采集耗时": [lambda x: x.time["q_collect"], TIME_],
        "qstream整体耗时": [lambda x: x.time["q_totol"], TIME_],
        "storm编译耗时": [lambda x: x.time["s_compiler"], TIME_],
    }
    # 调用拟合函数，并且输入需要预测的变量，用于预测对应场景下的数据。
    #前三个参数为预测数据的变量，依次分别为 比特数、 门深度、循环次数。
    fit_3_analysis([144, 300, 1000], [300, 1000], [100], task_list, fit_dict)

```

