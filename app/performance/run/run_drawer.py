# -*- coding: utf-8 -*-

from pathlib import Path

from app.performance.performer.types import ScanName
from app.performance.performer.core.drawer import Drawer

cur_path = Path.cwd()
conf_path = cur_path.parent / "conf"

pf_conf = conf_path / "performance.conf"
ol_json = conf_path / "online_conf.json"

print(conf_path)


def run_pf_drawer(scan_name: str, scan_values: list, doc_ids: list, save_path: str = None):
    """Only run Drawer."""
    draw_groups = {
        "QStreamMain": ["qstream.compile_time", "qstream.parse_time"],
        "QStreamOther": ["qstream.process_time", "qstream.task_analyze_time"],
        "QStreamMem": ["qstream.task_parser_mem", "qstream.task_compiler_mem"],
        "ChimeraDiy": [
            "chimera.low_level_schedule_time",
            "chimera.schedule_time",
            "chimera.send_task_time",
        ],
        "TotalMain": [
            "qstream.compile_time",
            "qstream.parse_time",
            "storm.compile_time",
            "storm.measure_time"
        ],
    }

    drawer_obj = Drawer(
        conf=str(pf_conf),
        scan_name=scan_name,
        scan_values=scan_values,
        doc_ids=doc_ids,
        draw_groups=draw_groups,
        save_path=save_path,
    )
    drawer_obj.run()


if __name__ == '__main__':
    save_path = r"F:\NewData\performance_data\desp_data\old_sync_pt\03-21\13.54.34-scan-deep\bak"

    scan_name = ScanName.DEEP.value
    scan_values = [300, 350, 400, 450, 500]
    doc_ids = [
        "65f975ff47792240899181c5",
        "65f98255f3e552ffe2186c0b",
        "65f98fe726308e5ed80b7472",
        "65f99fef0677d4a761373006",
        "65f9b1969ac24960341476f2"
    ]

    run_pf_drawer(scan_name, scan_values, doc_ids, save_path)
