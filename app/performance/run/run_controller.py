# -*- coding: utf-8 -*-

from pathlib import Path

from app.performance.performer.types import Scan<PERSON><PERSON>, ModeType
from app.performance.performer.core.drawer import Drawer
from app.performance.performer.core.controller import QCircuitController

cur_path = Path.cwd()
conf_path = cur_path.parent / "conf"

pf_conf = conf_path / "performance.conf"
ol_json = conf_path / "online_conf.json"

print(conf_path)


def run_pf_controller(scan_name: str, scan_list: list, mode_name: str = "sync", save_path: str = None):
    """Run QCircuitController."""
    per_ctrl = QCircuitController(
        conf=str(pf_conf),
        online_json=str(ol_json),
        scan_name=scan_name,
        scan_list=scan_list,
        mode=mode_name,
        save_path=save_path,
    )
    per_ctrl.default_dict = {
        ScanName.BIT_NUM.value: 10,
        ScanName.LOOP.value: 50,
        ScanName.DEEP.value: 200,
        ScanName.SHOT.value: 1000,
    }
    per_ctrl.run()

    doc_ids = per_ctrl.success_map.get("doc_ids") or []
    if doc_ids:
        draw_groups = {
            "QStreamMain": ["qstream.compile_time", "qstream.parse_time"],
            "QStreamOther": ["qstream.process_time", "qstream.task_analyze_time"],
            "QStreamMem": ["qstream.task_parser_mem", "qstream.task_compiler_mem"],
            "ChimeraDiy": [
                "chimera.low_level_schedule_time",
                "chimera.schedule_time",
                "chimera.send_task_time",
            ],
            "TotalMain": [
                "qstream.compile_time",
                "qstream.parse_time",
                "storm.compile_time",
                "storm.measure_time"
            ],
        }
        drawer_obj = Drawer(
            conf=str(pf_conf),
            scan_name=scan_name,
            scan_values=per_ctrl.success_map.get("scan_values"),
            doc_ids=doc_ids,
            draw_groups=draw_groups,
            save_path=per_ctrl.file_path,
        )
        drawer_obj.run()


if __name__ == '__main__':
    mode_name = ModeType.normal.value
    save_path = rf"E:\mv_correction\data\old_{mode_name}"

    scan_name = ScanName.BIT_NUM.value
    scan_list = list(range(5, 70, 5))

    run_pf_controller(scan_name, scan_list, mode_name, save_path)
