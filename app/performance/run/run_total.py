# -*- coding: utf-8 -*-

import os
import time
import json
from pathlib import Path

from loguru import logger

from app.performance.performer.types import <PERSON>an<PERSON><PERSON>, ModeType
from app.performance.performer.core.drawer import Drawer
from app.performance.performer.core.controller import QCircuitController
from app.performance.performer.tools.utilities import get_conf_params
from app.performance.performer.tools.sockets import DealerClient
from app.performance.performer.tools.mg_database import MgDatabase

cur_path = Path.cwd()
conf_path = cur_path.parent / "conf"

pf_conf = conf_path / "performance.conf"
ol_json = conf_path / "online_conf.json"

print(conf_path)


def create_dealer_client(s_url: str, identity: str = "") -> DealerClient:
    """Initial DealerClient object."""
    pid = os.getpid()
    id_name = f"{identity}_{pid}"
    client_obj = DealerClient(s_url, id_name)
    return client_obj


def create_db_client(mg_params: dict) -> MgDatabase:
    """Initial db client object."""
    # mg_params = self._conf_params.get("mongo", {})
    tdb_obj = MgDatabase(**mg_params)
    return tdb_obj


def create_file_path(save_path: str, mark: str) -> Path:
    """Get file prefix."""
    s_path = save_path or str(Path.cwd().parent / "result-performance")
    s_string = time.strftime("%Y-%m-%d/%H.%M.%S", time.localtime())

    file_path = Path(s_path) / f"{s_string}-{mark}"
    if not os.path.exists(file_path):
        os.makedirs(file_path, exist_ok=True)
    return file_path


def run_pf_total(
        scan_bit_num_list: list,
        scan_loop_list: list,
        scan_deep_list: list,
        scan_shot_list: list,
        mode_name: str = "sync",
        save_path: str = None,
):
    """Scan sequentially loop, deep, shot, bit_num."""
    total_dict = {}
    scan_name = ScanName.BIT_NUM.value
    file_path = create_file_path(save_path, "total")
    try:
        conf_params = get_conf_params(str(pf_conf))
        mg_params = conf_params.get("mongo", {})
        url_params = conf_params.get("url", {})
        s_url = url_params.get("storm")

        dealer_client = create_dealer_client(s_url, "performance")
        db_client = create_db_client(mg_params)

        for loop_val in scan_loop_list:
            for deep_val in scan_deep_list:
                for shot_val in scan_shot_list:
                    field = f"loop={loop_val}-deep={deep_val}-shot={shot_val}"
                    s_file_path = file_path / field
                    if not os.path.exists(str(s_file_path)):
                        os.makedirs(str(s_file_path), exist_ok=True)

                    logger.info("=" * 100)
                    logger.info(f"Fixed parameter: {field}")
                    logger.info(f"Scan {scan_name} list: {scan_bit_num_list}")
                    logger.info(f"Save path: {s_file_path}")

                    per_ctrl = QCircuitController(
                        conf=str(pf_conf),
                        online_json=str(ol_json),
                        scan_name=scan_name,
                        scan_list=scan_bit_num_list,
                        mode=mode_name,
                        save_path=save_path,
                        file_path=str(s_file_path),
                        dealer_client=dealer_client,
                    )
                    per_ctrl.default_dict = {
                        ScanName.BIT_NUM.value: 10,
                        ScanName.LOOP.value: loop_val,
                        ScanName.DEEP.value: deep_val,
                        ScanName.SHOT.value: shot_val,
                    }
                    per_ctrl.run()

                    logger.info(f"per_ctrl.success_map: {per_ctrl.success_map}")

                    doc_ids = per_ctrl.success_map.get("doc_ids") or []
                    if doc_ids:
                        draw_groups = {
                            "QStreamMain": ["qstream.compile_time", "qstream.parse_time"],
                            "QStreamOther": ["qstream.process_time", "qstream.task_analyze_time"],
                            "QStreamMem": ["qstream.task_parser_mem", "qstream.task_compiler_mem"],
                            "ChimeraDiy": [
                                "chimera.low_level_schedule_time",
                                "chimera.schedule_time",
                                "chimera.send_task_time",
                            ],
                            "TotalMain": [
                                "qstream.compile_time",
                                "qstream.parse_time",
                                "storm.compile_time",
                                "storm.measure_time"
                            ],
                        }

                        drawer_obj = Drawer(
                            conf=str(pf_conf),
                            scan_name=scan_name,
                            scan_values=per_ctrl.success_map.get("scan_values"),
                            doc_ids=doc_ids,
                            draw_groups=draw_groups,
                            save_path=per_ctrl.file_path,
                            db_client=db_client,
                        )

                        drawer_obj.run()
                        plot_data_dict = drawer_obj._plot_data_dict
                    else:
                        plot_data_dict = {}
                    total_dict.update(
                        {
                            field: {
                                "file_path": per_ctrl.file_path,
                                "success": per_ctrl.success_map,
                                "plot": plot_data_dict,
                            }
                        }
                    )
    except Exception as err:
        logger.error(f"run error: {err}")

    logger.info(f"total_dict: {total_dict}")
    file_name = f"total.json"
    json_file = Path(file_path) / file_name
    with open(str(json_file), mode="w", encoding="utf-8") as fp:
        json.dump(total_dict, fp, ensure_ascii=False, indent=4)


if __name__ == "__main__":
    mode_name = ModeType.normal.value
    save_path = rf"E:\mv_correction\data\old_{mode_name}_pt"

    scan_bit_num_list = list(range(5, 70, 5))
    scan_loop_list = list(range(50, 1050, 50))
    scan_deep_list = list(range(100, 1100, 100))
    scan_shot_list = [1000]

    run_pf_total(
        scan_bit_num_list,
        scan_loop_list,
        scan_deep_list,
        scan_shot_list,
        mode_name,
        save_path
    )
