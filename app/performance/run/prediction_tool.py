import json
import time
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
import numpy as np

TIME_ = "ms"
MEM_ = "MB"
file_path = "/home/<USER>/demo/{}.json"

# base tools.


def trans_string_time_ms(string: str) -> int:
    """Trans string to time ms. Like: 2024-02-22 15:51:53.345."""
    tail_str = string.rsplit(".", 1)[-1]
    tail_len = len(tail_str)
    tail_s = float(int(tail_str) / (10**tail_len))
    if tail_len <= 6:
        time_str = string
    else:
        time_str = string[: -(tail_len - 6)]
    time_tup = time.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
    time_s = time.mktime(time_tup)
    end_s = time_s + tail_s
    end_ms = int(end_s * 1e3)
    return end_ms


def calculate_time(data_1, data_2):
    """
    calculate use time by time date string. such as  2024-02-22 15:51:53.345,  2024-02-22 15:51:56.345
    """
    return trans_string_time_ms(data_2) - trans_string_time_ms(data_1)


class Task:
    """
    task msg class.
    """

    def __init__(self, doc_id, bit, loop, deep, shot) -> None:
        self.doc_id = doc_id
        self.bit = bit
        self.loop = loop
        self.deep = deep
        self.shot = shot
        self.mem = {
            "q_compiler": None,
            "q_parser": None,
        }
        self.time = {
            "q_totol": None,
            "q_compiler": None,
            "q_parser": None,
            "q_collect": None,
            "q_deal": None,
            "s_compiler": None,
            "s_deal": None,
        }

    def deal_detail_time(self, data_dict):
        """
        load task test time and memory msg to task cache dict.
        """
        if data_dict["qstream"]:
            self.mem["q_parser"] = (
                round(
                    int(data_dict["qstream"].get("task_parser_mem", 0)) / 1024 / 1024, 2
                )
                or None
            )
            self.mem["q_compiler"] = (
                round(
                    int(data_dict["qstream"].get("task_compiler_mem", 0)) / 1024 / 1024
                )
                or None
            )
            self.time["q_totol"] = calculate_time(*data_dict["qstream"]["total_time"])
            self.time["q_compiler"] = calculate_time(
                *data_dict["qstream"]["compile_time"]
            )
            self.time["q_parser"] = calculate_time(*data_dict["qstream"]["parse_time"])
            self.time["q_collect"] = calculate_time(
                *data_dict["qstream"]["process_time"]
            )
            self.time["q_deal"] = calculate_time(
                data_dict["qstream"]["compile_time"][1],
                data_dict["qstream"]["task_analyze_time"][1],
            )

        if data_dict["storm"]:
            self.time["s_compiler"] = calculate_time(
                *data_dict["storm"]["compile_time"]
            )
        if data_dict["storm"] and data_dict["qstream"]:
            self.time["s_deal"] = calculate_time(
                data_dict["qstream"]["total_time"][1],
                data_dict["storm"]["measure_time"][1],
            )

    def to_excel(self):
        """
        Generate dictionary data without nesting for excel.
        """
        res_dict = self.__dict__
        mem = res_dict.pop("mem")
        use_time = res_dict.pop("time")
        for key, value in mem.items():
            res_dict.update({f"[M]{key}({MEM_})": value})
        for key, value in use_time.items():
            res_dict.update({f"[T]{key}({TIME_})": value})

        return res_dict


# load test data.


def read_json_data(filepath: str) -> dict:
    """
    load test data by file.
    """
    with open(filepath, "r") as f:
        data = json.load(f)
        return data


def save_tasks(task_list: list[Task], file_name: str):
    data_list = [x.to_excel() for x in task_list]
    df = pd.DataFrame(data_list)
    df.to_excel(f"{file_name}.xlsx")


def deal_data(data: dict) -> list[Task]:
    task_list = []
    for key, value in data["total_dict"].items():
        doc_id = value["doc_id"]
        parser_key = {}
        for x in key.split("-"):
            x = x.split("=")
            parser_key.update({x[0]: int(x[1])})

        task = Task(
            doc_id=doc_id,
            bit=parser_key.get("bit_num", None),
            loop=parser_key.get("loop", None),
            deep=parser_key.get("deep", None),
            shot=parser_key.get("shot", None),
        )
        task_list.append(task)
        task_detail = data["doc_dict"].get(doc_id, None)
        if task_detail:
            task.deal_detail_time(task_detail)

    return task_list


def trans_data(file_name, save: bool = False) -> list[Task]:
    task_list = deal_data(read_json_data(file_path.format(file_name)))
    if save:
        save_tasks(task_list, file_name)
    return task_list


def get_scan_loop(task_list):
    loop_list = list(set([x.loop for x in task_list]))
    bit_list = list(set([x.bit for x in task_list]))
    shot_list = set([x.shot for x in task_list])
    deep_list = list(set([x.deep for x in task_list]))
    loop_list.sort()
    bit_list.sort()
    deep_list.sort()
    shot_list.sort()
    print("loop", loop_list)
    print("bit", bit_list)
    print("shot", shot_list)
    print("deep", deep_list)
    return bit_list, deep_list, loop_list, shot_list


def plot_data(loop_list, task_list):
    for loop in loop_list:
        temp_task_list = [x for x in task_list if x.loop == loop and x.bit == 60 and x.mem["q_compiler"] is not None]
        plt.plot( [x.deep for x in temp_task_list], [x.mem["q_compiler"] for x in temp_task_list], label = f"loop={loop}")
    plt.show()

    # temp_task_list = [x for x in task_list if x.loop == 100 and x.shot == 1000 and x.deep == 300 and x.mem["q_compiler"] is not None]
    # plt.plot( [x.bit for x in temp_task_list], [x.mem["q_compiler"] for x in temp_task_list])

    # temp_task_list = [x for x in task_list if x.loop == 200 and x.shot == 1000 and x.deep == 300 and x.mem["q_compiler"] is not None]
    # plt.plot( [x.bit for x in temp_task_list], [x.mem["q_compiler"] for x in temp_task_list])

    # temp_task_list = [x for x in task_list if x.loop == 100 and x.shot == 1000 and x.deep == 250 and x.mem["q_compiler"] is not None]
    # plt.plot( [x.bit for x in temp_task_list], [x.mem["q_compiler"] for x in temp_task_list])

    # temp_task_list = [x for x in task_list if x.loop == 200 and x.shot == 1000 and x.deep == 250 and x.mem["q_compiler"] is not None]
    # plt.plot( [x.bit for x in temp_task_list], [x.mem["q_compiler"] for x in temp_task_list])
    # plt.show()

    # temp_task_list = [x for x in task_list if x.loop == 100 and x.shot == 1000 and x.deep == 300 and x.time["q_compiler"] is not None]
    # plt.plot( [x.bit for x in temp_task_list], [x.time["q_compiler"] for x in temp_task_list])

    # temp_task_list = [x for x in task_list if x.loop == 200 and x.shot == 1000 and x.deep == 300 and x.time["q_compiler"] is not None]
    # plt.plot( [x.bit for x in temp_task_list], [x.time["q_compiler"] for x in temp_task_list])

    # temp_task_list = [x for x in task_list if x.loop == 100 and x.shot == 1000 and x.deep == 250 and x.time["q_compiler"] is not None]
    # plt.plot( [x.bit for x in temp_task_list], [x.time["q_compiler"] for x in temp_task_list])

    # temp_task_list = [x for x in task_list if x.loop == 200 and x.shot == 1000 and x.deep == 250 and x.time["q_compiler"] is not None]
    # plt.plot( [x.bit for x in temp_task_list], [x.time["q_compiler"] for x in temp_task_list])

    # temp_task_list = [x for x in task_list if x.loop == 100 and x.shot == 1000 and x.deep == 250 and x.time["q_parser"] is not None]
    # plt.plot( [x.bit for x in temp_task_list], [x.time["q_parser"] for x in temp_task_list])

    # temp_task_list = [x for x in task_list if x.loop == 200 and x.shot == 1000 and x.deep == 250 and x.time["q_parser"] is not None]
    # plt.plot( [x.bit for x in temp_task_list], [x.time["q_parser"] for x in temp_task_list])
    # plt.show()
    pass


# fit model.


def fit_3_data(task_list: list[Task], key):
    x = []
    y = []
    for task in task_list:
        if key(task):
            x.append([task.bit, task.deep, task.loop])
            y.append(key(task))

    x = np.asarray(x)
    y = np.asarray(y)
    model = LinearRegression().fit(x, y)
    print("模型系数: [bit, deep, loop]", model.coef_)
    print("截距:", model.intercept_)

    def calculate(bit, deep, loop):
        return round(model.predict([[bit, deep, loop]])[0], 2)

    return calculate


def calculte_result(bit, deep, loop, func, msg, unit):
    print(
        f"| 估计 | {msg} | {bit}bit|{deep}deep|{loop}loop | will use {func(bit, deep, loop)}({unit})|"
    )


def fit_3_analysis(bit_list, deep_list, loop_list, task_list, fit_dict):
    for msg, value in fit_dict.items():
        print(f"|{msg:^75}|\n")
        key, unit = value
        fit_func = fit_3_data(task_list, key)
        for bit in bit_list:
            for deep in deep_list:
                for loop in loop_list:
                    calculte_result(bit, deep, loop, fit_func, msg, unit)

        print("\n\n")


if __name__ == "__main__":
    # trans data
    # file_list = ["new_sync_pt", "old_sync_pt", "old_sync_ol"]
    # for x in file_list:
    #     trans_data(x, True)

    # load test data.
    file_name = "old_sync_ol"
    task_list = trans_data(file_name)

    # plot_data([100, 200], task_list)

    # fit
    fit_dict = {
        "qstream编译组包大小": [lambda x: x.mem["q_compiler"], MEM_],
        "qstream解析后大小": [lambda x: x.mem["q_parser"], MEM_],
        "qstream解析耗时": [lambda x: x.time["q_parser"], TIME_],
        "qstream编译耗时": [lambda x: x.time["q_compiler"], TIME_],
        "qstream采集耗时": [lambda x: x.time["q_collect"], TIME_],
        "qstream整体耗时": [lambda x: x.time["q_totol"], TIME_],
        "storm编译耗时": [lambda x: x.time["s_compiler"], TIME_],
    }
    fit_3_analysis([144, 300, 1000], [300, 1000], [100], task_list, fit_dict)
