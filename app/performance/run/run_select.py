# -*- coding: utf-8 -*-

import os
import time
import json

from pathlib import Path

from app.performance.performer.types import <PERSON>an<PERSON><PERSON>
from app.performance.performer.tools.utilities import get_conf_params
from app.performance.performer.tools.mg_database import MgDatabase
from app.performance.performer.core.drawer import Drawer

cur_path = Path.cwd()
conf_path = cur_path.parent / "conf"

pf_conf = conf_path / "performance.conf"
ol_json = conf_path / "online_conf.json"

print(conf_path)


def create_file_path(save_path: str, mark: str) -> Path:
    """Get file prefix."""
    s_path = save_path or str(Path.cwd().parent / "result-performance")
    s_string = time.strftime("%Y-%m-%d/%H.%M.%S", time.localtime())

    file_path = Path(s_path) / f"{s_string}-{mark}"
    if not os.path.exists(file_path):
        os.makedirs(file_path, exist_ok=True)
    return file_path


def create_db_client(mg_params: dict) -> MgDatabase:
    """Initial db client object."""
    # mg_params = self._conf_params.get("mongo", {})
    tdb_obj = MgDatabase(**mg_params)
    return tdb_obj


def extract_success(data_dir: str, mg_client: MgDatabase) -> dict:
    """Extract success information."""
    bit_num_values = []
    loop_values = []
    deep_values = []
    shot_values = []

    total_dict = {}
    all_doc_ids = []
    data_path = Path(data_dir)
    sub_path_list = os.listdir(data_dir)
    for sub_path in sub_path_list:
        file = data_path / sub_path / "task.json"
        try:
            with open(file, mode="r", encoding="utf-8") as fp:
                task_dict = json.load(fp)
        except:
            task_dict = {}
        success_map = task_dict.get("success", {})
        if success_map:
            scan_values = success_map.get("scan_values", [])
            task_ids = success_map.get("task_ids", [])
            doc_ids = success_map.get("doc_ids", [])
            all_doc_ids.extend(doc_ids)

            # default scan bit_num
            for idx, scan_val in enumerate(scan_values):
                field_name = f"{sub_path}-bit_num={scan_val}"
                s_dict = {
                    "task_id": task_ids[idx],
                    "doc_id": doc_ids[idx],
                }
                total_dict.update({field_name: s_dict})
                if scan_val not in bit_num_values:
                    bit_num_values.append(scan_val)

            info_list = sub_path.split("-")
            for once_info in info_list:
                field, value = once_info.split("=", 1)
                if value.isdigit():
                    value = int(value)
                    if field == ScanName.LOOP.value and value not in loop_values:
                        loop_values.append(value)
                    elif field == ScanName.DEEP.value and value not in deep_values:
                        deep_values.append(value)
                    elif field == ScanName.SHOT.value and value not in shot_values:
                        shot_values.append(value)

    doc_dict = {}
    if all_doc_ids:
        doc_data_list = mg_client.query_tasks(all_doc_ids)
        for idx, s_dict in enumerate(doc_data_list):
            doc_id = s_dict.get("task_id") or f"{all_doc_ids[idx]}"
            doc_dict.update({doc_id: s_dict})

    bit_num_values.sort()
    loop_values.sort()
    deep_values.sort()
    shot_values.sort()
    print(f"bit_num_values: {bit_num_values}")
    print(f"loop_values: {loop_values}")
    print(f"deep_values: {deep_values}")
    print(f"shot_values: {shot_values}")

    extract_success_dict = {
        "loop_values": loop_values,
        "deep_values": deep_values,
        "shot_values": shot_values,
        "bit_num_values": bit_num_values,
        "total_dict": total_dict,
        "doc_dict": doc_dict
    }
    return extract_success_dict


def select_scan_plot(scan_name: str, result_dict: dict, save_path: str, mg_client: MgDatabase):
    """Select scan member to plot. scan_name just support `loop`, `deep`."""
    file_path = create_file_path(save_path, f"select_{scan_name}")

    bit_num_values = result_dict.get("bit_num_values", [])
    loop_values = result_dict.get("loop_values", [])
    deep_values = result_dict.get("deep_values", [])
    shot_values = result_dict.get("shot_values", [])
    total_dict = result_dict.get("total_dict", {})
    doc_dict = result_dict.get("doc_dict", {})

    draw_groups = {
        "QStreamMain": ["qstream.compile_time", "qstream.parse_time"],
        "QStreamOther": ["qstream.process_time", "qstream.task_analyze_time"],
        "QStreamMem": ["qstream.task_parser_mem", "qstream.task_compiler_mem"],
        "ChimeraSome": [
            "chimera.low_level_schedule_time",
            "chimera.schedule_time",
            "chimera.send_task_time",
        ],
        "TotalMain": [
            "qstream.compile_time",
            "qstream.parse_time",
            "storm.compile_time",
            "storm.measure_time"
        ],
    }

    for shot in shot_values:
        for bit_num in bit_num_values:
            if scan_name == "loop":
                next_name = "deep"
                next_scans = deep_values
                last_scans = loop_values
            else:
                next_name = "loop"
                next_scans = loop_values
                last_scans = deep_values
            for next_val in next_scans:
                draw_path = str(file_path / f"shot={shot}-bit_num={bit_num}-{next_name}={next_val}")
                scan_values = []
                doc_ids = []
                s_doc_dict = {}
                for last_val in last_scans:
                    if scan_name == "loop":
                        field = f"loop={last_val}-deep={next_val}-shot={shot}-bit_num={bit_num}"
                    else:
                        field = f"loop={next_val}-deep={last_val}-shot={shot}-bit_num={bit_num}"
                    f_dict = total_dict.get(field, {})
                    doc_id = f_dict.get("doc_id", "")
                    if doc_id:
                        scan_values.append(last_val)
                        doc_ids.append(doc_id)
                        doc_data = doc_dict.get(doc_id, {})
                        s_doc_dict.update({doc_id: doc_data})
                drawer_obj = Drawer(
                    conf=str(pf_conf),
                    scan_name=scan_name,
                    scan_values=scan_values,
                    doc_ids=doc_ids,
                    draw_groups=draw_groups,
                    save_path=str(draw_path),
                    db_client=mg_client,
                    doc_dict=s_doc_dict,
                )
                drawer_obj.run()


if __name__ == '__main__':
    total_dir = r"F:\NewData\performance_data\desp_data\new_sync_pt\03-18 20.24.15-total"
    total_path = Path(total_dir)

    select_path = total_path / "reset_scan"
    select_json = str(Path(total_dir) / "select_success.json")

    conf_params = get_conf_params(str(pf_conf))
    mg_params = conf_params.get("mongo", {})
    db_client = create_db_client(mg_params)

    # # Create select_dict and save.
    select_dict = extract_success(total_dir, db_client)
    with open(select_json, mode="w", encoding="utf-8") as fp:
        json.dump(select_dict, fp, ensure_ascii=False, indent=4)
    print(f"save success json: {select_json}")

    # # Load select_dict.
    # with open(select_json, mode="r", encoding="utf-8") as fp:
    #     select_dict = json.load(fp)

    loop_values = select_dict.get("loop_values", [])
    deep_values = select_dict.get("deep_values", [])
    if len(loop_values) > 2:
        select_scan_plot("loop", select_dict, str(select_path), db_client)
    if len(deep_values) > 2:
        select_scan_plot("deep", select_dict, str(select_path), db_client)
