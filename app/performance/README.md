# Performance

## 1. 简介

工具 performance 用于测控软件的性能测试， 通过下发线路给 storm，storm 和 qstream 在执行任务时记录每个阶段耗时，performance 通过扫描一组线路，分析该变量对耗时的影响，目前支持扫描的变量有四种：

- bit_num:  控制线路中 bit 数量；
- loop: 控制线路的大循环数；
- deep: 控制线路的门深度；
- shot: 控制实验重复采集次数；



performance  在 项目 pyqcat-apps  中目录 pyqcat-apps/app/performance

```
performance 目录结构

├─performance
   ├─conf
   |  ├─online_conf.json
   |  └─performance.conf
   ├─performer
   └─run
      ├─run_controler.py
      ├─run_drawer.py
      ├─run_total.py
      └─run_select.py
```

- conf：该目录下存放 配置文件
- performer:  工具的核心代码
- run:   该目录下存放 测试脚本



## 2. 配置文件

配置文件有两个：

- online_conf.json :  直接使用 storm 项目的  online_conf.json,  这里只使用了该json 中 online_bits， online_couplers 两个参数，生成线路时使用；

- performance.conf  具体配置如下：

  ```
  # performance.conf
  
  [url]
  storm = "tcp://************:21101"  # storm 服务地址
  
  [mongo]
  host = "************"               # 记录统计时间的 mongo 服务器地址，一般："************" 或 "***********"
  port = 27017
  user = "ck"
  password = "C5mTH1HRnFTgD5mY"
  
  [log]
  log_path = "/var/log"               # 记录 performance 日志路径
  
  [gate]
  s_gate_width = 30.0                 # 被测试量子平台单门长度，主要是用于生成线路
  d_gate_width = 40.0                 # 被测试量子平台双门长度，主要是用于生成线路
  
  ```

  

## 3. 使用方法

介绍各个脚本使用, 先介绍下两个枚举类，后面脚本使用参数中会出现：

```python
# -*- coding: utf-8 -*-

from enum import Enum


class ModeType(Enum):
    normal = "sync"
    special = "async"


class ScanName(Enum):
    BIT_NUM = "bit_num"
    LOOP = "loop"
    DEEP = "deep"
    SHOT = "shot"

    
# ModeType 有两种模式：
# normal： 逐条下发线路，一条线路下发后，storm 执行完成后再下发下一条
# specail： 一次性下发所有线路，最后等待 storm 执行完成 

# ScanName 设置扫描名称支持四种选择
```



### run_controller

脚本 run_controller.py,  用于设置一个扫描变量，下发线路，并根据耗时统计绘图

```Python
# run_controller.py

...


if __name__ == '__main__':
    mode_name = ModeType.normal.value
    save_path = rf"E:\mv_correction\data\old_{mode_name}"

    scan_name = ScanName.BIT_NUM.value
    scan_list = list(range(5, 70, 5))

    run_pf_controller(scan_name, scan_list, mode_name, save_path)
```

参数：

- mode_name:  设置执行模式，默认设置  ModeType.normal.value 普通模式
- save_path：设置结果保存路径
- scan_name：设置扫描变量名，默认扫描 bit_num
- scan_list: 设置扫描列表，用户根据 scan_name 设置



### run_drawer

脚本 run_drawer.py，用于离线绘图，比如用户已经执行完某一组几个实验，但是没全部执行完成，根据已经执行几个实验，进行绘图

```Python
# run_drawer.py

...


if __name__ == '__main__':
    save_path = r"F:\desp_data\old_sync_pt\03-21\13.54.34-scan-deep\bak"

    scan_name = ScanName.DEEP.value
    scan_values = [300, 350, 400, 450, 500]
    doc_ids = [
        "65f975ff47792240899181c5",
        "65f98255f3e552ffe2186c0b",
        "65f98fe726308e5ed80b7472",
        "65f99fef0677d4a761373006",
        "65f9b1969ac24960341476f2"
    ]

    run_pf_drawer(scan_name, scan_values, doc_ids, save_path)
```

参数：

- save_path：设置绘图保存路径
- scan_name:  设置当前这组数据的扫描变量名
- scan_values:  设置完成实验扫描的值列表
- doc_ids:  设置完成实验的 doc_id, 要与 scan_values 保持对应；



### run_total

脚本 run_total.py ,  遍历扫描四个变量，依次扫描  loop, deep, shot,  bit_num ，该脚本执行比较耗时

```python
# run_total.py

...


if __name__ == "__main__":
    mode_name = ModeType.normal.value
    save_path = rf"E:\mv_correction\data\old_{mode_name}_pt"

    scan_bit_num_list = list(range(5, 70, 5))
    scan_loop_list = list(range(50, 1050, 50))
    scan_deep_list = list(range(100, 1100, 100))
    scan_shot_list = [1000]

    run_pf_total(
        scan_bit_num_list,
        scan_loop_list,
        scan_deep_list,
        scan_shot_list,
        mode_name,
        save_path
    )
```

参数：

- mode_name:  设置执行模式，默认设置  ModeType.normal.value 普通模式
- save_path：设置结果保存路径
- scan_bit_num_list:  设置扫描 bit_num  列表
- scan_loop_list:  设置扫描 loop 列表
- scan_deep_list:  设置扫描 deep 列表
- scan_shot_list:  设置扫描 shot  列表



### run_select

脚本 run_select.py, 这里是对 run_total.py 结果的数据处理，由于 run_total.py 最底层扫描的是 bit_num, 导致最终绘图只有 bit_num 的，run_select 将这些个执行成功的实验结果收集起来，然后再根据 loop, deep 为扫描量再次绘图

```python
# run_select.py

...


if __name__ == '__main__':
    total_dir = r"F:\NewData\performance_data\desp_data\new_sync_pt\03-18 20.24.15-total"
    total_path = Path(total_dir)

    select_path = total_path / "reset_scan"
    select_json = str(Path(total_dir) / "select_success.json")
    
    ...
```

该脚本只有一个参数：

- total_dir： 提供某次 run_total 结果目录



该基本运行结果：

- select_success.json ： 将  xx-total 目录下所有成功的实验 id, 耗时记录等收集起来
- reset_scan:  该目录存放将 loop, deep 作为 x 轴的绘图

