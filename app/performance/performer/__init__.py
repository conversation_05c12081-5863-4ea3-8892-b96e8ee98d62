# -*- coding: utf-8 -*-

import os
import sys
from pathlib import Path

from loguru import logger


def initial_logger():
    """Initial Logger object."""
    from .types import LogFormat
    from .tools.utilities import get_conf_params

    cur_path = Path.cwd()
    conf_path = cur_path.parent / "conf"
    pf_conf = conf_path / "performance.conf"
    conf_params = get_conf_params(str(pf_conf))
    log_params = conf_params.get("log", {})
    log_path = log_params.get("log_path", "")

    logger.remove()
    if sys.stdout:
        logger.add(sys.stdout, format=LogFormat.simple.value, level=20, colorize=True)

    if log_path:
        if not os.path.exists(log_path):
            os.makedirs(log_path, exist_ok=False)
        log_file = log_path + "/performance_{time:YYYY-MM-DD}.log"
        logger.add(log_file, format=LogFormat.detail.value, level=10, rotation="00:00")
        logger.info(f"Performance Log save path: {log_path}")
    logger.info(f"Performance start ...")


log_flag = False
if log_flag is False:
    initial_logger()
    log_flag = True
