# -*- coding: utf-8 -*-

from enum import Enum


class ScanName(Enum):
    BIT_NUM = "bit_num"
    LOOP = "loop"
    DEEP = "deep"
    SHOT = "shot"


class QProgType(Enum):
    normal = "normal"
    complex = "complex"
    without_cz = "without_cz"


class ModeType(Enum):
    normal = "sync"
    special = "async"


class LogFormat(Enum):
    simple = '<yellow>{time:YYYY-MM-DD HH:mm:ss.SSS}</yellow> | <level>{level: ^8}</level> | <level>{message}</level>'
    detail = '<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: ^8}</level> | ' \
             '<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>'
    pub = '<level>{message}</level>'


class ErrorCode(Enum):
    """Response Errcode value."""

    NO_ERROR = 0
    UNDEFINED_ERROR = 1
    TASK_PARAM_ERROR = 2
    JSON_ERROR = 3
    QUEUE_FULL = 4


class MsgTypeName(Enum):
    task_request = "MsgTask"
    task_request_ack = "MsgTaskAck"
    task_result = "MsgTaskResult"
    task_result_ack = "MsgTaskResultAck"


class ServerName(Enum):
    storm = "storm"
    qstream = "qstream"
    chimera = "chimera"
    qpilot = "qpilot"
