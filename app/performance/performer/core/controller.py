# -*- coding: utf-8 -*-

import os
import time
import json
import uuid
import random
from pathlib import Path
from typing import List, Dict

from loguru import logger

from ..types import ScanName, ModeType, MsgTypeName
from ..structures import (
    TaskRequest,
    TaskResultReply,
    TaskResponse,
    TaskParams,
    ConfigureParams,
)
from ..tools.sockets import DealerClient
from ..tools.create_prog import create_qprog_list
from ..tools.utilities import get_conf_params, get_json_params


class QCircuitController:
    """Control execute QCircuit class."""

    default_dict = {
        ScanName.BIT_NUM.value: 10,
        ScanName.LOOP.value: 2,
        ScanName.DEEP.value: 2,
        ScanName.SHOT.value: 1000,
    }

    default_list_dict = {
        ScanName.BIT_NUM.value: [5, 10, 20, 30, 40, 50, 60],
        ScanName.LOOP.value: [10, 50, 100, 200, 400, 700, 1000],
        ScanName.DEEP.value: [10, 50, 100, 200, 300, 500, 1000],
        ScanName.SHOT.value: [200, 500, 1000, 2000, 5000, 10000, 20000],
    }

    def __init__(
        self,
        conf: str,
        online_json: str,
        scan_name: str = "loop",
        scan_list: List = None,
        qprog_type: str = "complex",
        mode: str = "sync",
        task_priority: int = 1,
        compile_only: bool = False,
        save_path: str = None,
        file_path: str = None,
        dealer_client: "DealerClient" = None,
    ):
        """Initial object."""
        self._conf_params = get_conf_params(conf)
        self._online_params = get_json_params(online_json)

        self._scan_name = scan_name
        self._scan_list = scan_list or []
        self._qprog_type = qprog_type
        self._mode = mode
        self._task_priority = task_priority
        self._compile_only = compile_only
        self._save_path = save_path

        self._file_path = file_path or self._set_file_path()
        self._dealer_client = dealer_client or self._init_dealer_client("performance")
        self._serial_num = 0
        self._scan_values = []
        self._task_ids = []
        self._task_dict = {}
        self._success_map = {}

    @property
    def scan_name(self) -> str:
        """Return self._scan_name."""
        return self._scan_name

    @property
    def file_path(self) -> str:
        """Return self._scan_name."""
        return self._file_path

    @property
    def success_map(self) -> Dict:
        """Return self._success_map."""
        return self._success_map

    def _init_dealer_client(self, identity: str = "") -> DealerClient:
        """Initial DealerClient object."""
        pid = os.getpid()
        url_params = self._conf_params.get("url", {})
        s_url = url_params.get("storm")
        id_name = f"{identity}_{pid}"
        client_obj = DealerClient(s_url, id_name)
        return client_obj

    def _get_serial_num(self):
        """Get serial num."""
        self._serial_num += 1
        return self._serial_num

    def _generate_scan_params(self):
        """Get scan params. Generator."""
        gate_params = self._conf_params.get("gate", {})
        context_params = self._online_params.get("context_params", {})
        s_gate_width = float(gate_params.get("s_gate_width") or 30)
        d_gate_width = float(gate_params.get("d_gate_width") or 40)
        online_bits = context_params.get("online_bits") or []
        online_couplers = context_params.get("online_couplers") or []

        q_total = len(online_bits)
        names = list(self.default_list_dict.keys())
        special_field = ScanName.BIT_NUM.value

        # Check scan_list value.
        if self._scan_name not in names:
            logger.warning(
                f"Not support scan_name: {self._scan_name}, no set scan_list. "
                f"Just support names: {names}"
            )
            scan_list = self._scan_list or [1]
        else:
            default_list = self.default_list_dict.get(self._scan_name)
            scan_list = self._scan_list or default_list

        q_num_error = False
        for scan_val in scan_list:
            once_params = {}
            once_params.update(self.default_dict)
            if self._scan_name in names:
                once_params.update({self._scan_name: scan_val})

            # Check bit number is invalid or not.
            bit_num = once_params.get(special_field)
            if bit_num > q_total:
                bit_num = q_total
                if self._scan_name == special_field:
                    scan_val = q_total
                if q_num_error is True and self._scan_name == special_field:
                    break
                else:
                    q_num_error = True
            bit_list = random.sample(online_bits, k=bit_num)
            bit_list.sort(key=lambda x: int(x[1:]))
            logger.info(f"Select bit_list: {bit_list}")
            once_params.update(
                {
                    special_field: bit_num,
                    "bit_list": bit_list,
                }
            )

            qprog_list = create_qprog_list(
                qprog_type=self._qprog_type,
                q_bits=bit_list,
                c_list=online_couplers,
                s_gate_width=s_gate_width,
                d_gate_width=d_gate_width,
                gate_deep=once_params.get(ScanName.DEEP.value),
                loop=once_params.get(ScanName.LOOP.value),
            )
            serial_num = self._get_serial_num()
            task_id = "".join(str(uuid.uuid4()).split("-"))
            qprog_str = json.dumps(qprog_list)

            configure_obj = ConfigureParams(
                Shot=once_params.get(ScanName.SHOT.value),
                # IsProbCount=True,
                TaskPriority=1,
                CompileOnly=self._compile_only,
            )
            configure_dict = configure_obj.__dict__
            # configure_dict = {
            #     "Shot": once_params.get(ScanName.SHOT.value),
            #     "IsProbCount": True,
            #     "TaskPriority": 1,
            #     "CompileOnly": self._compile_only,
            # }

            tr_obj = TaskRequest(
                SN=serial_num,
                TaskId=task_id,
                ConvertQProg=qprog_str,
                Configure=configure_dict,
            )
            task_params = TaskParams(
                task_id=task_id,
                scan_name=self._scan_name,
                ctrl_params=once_params,
                task_request=tr_obj.__dict__,
            )
            yield scan_val, task_params

    def _receive_once_result(self) -> str:
        """Receive once task result."""
        task_id = ""
        time.sleep(0.001)
        msg_dict = self._dealer_client.recv()
        msg_type = msg_dict.get("MsgType", "")
        if msg_type == MsgTypeName.task_result.value:
            task_result = TaskResultReply(**msg_dict)
            task_id = task_result.TaskId
            task_params: "TaskParams" = self._task_dict.get(task_id)
            task_params.doc_id = task_result.DocId
            task_params.err_info = task_result.ErrInfo
            resp_obj = TaskResponse(
                MsgType=f"{msg_type}Ack",
                SN=task_result.SN,
            )
            self._dealer_client.send(resp_obj.__dict__)
        elif msg_type == MsgTypeName.task_request_ack.value:
            logger.info(f"Receive msg_dict: {msg_dict}")
        else:
            logger.info(f"Receive msg_dict: {msg_dict}")
        return task_id

    def _async_receive_task_result(self):
        """Async mode receive task result information."""
        task_signal_dict = dict.fromkeys(self._task_ids, False)
        while not all(task_signal_dict.values()):
            receive_id = self._receive_once_result()
            if receive_id in self._task_ids:
                task_signal_dict.update({receive_id: True})

    def _send_tasks(self):
        """Send tasks to storm."""
        logger.info(f"Send and Receive tasks, mode: {self._mode}, start...")
        for scan_val, task_params in self._generate_scan_params():
            task_id = task_params.task_id
            logger.info(
                f"Scan {self._scan_name} value {scan_val}, " f"task_id: {task_id}"
            )
            self._dealer_client.send(task_params.task_request)
            task_params.task_request.update({"ConvertQProg": "remove"})
            self._scan_values.append(scan_val)
            self._task_ids.append(task_id)
            self._task_dict.update({task_id: task_params})

            if self._mode == ModeType.normal.value:
                wait_flag = True
                while wait_flag:
                    time.sleep(0.001)
                    receive_id = self._receive_once_result()
                    if receive_id == task_id:
                        wait_flag = False

        if self._mode == ModeType.special.value:
            self._async_receive_task_result()
        logger.info(f"Receive tasks, mode: {self._mode}, end.")

    def _set_file_path(self) -> str:
        """Get file prefix."""
        s_path = self._save_path or str(Path.cwd().parent / "result-performance")
        s_string = time.strftime("%Y-%m-%d/%H.%M.%S", time.localtime())
        s_mark = f"scan-{self._scan_name}"

        file_path = Path(s_path) / f"{s_string}-{s_mark}"
        if not os.path.exists(file_path):
            os.makedirs(file_path, exist_ok=True)
        return str(file_path)

    def _save_tasks(self):
        """Save tasks information."""
        file_name = "task.json"
        file = Path(self._file_path) / file_name
        data = {
            "scan_name": self._scan_name,
            "mode": self._mode,
            "scan_values": self._scan_values,
            "task_ids": self._task_ids,
            "success": self._success_map,
            "task_dict": {
                key: value.__dict__ for key, value in self._task_dict.items()
            },
        }
        with open(str(file), mode="w", encoding="utf-8") as fp:
            json.dump(data, fp, ensure_ascii=False, indent=4)
        logger.info(f"Save json file: {file}")

    def _check_tasks(self):
        """Check task doc_id is or not."""
        scan_values = []
        task_ids = []
        doc_ids = []
        for idx, task_id in enumerate(self._task_ids):
            scan_val = self._scan_values[idx]
            task_params: "TaskParams" = self._task_dict.get(task_id)
            if task_params.doc_id and not task_params.err_info:
                scan_values.append(scan_val)
                task_ids.append(task_id)
                doc_ids.append(task_params.doc_id)
            else:
                err_info = task_params.err_info
                logger.warning(
                    f"scan {self._scan_name} value {scan_val} error: {err_info}"
                )
        self._success_map.update(
            {
                "scan_values": scan_values,
                "task_ids": task_ids,
                "doc_ids": doc_ids,
            }
        )
        logger.info(f"success_map: {self._success_map}")

    def run(self):
        """Controller Run logic."""
        self._send_tasks()
        self._check_tasks()
        self._save_tasks()
