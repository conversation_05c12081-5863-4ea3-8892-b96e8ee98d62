# -*- coding: utf-8 -*-

import os
import time
import json
import platform
from pathlib import Path
from typing import List, Dict, Union

import matplotlib.pyplot as plt
from matplotlib.figure import Figure
from matplotlib.backends.backend_svg import FigureCanvasSVG
from loguru import logger


if platform.system() == "Windows":
    DRAW_FONT = "Times New Roman"
else:
    DRAW_FONT = "DejaVu Sans"


from ..types import ServerName
from ..tools.mg_database import MgDatabase
from ..tools.utilities import get_conf_params, trans_string_time_ms


class Drawer:
    """Draw class."""

    def __init__(
        self,
        conf: str,
        scan_name: str = "loop",
        scan_values: List = None,
        doc_ids: List = None,
        draw_groups: Union[List, Dict] = None,
        save_path: str = None,
        db_client: "MgDatabase" = None,
        doc_dict: Dict = None
    ):
        """Initial object."""
        self._conf_params = get_conf_params(conf)

        self._scan_name = scan_name
        self._scan_values = scan_values or []
        self._doc_ids = doc_ids or []
        self._draw_groups = draw_groups or []
        self._save_path = save_path

        self._file_path = self._set_file_path()
        self._db_client = db_client or self._init_db_client()
        self._doc_dict = doc_dict or {}
        self._plot_data_dict = {}

    def _init_db_client(self) -> MgDatabase:
        """Initial db client object."""
        mg_params = self._conf_params.get("mongo", {})
        tdb_obj = MgDatabase(**mg_params)
        return tdb_obj

    @staticmethod
    def normal_plot(
        x,
        *y,
        labels: List[str] = None,
        xlabel: str = "X",
        ylabel: str = "Y",
        title: str = "Title",
        png_name: str = "demo.png",
    ):
        """Draw normal line plot."""
        font_dict = {
            "fontfamily": DRAW_FONT,
            "fontweight": "bold",
            "fontsize": 36,
        }

        font_demo = {
            "family": DRAW_FONT,
            "weight": "normal",
            "size": 24,
        }

        # fig, axis = plt.subplots(figsize=(20, 12))

        fig = Figure(figsize=(20, 12), tight_layout=True)
        _ = FigureCanvasSVG(fig)
        axis = fig.subplots(nrows=1, ncols=1)

        if title is not None:
            axis.set_title(title, **font_dict)
        axis.tick_params(axis="both", which="major", labelsize=24)

        for i, y_ in enumerate(y):
            axis.plot(x, y_, color=None, marker="x", markersize=9, linewidth=2.5, alpha=1.0)

        axis.legend(labels=labels, loc="upper left", prop=font_demo)
        axis.set_xlabel(xlabel, **font_dict)
        axis.set_ylabel(ylabel, **font_dict)
        axis.grid(True)

        # fig.tight_layout()
        fig.savefig(png_name, dpi=200)
        plt.close(fig)

    def _draw_data(
        self, data: Dict, x_label: str, t_mark: str = None, png_name: str = None
    ):
        """Plot data dict."""
        t_mark = t_mark or ""
        x_list = data.get(x_label)
        data.pop(x_label)
        labels = list(data.keys())
        default_png = f"{x_label}_performance.png"
        png_name = png_name or default_png

        y_label_map = {
            "time": "Time(ms)",
            "mem": "Memory(MB)"
        }
        title_map = {
            "time": f"Scan {x_label} {t_mark} Cost Time",
            "mem": f"Scan {x_label} {t_mark} Cost Memory",
        }
        mode = "time"
        mode_flags = [True if lab.endswith("mem") else False for lab in labels]
        if all(mode_flags):
            mode = "mem"

        y_label = y_label_map.get(mode)
        title = title_map.get(mode)

        try:
            self.normal_plot(
                x_list,
                *data.values(),
                labels=labels,
                xlabel=x_label,
                ylabel=y_label,
                title=title,
                png_name=png_name,
            )
            logger.info(f"Save name: {png_name}")
        except Exception as err:
            logger.warning(f"Plot {labels} error: {err}")

    def _check_value(self):
        """Check scan_values and doc_ids."""
        doc_len = len(self._doc_ids)
        scan_len = len(self._scan_values)
        if doc_len:
            new_scan_values = list(range(1, doc_len + 1))
            if self._scan_values and scan_len != doc_len:
                logger.warning(
                    f"The scan_values length is not match doc_ids, "
                    f"so reset scan_values: new_scan_values: {new_scan_values}"
                )
                self._scan_values = new_scan_values
            elif not self._scan_values:
                logger.info(
                    f"The scan_values are empty, "
                    f"so reset scan_values: new_scan_values: {new_scan_values}"
                )
                self._scan_values = new_scan_values
        else:
            raise ValueError(f"The doc_ids must be set! dco_ids: {self._doc_ids}")

    def _query_data(self):
        """Query doc_ids data."""
        if not self._doc_dict:
            doc_data_list = self._db_client.query_tasks(self._doc_ids)
            for idx, s_dict in enumerate(doc_data_list):
                doc_id = s_dict.get("task_id") or f"{self._doc_ids[idx]}"
                self._doc_dict.update({doc_id: s_dict})

    @staticmethod
    def _calculate_time_diff(time_list: List[str]) -> int:
        """Calculate diff time."""
        st_str, et_str = time_list
        st_ms = trans_string_time_ms(st_str)
        et_ms = trans_string_time_ms(et_str)
        diff_ms = et_ms - st_ms
        return diff_ms

    def _create_plot_data(self):
        """Create plot data."""
        plot_data_dict = {}
        special_fields = ["loop_time", "loop_mem"]
        default_fields = ["doc_ids", "scan_values"]
        for idx, doc_id in enumerate(self._doc_ids):
            doc_data = self._doc_dict.get(doc_id) or {}

            for member in ServerName.__members__:
                md_dict = doc_data.get(member) or {}
                if md_dict:
                    scan_val = self._scan_values[idx]
                    mp_dict = plot_data_dict.get(member) or {}
                    for d_field, d_value in zip(default_fields, [doc_id, scan_val]):
                        if d_field in mp_dict:
                            mp_dict[d_field].append(d_value)
                        else:
                            mp_dict[d_field] = [d_value]

                    for field, value in md_dict.items():
                        cost_t = None
                        if field not in special_fields:
                            try:
                                if field.endswith("_time"):
                                    cost_t = self._calculate_time_diff(value)
                                elif field.endswith("_mem"):
                                    cost_t = int(value) / (1024 * 1024)
                            except Exception as err:
                                logger.warning(
                                    f"doc_id: {doc_id}, calculate {member}.{field} error: {err}"
                                )

                        if cost_t is not None:
                            if field in mp_dict:
                                mp_dict[field].append(cost_t)
                            else:
                                mp_dict[field] = [cost_t]

                    if member not in plot_data_dict:
                        plot_data_dict.update({member: mp_dict})

        self._plot_data_dict.clear()
        self._plot_data_dict.update(plot_data_dict)

    def _set_file_path(self) -> str:
        """Get file prefix."""
        if not self._save_path:
            s_string = time.strftime("%Y-%m-%d/%H.%M.%S", time.localtime())
            s_path = str(
                Path.cwd().parent
                / "result-performance"
                / f"{s_string}-scan-{self._scan_name}"
            )
        else:
            s_path = self._save_path
        if not os.path.exists(s_path):
            os.makedirs(s_path, exist_ok=True)
        return s_path

    def _save_plot_data(self):
        """Save plot data."""
        file_name = f"plot_data.json"
        file = Path(self._file_path) / file_name
        with open(str(file), mode="w", encoding="utf-8") as fp:
            json.dump(self._plot_data_dict, fp, ensure_ascii=False, indent=4)
        logger.info(f"Save json file: {file}")

    def _plot(self):
        """Plot all data."""
        scan_name = self._scan_name
        scan_values = []
        special_fields = ["doc_ids", "scan_values"]
        draw_groups = self._draw_groups

        total_plot_data = {scan_name: scan_values}
        for s_name, s_dict in self._plot_data_dict.items():
            s_scan_values = s_dict.get("scan_values") or []
            s_plot_data = {
                f"{s_name}.{key}": val
                for key, val in s_dict.items()
                if key not in special_fields
            }
            if not scan_values:
                scan_values.extend(s_scan_values)

            if scan_values == s_scan_values:
                total_plot_data.update(s_plot_data)

            s_plot_data.update({scan_name: s_scan_values})
            file_name = f"{s_name}.png"
            file = Path(self._file_path) / file_name
            self._draw_data(s_plot_data, scan_name, s_name, png_name=str(file))

        t_file_name = f"plot_all.png"
        t_file = Path(self._file_path) / t_file_name
        self._draw_data(total_plot_data, scan_name, png_name=str(t_file))

        # Optimize plot group.
        if isinstance(draw_groups, dict):
            draw_marks = list(draw_groups.keys())
            draw_groups = list(draw_groups.values())
        else:
            draw_marks = [f"group{idx + 1}" for idx in range(len(draw_groups))]

        for group_idx, group_list in enumerate(draw_groups):
            if group_list:
                g_mark = draw_marks[group_idx]
                g_plot_data = {}
                g_scan_values = []
                for group_name in group_list:
                    gs_name, gs_key = group_name.split(".", 1)
                    gs_dict = self._plot_data_dict.get(gs_name, {})
                    gs_scan_values = gs_dict.get("scan_values", [])
                    gs_key_values = gs_dict.get(gs_key, [])
                    g_plot_data.update({group_name: gs_key_values})

                    if g_scan_values:
                        if gs_scan_values != g_scan_values:
                            logger.warning(
                                f"{g_mark} scan_values: {g_scan_values}, "
                                f"but {group_name} scan_values: {gs_scan_values}"
                            )
                    else:
                        if gs_scan_values:
                            g_scan_values.extend(gs_scan_values)
                g_plot_data.update({scan_name: g_scan_values})

                g_file_name = f"{g_mark}.png"
                g_file = Path(self._file_path) / g_file_name
                self._draw_data(g_plot_data, scan_name, g_mark, png_name=str(g_file))

    def run(self):
        """Drawer run logic."""
        self._check_value()
        self._query_data()
        self._create_plot_data()
        self._save_plot_data()
        self._plot()
