# -*- coding: utf-8 -*-


from pymongo import MongoClient
from gridfs import GridFS

from loguru import logger


class MgDatabase:
    """Use mongodb save or update task information."""

    def __init__(
        self,
        host: str,
        port: int,
        user: str = "",
        password: str = "",
        max_connections: int = 1000,
    ):
        """Initial object."""
        self._host = host
        self._port = port
        self._user = user
        self._password = password
        self._max_connections = max_connections

        self._mg_client = self._get_mg_client()
        self._tp_collection = self._mg_client["UserData"]["TaskPerformer"]

    def _get_mg_client(self):
        """Get mongodb client object."""
        if self._user and self._password:
            mg_url = f"mongodb://{self._user}:{self._password}@{self._host}:{self._port}/"
        else:
            mg_url = f"mongodb://{self._host}:{self._port}/"
        mg_client = MongoClient(
            mg_url,
            maxPoolSize=self._max_connections,
            serverSelectionTimeoutMS=10000,
            socketTimeoutMS=10000,
        )
        return mg_client

    def query_tasks(self, doc_ids: list) -> list:
        """By status query task_id list"""
        try:
            res_cursor = self._tp_collection.find(
                {"task_id": {"$in": doc_ids}},
                {"_id": 0, "create_time": 0},
            )
            task_list = [task_dict for task_dict in res_cursor]
            return task_list
        except Exception as err:
            logger.error(f"Query tasks error: {err}")
