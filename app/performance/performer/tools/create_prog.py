# -*- coding: utf-8 -*-

import re
import random
from enum import Enum
from typing import Union, List, Dict

import numpy as np
from loguru import logger


from ..types import QProgType


def random_qprog(
    q_bits: List[Union[int, str]],
    c_list: List[List[str]],
    s_gate_width: float = 30.0,
    d_gate_width: float = 40.0,
    gate_deep: int = 100,
    loop: int = 1,
) -> List:
    """Random create qprog_list."""
    phase_list = np.linspace(-np.pi, np.pi, 20)
    theta_list = [90, 180]

    trans_bits = [bit if isinstance(bit, int) else int(bit[1:]) for bit in q_bits]
    trans_bits.sort()
    bit_num = len(trans_bits)

    support_cz_list = []
    for once_cz_bits in c_list:
        r_flags = [
            True if int(b_str) in trans_bits else False for b_str in once_cz_bits
        ]
        if all(r_flags):
            support_cz_list.append(once_cz_bits)
    cz_num = len(support_cz_list)

    idx = 0
    measure_time = 0
    qprog_list = []
    while idx < gate_deep:
        if idx % 2 == 1:
            select_gate = "double"
        else:
            select_gate = "single"

        if select_gate == "single":
            gate_width = s_gate_width
            for bit in trans_bits:
                s_phase = float(np.rad2deg(random.choice(phase_list)))
                s_theta = random.choice(theta_list)
                s_gate = {"RPhi": [bit, s_phase, s_theta, measure_time]}
                qprog_list.append(s_gate)
        else:
            gate_width = d_gate_width
            if support_cz_list:
                select_cz_bits = random.sample(support_cz_list, k=round(cz_num * 0.8))
            else:
                select_cz_bits = []
            add_cz_bits = []
            for s_cz_bits in select_cz_bits:
                add_flags = [
                    True if s_bit not in add_cz_bits else False for s_bit in s_cz_bits
                ]
                if all(add_flags):
                    s_bit, l_bit = s_cz_bits
                    d_gate = {"CZ": [int(s_bit), int(l_bit), measure_time]}
                    qprog_list.append(d_gate)
                    add_cz_bits.extend(s_cz_bits)

        idx += 1
        measure_time += gate_width

    m_num = random.randint(1, bit_num)
    m_bits = random.sample(trans_bits, k=m_num)
    m_gate = {"Measure": [m_bits, measure_time + s_gate_width]}
    qprog_list.append(m_gate)

    if loop < 1:
        raise ValueError("loop must be large 0!")
    new_qprog_list = [qprog_list] * loop

    return new_qprog_list


def random_complex_qprog(
    q_bits: List[Union[int, str]],
    c_list: List[List[str]],
    s_gate_width: float = 30.0,
    d_gate_width: float = 40.0,
    gate_deep: int = 100,
    loop: int = 1,
) -> List:
    """Random create qprog_list."""
    phase_list = np.linspace(-np.pi, np.pi, 20)
    theta_list = [90, 180]

    trans_bits = [bit if isinstance(bit, int) else int(bit[1:]) for bit in q_bits]
    trans_bits.sort()
    bit_num = len(trans_bits)

    support_cz_list = []
    for once_cz_bits in c_list:
        r_flags = [
            True if int(b_str) in trans_bits else False for b_str in once_cz_bits
        ]
        if all(r_flags):
            support_cz_list.append(once_cz_bits)
    cz_num = len(support_cz_list)

    if loop < 1:
        raise ValueError("loop must be large 0!")

    total_qprog_list = []
    for i in range(loop):
        idx = 0
        measure_time = 0
        qprog_list = []
        while idx < gate_deep:
            if idx % 2 == 1:
                select_gate = "double"
            else:
                select_gate = "single"

            if select_gate == "single":
                gate_width = s_gate_width
                for bit in trans_bits:
                    s_phase = float(np.rad2deg(random.choice(phase_list)))
                    s_theta = random.choice(theta_list)
                    s_gate = {"RPhi": [bit, s_phase, s_theta, measure_time]}
                    qprog_list.append(s_gate)
            else:
                gate_width = d_gate_width
                if support_cz_list:
                    select_cz_bits = random.sample(
                        support_cz_list, k=round(cz_num * 0.8)
                    )
                else:
                    select_cz_bits = []
                add_cz_bits = []
                for s_cz_bits in select_cz_bits:
                    add_flags = [
                        True if s_bit not in add_cz_bits else False
                        for s_bit in s_cz_bits
                    ]
                    if all(add_flags):
                        s_bit, l_bit = s_cz_bits
                        d_gate = {"CZ": [int(s_bit), int(l_bit), measure_time]}
                        qprog_list.append(d_gate)
                        add_cz_bits.extend(s_cz_bits)

            idx += 1
            measure_time += gate_width

        m_num = random.randint(1, bit_num)
        m_bits = random.sample(trans_bits, k=m_num)
        m_gate = {"Measure": [m_bits, measure_time + s_gate_width]}
        qprog_list.append(m_gate)

        total_qprog_list.append(qprog_list)

    return total_qprog_list


def random_qprog_without_cz(
    q_bits: List[Union[int, str]],
    s_gate_width: float = 30.0,
    gate_deep: int = 100,
    loop: int = 1,
) -> List:
    """Random create qprog_list."""
    phase_list = np.linspace(-np.pi, np.pi, 20)
    theta_list = [90, 180]

    trans_bits = [bit if isinstance(bit, int) else int(bit[1:]) for bit in q_bits]
    bit_num = len(trans_bits)
    qprog_list = []
    if gate_deep > 0:
        gate_idx_list = list(range(gate_deep))
        for bit in trans_bits:
            g_num = random.randint(1, gate_deep)
            g_idx_list = list(set(random.choices(gate_idx_list, k=g_num)))
            g_idx_list.sort()
            for g_idx in g_idx_list:
                s_phase = float(np.deg2rad(random.choice(phase_list)))
                s_theta = random.choice(theta_list)
                s_gate = {"RPhi": [bit, s_phase, s_theta, g_idx * s_gate_width]}
                qprog_list.append(s_gate)

    m_num = random.randint(1, bit_num)
    m_bits = list(set(random.choices(trans_bits, k=m_num)))
    m_gate = {"Measure": [m_bits, (gate_deep + 1) * s_gate_width]}
    qprog_list.append(m_gate)

    if loop < 1:
        raise ValueError("loop must be large 0!")
    new_qprog_list = [qprog_list] * loop

    return new_qprog_list


def create_qprog_list(
    qprog_type: str,
    q_bits: List[Union[int, str]],
    c_list: List[str] = None,
    s_gate_width: float = 30.0,
    d_gate_width: float = 40.0,
    gate_deep: int = 1,
    loop: int = 1,
) -> List[List[Dict]]:
    """Random create qprog list api."""
    if c_list:
        pattern = re.compile(r"\d+")
        new_c_list = [pattern.findall(c_str) for c_str in c_list]
    else:
        new_c_list = []

    qprog_list = []
    if qprog_type == QProgType.normal.value:
        qprog_list = random_qprog(
            q_bits=q_bits,
            c_list=new_c_list,
            s_gate_width=s_gate_width,
            d_gate_width=d_gate_width,
            gate_deep=gate_deep,
            loop=loop,
        )
    elif qprog_type == QProgType.complex.value:
        qprog_list = random_complex_qprog(
            q_bits=q_bits,
            c_list=new_c_list,
            s_gate_width=s_gate_width,
            d_gate_width=d_gate_width,
            gate_deep=gate_deep,
            loop=loop,
        )
    elif qprog_type == QProgType.without_cz.value:
        qprog_list = random_qprog_without_cz(
            q_bits=q_bits,
            s_gate_width=s_gate_width,
            gate_deep=gate_deep,
            loop=loop,
        )
    else:
        logger.warning(f"Not support qprog_type: {qprog_type}")
    return qprog_list
