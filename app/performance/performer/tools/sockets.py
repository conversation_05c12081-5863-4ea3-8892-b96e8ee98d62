# -*- encoding: utf-8 -*-

# This code is part of pyqcat-storm.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/09/20
# __author:       SS Fang


"""
ZeroMQ SUB client sockets.
"""


import time
import json
from typing import Dict

import zmq
from loguru import logger


class DealerClient:
    """ZeroMQ DEALER client."""

    def __init__(
        self,
        url: str,
        identity: str,
        context: zmq.Context = None,
    ):
        """Initial object."""
        self.url = url
        self.identity = identity.encode(encoding="utf-8")
        self.context = context or zmq.Context.instance()

        self._socket = self._init_socket()
        logger.info(f"Connect {self.url} client {self.identity} running ...")

    @property
    def socket(self) -> zmq.Socket:
        """zmq.Socket object."""
        return self._socket

    def _init_socket(self) -> zmq.Socket:
        """Initial zmq.Socket object."""
        sock: zmq.Socket = self.context.socket(zmq.DEALER)
        sock.setsockopt(zmq.IDENTITY, self.identity)
        sock.connect(self.url)
        time.sleep(0.05)  # Allow connection to come up.
        return sock

    def send_bytes(self, info: bytes):
        """Send heart beat message."""
        remote_identity = b""
        msg_parts = [info, remote_identity]
        self.socket.send_multipart(msg_parts)

    def send(self, message: Dict):
        """Send message."""
        json_str = json.dumps(message)
        zmq_buf = json_str.encode(encoding="utf-8")
        msg_parts = [zmq_buf]
        self.socket.send_multipart(msg_parts)

    def recv(self) -> Dict:
        """Receive message."""
        msg_list = self.socket.recv_multipart()
        msg_dict = {}
        if msg_list:
            zmq_buffer = msg_list[-1]
            try:
                msg_dict = json.loads(zmq_buffer)
            except Exception as err:
                logger.warning(f"Json load error: {err}, message: {msg_list}")
        else:
            logger.warning(f"Receive empty, message: {msg_list}")
        return msg_dict


class SubscriberClient:
    """ZeroMQ SUB client."""

    def __init__(
        self,
        url: str,
        topic: str,
        operation: str,
        context: zmq.Context = None,
    ):
        """Initial object."""
        self.url = url
        self.topic = topic.encode(encoding="utf-8")
        self.operation = operation.encode(encoding="utf-8")
        self.context = context or zmq.Context.instance()

        self._socket = self._init_socket()
        logger.info(f"Subscribe to {self.topic} client running ...")

    @property
    def socket(self) -> zmq.Socket:
        """zmq.Socket object."""
        return self._socket

    def _init_socket(self) -> zmq.Socket:
        """Initial zmq.Socket object."""
        topic_bytes = self.topic
        sock: zmq.Socket = self.context.socket(zmq.SUB)
        sock.setsockopt(zmq.SUBSCRIBE, topic_bytes)
        sock.connect(self.url)
        time.sleep(0.01)
        return sock

    def recv(self) -> Dict:
        """Receive message."""
        topic, operation, message, *_ = self.socket.recv_multipart()

        msg_dict = {}
        try:
            if operation == self.operation:
                msg_dict = json.loads(message)
        except Exception as err:
            logger.warning(
                f"receive from: {topic}, operation: {operation},"
                f"message: {message}, json loads error: {err}",
            )
        return msg_dict
