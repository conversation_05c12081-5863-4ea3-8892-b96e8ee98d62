# -*- coding: utf-8 -*-

import time
import json
from ast import literal_eval
from configparser import Config<PERSON><PERSON><PERSON>
from typing import Dict

from loguru import logger


def trans_time_ms_string(ms_time: int) -> str:
    """Trans ms time to string."""
    s_time = ms_time * 1e-3  # unit: s
    s_string = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(s_time))
    ms_string = "{:.3f}".format(s_time - int(s_time))
    end_string = f"{s_string}{ms_string[1:]}"
    return end_string


def trans_string_time_ms(string: str) -> int:
    """Trans string to time ms. Like: 2024-02-22 15:51:53.345."""
    tail_str = string.rsplit(".", 1)[-1]
    tail_len = len(tail_str)
    tail_s = float(int(tail_str) / (10 ** tail_len))
    if tail_len <= 6:
        time_str = string
    else:
        time_str = string[: -(tail_len - 6)]
    time_tup = time.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
    time_s = time.mktime(time_tup)
    end_s = time_s + tail_s
    end_ms = int(end_s * 1e3)
    return end_ms


def get_conf_params(conf_file: str) -> Dict:
    """Get conf file data."""
    try:
        config_parser = ConfigParser(inline_comment_prefixes="#")
        config_parser.read(conf_file, encoding="utf-8")
        sec = config_parser.sections()
        data = dict.fromkeys(sec)
        for op in sec:
            ops = config_parser.options(op)
            temp_dict = dict.fromkeys(ops)
            data[op] = temp_dict
            for key in ops:
                value = literal_eval(config_parser.get(op, key))
                temp_dict[key] = value
    except Exception as err:
        logger.error(f"load conf {conf_file} error: {err}")
        data = {}
    return data


def get_json_params(json_file: str) -> Dict:
    """Get json file data."""
    try:
        with open(json_file, mode="r", encoding="utf-8") as fp:
            data = json.load(fp)
    except Exception as err:
        logger.error(f"load json {json_file} error: {err}")
        data = {}
    return data
