# -*- coding: utf-8 -*-

from dataclasses import dataclass, field


@dataclass
class ConfigureParams:
    """To configure of the task."""

    # IsSrcResult: bool = False
    # IsAmend: bool = False
    # IsFidelityMat: bool = False
    # IsProbCount: bool = False
    Shot: int = 1000
    IsExperiment: bool = False
    CompileOnly: bool = False
    TaskPriority: int = 0


@dataclass
class TaskRequest:
    MsgType: str = "MsgTask"
    SN: int = 0
    TaskId: str = ""
    ConvertQProg: str = ""
    Configure: dict = field(default_factory=dict)


@dataclass
class TaskResultReply:
    MsgType: str = "MsgTaskResult"
    SN: int = 0
    TaskId: str = ""
    Key: list = field(default_factory=list)
    # TaskResult: list = field(default_factory=list)
    # FidelityMat: list = field(default_factory=list)
    ProbCount: list = field(default_factory=list)
    NoteTime: dict = field(default_factory=dict)
    DocId: str = ""
    ErrCode: int = 0
    ErrInfo: str = ""


@dataclass
class TaskResponse:
    MsgType: str = ""
    SN: int = 0
    ErrCode: str = ""
    ErrInfo: str = ""


@dataclass
class TaskParams:
    """Note Task Params."""
    task_id: str = ""
    doc_id: str = ""
    scan_name: str = ""
    ctrl_params: dict = field(default_factory=dict)
    task_request: dict = field(default_factory=dict)
    err_info: str = ""
