# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/03/19
# __author:       <PERSON><PERSON><PERSON>

import itertools
import operator
from functools import reduce

import scheudle_lib
from pyQCat import MetaData
from pyQCat.executor.context_manager import deepin_set_param_value
from pyQCat.experiments import CompositeExperiment
from schedule_top_experiment import *
from structure import *


class ScheduleCompositeExperiment(CompositeExperiment):
    _sub_experiment_class = ScheduleTopExperiment

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator(
            "run_mode", [ExperimentRunMode.sync_mode, ExperimentRunMode.async_mode]
        )
        options.schedule_name = ""
        options.scan_parameters = None
        options.const_parameters = None
        options.run_mode = ExperimentRunMode.async_mode
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.scan_name_list = None
        options.father_scan_parameters = None
        options.child_scan_parameters = None
        options.describe_list = []
        options.total_loop = None
        options.x_arr = None
        options.y_arr = None
        options.child_exp_data = []
        return options

    def _check_options(self):
        super()._check_options()

        self._load_schedule_exp()

        # check scan parameter
        self.experiment_options.scan_parameters = self._check_parameters(
            self.experiment_options.scan_parameters
        )

        # check const parameter
        self.experiment_options.const_parameters = self._check_parameters(
            self.experiment_options.const_parameters
        )

        # check name/obj map
        for qubit in self.qubits:
            self.run_options.unit_name2obj[qubit.name] = qubit
        for coupler in self.couplers:
            self.run_options.unit_name2obj[coupler.name] = coupler
        for pair in self.qubit_pairs:
            self.run_options.unit_name2obj[pair.name] = pair
        self.child_experiment.run_options.unit_name2obj = self.run_options.unit_name2obj

        # check scan parameters
        self._validate_scan_parameters()

        # check const parameters
        self._validate_const_parameters()

        # set child exp run mode
        self.child_experiment.run_options.run_mode = self.experiment_options.run_mode

    def _validate_scan_parameters(self):
        same_level_parameter_map = self.check_same_level_parameters(
            self.experiment_options.scan_parameters
        )

        loop_list = []
        for parameters in same_level_parameter_map.values():
            loop_list.append(self._check_same_loop(parameters))

        all_level_parameters = list(same_level_parameter_map.values())

        scan_name_list = [
            self._scan_parameters_info(parameters)
            for parameters in all_level_parameters
        ]
        self.child_experiment.analysis_options.x_label = scan_name_list[-1]

        self.run_options.scan_name_list = scan_name_list
        self.run_options.total_loop = reduce(operator.mul, loop_list)
        self.run_options.father_scan_parameters = all_level_parameters[:-1]
        self.run_options.child_scan_parameters = all_level_parameters[-1]

        # check y arr
        y_arr = None
        for parameter in self.run_options.child_scan_parameters:
            if parameter.is_plot is True:
                y_arr = parameter.value
                break
        self.run_options.y_arr = (
            y_arr or self.run_options.child_scan_parameters[0].value
        )

        if self.run_options.father_scan_parameters:
            # check x arr
            x_arr = None
            for parameter in all_level_parameters[-2]:
                if parameter.is_plot is True:
                    x_arr = parameter.value
                    break
            self.run_options.x_arr = x_arr or all_level_parameters[-2][0].value

            self.run_options.child_count = self.run_options.total_loop // reduce(
                operator.mul, loop_list[-2:]
            )
            if len(all_level_parameters) > 2:
                top_parameters = all_level_parameters[:-2]
                value_list = []
                for parameters in top_parameters:
                    value_list.append(list(zip(*[p.value for p in parameters])))
                scan_values = list(itertools.product(*value_list))
                for idx, one_scan_value in enumerate(scan_values):
                    msg = ""
                    for i, same_level_values in enumerate(one_scan_value):
                        for j, v in enumerate(same_level_values):
                            parameter = top_parameters[i][j]
                            msg += f"{parameter.key}({v}) "
                    self.run_options.describe_list.append(msg)
            else:
                self.run_options.describe_list.append(
                    self.experiment_options.schedule_name
                )

    def _validate_const_parameters(self):
        if self.experiment_options.const_parameters:
            new_parameters = []
            for parameter in self.experiment_options.const_parameters:
                if isinstance(parameter, QubitParameter):
                    self._set_qubit_parameter(parameter)
                else:
                    new_parameters.append(parameter)
            self.experiment_options.const_parameters = new_parameters

    def _set_qubit_parameter(self, parameter: QubitParameter, exp=None):
        if exp:
            qubit = exp.run_options.unit_name2obj.get(
                parameter.bit_name or self.qubit.name
            )
        else:
            qubit = self.run_options.unit_name2obj.get(
                parameter.bit_name or self.qubit.name
            )
        deepin_set_param_value(qubit, parameter.key, parameter.value)
        pyqlog.log("EXP", f"Set QubitParameter {parameter}")

    def _load_schedule_exp(self):
        schedule_cls = getattr(
            scheudle_lib, self.experiment_options.schedule_name, None
        )

        if schedule_cls is None:
            raise ExperimentOptionsError(
                self, msg=f"No find schedule {self.experiment_options.schedule_name}"
            )

        schedule_exp = schedule_cls(
            inst=self.inst,
            qubits=self.qubits,
            couplers=self.couplers,
            qubit_pair=self.qubit_pair,
            compensates=self.compensates,
            discriminators=self.discriminator,
            working_dc=self.working_dc,
            ac_bias=self.ac_bias,
            is_paternal=False,
            config=self.config,
        )

        schedule_exp._experiment_options.update(
            **self._child_experiment.experiment_options
        )
        schedule_exp._analysis_options.update(**self._child_experiment.analysis_options)
        schedule_exp._run_options.update(**self._child_experiment.run_options)

        self._child_experiment = schedule_exp

    def _check_parameters(self, parameters):
        if parameters:
            if not isinstance(parameters, List):
                parameters = [parameters]

            for i in range(len(parameters)):
                cur_parameter = parameters[i]
                if isinstance(cur_parameter, dict):
                    parameter_type = cur_parameter.get("name")
                    if parameter_type == ParameterType.PULSE.value:
                        parameters[i] = PulseParameter.from_dict(cur_parameter)
                    elif parameter_type == ParameterType.INST.value:
                        parameters[i] = InstrumentParameter.from_dict(cur_parameter)
                    elif parameter_type == ParameterType.QUBIT.value:
                        parameters[i] = QubitParameter.from_dict(cur_parameter)
                        assert parameters[i].level > 0
                    elif parameter_type == ParameterType.EXP.value:
                        parameters[i] = ExperimentParameter.from_dict(cur_parameter)
                        assert parameters[i].level > 0
                    else:
                        raise ExperimentOptionsError(
                            self, msg=f"Unknown parameter {cur_parameter}"
                        )
                if not isinstance(parameters[i], Parameter):
                    raise ExperimentOptionsError(
                        self, msg=f"scan parameter must be `ScanParameter` class."
                    )

            for p in parameters:
                if not p.qubit:
                    p.qubit = self.qubits[0].name

            return parameters

        return []

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()
        metadata.process_meta = {
            "describe_list": self.run_options.describe_list,
            "x_arr": self.run_options.x_arr,
            "y_arr": self.run_options.y_arr,
            "total_loop": self.run_options.total_loop,
            "scan_name_list": self.run_options.scan_name_list,
            "file": self.file,
        }
        return metadata

    def run(self):
        super().run()

        const_parameters = self.experiment_options.const_parameters or []
        father_scan_parameters = self.run_options.father_scan_parameters

        if father_scan_parameters:
            all_level_value_list = []

            for parameters in father_scan_parameters:
                all_level_value_list.append(list(zip(*[p.value for p in parameters])))

            scan_values = list(itertools.product(*all_level_value_list))

            if self.experiment_options.run_mode == ExperimentRunMode.sync_mode:
                self.sync_run_exp(scan_values)
            else:
                self.async_run_exp(scan_values)
        else:
            once_exp = deepcopy(self.child_experiment)
            once_exp.set_parent_file(self)
            once_exp.set_run_options(
                const_parameters=const_parameters,
                scan_parameters=deepcopy(self.run_options.child_scan_parameters),
            )
            self._check_simulator_data(once_exp, 0)
            once_exp.run()

    def sync_run_exp(self, scan_values):
        for idx in range(len(scan_values)):
            once_exp = self._child_exp(idx, scan_values)
            once_exp.run()
            self._add_child_experiment(once_exp)
        self._schedule_analysis()

    def async_run_exp(self, scan_values):
        asyncio.run(self._async_run_exp(scan_values))

    def _child_exp(self, idx, scan_values):
        one_scan_value = scan_values[idx]
        const_parameters = self.experiment_options.const_parameters or []
        father_scan_parameters = self.run_options.father_scan_parameters

        once_exp = deepcopy(self.child_experiment)
        once_child_parameters = deepcopy(const_parameters)
        father_scan_parameters = deepcopy(father_scan_parameters)

        for i, same_level_values in enumerate(one_scan_value):
            for j, v in enumerate(same_level_values):
                parameter = father_scan_parameters[i][j]
                parameter.value = v
                if isinstance(parameter, QubitParameter):
                    self._set_qubit_parameter(parameter, self.child_experiment)
                elif isinstance(parameter, ExperimentParameter):
                    once_exp.set_experiment_options(**parameter.once_value)
                once_child_parameters.append(parameter)
        describe = self._scan_parameters_info(once_child_parameters)
        self.run_options.describe_list.append(describe)
        once_exp.set_parent_file(self, describe, idx, len(scan_values))
        once_exp.set_run_options(
            const_parameters=once_child_parameters,
            scan_parameters=deepcopy(self.run_options.child_scan_parameters),
        )
        self._check_simulator_data(once_exp, idx)
        return once_exp

    def _add_child_experiment(self, exp: ScheduleTopExperiment):
        if self.experiment_data:
            new_data = None
            for key, v in exp.experiment_data.y_data.items():
                new_data = np.hstack((self.experiment_data.y_data[key], v))
                self.experiment_data.y_data[key] = new_data
            if len(new_data) == len(self.run_options.x_arr) * len(
                self.run_options.y_arr
            ):
                self.run_options.child_exp_data.append(self.experiment_data)
                self.experiment_data = None
        else:
            self.experiment_data = exp.experiment_data

    def set_child_exp_options(self, **fields):
        self._experiment_options.child_exp_options.update(**fields)

    @staticmethod
    def _scan_parameters_info(parameters: List[Parameter]):
        p_infos = [str(p) for p in parameters if p.is_plot is True] or [
            str(parameters[0])
        ]
        return " ".join(p_infos)

    @staticmethod
    def check_same_level_parameters(parameters: List[PulseParameter]):
        same_level_parameter_map = defaultdict(list)

        for parameter in parameters:
            same_level_parameter_map[parameter.level].append(parameter)

        same_level_parameter_map = dict(
            sorted(same_level_parameter_map.items(), key=lambda x: x[0], reverse=True)
        )
        return same_level_parameter_map

    @staticmethod
    def _check_same_loop(parameters: List[Parameter]):
        scan_length_list = list(set([len(p.value) for p in parameters]))

        if len(scan_length_list) != 1:
            raise ValueError("loop num no same!")

        return scan_length_list[0]

    def _schedule_analysis(self):
        for i in range(self.run_options.child_count):
            child_exp_data = self.run_options.child_exp_data[i]
            child_exp_data.metadata = self._metadata()
            child_exp_data.metadata.draw_meta = {
                "INFO": self.run_options.describe_list[i]
            }
            analysis = ScanCurveAnalysis(self.run_options.child_exp_data[i])
            analysis.run_analysis()
            self.file.save_figure(analysis.drawer.figure, f"(result)-{i}", close=False)

    async def _async_run_exp(self, scan_values):
        task_list = []
        exp_list = []
        for idx in range(len(scan_values)):
            once_exp = self._child_exp(idx, scan_values)
            task = once_exp.async_run()
            task_list.append(task)
            exp_list.append(once_exp)
        try:
            await asyncio.gather(*task_list)
            for exp in exp_list:
                self._add_child_experiment(exp)
        except asyncio.CancelledError:
            pyqlog.error(f"Async run experiment {self.label} cancelled.")

        self._schedule_analysis()


class ScanCurveAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.x_arr = None
        options.y_arr = None
        options.once_point = None
        options.shape = None
        return options

    def _process_meta(self, key):
        return self.experiment_data.metadata.process_meta.get(key)

    def _prepare_subplot(self):
        scan_name_list = self._process_meta("scan_name_list")
        sub_title_list = list(self.experiment_data.y_data.keys())
        subplots = (len(sub_title_list) // 2, 2)
        x_label_list = [scan_name_list[-2] for _ in range(len(sub_title_list))]
        y_label_list = [scan_name_list[-1] for _ in range(len(sub_title_list))]
        x_arr = self._process_meta("x_arr")
        y_arr = self._process_meta("y_arr")

        self.set_options(
            y_label=y_label_list,
            x_label=x_label_list,
            subplots=subplots,
            sub_title=sub_title_list,
            x_arr=self._process_meta("x_arr"),
            y_arr=self._process_meta("y_arr"),
            shape=(len(x_arr), len(y_arr)),
        )

    def _create_analysis_data(self):
        return {}

    def _visualization(self):
        self.drawer.set_options(title=self._description())

        for j, data in enumerate(list(self.experiment_data.y_data.values())):
            z_arr = data.reshape(self.options.shape)

            self.drawer.draw_color_map(
                self.options.x_arr,
                self.options.y_arr,
                z_arr.T,
                ax_index=j,
                **self.options.pcolormesh_options,
            )

        # Finalize plot.
        self.drawer.format_canvas()
