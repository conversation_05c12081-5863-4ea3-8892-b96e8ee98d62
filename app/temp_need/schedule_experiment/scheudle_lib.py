# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/03/19
# __author:       <PERSON><PERSON><PERSON>


from pyQCat.parameters import options_wrapper
from pyQCat.pulse.pulse_function import *
from pyQCat.pulse.pulse_lib import *
from pyQCat.pulse_adjust import params_to_pulse
from pyQCat.structures import MetaData
from schedule_top_experiment import *


class RabiSchedule(ScheduleTopExperiment):

    mode_adapter = ExperimentAdapterMode.adapter_coupler

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("name", ["Xpi", "Xpi/2"])
        options.set_validator("N", int)
        options.name = "Xpi"
        options.N = 1
        return options

    def _set_xy_pulses(self):
        """Set RabiScanAmp experiment XY pulses."""
        if self.experiment_options.name == "Xpi":
            pulse = pi_pulse(self.qubit)() * self.experiment_options.N
        else:
            pulse = half_pi_pulse(self.qubit)
            pulse = pulse() * self.experiment_options.N
        self.play_pulse("XY", self.qubit, pulse)


class RamseySchedule(ScheduleTopExperiment):

    mode_adapter = ExperimentAdapterMode.adapter_coupler

    def _set_xy_pulses(self):
        """Set RabiScanAmp experiment XY pulses."""
        pulse = half_pi_pulse(self.qubit)()
        pulse += Constant(0, 0, "XY")()
        pulse += half_pi_pulse(self.qubit)()
        self.play_pulse("XY", self.qubit, pulse)


class APESchedule(ScheduleTopExperiment):

    mode_adapter = ExperimentAdapterMode.adapter_coupler

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("theta_type", ["Xpi", "Xpi/2"])
        options.set_validator("phi_num", (1, 10, 0))
        options.set_validator("N", (1, 20, 0))

        options.phi_num = 1
        options.theta_type = "Xpi"
        options.N = 9

        return options

    def _set_xy_pulses(self):
        if self.experiment_options.theta_type == "Xpi":
            ape_pulse_zero_phase = pi_pulse(self.qubit)
            ape_pulse_pi_phase = pi_pulse(self.qubit)
        else:
            ape_pulse_zero_phase = half_pi_pulse(self.qubit)
            ape_pulse_pi_phase = half_pi_pulse(self.qubit)

        ape_pulse_zero_phase.phase = 0
        ape_pulse_pi_phase.phase = np.pi * self.experiment_options.phi_num

        ape_pulse = (ape_pulse_zero_phase() + ape_pulse_pi_phase()) * self.experiment_options.N

        self.play_pulse("XY", self.qubit, ape_pulse)


class ACCrosstalkSchedule(ScheduleTopExperiment):

    mode_adapter = ExperimentAdapterMode.adapter_coupler

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator('tq_name', str, limit_null=True)
        options.set_validator('bq_name', str, limit_null=True)
        options.set_validator('drive_type', ["Drag", "Square"])
        options.set_validator('ac_buffer_pre', float)
        options.set_validator('ac_buffer_after', float)

        options.tq_name = None
        options.bq_name = None
        options.drive_type = "Drag"
        options.ac_buffer_pre = 5000
        options.ac_buffer_after = 200

        return options

    def _set_xy_pulses(self):
        tq = self.get_bit_obj(self.experiment_options.tq_name)
        bq = self.get_bit_obj(self.experiment_options.bq_name)

        self.run_options.tq = tq
        self.run_options.bq = bq

        if isinstance(tq, Coupler):
            tq = self.run_options.coupler_cali_options.qd

        pre_delay = Constant(self.experiment_options.ac_buffer_pre, 0, name='XY')
        after_delay = Constant(self.experiment_options.ac_buffer_after, 0, name='XY')
        if self.experiment_options.drive_type == 'Drag':
            drive_pulse = pi_pulse(tq)
        else:
            baseband_freq = tq.XYwave.baseband_freq
            pulse_params = {
                "time": 5000,
                "offset": 15,
                "amp": 1.0,
                "detune": 0,
                "freq": baseband_freq
            }
            drive_pulse = SquareEnvelop(**pulse_params)

        xy_pulse = pre_delay() + drive_pulse() + after_delay()
        self.run_options.xy_pulse_width = xy_pulse.width

        self.play_pulse("XY", tq, xy_pulse)

    def _set_z_pulses(self):
        self.play_pulse("Z", self.run_options.tq, Constant(self.run_options.xy_pulse_width, 0)())
        self.play_pulse("Z", self.run_options.bq, Constant(self.run_options.xy_pulse_width, 0)())

    def _set_single_readout_pulse(self, qubit=None):

        if isinstance(self.run_options.tq, Qubit):
            qubit = self.run_options.tq
        else:
            qubit = self.run_options.coupler_cali_options.qp

        super()._set_single_readout_pulse(qubit)


class CZAssistSchedule(ScheduleTopExperiment):

    mode_adapter = ExperimentAdapterMode.adapter_qubit_pair

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("cz_num", int)
        options.set_validator("add_cz", bool)
        options.set_validator("control_gate", ["I", "X"])
        options.set_validator("ramsey_bit", str)

        options.cz_num = 1
        options.add_cz = True
        options.control_gate = "I"
        options.ramsey_bit = None

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "control_gate": self.experiment_options.control_gate,
            "ramsey_bit": self.experiment_options.ramsey_bit,
            "add_cz": self.experiment_options.add_cz,
        }
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        ramsey_bit = self.get_bit_obj(self.experiment_options.ramsey_bit)
        if ramsey_bit == self.run_options.pair_options.qh:
            control_bit = self.run_options.pair_options.ql
        elif ramsey_bit == self.run_options.pair_options.ql:
            control_bit = self.run_options.pair_options.qh
        else:
            raise ExperimentOptionsError(self, msg=f"ramsey bit {ramsey_bit} error!")

        self.run_options.ramsey_bit = ramsey_bit
        self.run_options.control_bit = control_bit

    def _set_xy_pulses(self):
        cz_num = self.experiment_options.cz_num
        control_gate = self.experiment_options.control_gate
        cz_width = self.run_options.pair_options.width

        for qubit in self.qubits:
            if qubit.name.startswith("c"):
                continue

            pulse_2 = Constant(cz_width, 0, name="XY")
            if qubit == self.run_options.ramsey_bit:
                pulse_1 = half_pi_pulse(qubit)
                pulse_3 = half_pi_pulse(qubit)
            elif qubit == self.run_options.control_bit:
                pulse_1 = stimulate_state_pulse(control_gate, qubit)
                pulse_3 = stimulate_state_pulse("I", qubit)
            else:
                pulse_1 = stimulate_state_pulse("I", qubit)
                pulse_3 = stimulate_state_pulse("I", qubit)

            xy_pulse = pulse_1() + pulse_2() * cz_num + pulse_3()
            self.play_pulse("XY", qubit, xy_pulse)

    def _set_z_pulses(self):
        cz_num = self.experiment_options.cz_num
        cz_width = self.run_options.pair_options.width
        add_cz = self.experiment_options.add_cz

        ql = self.run_options.pair_options.ql
        qh = self.run_options.pair_options.qh
        parking_qubits = self.run_options.pair_options.env_bits
        gate_params = self.run_options.pair_options.gate_params

        qubit_list = [ql, qh]
        qubit_list.extend(parking_qubits)
        drag_time = qh.XYwave.time + qh.XYwave.offset * 2

        for qubit in qubit_list:
            if isinstance(qubit, Qubit):
                pulse_1 = zero_pulse(qubit, name="Z")
                pulse_3 = zero_pulse(qubit, name="Z")
            else:
                pulse_1 = Constant(drag_time, 0)
                pulse_3 = Constant(drag_time, 0)

            s_gate_params = deepcopy(gate_params.get(qubit.name))
            s_gate_params.update({"width": cz_width})
            if add_cz is False:
                s_gate_params.update({"amp": 0})

            pulse_2 = params_to_pulse(**s_gate_params)

            z_pulse = pulse_1() + pulse_2() * cz_num + pulse_3()
            self.play_pulse("Z", qubit, z_pulse)


@options_wrapper
class DistortionT1Schedule(ScheduleTopExperiment):

    mode_adapter = ExperimentAdapterMode.adapter_coupler

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator('gauss_sigma', float)
        options.set_validator('gauss_width', float)
        options.set_validator('const_width', float)
        options.set_validator('ta', float)
        options.set_validator('add_tb_width', float)
        options.set_validator('z_amp', (-1, 1, 2))
        options.set_validator('xy_delay', float)

        options.z_amp = -0.5
        options.gauss_sigma = 5.0
        options.gauss_width = 15.0
        options.const_width = 100
        options.ta = 2000
        options.tb = 2000
        options.add_tb_width = 100
        options.xy_delay = 100

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.exp_qubit = None
        options.drag_qubit = None
        options.rdz_amp = None

        return options

    def _set_xy_pulses(self):
        gauss_width = self.experiment_options.gauss_width
        const_width = self.experiment_options.const_width
        ta = self.experiment_options.ta
        tb = self.experiment_options.tb
        xy_delay = self.experiment_options.xy_delay

        align_with = gauss_width + const_width + ta + xy_delay
        all_width = gauss_width + const_width + ta + tb + gauss_width

        drag_middle = pi_pulse(self.run_options.drag_qubit)
        drag_width = drag_middle.width
        half_drag_width = drag_width / 2

        const_head = Constant(align_with - half_drag_width, 0, 'XY')
        new_width = all_width - const_head.width - drag_width
        if new_width > 0:
            const_tail = Constant(new_width, 0, 'XY')
            xy_pulse = const_head() + drag_middle() + const_tail()
        else:
            xy_pulse = const_head() + drag_middle()

        self.play_pulse("XY", self.run_options.drag_qubit, xy_pulse)

    def _set_z_pulses(self):
        z_amp = self.experiment_options.z_amp
        gauss_sigma = self.experiment_options.gauss_sigma
        gauss_width = self.experiment_options.gauss_width
        const_width = self.experiment_options.const_width
        ta = self.experiment_options.ta
        tb = self.experiment_options.tb
        qubit = self.run_options.exp_qubit
        gauss_up = GaussianUp(gauss_width, 0, sigma=gauss_sigma)
        const_one = Constant(const_width + ta, 0)
        const_two = Constant(tb + gauss_width, z_amp + 0)
        z_pulse = gauss_up() + const_one() + const_two()
        self.play_pulse("Z", qubit, z_pulse)

    def _check_options(self):
        super()._check_options()

        if self.run_options.coupler_cali_options:
            qd = self.run_options.coupler_cali_options.qd
            drag_width = qd.XYwave.time + 2 * qd.XYwave.offset
            target_component = self.coupler
        elif hasattr(self, "coupler"):
            drag_width = self.qubits[0].XYwave.time + 2 * self.qubits[0].XYwave.offset
            target_component = self.coupler
        else:
            drag_width = self.qubit.XYwave.time + 2 * self.qubit.XYwave.offset
            target_component = self.qubit

        # adjust tb by xy_delay, to optimize readout
        xy_delay = self.experiment_options.xy_delay
        add_tb_width = self.experiment_options.add_tb_width
        new_tb = xy_delay + drag_width / 2 + add_tb_width

        # new case distortion, set ac, readout point
        z_amp = self.experiment_options.z_amp

        # bugfixed, options_wrapper will run twice !!!
        rdz_amp = self.run_options.rdz_amp
        if rdz_amp is None:
            for qc in self.compensates.keys():
                if qc.name == target_component.name:
                    qc.ac = 0.0
                    old_rdz_amp = qc.readout_point.amp
                    new_rdz_amp = old_rdz_amp + z_amp
                    qc.readout_point_model = "Constant"
                    qc.readout_point = Options(amp=new_rdz_amp)
                    self.run_options.rdz_amp = new_rdz_amp

                    pyqlog.info(
                        f"target: {target_component}, "
                        f"ac: {qc.ac} V, "
                        f"readout_point_model: {qc.readout_point_model}, "
                        f"readout_point.amp: {qc.readout_point.amp} V"
                    )
            if self.ac_bias.get(target_component.name):
                old_awg_bias = self.ac_bias[target_component.name][1]
                self.ac_bias[target_component.name][1] = old_awg_bias - z_amp

        self.set_experiment_options(tb=new_tb)
        self.set_run_options(exp_qubit=self.qubit, drag_qubit=self.qubit)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "XY-Delay": (self.experiment_options.xy_delay, "ns")
        }
        return metadata
