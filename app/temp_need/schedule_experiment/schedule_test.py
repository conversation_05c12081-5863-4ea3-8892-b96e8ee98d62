# -*- coding: utf-8 -*-
import time

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/03/19
# __author:       <PERSON><PERSON><PERSON>

import numpy as np
from app.config import init_backend
from pyQCat.tools import qarange
from schedule_composite_experiment import ScheduleCompositeExperiment
from scheudle_lib import *


def schedule_test(backend):
    ctx = backend.context_manager.generate_context(
        name="cz_gate_calibration",
        physical_unit="q1q2",
        readout_type="union-01-01"
    )
    exp = ScheduleCompositeExperiment.from_experiment_context(ctx)
    exp.set_experiment_options(
        schedule_name="CZAssistSchedule",
        use_simulator=True,
        run_mode="async",
        scan_parameters=[
            PulseParameter(key="phase", level=0, qubit="q1", index=2, mode="xy", value=qarange(0, np.pi * 2, 0.2), is_plot=True),
            PulseParameter(key="amp", level=1, qubit="q1", index=1, mode="z", value=qarange(0, 0.3, 0.02), is_plot=True),
        ],
        const_parameters=[
            # PulseParameter(key="amp", qubit="q1", index=1, mode="z", value=0.1),
            # PulseParameter(key="amp", qubit="q2", index=1, mode="z", value=0.1),
            # PulseParameter(key="amp", qubit="c-12", index=1, mode="z", value=0.2),
        ]
    )
    exp.set_child_exp_options(
        ramsey_bit="q1",
        fake_pulse=True
    )
    exp.run()


if __name__ == '__main__':

    _backend = init_backend()

    start = time.perf_counter()
    schedule_test(_backend)
    end = time.perf_counter()
    print("Elapsed time: ", end - start)
