# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/02/23
# __author:       <PERSON><PERSON><PERSON>

"""
Quantum State Tomography experiment
"""

import itertools
from collections import defaultdict
from copy import deepcopy
from typing import List

import numpy as np

from ..top_experiment import TopExperiment
from ...analysis import StateTomographyAnalysis
from ...analysis.algorithms import tensor_combinations
from ...errors import ExperimentFieldError
from ...gate.notable_gate import GateBucket
from ...log import pyqlog
from ...pulse.pulse_lib import Constant
from ...structures import MetaData, Options
from ...tools.utilities import get_multi_readout_channels


class StateTomography(TopExperiment):
    """Quantum state tomography experiment.

    # section: overview
        Quantum state tomography (QST) is a method for experimentally
        reconstructing the quantum state from measurement data.

        A QST experiment measures the state prepared by quantum
        circuit in different measurement bases and post-processes the
        measurement data to reconstruct the state.

    # section: analysis_ref
        :py:class:`StateTomographyAnalysis`
    """

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("base_gate", list)
        options.set_validator("pre_gate", list)

        options.pre_gate = ["I"]
        options.base_gate = ["I", "X/2", "Y/2"]

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.qh = None
        options.ql = None
        options.parking_qubits = None
        options.exp_gates = None
        options.gate_bucket = None
        options.ideal_matrix = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("use_mle", bool)

        options.use_mle = True
        options.base_gate = None
        options.base_ops = None

        return options

    def _set_xy_pulses(self):
        if self.qubit_pair is None:
            self._set_single_gate_xy_pulse()
        else:
            self._set_double_gate_xy_pulse()

    def _set_z_pulses(self):

        if self.qubit_pair:
            exp_gates = self.run_options.exp_gates
            qh = self.run_options.qh
            ql = self.run_options.ql
            gate_bucket = self.run_options.gate_bucket

            # generate exp pulse
            qh_z_pulse = []
            ql_z_pulse = []
            for one_loop_gates in exp_gates:
                qh_gates, ql_gates = one_loop_gates
                qh_z_pulse.append(gate_bucket.get_z_pulse(qh, qh_gates))
                ql_z_pulse.append(gate_bucket.get_z_pulse(ql, ql_gates))
            self.play_pulse("Z", qh, qh_z_pulse)
            self.play_pulse("Z", ql, ql_z_pulse)

            # generate parking pulse
            parking_qubit_pulses = defaultdict(list)
            for one_loop_gates in exp_gates:
                gates, _ = one_loop_gates
                for qubit in self.run_options.parking_qubits:
                    pulse = gate_bucket.get_z_pulse(qubit, gates)
                    parking_qubit_pulses[qubit].append(pulse)
            for qubit, pulses in parking_qubit_pulses.items():
                self.play_pulse("Z", qubit, pulses)

    def _set_single_gate_xy_pulse(self):
        pre_gate = self.experiment_options.pre_gate
        gate_bucket = self.run_options.gate_bucket

        gates = []
        for gate in self.experiment_options.base_gate:
            pre_gates = deepcopy(pre_gate)
            pre_gates.append(gate)
            gates.append(pre_gates)

        xy_pulses = []
        for gate in gates:
            xy_pulses.append(gate_bucket.get_xy_pulse(self.qubit, gate))

        self.play_pulse("XY", self.qubit, xy_pulses)

    def _set_double_gate_xy_pulse(self):
        # check pre gate
        pre_gate = self.experiment_options.pre_gate
        gate_bucket = self.run_options.gate_bucket
        qh = self.run_options.qh
        ql = self.run_options.ql

        # generate exp gates
        exp_gates = []
        for gate in self.experiment_options.base_gate:
            pre_gates = deepcopy(pre_gate)
            pre_gates[0].append(gate[0])
            pre_gates[1].append(gate[1])
            exp_gates.append(pre_gates)
        self.set_run_options(exp_gates=exp_gates)

        # generate qh and ql exp pulse
        qh_xy_pulse = []
        ql_xy_pulse = []
        for one_loop_gates in exp_gates:
            qh_gates, ql_gates = one_loop_gates
            qh_xy_pulse.append(gate_bucket.get_xy_pulse(qh, qh_gates))
            ql_xy_pulse.append(gate_bucket.get_xy_pulse(ql, ql_gates))
        self.play_pulse("XY", qh, qh_xy_pulse)
        self.play_pulse("XY", ql, ql_xy_pulse)

        # generate parking qubit exp pulse
        for qubit in self.run_options.parking_qubits:
            if qubit.name.startswith("q"):
                pulse_list = []
                for pulse in ql_xy_pulse:
                    xy_p = Constant(pulse.width, 0, name="XY")()
                    pulse_list.append(xy_p)
                self.play_pulse("XY", qubit, pulse_list)

    def _set_measure_pulses(self):
        """Set readout pulse."""
        if self.qubit:
            self._set_single_readout_pulse(qubit=self.qubit)
        else:
            qh = self.run_options.qh
            ql = self.run_options.ql
            self._set_union_readout_pulse(qubits=[qh, ql])

    def _check_options(self):
        super()._check_options()

        # check pre gate
        pre_gate = self.experiment_options.pre_gate
        pre_gate = self._check_pre_gate(pre_gate)
        self.set_experiment_options(pre_gate=pre_gate)

        # check gate bucket
        gate_bucket = self.run_options.gate_bucket
        if gate_bucket is None:
            pyqlog.log("EXP", "Gate Bucket is null, create gate bucket!")
            gate_bucket = GateBucket()
            self.set_run_options(gate_bucket=gate_bucket)

            # check qubit
            if self.qubit:
                # one qubit qst
                gate_bucket.bind_single_gates(self.qubit)
            else:
                # two qubit qst
                pair = self.qubit_pair
                if pair is None:
                    raise ExperimentFieldError(
                        self.label, "qubit pair is none, please check!"
                    )
                parking_qubits = []
                base_qubits = []
                base_qubits.extend(self.qubits)
                base_qubits.extend(self.couplers)
                for qubit in base_qubits:
                    if qubit.name == pair.ql:
                        gate_bucket.bind_single_gates(qubit)
                        self.set_run_options(ql=qubit)
                    elif qubit.name == pair.qh:
                        gate_bucket.bind_single_gates(qubit)
                        self.set_run_options(qh=qubit)
                    if qubit.name in pair.parking_bits:
                        parking_qubits.append(qubit)
                self.set_run_options(parking_qubits=parking_qubits)
                gate_bucket.bind_cz_gates(pair, base_qubits)

            # check base ops and base gate
            base_ops = self.analysis_options.base_ops
            base_gate = self.experiment_options.base_gate
            # `base_gate` (an experiment option of `StateTomography`) is corresponding to `mea_base_gates` in `TomographyCalculator1`.
            if base_ops is None:
                base_ops = [gate_bucket.get_matrix(gate) for gate in base_gate]
                if self.qubit_pair is not None:
                    base_ops = tensor_combinations(base_ops, repeat=2)
                self.set_analysis_options(base_ops=base_ops)
            if self.qubit_pair is not None and isinstance(base_gate[0], str):
                base_gate = list(itertools.product(base_gate, repeat=2))
                self.set_experiment_options(base_gate=base_gate)

            # check dcm
            if self.qubit_pair:
                if (
                    not isinstance(self.discriminator, List)
                    or len(self.discriminator) != 2
                ):
                    raise ExperimentFieldError(self.label, f"QST must support dcm")

                # bug fix 2023/04/27: only two qubit qst need to set mul readout channel
                qh = self.run_options.qh
                ql = self.run_options.ql
                multi_readout_channels = get_multi_readout_channels([qh, ql])
                self.set_experiment_options(
                    multi_readout_channels=multi_readout_channels,
                )

            self.set_experiment_options(data_type="I_Q", is_dynamic=0)

        ideal_matrix = self._calcu_ideal_matrix(pre_gate)
        self.set_run_options(ideal_matrix=ideal_matrix)

    def _metadata(self) -> MetaData:
        metadata = super()._metadata()

        pre_gate = self.experiment_options.pre_gate
        ideal_matrix = self.run_options.ideal_matrix
        metadata.draw_meta = {"pre_gate": pre_gate if pre_gate else ""}
        metadata.process_meta = {"ideal_matrix": ideal_matrix}

        return metadata

    def _check_pre_gate(self, pre_gate):
        qubit_nums = 2 if self.qubit_pair else 1

        if pre_gate is None:
            return [[], []] if qubit_nums == 2 else []

        if isinstance(pre_gate, str):
            return [[pre_gate], [pre_gate]] if qubit_nums == 2 else [pre_gate]
        elif isinstance(pre_gate, list) and qubit_nums == 2:
            if len(pre_gate) == 1:
                return [deepcopy(pre_gate), deepcopy(pre_gate)]
            elif len(pre_gate) == 2:
                return pre_gate
        elif qubit_nums == 1:
            return pre_gate

        raise ExperimentFieldError(
            self.label, f"QST pre gate {pre_gate} is not excepted!"
        )

    def _calcu_ideal_matrix(self, pre_gate: List):
        """Calculate pre_gate ideal matrix."""
        gate_bucket = self.run_options.gate_bucket
        q_nums = 2 if self.qubit_pair else 1
        ideal_r_arr = np.zeros((2**q_nums, 2**q_nums))
        ideal_r_arr[0][0] = 1

        if q_nums == 2:
            ideal_u = None
            for l_gate, r_gate in zip(*pre_gate):
                l_mat = gate_bucket.get_matrix(l_gate)
                r_mat = gate_bucket.get_matrix(r_gate)
                if l_gate == "CZ" or r_gate == "CZ":
                    s_mat = l_mat
                else:
                    s_mat = np.kron(l_mat, r_mat)
                if ideal_u is None:
                    ideal_u = s_mat
                else:
                    ideal_u = np.matmul(s_mat, ideal_u)
        else:
            ideal_u = None
            for p_gate in pre_gate:
                s_mat = gate_bucket.get_matrix(p_gate)
                if ideal_u is None:
                    ideal_u = s_mat
                else:
                    ideal_u = np.matmul(s_mat, ideal_u)
        try:
            conj_ideal_u = np.transpose(np.conj(ideal_u))
            ideal_matrix = np.matmul(
                np.matmul(ideal_u, ideal_r_arr),
                conj_ideal_u,
            )
        except Exception as err:
            pyqlog.warning(f"Calculate pre_gate ideal_matrix error: {err}")
            ideal_matrix = None
        return ideal_matrix

    def _special_run_analysis(self):
        """Experiment special analysis run logic."""
        counts = 3 ** (2 if self.qubit_pair else 1)
        self._run_analysis(
            x_data=[i for i in range(counts)], analysis_class=StateTomographyAnalysis
        )
        self.file.save_data(
            self.analysis.results.density_matrix.value,
            name="density_matrix",
            suffix="density_matrix",
        )

    def run(self):
        super().run()
        self._special_run_analysis()
