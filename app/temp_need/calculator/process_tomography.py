# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/02/24
# __author:       <PERSON><PERSON><PERSON>

import itertools
from copy import deepcopy

from ..composite_experiment import CompositeExperiment
from ..single import StateTomography
from ...analysis import AnalysisResult
from ...analysis.tomography_analysis import ProcessTomographyAnalysisV1
from ...errors import ExperimentOptionsError
from ...gate import GateBucket, GateCollection
from ...structures import MetaData, Options


class ProcessTomography(CompositeExperiment):

    _sub_experiment_class = StateTomography

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options for AC spectrum experiment.

        Experiment options:
            init_fringe (float): The init value of fringe.
            delays (Union[List, np.ndarray]): Delay time scanned when performing Ramsey
                                              experiments.
            z_amp_list (Union[List, np.ndarray]): Z line amplitude (flux pulse) sweep list.
            freq_bound (Optional[float], optional): Experiment will be stopped when qubit
                                                    frequency delta value lower than this value.
                                                    Defaults to 800MHz.
            osc_freq_limit (Optional[float], optional): [description]. Defaults to 2.5.
        """
        options = super()._default_experiment_options()

        options.set_validator("base_gates", list)
        options.set_validator("qst_base_gates", list)
        options.set_validator("goal_gate", GateCollection.gate_infos())

        options.base_gates = ["I", "X/2", "Y/2", "-X/2"]
        options.qst_base_gates = ["I", "X/2", "Y/2"]
        options.goal_gate = None
        options.goal_matrix = None  # `goal_matrix` (an experiment option) is corresponding to `goal_gate_matrix` (an analysis option)

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("use_mle", bool)
        options.set_validator("fidelity_accuracy", (1, 10, 0))

        options.use_mle = True
        options.sigma_basis = None
        options.goal_gate_matrix = None
        options.base_ops = None
        options.qst_base_ops = None
        options.labels = None
        options.qubit_nums = None
        options.goal_gate = None

        # accuracy of result calculation fidelity
        options.fidelity_accuracy = 3

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set run experiment operate options.

        Options:
            parking_qubits (List): List of BaseQubit,
                when name in parking_bits.

        """
        options = super()._default_run_options()

        options.qh = None
        options.ql = None

        options.gate_bucket = GateBucket()

        return options

    def _check_options(self):
        super()._check_options()
        base_gates = self.experiment_options.base_gates

        qubit_nums = 2 if self.qubit_pair else 1
        gate_bucket = self.run_options.gate_bucket
        self._label += f"{qubit_nums}Q"

        self.set_analysis_options(
            goal_gate=self.experiment_options.goal_gate,
            goal_gate_matrix=self.experiment_options.goal_matrix,
            base_ops=[gate_bucket.get_matrix(gate) for gate in base_gates],
            qst_base_ops=[
                gate_bucket.get_matrix(gate)
                for gate in self.child_experiment.experiment_options.base_gate
            ],
            labels=list(gate_bucket.pauli_matrix.keys()),
            sigma_basis=list(gate_bucket.pauli_matrix.values()),
            qubit_nums=qubit_nums,
        )

        if qubit_nums == 2:
            self.set_analysis_options(result_name=self.qubit_pair.name)
            for qubit in self.qubits:
                if qubit.name == self.qubit_pair.qh:
                    self.set_run_options(qh=qubit)
                elif qubit.name == self.qubit_pair.ql:
                    self.set_run_options(ql=qubit)
        else:
            self.set_analysis_options(result_name=self.qubits[0].name)

    def _metadata(self) -> MetaData:
        """Return experiment metadata for ExperimentData."""
        metadata = super()._metadata()

        metadata.draw_meta = {"Gate": self.experiment_options.goal_gate}

        return metadata

    def run(self):
        super().run()

        pre_gate_list = self._generate_pre_gates()

        for i, pre_gate in enumerate(pre_gate_list):
            qst_exp = deepcopy(self.child_experiment)
            qst_exp.set_experiment_options(pre_gate=pre_gate)
            qst_exp.set_parent_file(
                self,
                description=str(pre_gate).replace("/", "-"),
                index=i,
                total=len(pre_gate_list),
            )
            self._check_simulator_data(qst_exp, i)
            qst_exp.run()

            result = qst_exp.analysis.results
            density_matrix = result.density_matrix.value

            qst_exp.analysis.provide_for_parent["density_matrix"] = density_matrix
            self._experiments.append(qst_exp)

        x_data = [i for i in range(len(pre_gate_list))]
        self._run_analysis(x_data=x_data, analysis_class=ProcessTomographyAnalysisV1)

        self.file.save_text(
            self.analysis.results.ideal_chi_matrix.__str__(), name="ideal_chi_matrix"
        )
        self.file.save_text(
            self.analysis.results.exp_chi_matrix.__str__(), name="exp_chi_matrix"
        )
        self.file.save_data(
            self.analysis.results.ideal_chi_matrix.value,
            name="ideal_chi_matrix",
        )
        self.file.save_data(
            self.analysis.results.exp_chi_matrix.value,
            name="exp_chi_matrix",
        )

        self._set_result_path()

    def _generate_pre_gates(self):
        qubit_nums = 2 if self.qubit_pair else 1
        base_gates = self.experiment_options.base_gates
        goal_gate = self.experiment_options.goal_gate

        if qubit_nums == 1:
            pre_gate_list = []
            for index in range(2):
                if index == 0:
                    pre_gate_list.extend(
                        [[gate] for gate in self.experiment_options.base_gates]
                    )
                else:
                    pre_gate_list.extend(
                        [
                            [gate, self.experiment_options.goal_gate]
                            for gate in self.experiment_options.base_gates
                        ]
                    )
        else:
            if goal_gate == "CZ":
                goal_gate = [["CZ"], ["CZ"]]
            else:
                raise ExperimentOptionsError(
                    self, key="goal_gate",
                    value=goal_gate, msg="Current only support cz gate!"
                )
            gates_left = []
            gates_right = []
            for item in itertools.product(base_gates, repeat=2):
                gates_left.append([[item[0]], [item[1]]])
                r_l = [item[0]]
                r_l.extend(goal_gate[0])
                r_r = [item[1]]
                r_r.extend(goal_gate[1])
                gates_right.append([r_l, r_r])
            gates_left.extend(gates_right)
            pre_gate_list = gates_left

        return pre_gate_list

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        if self.experiment_options.goal_gate == "CZ":
            for key, result in self.analysis.results.items():
                if key == "fidelity":
                    result.extra["path"] = f"QubitPair.metadata.std.fidelity.qpt_fidelity"
                elif key == "process_fidelity":
                    result.extra["path"] = f"QubitPair.metadata.std.fidelity.qpt_process_fidelity"
            qh_qpt = self.run_options.qh.qpt_fidelity
            ql_qpt = self.run_options.ql.qpt_fidelity
            v = self.analysis.results.fidelity.value + 2 - (ql_qpt + qh_qpt)

            self.analysis.results["ref_fidelity"] = AnalysisResult(
                name="ref_fidelity",
                value=self.analysis.results.fidelity.value,
                extra={"path": f"QubitPair.metadata.std.pair_fidelity.ref_fidelity.qpt", "name": self.qubit_pair.name},
            )

            self.analysis.results["aft_fidelity"] = AnalysisResult(
                name="aft_fidelity",
                value=v,
                extra={"path": f"QubitPair.metadata.std.pair_fidelity.aft_fidelity.qpt", "name": self.qubit_pair.name},
            )

        elif self.experiment_options.goal_gate == "X" and not self.qubit_pair:
            for key, result in self.analysis.results.items():
                if key == "fidelity":
                    result.extra["path"] = f"Qubit.qpt_fidelity"

# ----------------------------------------------------
# # -*- coding: utf-8 -*-
#
# # This code is part of pyqcat-monster.
# #
# # Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# # Unless required by applicable law or agreed to in writing, software
# # distributed under the License is distributed on an "AS IS" BASIS,
# # WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#
# # __date:         2023/02/24
# # __author:       YangChao Zhao
#
# import itertools
# from copy import deepcopy
#
# # from .. import TopExperiment
# from pyQCat.experiments.top_experiment import TopExperiment
# from ..composite_experiment import CompositeExperiment
# from ..single import StateTomography
# from ...analysis import AnalysisResult
# from ...analysis.tomography_analysis import ProcessTomographyAnalysisV1
# from ...errors import ExperimentOptionsError
# from ...gate import GateBucket, GateCollection
# from ...structures import MetaData, Options
#
#
# # class ProcessTomography(CompositeExperiment):
# class ProcessTomography(TopExperiment):
#
#     # _sub_experiment_class = StateTomography
#
#     @classmethod
#     def _default_experiment_options(cls) -> Options:
#         options = super()._default_experiment_options()
#
#         options.prep_base_gates = ["I", "X/2", "Y/2", "-X/2"]
#         options.set_validator("prep_base_gates", list)  # `base_gates` (an experiment option) is corresponding to `prep_base_gates` in `TomographyCalculator1`. `base_gates` is now renamed to `prep_base_gates`, so the same thing has the same names in tomography calculators and process tomography experiments.
#
#         options.mea_base_gates = ["I", "X/2", "Y/2"]
#         options.set_validator("mea_base_gates", list)  # `base_gate` (an experiment option of `StateTomography`) is corresponding to `mea_base_gates` in `TomographyCalculator1`. Since `ProcessTomography` doesn't depend on `StateTomography` anymore, we now have an new experiment option called `mea_base_gates` in `ProcessTomography`. `base_gate` is the predecessor of this new experiment option.
#
#         options.goal_gate = None
#         options.set_validator("goal_gate", GateCollection.gate_infos())
#
#         options.goal_gate_matrix = None
#
#         return options
#
#     @classmethod
#     def _default_analysis_options(cls) -> Options:
#         options = super()._default_analysis_options()
#
#         options.use_mle = True
#         options.set_validator("use_mle", bool)
#
#         options.fidelity_accuracy = 3
#         options.set_validator("fidelity_accuracy", (1, 10, 0))
#
#         options.sigma_basis = None
#         options.goal_gate_matrix = None
#         options.goal_gate = None
#         options.prep_base_gates = None
#         options.labels = None
#         options.qubit_nums = None
#         options.mea_base_gates = None
#
#         return options
#
#     @classmethod
#     def _default_run_options(cls) -> Options:
#         options = super()._default_run_options()
#
#         options.qh = None
#         options.ql = None
#         options.gate_bucket = GateBucket()
#
#         return options
#
#     def _check_options(self):
#         super()._check_options()
#
#         qubit_nums = 2 if self.qubit_pair else 1
#         self._label += f"{qubit_nums}Q"
#         gate_bucket = self.run_options.gate_bucket
#
#         self.set_analysis_options(
#             goal_gate=self.experiment_options.goal_gate,
#             goal_gate_matrix=self.experiment_options.goal_gate_matrix,
#             prep_base_gates=self.experiment_options.prep_base_gates,  # `base_gates` (an experiment option) is corresponding to `prep_base_gates` in `TomographyCalculator1`. `base_gates` is now renamed to `prep_base_gates`, so the same thing has the same names in tomography calculators and process tomography experiments.
#             # mea_base_gates=self.child_experiment.experiment_options.base_gate,# `base_gate` (an experiment option of `StateTomography`) is corresponding to `mea_base_gates` in `TomographyCalculator1`.
#             mea_base_gates=self.experiment_options.mea_base_gates,# `base_gate` (an experiment option of `StateTomography`) is corresponding to `mea_base_gates` in `TomographyCalculator1`, now it is renamed `mea_base_gates`.
#             labels=list(gate_bucket.pauli_matrix.keys()),
#             qubit_nums=qubit_nums,
#         )
#
#         if qubit_nums == 2:
#             self.set_analysis_options(result_name=self.qubit_pair.name)
#             for qubit in self.qubits:
#                 if qubit.name == self.qubit_pair.qh:
#                     self.set_run_options(qh=qubit)
#                 elif qubit.name == self.qubit_pair.ql:
#                     self.set_run_options(ql=qubit)
#         else:
#             self.set_analysis_options(result_name=self.qubits[0].name)
#
#     def _metadata(self) -> MetaData:
#         """Return experiment metadata for ExperimentData."""
#         metadata = super()._metadata()
#         metadata.draw_meta = {"Gate": self.experiment_options.goal_gate}
#         return metadata
#
#     def run(self):
#         super().run()
#
#         pre_gate_list = self._generate_pre_gates()
#
#         # for i, pre_gate in enumerate(pre_gate_list):
#         #     qst_exp = deepcopy(self.child_experiment)
#         #     qst_exp.set_experiment_options(pre_gate=pre_gate)
#         #     qst_exp.set_parent_file(
#         #         self,
#         #         description=str(pre_gate).replace("/", "-"),
#         #         index=i,
#         #         total=len(pre_gate_list),
#         #     )
#         #     self._check_simulator_data(qst_exp, i)
#         #     # qst_exp.run()
#         #
#         #     result = qst_exp.analysis.results
#         #     density_matrix = result.density_matrix.value
#         #
#         #     qst_exp.analysis.provide_for_parent["density_matrix"] = density_matrix
#         #     self._experiments.append(qst_exp)
#
#         x_data = [i for i in range(len(pre_gate_list))]
#         self._run_analysis(x_data=x_data, analysis_class=ProcessTomographyAnalysisV1)
#
#         self.file.save_text(
#             self.analysis.results.ideal_chi_matrix.__str__(), name="ideal_chi_matrix"
#         )
#         self.file.save_text(
#             self.analysis.results.exp_chi_matrix.__str__(), name="exp_chi_matrix"
#         )
#         self.file.save_data(
#             self.analysis.results.ideal_chi_matrix.value,
#             name="ideal_chi_matrix",
#         )
#         self.file.save_data(
#             self.analysis.results.exp_chi_matrix.value,
#             name="exp_chi_matrix",  # chi_exp is also called exp_chi_matrix.
#         )
#
#         self._set_result_path()
#
#     def _generate_pre_gates(self):
#         qubit_nums = 2 if self.qubit_pair else 1
#         base_gates = self.experiment_options.base_gates
#         goal_gate = self.experiment_options.goal_gate
#
#         if qubit_nums == 1:
#             pre_gate_list = []
#             for index in range(2):
#                 if index == 0:
#                     pre_gate_list.extend(
#                         [[gate] for gate in self.experiment_options.base_gates]
#                     )
#                 else:
#                     pre_gate_list.extend(
#                         [
#                             [gate, self.experiment_options.goal_gate]
#                             for gate in self.experiment_options.base_gates
#                         ]
#                     )
#         else:
#             if goal_gate == "CZ":
#                 goal_gate = [["CZ"], ["CZ"]]
#             else:
#                 raise ExperimentOptionsError(
#                     self, key="goal_gate",
#                     value=goal_gate, msg="Current only support cz gate!"
#                 )
#             gates_left = []
#             gates_right = []
#             for item in itertools.product(base_gates, repeat=2):
#                 gates_left.append([[item[0]], [item[1]]])
#                 r_l = [item[0]]
#                 r_l.extend(goal_gate[0])
#                 r_r = [item[1]]
#                 r_r.extend(goal_gate[1])
#                 gates_right.append([r_l, r_r])
#             gates_left.extend(gates_right)
#             pre_gate_list = gates_left
#
#         return pre_gate_list
#
#     def _set_result_path(self):
#         """Set path to save parameter of Qubit or Coupler."""
#         if self.experiment_options.goal_gate == "CZ":
#             for key, result in self.analysis.results.items():
#                 if key == "fidelity":
#                     result.extra["path"] = f"QubitPair.metadata.std.fidelity.qpt_fidelity"
#                 elif key == "process_fidelity":
#                     result.extra["path"] = f"QubitPair.metadata.std.fidelity.qpt_process_fidelity"
#             qh_qpt = self.run_options.qh.qpt_fidelity
#             ql_qpt = self.run_options.ql.qpt_fidelity
#             v = self.analysis.results.fidelity.value + 2 - (ql_qpt + qh_qpt)
#
#             self.analysis.results["ref_fidelity"] = AnalysisResult(
#                 name="ref_fidelity",
#                 value=self.analysis.results.fidelity.value,
#                 extra={"path": f"QubitPair.metadata.std.pair_fidelity.ref_fidelity.qpt", "name": self.qubit_pair.name},
#             )
#
#             self.analysis.results["aft_fidelity"] = AnalysisResult(
#                 name="aft_fidelity",
#                 value=v,
#                 extra={"path": f"QubitPair.metadata.std.pair_fidelity.aft_fidelity.qpt", "name": self.qubit_pair.name},
#             )
#         elif self.experiment_options.goal_gate == "X" and not self.qubit_pair:
#             for key, result in self.analysis.results.items():
#                 if key == "fidelity":
#                     result.extra["path"] = f"Qubit.qpt_fidelity"
