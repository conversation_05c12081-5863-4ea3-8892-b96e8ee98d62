# -*- coding: utf-8 -*-

# This code is part of QStream.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/07/20
# __author:       <PERSON><PERSON><PERSON>

import itertools
import warnings

import numpy as np
import qutip as qp

from .algorithms.tomography import (
    gen_ideal_chi_matrix,
    init_qpt,
    init_qst,
    qpt,
    qpt_mle,
    qst,
    qst_mle,
    tensor_combinations,
    TomographyCalculator2,
    TomographyCalculator1,
)
from .specification import ParameterRepr
from .top_analysis import TopAnalysis
from .visualization.tomography_drawer import TomographyDrawer
from ..log import logger
from ..structures import Options, QDict

warnings.filterwarnings("ignore")


class TomographyAnalysis(TopAnalysis):
    """Base analysis for state and process tomography experiments."""

    @property
    def drawer(self) -> TomographyDrawer:
        """A short-cut for curve drawer instance."""
        return self._options.drawer

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        **Analysis Options**:

            - **labels (List)** - Matrix distribution chart axis label.

            - **use_mle (bool)** - Whether to use maximum likelihood estimation

            - **drawer (TomographyDrawer)** - Matrix drawing, plotting the real
              and imaginary parts of a matrix.

        Returns:
            Tomography base experiment analysis options.
        """
        options = super()._default_options()
        options.set_validator(field="drawer", validator_value=TomographyDrawer)

        options.qubit_nums = 1
        options.labels = None
        options.use_mle = False
        options.drawer = TomographyDrawer()
        options.fidelity_accuracy = 3

        return options

    def run_analysis(self):
        """Start analysis.

        Define standard chromatography procedures as follows:

            - Initialize tomography analysis with experiment data
            - Tomography operations
            - Extract tomography result
            - Visualization of experimental results
        """
        self._initialize()
        self._tomography()
        self._extract_result()
        if self.options.is_plot:
            self._visualization()

    def _initialize(self):
        """Initialize tomography analysis with experiment data."""
        # create analysis data and result data.
        self._analysis_data_dict = self._create_analysis_data()
        self._results = self._create_analysis_result()

        if self.options.is_plot:
            self._initialize_canvas()

    def _extract_result(self):
        """Extract experimental results according to experimental result parameters."""
        for param_repr in self.options.result_parameters:
            if isinstance(param_repr, ParameterRepr):
                param_repr = param_repr.name
            value = self.analysis_datas.get(param_repr)
            if isinstance(value, float):
                value = np.round(value * 100, self.options.fidelity_accuracy)
            self.results.get(param_repr).value = value

    def _description(self):
        """A description of the important information in the figure."""
        outcomes = self.__class__.__name__

        draw_str = ""
        draw_meta = self.experiment_data.metadata.draw_meta
        for key, value in draw_meta.items():
            draw_str += f"{key}={value} "

        outcomes += f"\n{draw_str}\n"

        if self.results.get("fidelity"):
            outcomes += self.results.get("fidelity").__str__()
        if self.results.get("process_fidelity"):
            outcomes += self.results.get("process_fidelity").__str__()

        return outcomes

    def _tomography(self):
        pass

    def _create_analysis_data(self):
        pass

    def _visualization(self):
        """Plot the real and imaginary parts of a matrix."""
        if self.results.density_matrix:
            density_matrix = self.results.density_matrix.value
            matrix = [density_matrix]
            self.drawer.draw_matrix_divide(matrix)
        else:
            ideal_chi_matrix = self.results.ideal_chi_matrix.value
            exp_chi_matrix = self.results.exp_chi_matrix.value
            matrix = [ideal_chi_matrix, exp_chi_matrix]
            if self.options.qubit_nums == 2:
                self.drawer.draw_matrix(matrix)
            else:
                self.drawer.draw_matrix_divide(matrix)
        self.drawer.set_options(sup_title=self._description())
        self.drawer.format_canvas()

    def _initialize_canvas(self):
        """Initialize matplotlib canvas."""
        if self.options.qubit_nums == 2:
            labels = []
            for label in list(itertools.product(self.options.labels, repeat=2)):
                labels.append("".join(label))
            self.options.labels = labels

        self.drawer.set_options(
            sup_title=self._description(),
            title=self.options.titles,
            xlabels=self.options.labels,
            ylabels=self.options.labels,
        )
        self.drawer.initialize_canvas()


class StateTomographyAnalysis(TomographyAnalysis):
    """Analysis for state tomography experiments."""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        **Analysis Options**:

            - **titles (List)** - Subplot title, default is ``[real, image]``.

            - **result_parameters (List)** - Expect to extract the ``density_matrix``

            - **base_gate (List)** - Projection gate in three directions (X, Y, Z).

            - **base_ops (List)** - Projection operator in three directions (X, Y, Z).

        Returns:
            State tomography experiment analysis options.
        """
        options = super()._default_options()
        options.titles = ["real", "image"]
        options.base_gate = ["I", "X/2", "Y/2"]
        options.base_ops = None

        options.result_parameters = [
            "density_matrix",
            ParameterRepr("fidelity", "fidelity", "%"),
        ]
        return options

    def _tomography(self):
        """Calculate the density matrix."""
        measure_result = self.analysis_datas.get(
            "measure_result"
        )  # type(measure_result) == <class 'numpy.ndarray'> # {ndarray: (3, 2)}
        base_ops = self.analysis_datas.get("base_ops")
        if self.options.use_mle:
            rho = qst_mle(measure_result, base_ops)
            qst_matrix = rho
        else:
            # bugfixed: maybe numpy version
            rho = qst(measure_result, init_qst(base_ops))
            qst_matrix = rho[0]
        self.analysis_datas.density_matrix = qst_matrix

        process_meta = self.experiment_data.metadata.process_meta
        ideal_matrix = process_meta.get("ideal_matrix")
        if ideal_matrix is not None:
            fidelity = qp.fidelity(qp.Qobj(qst_matrix), qp.Qobj(ideal_matrix))
            self.analysis_datas.fidelity = fidelity
            logger.info(f"QST fidelity: {fidelity}")

    def _create_analysis_data(self):
        """Constructing analysis data."""
        base_ops = self.options.base_ops
        result = list(self.experiment_data.y_data.values())

        analysis_data_dict = QDict()
        analysis_data_dict["measure_result"] = np.vstack(result).T
        analysis_data_dict["base_ops"] = base_ops
        return analysis_data_dict


class ProcessTomographyAnalysis(TomographyAnalysis):
    """Analysis for process tomography experiments."""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        **Analysis Options**:

            - **titles (List)** - Subplot title, default is
              ``[ideal-real, ideal-image, exp-real, exp-image]``.

            - **labels (List)** - Axis labels, default is ``[I, X, Y, Z]``.

            - **result_parameters (List)** - Expect to extract the ``exp_chi_matrix``,
              ``ideal_chi_matrix`` , ``fidelity`` and ``process_fidelity``.

        Returns:
            Process tomography experiment analysis options.
        """
        options = super()._default_options()
        options.titles = ["ideal-real", "ideal-image", "exp-real", "exp-image"]
        options.labels = ["I", "X", "Y", "Z"]
        options.result_parameters = [
            "exp_chi_matrix",
            "ideal_chi_matrix",
            ParameterRepr("fidelity", "fidelity", "%"),
            ParameterRepr("process_fidelity", "process_fidelity", "%"),
        ]
        return options

    def _create_analysis_data(self):
        """Constructing analysis data."""
        goal_gate_matrix = self.options.goal_gate_matrix
        base_ops = self.options.base_ops
        qst_base_ops = self.options.qst_base_ops

        measure_results = None
        for data in self.experiment_data.child_data():
            measure_result = np.vstack(list(data.y_data.values())).T
            if measure_results is None:
                measure_results = measure_result
            else:
                measure_results = np.vstack((measure_results, measure_result))

        analysis_data_dict = QDict()
        analysis_data_dict["goal_gate_matrix"] = goal_gate_matrix
        analysis_data_dict["base_ops"] = base_ops
        analysis_data_dict["qst_base_ops"] = qst_base_ops
        analysis_data_dict["density_matrix"] = self.experiment_data.y_data.get(
            "density_matrix"
        )
        analysis_data_dict["measure_results"] = measure_results

        return analysis_data_dict

    def _tomography(self):
        """Tomography process.

        We do as follows:

            - calculate ideal chi matrix
            - calculate exp chi matrix
            - calculate fidelity
        """
        sigma_basis = self.options.sigma_basis
        qubit_nums = self.options.qubit_nums
        sigma_basis = tensor_combinations(sigma_basis, repeat=qubit_nums)
        goal_gate_matrix = self.analysis_datas.goal_gate_matrix
        base_ops = self.analysis_datas.base_ops
        qst_base_ops = self.analysis_datas.qst_base_ops
        qst_base_ops = tensor_combinations(qst_base_ops, repeat=qubit_nums)
        sigma_ops = init_qpt(sigma_basis)
        ideal_chi_matrix, *_ = gen_ideal_chi_matrix(
            goal_gate_matrix, sigma_ops, base_ops
        )

        density_matrix_list = self.analysis_datas.density_matrix
        mid = int(len(density_matrix_list) / 2)
        l_rhos = density_matrix_list[:mid]
        r_rhos = density_matrix_list[mid:]

        if self.options.use_mle:
            exp_chi_matrix = qpt_mle(
                list(
                    self.analysis_datas.measure_results[
                        mid * (3**qubit_nums) :
                    ].reshape(4**qubit_nums, 3**qubit_nums, 2**qubit_nums)
                ),
                l_rhos,
                qst_base_ops,
                sigma_basis,
                sigma_ops,
            )
        else:
            exp_chi_matrix = qpt(l_rhos, r_rhos, sigma_ops)

        self.analysis_datas["ideal_chi_matrix"] = ideal_chi_matrix
        self.analysis_datas["exp_chi_matrix"] = exp_chi_matrix

        self._cal_fidelity()

    def _cal_fidelity(self):
        """Calculate CZ fidelity."""
        fidelity = qp.fidelity(
            qp.Qobj(self.analysis_datas.exp_chi_matrix),
            qp.Qobj(self.analysis_datas.ideal_chi_matrix),
        )
        process_fidelity = qp.process_fidelity(
            qp.Qobj(self.analysis_datas.exp_chi_matrix),
            qp.Qobj(self.analysis_datas.ideal_chi_matrix),
        )
        self.analysis_datas.fidelity = fidelity
        self.analysis_datas.process_fidelity = process_fidelity.real


class ProcessTomographyAnalysisV1(TomographyAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        **Analysis Options**:
            - **titles (List)** - Subplot title, defaults to
              ``[ideal-real, ideal-image, exp-real, exp-image]``.
            - **labels (List)** - Axis labels, defaults to ``[I, X, Y, Z]``.
            - **result_parameters (List)** - Expect to extract the ``exp_chi_matrix``,
              ``ideal_chi_matrix`` and ``process_fidelity``.

        Returns:
            Process tomography experiment analysis options.
        """
        options = super()._default_options()

        options.titles = ["ideal-real", "ideal-image", "exp-real", "exp-image"]
        options.labels = ["I", "X", "Y", "Z"]
        options.result_parameters = [
            "exp_chi_matrix",
            "ideal_chi_matrix",
            ParameterRepr("process_fidelity", "process_fidelity", "%"),
        ]

        return options

    def _create_analysis_data(self):
        return {}

    def _tomography(self):
        tomography_calculator1 = TomographyCalculator1(
            self.options.mea_base_gates, self.options.base_gates
        )

        tomography_calculator1.analysis_options.goal_gate = self.options.goal_gate
        tomography_calculator1.analysis_options.use_mle = self.options.use_mle
        # tomography_calculator1.analysis_options.pre_gate_list
        tomography_calculator1.analysis_options.mode = "qpt"
        tomography_calculator1.analysis_options.save_exp_chi_matrix = False
        tomography_calculator1.analysis_options.goal_gate_matrix = (
            self.options.goal_gate_matrix
        )

        measure_results = None
        for data in self.experiment_data.child_data():
            measure_result = np.vstack(list(data.y_data.values())).T
            if measure_results is None:
                measure_results = measure_result
            else:
                measure_results = np.vstack((measure_results, measure_result))
        tomography_calculator1.calculate(measure_results)
        # tomography_calculator1.calculate(np.array(self.experiment_data.y_data.values()))

        self._analysis_data_dict = tomography_calculator1.qpt_result


class ProcessTomographyAnalysisV2(TomographyAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.labels = ["I", "X", "Y", "Z"]
        options.neglect_mode = "neglect_chi_prep"
        options.cal_diag_in = True
        options.phase_opt = False
        options.result_parameters = [
            "process_fidelity",
            "chi_fidelity",
            "chi_err_rate",
            "phase",
        ]
        options.titles = [
            rf"$\chi^{{err}}_{{noSPAM}}(real)$",
            rf"$\chi^{{err}}_{{noSPAM}}(imag)$",
            rf"$\chi^{{exp}}$(in&out)",
            rf"$\chi^{{exp,err}}$(in&out)",
            rf"$D_{{noSPAM}}$",
        ]
        return options

    def _create_analysis_data(self):
        return {}

    def _tomography(self):
        calculator = TomographyCalculator2()
        calculator.analysis_options.goal_gate = (
            self.experiment_data.metadata.process_meta.get("goal_gate", "I")
        )
        calculator.analysis_options.neglect_mode = self.options.neglect_mode
        calculator.analysis_options.cal_diag_in = self.options.cal_diag_in
        calculator.analysis_options.phase_opt = self.options.phase_opt

        probability = list(self.experiment_data.y_data.values())
        calculator.calculate(probability)
        self._analysis_data_dict = calculator.result

    def _visualization(self):
        """Plot the real and imaginary parts of a matrix."""
        self.drawer.draw_matrix_divide(
            [
                self.analysis_datas.chi_err,
            ],
            row=3,
            col=2,
        )
        self.drawer.draw_matrix(
            [
                self.analysis_datas.chi_exp,
                self.analysis_datas.chi_exp_err,
                self.analysis_datas.chi_err_diag,
            ],
            start=2,
            row=3,
            col=2,
        )
        self.drawer.set_options(sup_title=self._description())
        self.drawer.format_canvas()

    def _extract_result(self):
        """Extract experimental results according to experimental result parameters."""
        for param_repr in self.options.result_parameters:
            value = self.analysis_datas.get(param_repr)
            self.results.get(param_repr).value = value

        # save matrix to local

    def _description(self):
        """A description of the important information in the figure."""
        outcomes = self.__class__.__name__

        draw_str = ""
        draw_meta = self.experiment_data.metadata.draw_meta
        for key, value in draw_meta.items():
            draw_str += f"{key}={value} "

        outcomes += f"\n{draw_str}\n"

        for k, v in self.results.items():
            outcomes += v.__str__()

        return outcomes


class StateTomographyAnalysisV2(TomographyAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.result_parameters = [
            "rho_exp",
            "rho_ideal",
            "fidelity",
        ]
        options.titles = ["rho_exp", "rho_ideal"]
        return options

    def _create_analysis_data(self):
        return {}

    def _tomography(self):
        calculator = TomographyCalculator2()
        calculator.analysis_options.mode = "qst"
        calculator.analysis_options.pre_gate_list = (
            self.experiment_data.metadata.process_meta.get("pre_gate_list", [])
        )
        probability = list(self.experiment_data.y_data.values())
        calculator.calculate(probability)
        self._analysis_data_dict = calculator.qst_result

    def _visualization(self):
        """Plot the real and imaginary parts of a matrix."""

        self.drawer.draw_matrix(
            [self.analysis_datas.rho_exp, self.analysis_datas.rho_ideal], row=1, col=2
        )
        self.drawer.set_options(sup_title=self._description())
        self.drawer.format_canvas()

    def _extract_result(self):
        """Extract experimental results according to experimental result parameters."""
        for param_repr in self.options.result_parameters:
            value = self.analysis_datas.get(param_repr)
            self.results.get(param_repr).value = value
