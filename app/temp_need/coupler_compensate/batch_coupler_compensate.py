# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/07/16
# __author:       <PERSON><PERSON><PERSON>

# type: ignore

import json
import copy
import numpy as np
import matplotlib.pyplot as plt
import textwrap
from pathlib import Path
from dataclasses import dataclass, field, asdict
from typing import Optional, List, Dict, Any, Union
from pyQCat.qubit import BaseQubit, QubitPair
from pyQCat.experiments.batch_experiment import BatchExperiment
from pyQCat.log import pyqlog
from pyQCat.structures import QDict
from pyQCat.analysis.quality import GoodnessofFit
from pyQCat.types import Quality

try:
    from pyQCat.analysis.algorithms.comp_wq2q2q_fit import (
        comp_wq2q2q_fit,
        freq2amp,
        comp_wq2q2q,
        _check_spectrum_key,
        _check_rho_key
    )
except Exception:
    print("no find comp_wq2q2q_fit!")

    def comp_wq2q2q_fit(**kwargs):
        pass


@dataclass
class SpectrumMap:
    ql: List = field(default_factory=list)
    qb: List = field(default_factory=list)
    qh: List = field(default_factory=list)


@dataclass
class ExperimentPoint:
    # experiment input
    pair: str = ""
    qt: str = ""
    qs: str = ""
    qt_freq: float = 0.0
    cz_freq_gap: float = 0.0
    qs_amp: float = 0.0
    qs_actual_amp: float = 0.0
    qs_point_name: str = ""
    qb_amp_list: List = field(default_factory=list)
    r2: float = 0.0
    idle_point_data: Dict = field(default_factory=dict)

    # experiment output
    qt_amp_list: List = field(default_factory=list)

    # analysis input
    qt_amp_fit_list: List = field(default_factory=list)

    def __repr__(self) -> str:
        qs_point_name = self.qs_point_name or self.qs_amp
        return f"Compen {self.qt}-Point({self.qt_freq})-Gap({self.cz_freq_gap}) {self.qs}-Amp({self.qs_actual_amp})-{qs_point_name}"

    def qb_zero_start_validate(self):
        "用户自定义的起始点必须为 0"
        if self.qb_amp_list:
            if not bool({0, 0.0} & set(self.qb_amp_list)):
                self.qb_amp_list.insert(0, 0.0)

    def result_qb_zero_start_validate(self, spectrum_map: SpectrumMap, method="copy"):
        """
        当 qb 电压为 0 时可能校准失败, 此时需要检查 0 起始点是否存在，如果因为
        FixPointCalibration 实验导致的校准失败, 则默认将此时 qt 的 amp 设置为
        qt_freq 所在位置
        """
        if self.qb_amp_list and self.qt_amp_list:
            if not {0, 0.0} & set(self.qb_amp_list):
                if method == "spectrum":
                    spectrum_params = getattr(spectrum_map, self.qt)
                    # trans qt_freq to amp
                    amp = freq2amp(
                        self.qt_freq,
                        *spectrum_params[2],
                        spectrum_type=spectrum_params[0],
                        branch=spectrum_params[1],
                    )
                    amp -= self.idle_point_data.get(self.qt, 0)
                    pyqlog.info(
                        f"{self} filter qb zero point, default insert by qt freq!"
                    )
                    self.qb_amp_list.insert(0, 0.0)
                    self.qt_amp_list.insert(0, amp)
                elif method == "copy":
                    self.qb_amp_list.insert(0, 0.0)
                    self.qt_amp_list.insert(0, self.qt_amp_list[0])
                else:
                    raise ValueError(f"method {method} is not supported now.")

    def result_qt_zero_start_validate(self):
        if self.qt_amp_list:
            self.qt_amp_list = list(np.array(self.qt_amp_list) - self.qt_amp_list[0])


@dataclass
class FitResult:
    popt: List = field(default_factory=list)
    r2: float = 0.0
    quality: str = ""

    def __repr__(self):
        return f"Quality-{self.quality} R2({self.r2})"


@dataclass
class FitInputCollections:
    fit_spectrum: bool = True
    spectrum_map: SpectrumMap = field(default_factory=SpectrumMap)
    points: List[ExperimentPoint] = field(default_factory=list)
    result: FitResult = field(default_factory=FitResult)

    def __repr__(self):
        if self.points:
            return f"{self.points[0].pair} QCShift Result"
        else:
            super().__repr__()

    def to_comp_wq2q2q_fit_data(self) -> Dict:
        z_amp_qb_exp_set = []
        z_amp_qt_exp_set = []
        z_amp_qs_exp_set = []
        eigen_set = []
        qt_name_set = []

        for point in self.points:
            z_amp_qb_exp_set.append(point.qb_amp_list)
            z_amp_qt_exp_set.append(point.qt_amp_list)
            z_amp_qs_exp_set.append(point.qs_actual_amp)
            eigen_set.append(point.qt_freq)
            qt_name_set.append(point.qt)

        return {
            "z_amp_qb_exp_set": z_amp_qb_exp_set,
            "z_amp_qt_exp_set": z_amp_qt_exp_set,
            "z_amp_qs_exp_set": z_amp_qs_exp_set,
            "eigen_set": eigen_set,
            "qt_name_set": qt_name_set,
            "spectrum_info": asdict(self.spectrum_map),
            "fit_spectrum": self.fit_spectrum,
        }

    @classmethod
    def from_origin_data_json(cls, data_file: str):
        with open(data_file, mode="r", encoding="utf-8") as fp:
            data = json.load(fp)

        origin_data = data.get("origin")

        return cls(
            fit_spectrum=origin_data.get("fit_spectrum"),
            spectrum_map=SpectrumMap(**origin_data.get("spectrum_map")),
            points=[
                ExperimentPoint(**point_data)
                for point_data in origin_data.get("points")
            ],
            result=FitResult(**origin_data.get("result", {})),
        )


class BatchCouplerCompensate(BatchExperiment):
    """
    BatchCouplerCompensate is used to collect the Shift response data of the
    coupler to QH and QL, and further fit the response model for voltage
    compensation of the coupler.
    """

    @classmethod
    def _default_experiment_options(cls):
        """Experiment options for user

        Options:
            qc_bias_map (Optional options): Bias qubit scan ac list, u can set as follows:
                {
                    "q1q2": {
                        "ql": [0, 0.01, 0.02, ...],
                        "qh": [0, 0.01, 0.02, ...],
                    }
                }
                default set by QCShiftFixedPointCalibration experiment.
            f_traget_map (Optional options): Target qubit goal frequency, u can set as follos:
                {
                    "q1q2": {
                        "ql": [4500., 4480., 4520.],
                        "qh": [4300., 4280., 4320.],
                    }
                }
                default set by function `_batch_up`
            f_spectator_map (Optional options): Spectator qubit work point, u can set as follows:
                {
                    "q1q2": {
                        "ql": [(0., "idle"), (0.1, "min")],
                        "qh": [(0., "idle"), (0.1, "min")],
                    }
                }
                default set by function `_batch_up`
            sepctrum_map (Optional options): AC spectrum, u can set as follows:
                {
                    "q1": ["standard", "left", [0., 0., 0., 0., 0.]],
                    "c1-2": ["standard", "left", [0., 0., 0., 0., 0.]],
                }
                default set by function `_batch_up`
            bound_map (Optional options): QC scan bound, u can set as follows:
                {
                   "q1q2": {
                        "ql": -0.05,
                        "qh": -0.05,
                    }
                }
                default set by function `_batch_up`
        """
        options = super()._default_experiment_options()
        options.qc_bias_map = {}
        options.f_traget_map = {}
        options.f_spectator_map = {}
        options.sepctrum_map = {}
        options.bound_map = {}
        options.qt_freq_gap = 20
        options.default_bound = -0.05
        return options

    @classmethod
    def _default_analysis_options(cls):
        options = super()._default_analysis_options()
        options.fit_spectrum = True
        options.quality_bounds = 0.8
        options.popt = []
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.fit_input_data: Dict[str, FitInputCollections] = {}
        options.running_point_map: Dict[str, ExperimentPoint] = {}
        options.max_loop = 0
        return options

    def _batch_up(self):
        super()._batch_up()
        qt_freq_gap = self.experiment_options.qt_freq_gap
        sepctrum_map = self.experiment_options.sepctrum_map
        qc_bias_map = self.experiment_options.qc_bias_map
        bound_map = self.experiment_options.bound_map

        def _auto_build_ac_spetrum(qubit_obj: BaseQubit):
            """自动构建各比特的 AC 谱"""
            if qubit_obj.name in sepctrum_map:
                return sepctrum_map.get(qubit_obj.name)
            else:
                ac_spectrum_v = qubit_obj.ac_spectrum_v
                branch = "right" if qubit_obj.idle_point >= 0 else "left"
                return [ac_spectrum_v[-1], branch, ac_spectrum_v[:-1]]

        # 检查整理扫描参数
        for pair in self.experiment_options.physical_units:
            # 获取 Pair 关键信息
            pair_struct: QDict = self.context_manager.chip_data.get_std_pair_struct(
                pair
            )

            # 初始化拟合结果
            fit_data = FitInputCollections()
            self.run_options.fit_input_data[pair] = fit_data

            # 设置 AC 谱
            fit_data.spectrum_map.ql = _auto_build_ac_spetrum(pair_struct.ql)
            fit_data.spectrum_map.qh = _auto_build_ac_spetrum(pair_struct.qh)
            fit_data.spectrum_map.qb = _auto_build_ac_spetrum(pair_struct.qc)

            # 检查是否有外部设置的 bound
            bound_data = bound_map.get(pair)
            if not bound_data:
                temp_bound_data = {
                    "ql": self.experiment_options.default_bound,
                    "qh": self.experiment_options.default_bound,
                }
                bound_map[pair] = temp_bound_data

            # 检查是否有外部设置的 f_target
            f_target = self.experiment_options.f_target_map.get(pair)
            if not f_target:
                ql_freq = pair_struct.pair.cz_value(pair_struct.ql_name, "freq")
                qh_freq = pair_struct.pair.cz_value(pair_struct.qh_name, "freq")
                temp_target = {
                    "ql": [ql_freq, ql_freq - qt_freq_gap, ql_freq + qt_freq_gap],
                    "qh": [qh_freq, qh_freq - qt_freq_gap, qh_freq + qt_freq_gap],
                }
                self.experiment_options.f_target_map[pair] = temp_target

            # 检查是否有外部设置的 f_spectator
            f_spectator = self.experiment_options.f_spectator_map.get(pair)
            if not f_spectator:
                ql, qh = pair_struct.ql, pair_struct.qh
                temp_spectator = {
                    # "ql": [(0, "idle"), (ql.dc_min - ql.dc_max - ql.idle_point, "min")],
                    "ql": [
                        (0, "idle"),
                        (1 / (2 * fit_data.spectrum_map.ql[-1][2]) - ql.idle_point, "min"),
                    ],
                    "qh": [
                        (0, "idle"),
                        (1 / (2 * fit_data.spectrum_map.qh[-1][2]) - qh.idle_point, "min"),
                    ],
                    # "qh": [(0, "idle"), (qh.dc_min - qh.dc_max - qh.idle_point, "min")],
                    # "qh": [(0, "idle"), (qh.dc_min - qh.dc_max - qh.idle_point, "min")],
                }
                self.experiment_options.f_spectator_map[pair] = temp_spectator

            # 生成各个 QubitPair 的测试工作点
            f_target = self.experiment_options.f_target_map.get(pair)
            f_spectator = self.experiment_options.f_spectator_map.get(pair)
            for target_q, target_freq_list in f_target.items():
                for target_freq in target_freq_list:
                    for spectator_q, spectator_amp_list in f_spectator.items():
                        if spectator_q != target_q:
                            for spectator_amp, label in spectator_amp_list:
                                # 检查是否有外部设置的 qc_bias 扫描列表
                                qb_amp_list = None
                                if (
                                    pair in qc_bias_map
                                    and target_q in qc_bias_map[pair]
                                ):
                                    qb_amp_list = qc_bias_map[pair][target_q]
                                # 生成工作点
                                point = ExperimentPoint(
                                    idle_point_data={
                                        "qh": pair_struct.qh.idle_point,
                                        "ql": pair_struct.ql.idle_point,
                                        "qb": pair_struct.qc.idle_point,
                                        "qc": pair_struct.qc.idle_point,
                                    },
                                    pair=pair,
                                    qt=target_q,
                                    qs=spectator_q,
                                    qt_freq=target_freq,
                                    qs_amp=spectator_amp,
                                    qs_actual_amp=spectator_amp
                                    + getattr(pair_struct, spectator_q).idle_point,
                                    qs_point_name=label,
                                    cz_freq_gap=target_freq
                                    - pair_struct.pair.cz_value(
                                        getattr(pair_struct.pair, target_q), "freq"
                                    ),
                                    qb_amp_list=qb_amp_list,
                                )
                                point.qb_zero_start_validate()
                                fit_data.points.append(point)

            point_infos = "\n".join([str(p) for p in fit_data.points])
            pyqlog.info(
                f"Build {pair} experiment point suc, detail as follows:\n{point_infos}"
            )

        # 统计最大的扫描次数
        max_loop = 0
        for fit_data in self.run_options.fit_input_data.values():
            max_loop = max(max_loop, len(fit_data.points))
        self.run_options.max_loop = max_loop

        # 记录初始 shift 参数
        self._record_shift_data()

    def _set_experiment_point(self, idx: int):
        work_pairs = []
        for pair in self.experiment_options.physical_units:
            fit_data = self.run_options.fit_input_data.get(pair)
            if idx < len(fit_data.points):
                work_pairs.append(pair)
                point: ExperimentPoint = fit_data.points[idx]
                self.run_options.dir_describe[pair] = str(point)
                self.run_options.running_point_map[pair] = point
                bound = self.experiment_options.bound_map[pair][point.qt]
                self.change_regular_exec_exp_options(
                    exp_name="QCZShiftFixPointCalibration",
                    qc_ac_list=point.qb_amp_list,
                    qt_freq=point.qt_freq,
                    tq_name=point.qt,
                    bq_name=point.qs,
                    bq_amp=point.qs_amp,
                    qc_ac_bound=bound,
                )
                self.change_parallel_exec_exp_options(
                    exp_name="QCZShiftFixPointCalibration",
                    unit=pair,
                    qc_ac_list=point.qb_amp_list,
                    qt_freq=point.qt_freq,
                    tq_name=point.qt,
                    bq_name=point.qs,
                    bq_amp=point.qs_amp,
                    qc_ac_bound=bound,
                )
                pyqlog.info(f"{pair} change point {point}")
        return work_pairs

    def _run_batch(self):
        for idx in range(self.run_options.max_loop):
            pairs = self._set_experiment_point(idx)
            self._run_flow(
                flows=self.experiment_options.flows,
                physical_units=pairs,
            )
            self._record_shift_data()

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Optional[Exception] = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        for unit in record.analysis_data.keys():
            point: ExperimentPoint = self.run_options.running_point_map.get(unit)
            spectrum_map: SpectrumMap = self.run_options.fit_input_data.get(
                unit
            ).spectrum_map
            qt_amp_list = record.analysis_data[unit].get("result").get("qt_amp_list")
            qb_amp_list = record.analysis_data[unit].get("result").get("qb_amp_list")
            point.qb_amp_list = list(qb_amp_list)
            point.qt_amp_list = list(qt_amp_list)
            point.result_qb_zero_start_validate(spectrum_map)
            pyqlog.info(f"{unit}-{point} finish!")

        return record

    def _run_analysis(self):
        super()._run_analysis()

        for unit, fit_data in self.run_options.fit_input_data.items():
            try:
                pair = None
                if self.experiment_options.refresh_context is True:
                    pair = self.context_manager.chip_data.get_physical_unit(unit)
                self.off_line_analysis(
                    origin_data=fit_data,
                    result_path=str(Path(self.run_options.record_path).parent),
                    pair=pair
                )
            except Exception:
                import traceback

                pyqlog.error(f"{unit} analysis error\n{traceback.format_exc()}")

    def _record_shift_data(self):
        for unit, fit_data in self.run_options.fit_input_data.items():
            with open(
                str(
                    Path(
                        Path(self.run_options.record_path).parent,
                        f"{unit}-QCShift Origin Data.json",
                    )
                ),
                mode="w",
                encoding="utf-8",
            ) as fp:
                data = {
                    "origin": asdict(fit_data),
                    "adapter_for_fit": fit_data.to_comp_wq2q2q_fit_data(),
                }
                json.dump(data, fp, indent=4, ensure_ascii=False)

    @staticmethod
    def off_line_analysis(
        origin_data: Union[str, FitInputCollections],
        result_path: str = "",
        quality_bound: float = 0.8,
        pair: Optional[QubitPair] = None
    ):
        result_path = result_path or r"app\temp_need\coupler_compensate"

        # 分析流程
        if isinstance(origin_data, str):
            analysis = CouplerCompensateAnalyzer(data_file=origin_data)
        else:
            analysis = CouplerCompensateAnalyzer(experiment_data=origin_data)
        analysis.run()

        r2_mean = np.mean([point.r2 for point in analysis.experiment_data.points])
        analysis.experiment_data.result.r2 = r2_mean
        if r2_mean > quality_bound:
            analysis.experiment_data.result.quality = Quality.perfect.value
        else:
            analysis.experiment_data.result.quality = Quality.bad.value

        np.savetxt(
            fname=str(Path(result_path, f"{analysis.experiment_data}-result.dat")),
            X=np.array(analysis.result),
        )

        # 绘图流程
        drawer = CouplerCompensateDrawer(experiment_data=analysis.experiment_data)
        drawer.run()
        drawer.result.savefig(str(Path(result_path, f"{drawer._experiment_data}.png")))

        # 质量评估
        colletions = analysis.experiment_data
        if pair and colletions.result.quality == Quality.perfect.value:
            result_data = colletions.result.popt
            spectrum_map = colletions.spectrum_map
            ql_spectrum = list(result_data[3: 7])
            qb_spectrum = list(result_data[7: 11])
            qh_spectrum = list(result_data[11: 15])
            ql_spectrum.insert(-2, 0)
            qb_spectrum.insert(-2, 0)
            qh_spectrum.insert(-2, 0)
            coupling = QDict(
                popt=list(result_data[:3]),
                spectrum={
                    "ql": [spectrum_map.ql[0], spectrum_map.ql[1], ql_spectrum],
                    "qb": [spectrum_map.qb[0], spectrum_map.qb[1], qb_spectrum], 
                    "qh": [spectrum_map.qh[0], spectrum_map.qh[1], qh_spectrum] 
                }
            )
            pair.metadata.std.process.qc_shift.update(
                {"coupling": coupling}
            )
            pair.save_data()
        elif pair:
            pyqlog.warning(f"{pair.name} qshift quality bad!")
        return analysis.experiment_data


class CouplerCompensateTools:
    def __init__(
        self,
        experiment_data: Optional[FitInputCollections] = None,
        data_file: Optional[str] = None,
    ) -> None:
        if experiment_data:
            self._experiment_data = experiment_data
        elif data_file:
            self._experiment_data = FitInputCollections.from_origin_data_json(data_file)
        else:
            raise ValueError("No find any experiment data!")
        self._result: Any = None

    @property
    def result(self) -> Any:
        return self._result

    @property
    def experiment_data(self) -> FitInputCollections:
        return self._experiment_data

    def run():
        pass


class CouplerCompensateAnalyzer(CouplerCompensateTools):
    def run(self):
        # qb 补 0 /  qt 补 0
        for point in self.experiment_data.points:
            point.result_qb_zero_start_validate(self.experiment_data.spectrum_map)
            point.result_qt_zero_start_validate()

        # 计算拟合结果
        self._result = comp_wq2q2q_fit(
            **self._experiment_data.to_comp_wq2q2q_fit_data()
        )
        self.experiment_data.result.popt = self.result
        spectrum_info = asdict(self.experiment_data.spectrum_map)
        popt = self.result

        pos = 3
        for idx, (bit, spectrum) in enumerate(spectrum_info.items()):
            if spectrum[0] == "standard":
                paras = popt[pos : pos + 4]
                pos += 4
            else:
                paras = popt[pos : pos + 6]
                pos += 6

            paras = np.insert(paras, 3, 0)
            spectrum_info.update({bit: (spectrum[0], spectrum[1], paras.tolist())})
        self.experiment_data.spectrum_map = SpectrumMap(**spectrum_info)

        # 使用拟合结果计算 fit_data
        for point in self.experiment_data.points:
            self._calculate_fit_data(point)

    def _calculate_fit_data(self, point: ExperimentPoint):
        qt_amp_fit_list = comp_wq2q2q(
            np.array(point.qb_amp_list),
            point.qs_actual_amp,
            _check_spectrum_key(
                asdict(copy.deepcopy(self.experiment_data.spectrum_map)), point.qt
            ),
            point.qt_freq,
            # *self._result[:3],
            self._adapter_popt(point.qt)
        )
        point.qt_amp_fit_list = list(qt_amp_fit_list)
        quality = GoodnessofFit(0.9, 0.8, 0.7)
        quality.evaluate(np.array(point.qt_amp_list), np.array(point.qt_amp_fit_list))
        point.r2 = quality.value

    def _adapter_popt(self, qt_name: str):
        rho_info = {'h-b': self._result[0], 'l-b': self._result[1], 'h-l': self._result[2]}
        return _check_rho_key(rho_info, qt_name)


class CouplerCompensateDrawer(CouplerCompensateTools):
    child_figsize = (4, 4)

    def _plot_point(self, point: ExperimentPoint, ax):
        ax.plot(point.qb_amp_list, point.qt_amp_list, "o-", label="origin")
        if point.qt_amp_fit_list:
            ax.plot(point.qb_amp_list, point.qt_amp_fit_list, label="fit")
        wrapped_title = textwrap.fill(f"{point} R2({point.r2})", width=20)
        ax.set_title(wrapped_title, wrap=True)
        ax.set_ylabel(f"{point.qt} Amp (V)")
        ax.grid(True)  # 显示网格
        ax.legend()  # 显示图例

    def run(self):
        points = self._experiment_data.points

        # 计算需要的行数和列数
        num_points = len(points)
        num_cols = int(np.ceil(np.sqrt(num_points)))
        num_rows = int(np.ceil(num_points / num_cols))

        # 计算父图的尺寸，每个子图默认 4x4 英寸
        fig_width = num_cols * self.child_figsize[0]
        fig_height = num_rows * self.child_figsize[1]

        # 创建画布和子图网格
        fig, axs = plt.subplots(num_rows, num_cols, figsize=(fig_width, fig_height))

        # 将axs转换为一维数组方便迭代
        axs = axs.ravel()

        # 生成子图
        for idx, point in enumerate(points):
            self._plot_point(point, axs[idx])

        # 隐藏多余的子图
        for j in range(num_points, num_rows * num_cols):
            axs[j].axis("off")

        # 调整子图之间的间距
        plt.tight_layout()

        # 调整子图间距，防止标题被遮挡
        plt.tight_layout(rect=[0, 0.03, 1, 0.95])

        # 设置父标题和X标签
        fig.suptitle(str(self._experiment_data), fontsize=16, fontweight="bold")
        fig.supxlabel("Coupler Z Amp (V)")

        self._result = fig


def acq_experiment_data():
    from app.config import init_backend

    backend = init_backend()
    batch = BatchCouplerCompensate(backend)
    batch.set_experiment_options(
        physical_units=["q39q40"],
        flows=["QCZShiftFixPointCalibration"],
        param_path=r".\coupler_compensate.json",
        bound_map={
            "q39q40": {
                "ql": -0.04,
                "qh": -0.03,
            }
        },
    )
    batch.run()


def off_line_analsis():
    from app.config import init_backend
    backend = init_backend()
    pair = backend.context_manager.chip_data.get_physical_unit("q39q40")
    BatchCouplerCompensate.off_line_analysis(
        origin_data=r"app\temp_need\coupler_compensate\q39q40-QCShift Origin Data.json",
        pair=pair
    )


if __name__ == "__main__":
    # acq_experiment_data()
    off_line_analsis()
