if __name__ == "__main__":
    import json
    from app.config import init_backend
    from pyQCat.executor.structures import ChipConfigField

    backend = init_backend()

    with open(
        "app/temp_need/offset_update/origin_offset.json",
        mode="r",
        encoding="utf-8",
    ) as f:
        data = json.load(f)
        backend.context_manager.chip_data.cache_config[
            ChipConfigField.hardware_offset.value
        ] = data

    backend.save_chip_data_to_db(names=[ChipConfigField.hardware_offset.value])
