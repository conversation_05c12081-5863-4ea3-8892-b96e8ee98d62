# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/19
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy
from typing import List, Union

from structure import InstrumentParameter, PulseParameter

from pyQCat.concurrent.worker import ExperimentProtocolBuilder
from pyQCat.errors import ExperimentOptionsError
from pyQCat.log import pyqlog
from pyQCat.pulse import PulseComponent
from pyQCat.pulse.pulse_function import Constant, pi_pulse, zero_pulse
from pyQCat.pulse.pulse_lib import (
    FlatTopGaussian,
    FlatTopGaussianAsymmetric,
    FlatTopGaussianSide,
)


class ExperimentBuilderV2(ExperimentProtocolBuilder):
    def scheduler_experiment_wrapper(self):
        # expand the qubit experiment into a coupler experiment
        self._coupler_exp_adapter()

        # tackle const parameters before analyze scan parameters
        self._tackle_const_parameters()

        # tackle scan parameters to generate xy/z pulse map
        self._tackle_scan_parameters()

        # super register
        self._validate_fake_pulse()

    def _coupler_exp_adapter(self):
        if self.run_options.coupler_cali_options:
            qd = self.run_options.coupler_cali_options.qd
            qp = self.run_options.coupler_cali_options.qp
            qc = self.run_options.coupler_cali_options.qc

            probe_pi_pulse = pi_pulse(qp)()
            drive_pulse = self.xy_pulses.get(qd)[0]

            # load qc z pulse
            qc_z_pulse = self.z_pulses.get(qc)
            if qc_z_pulse:
                qc_z_pulse = qc_z_pulse[0]
            else:
                qc_z_pulse = Constant(drive_pulse.width, 0)()

            # adapter probe qubit and drive qubit xy pulse
            drive_pulse += zero_pulse(qp)()
            probe_pulse = Constant(drive_pulse.width, 0, "XY")() + probe_pi_pulse

            # adapter coupler z pulse
            if (
                self.coupler.readout_point.amp == 0.0
                or self.coupler.pi_pulse_point.amp == 0.0
                or self.coupler.readout_point.amp * self.coupler.pi_pulse_point.amp < 0
            ):
                self.run_options.coupler_cali_options.readout_point_mode = 0
                z_pi_pulse = FlatTopGaussian(
                    time=probe_pi_pulse.width, **self.coupler.pi_pulse_point
                )
            elif (
                0 > self.coupler.pi_pulse_point.amp >= self.coupler.readout_point.amp
                or 0 < self.coupler.pi_pulse_point.amp <= self.coupler.readout_point.amp
            ):
                if self.coupler.pi_pulse_point.amp == self.coupler.readout_point.amp:
                    self.run_options.coupler_cali_options.readout_point_mode = 1
                else:
                    self.run_options.coupler_cali_options.readout_point_mode = 2
                z_pi_pulse = FlatTopGaussianSide(
                    time=probe_pi_pulse.width, **self.coupler.pi_pulse_point
                )
            elif self.coupler.pi_pulse_point.amp < 0:
                z_pi_pulse = FlatTopGaussianAsymmetric(
                    time=probe_pi_pulse.width,
                    **self.coupler.pi_pulse_point,
                    amp2=self.coupler.pi_pulse_point.amp
                    - self.coupler.readout_point.amp,
                    side="right",
                )
                self.run_options.coupler_cali_options.readout_point_mode = 1
            else:
                z_pi_pulse = FlatTopGaussianAsymmetric(
                    time=probe_pi_pulse.width,
                    **self.coupler.pi_pulse_point,
                    amp2=self.coupler.pi_pulse_point.amp
                    - self.coupler.readout_point.amp,
                    side="right",
                )
                self.run_options.coupler_cali_options.readout_point_mode = 1
            qc_z_pulse += z_pi_pulse()

            # repeat play pulse
            self.play_pulse("XY", qd, drive_pulse)
            self.play_pulse("XY", qp, probe_pulse)
            self.play_pulse("Z", qc, qc_z_pulse)

    @staticmethod
    def _format_fake_pulse(pulse):
        new_pulse = pulse._fake_pulse[0]()
        for pulse in pulse._fake_pulse[1:]:
            new_pulse += pulse()
        return new_pulse

    def _validate_fake_pulse(self):
        def auto_set_sweep_delay_list():
            width_list = [pulse.width for pulse in temp_pulse_list]
            if len(set(width_list)) != 1:
                self.sweep_readout_trigger_delay(qubit.readout_channel, width_list)

        for qubit, fake_pulse_list in self.run_options.xy_pulse_map.items():
            temp_pulse_list = fake_pulse_list
            for fake_pulse in fake_pulse_list:
                fake_pulse.bit = qubit.name
            self.xy_pulses[qubit] = temp_pulse_list

            if (
                qubit not in self.run_options.z_pulse_map
                and qubit in self.readout_qubits
            ):
                auto_set_sweep_delay_list()

        for qubit, fake_pulse_list in self.run_options.z_pulse_map.items():
            temp_pulse_list = fake_pulse_list
            for fake_pulse in fake_pulse_list:
                fake_pulse.bit = qubit.name
            self.z_pulses[qubit] = temp_pulse_list

            if qubit in self.readout_qubits:
                auto_set_sweep_delay_list()

    def _tackle_const_parameters(self):
        for parameter in self.run_options.const_parameters:
            unit_obj = parameter.qubit

            if isinstance(parameter, PulseParameter):
                if parameter.mode == "xy":
                    pulse_list = self.xy_pulses.get(unit_obj)
                elif parameter.mode == "z":
                    pulse_list = self.z_pulses.get(unit_obj)
                else:
                    raise ExperimentOptionsError(self, msg="Only support xy or z mode!")
                new_pulse = self._set_pulse_from_parameters(
                    pulse_list[0], parameter.key, parameter.index, parameter.value
                )
                pulse_list[0] = new_pulse

            elif isinstance(parameter, InstrumentParameter):
                self._set_inst_parameter(parameter)

    @staticmethod
    def _set_pulse_from_parameters(
        pulse: PulseComponent, key: str, index: Union[str, int, List[str]], value: float
    ):
        if index == "all":
            goal_index_list: List = list(range(len(pulse.fake_pulse_order)))
        elif isinstance(index, int):
            goal_index_list: List = [index]

        new_pulse = None

        for idx, p_idx in enumerate(pulse.fake_pulse_order):
            base_pulse = deepcopy(pulse._fake_pulse[p_idx])
            if idx in goal_index_list:
                if key == "bias":
                    base_pulse.amp += value
                else:
                    setattr(base_pulse, key, value)
            base_pulse = base_pulse.__class__._from_parameters(base_pulse.parameters)()
            if new_pulse is None:
                new_pulse = base_pulse
            else:
                new_pulse += base_pulse

        return new_pulse

    def _set_inst_parameter(self, parameter):
        pyqlog.log("EXP", f"Set {str(parameter)}")

        qubit = parameter.qubit
        if parameter.mode == "xy":
            module_name = "XY_control"
            channel = qubit.xy_channel
        elif parameter.mode == "m":
            module_name = "Readout_control"
            channel = qubit.readout_channel
        else:
            raise ExperimentOptionsError(self, msg=f"Unknown mode: {parameter.mode}")

        if isinstance(parameter.value, List):
            func_name = f"sweep_{parameter.key}"
            func = getattr(self.inst, func_name, None)
            if func:
                func(
                    module_name,
                    channel,
                    points=parameter.value,
                    repeat=self.experiment_options.repeat,
                )
        else:
            func_name = f"set_{parameter.key}"
            func = getattr(self.inst, func_name, None)
            if func:
                func(module_name, channel, parameter.value)

    def _tackle_scan_parameters(self):
        inst_parameters = []
        parameter_pulse_map = {}
        for parameter in self.run_options.scan_parameters:
            unit_name = parameter.qubit or self.qubit.name
            if not isinstance(unit_name, str):
                unit_name = unit_name.name
            unit_obj = self.run_options.unit_name2obj[unit_name]
            parameter.qubit = unit_obj

            if isinstance(parameter, PulseParameter):
                if parameter.mode == "xy":
                    pulse = self.xy_pulses.get(unit_obj)[0]
                elif parameter.mode == "z":
                    pulse = self.z_pulses.get(unit_obj)[0]
                else:
                    raise ExperimentOptionsError(self, msg="Only support xy or z mode!")

                parameter_pulse_map[parameter] = pulse
            else:
                inst_parameters.append(parameter)

        self.run_options.parameter_pulse_map = parameter_pulse_map

        self._divide_scan_parameter(self.run_options.scan_parameters)

    def _divide_scan_parameter(self, parameters: List[PulseParameter], idx: int = 0):
        scan_length = len(parameters[0])
        self.run_options.total_loop = scan_length
        pulse_set = set()
        for i in range(scan_length):
            for parameter in parameters:
                if isinstance(parameter, PulseParameter):
                    pp_idx = i + idx * scan_length
                    pulse, pulse_list = self._get_pulse_from_parameters(
                        parameter, pp_idx
                    )
                    new_pulse = self._set_pulse_from_parameters(
                        pulse, parameter.key, parameter.index, parameter.value[i]
                    )
                    pulse_list[pp_idx] = new_pulse
                    pulse_set.add(parameter.goal_name)
                elif i == 0:
                    self._set_inst_parameter(parameter)

                if parameter.is_plot is True:
                    self.run_options.x_data = parameter.value

    def _get_pulse_from_parameters(self, parameter: PulseParameter, index: int = 0):
        pulse_map = getattr(self.run_options, f"{parameter.mode}_pulse_map")

        if parameter.qubit in pulse_map and index < len(pulse_map[parameter.qubit]):
            return pulse_map[parameter.qubit][index], pulse_map[parameter.qubit]
        else:
            pulse = self.run_options.parameter_pulse_map.get(parameter)
            new_pulse = deepcopy(pulse)
            pulse_map[parameter.qubit].append(new_pulse)
            return new_pulse, pulse_map[parameter.qubit]

    def build(self):
        if hasattr(self.run_options, "const_parameters"):
            self.scheduler_experiment_wrapper()
        self._register()
        self._prepare_measure_wrapper()


def build_experiment_message_v2(exp_env):
    PulseComponent._fake = True
    builder = ExperimentBuilderV2.from_experiment_environment(exp_env)

    for func in builder.run_options.injection_func:
        setattr(builder, func.__name__, func)

    builder.initial()

    if exp_env.set_xy_pulses:
        exp_env.set_xy_pulses(builder)

    if exp_env.set_z_pulses:
        exp_env.set_z_pulses(builder)

    builder.set_measure_pulses()

    if exp_env.update_instrument:
        exp_env.update_instrument(builder)

    builder.build()

    return builder.experiment_protocol
