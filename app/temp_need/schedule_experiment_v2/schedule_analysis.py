# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/07/30
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy

import numpy as np

from pyQCat.analysis.base_analysis import BaseAnalysis
from pyQCat.analysis.standard_curve_analyis import CurveDrawer, StandardCurveAnalysis
from pyQCat.concurrent.worker.analysis_interface import run_analysis_process
from pyQCat.structures import ExperimentData, Options, QDict


class SchedulerAnalysis(BaseAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()
        options.curve_drawer = CurveDrawer()
        return options

    def run_analysis(self):
        self.drawer._figure = []
        loop_list: list = self.experiment_data.metadata.process_meta.get(
            "loop_list", []
        )
        describe_list: list = self.experiment_data.metadata.process_meta.get(
            "describe_list", []
        )
        loop_label_list: list = self.experiment_data.metadata.process_meta.get(
            "loop_label_list", []
        )
        loop = loop_list[-2]

        total = len(self.experiment_data._child_data)
        np.arange(0, 10)
        idx = 0
        child_idx = 0
        while idx < total:
            child_data = list(self.experiment_data._child_data.values())[
                idx : idx + loop
            ]
            meta_data = deepcopy(self.experiment_data.metadata)
            meta_data.draw_meta.update({"State": describe_list[child_idx]})
            child_exp_data = ExperimentData(
                x_data=np.arange(0, loop),
                y_data={},
                metadata=meta_data,
                child_data=child_data,
            )
            analysis_options = QDict(plot_2d=True, pure_exp_mode=False, x_label=loop_label_list[-2])
            child_ana = run_analysis_process(
                analysis_class=StandardCurveAnalysis,
                experiment_data=child_exp_data,
                analysis_options=analysis_options,
            )
            self.drawer.figure.append(child_ana.drawer.figure)
            idx += loop
            child_idx += 1

