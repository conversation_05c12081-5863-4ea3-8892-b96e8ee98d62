# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/03/19
# __author:       <PERSON><PERSON><PERSON>


from typing import TYPE_CHECKING

import numpy as np
from schedule_top_experiment import ExperimentA<PERSON>pterMode, ScheduleTopExperiment

from pyQCat.pulse.pulse_function import Constant, half_pi_pulse, pi_pulse
from pyQCat.structures import Options

if TYPE_CHECKING:
    from pyQCat.concurrent.worker import ExperimentProtocolBuilder


class RabiSchedule(ScheduleTopExperiment):
    mode_adapter = ExperimentAdapterMode.adapter_coupler

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("name", ["Xpi", "Xpi/2"])
        options.set_validator("N", int)
        options.name = "Xpi"
        options.N = 1
        return options

    @staticmethod
    def set_xy_pulses(builder: "ExperimentProtocolBuilder"):
        """Set RabiScanAmp experiment XY pulses."""
        qubit = builder.qubit
        if builder.experiment_options.name == "Xpi":
            pulse = pi_pulse(qubit)() * builder.experiment_options.N
        else:
            pulse = half_pi_pulse(qubit)
            pulse = pulse() * builder.experiment_options.N
        builder.play_pulse("XY", qubit, pulse)


class RamseySchedule(ScheduleTopExperiment):
    mode_adapter = ExperimentAdapterMode.adapter_coupler

    @staticmethod
    def set_xy_pulses(builder: "ExperimentProtocolBuilder"):
        """Set RabiScanAmp experiment XY pulses."""
        pulse = half_pi_pulse(builder.qubit)()
        pulse += Constant(0, 0, "XY")()
        pulse += half_pi_pulse(builder.qubit)()
        builder.play_pulse("XY", builder.qubit, pulse)


class APESchedule(ScheduleTopExperiment):
    mode_adapter = ExperimentAdapterMode.adapter_coupler

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("theta_type", ["Xpi", "Xpi/2"])
        options.set_validator("phi_num", (1, 10, 0))
        options.set_validator("N", (1, 20, 0))

        options.phi_num = 1
        options.theta_type = "Xpi"
        options.N = 9

        return options

    @staticmethod
    def set_xy_pulses(builder: "ExperimentProtocolBuilder"):
        if builder.experiment_options.theta_type == "Xpi":
            ape_pulse_zero_phase = pi_pulse(builder.qubit)
            ape_pulse_pi_phase = pi_pulse(builder.qubit)
        else:
            ape_pulse_zero_phase = half_pi_pulse(builder.qubit)
            ape_pulse_pi_phase = half_pi_pulse(builder.qubit)

        ape_pulse_zero_phase.phase = 0
        ape_pulse_pi_phase.phase = np.pi * self.experiment_options.phi_num

        ape_pulse = (
            ape_pulse_zero_phase() + ape_pulse_pi_phase()
        ) * builder.experiment_options.N

        builder.play_pulse("XY", builder.qubit, ape_pulse)
