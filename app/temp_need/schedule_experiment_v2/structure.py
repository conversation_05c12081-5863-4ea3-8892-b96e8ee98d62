# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/03/19
# __author:       <PERSON><PERSON><PERSON>

from dataclasses import dataclass, asdict
from typing import List, Union, Dict
from enum import Enum
import numpy as np

from pyQCat.experiments.top_experiment import QubitType


class ParameterType(Enum):

    PULSE = "pulse"
    INST = "inst"
    QUBIT = "qubit"
    EXP = "experiment"


@dataclass
class Parameter:

    key: str = ""
    qubit: Union[str, QubitType] = None
    level: int = 0
    value: Union[float, List] = None
    is_plot: bool = False

    def to_dict(self) -> Dict:
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict):
        return cls(**data)

    @property
    def value_info(self):
        msg = (
            f"Sequence({len(self.value)})"
            if isinstance(self.value, list)
            else self.value
        )
        return f"value[{msg}]"

    @property
    def bit_name(self):
        return self.qubit if isinstance(self.qubit, str) else self.qubit.name

    def __len__(self):
        if isinstance(self.value, list) or isinstance(self.value, np.ndarray):
            return len(self.value)

        return 1 if self.value else 0


@dataclass
class InstrumentParameter(Parameter):

    name: str = "inst"
    mode: str = ParameterType.INST.value

    def __hash__(self):
        return hash(
            (
                self.key,
                self.bit_name,
                self.mode,
                self.level,
            )
        )

    def __str__(self):
        describe = "-".join([self.bit_name, self.mode, self.key, self.value_info])
        return f"{self.name.upper()}({describe})"


@dataclass
class PulseParameter(Parameter):

    index: Union[str, int, List[int]] = 0
    name: str = ParameterType.PULSE.value
    mode: str = "xy"

    def __hash__(self):
        return hash(
            (
                self.key,
                self.bit_name,
                self.index,
                self.mode,
                self.level,
            )
        )

    def __str__(self):
        describe = "-".join([self.bit_name, self.mode, self.key, f'idx[{self.index}]', self.value_info])
        return f"{self.name.upper()}({describe})"

    @property
    def goal_name(self):
        return f"{self.qubit.name}-{self.mode}"


@dataclass
class QubitParameter(Parameter):

    name = ParameterType.QUBIT.value

    def __hash__(self):
        return hash(
            (
                self.key,
                self.bit_name,
                self.level,
            )
        )

    def __str__(self):
        describe = "-".join([self.bit_name, self.key, self.value_info])
        return f"{self.name.upper()}({describe})"


@dataclass
class ExperimentParameter(Parameter):

    name = ParameterType.EXP.value

    def __hash__(self):
        return hash(
            (
                self.key,
                self.level
            )
        )

    def __str__(self):
        describe = "-".join([self.key, self.value_info])
        return f"{self.name.upper()}({describe})"

    @property
    def once_value(self):
        return {self.key: self.value}


class ExperimentRunMode:
    sync_mode: str = "sync"
    async_mode: str = "async"
