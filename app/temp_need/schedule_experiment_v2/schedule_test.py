# -*- coding: utf-8 -*-
# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# __date:         2024/03/19
# __author:       <PERSON><PERSON><PERSON>
import asyncio
import time

import numpy as np
from schedule_composite_experiment import ScheduleCompositeExperiment
from scheudle_lib import RabiSchedule, RamseySchedule
from structure import PulseParameter

from app.config import init_backend
from pyQCat.tools import qarange


def schedule_test(backend):
    ctx = backend.context_manager.generate_context(
        name="qubit_calibration", physical_unit="q1", readout_type="01"
    )

    ScheduleCompositeExperiment._sub_experiment_class = RamseySchedule
    exp = ScheduleCompositeExperiment.from_experiment_context(ctx)
    exp.set_experiment_options(
        use_simulator=True,
        run_mode="sync",
        scan_parameters=[
            PulseParameter(
                key="width",
                level=2,
                qubit="q1",
                index=1,
                mode="xy",
                value=qarange(10, 100, 10),
                is_plot=True,
            ),
            PulseParameter(
                key="amp",
                level=1,
                qubit="q1",
                index=0,
                mode="xy",
                value=qarange(1, 0, -0.02),
                is_plot=True,
            ),
            PulseParameter(
                key="amp",
                level=1,
                qubit="q1",
                index=2,
                mode="xy",
                value=qarange(0, 1, 0.02),
                # is_plot=True,
            ),
        ],
        const_parameters=[
            # PulseParameter(key="amp", qubit="q1", index=1, mode="z", value=0.1),
            # PulseParameter(key="amp", qubit="q2", index=1, mode="z", value=0.1),
            # PulseParameter(key="amp", qubit="c-12", index=1, mode="z", value=0.2),
        ],
    )
    asyncio.run(exp.run_experiment())


if __name__ == "__main__":
    _backend = init_backend()

    start = time.perf_counter()
    schedule_test(_backend)
    end = time.perf_counter()
    print("Elapsed time: ", end - start)
