# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/03/19
# __author:       <PERSON><PERSON><PERSON>
import itertools
import operator
from collections import defaultdict
from copy import deepcopy
from functools import reduce
from typing import List

from schedule_analysis import SchedulerAnalysis
from schedule_top_experiment import Options, ScheduleTopExperiment
from structure import (
    ExperimentParameter,
    InstrumentParameter,
    Parameter,
    ParameterType,
    PulseParameter,
    QubitParameter,
)

from pyQCat.errors import ExperimentOptionsError
from pyQCat.executor.context_manager import deepin_set_param_value
from pyQCat.experiments.composite_experiment import CompositeExperiment
from pyQCat.log import pyqlog
from pyQCat.structures import MetaData


class ScheduleCompositeExperiment(CompositeExperiment):
    _sub_experiment_class = ScheduleTopExperiment

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.scan_parameters = None
        options.const_parameters = None
        options.minimize_mode = True
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.father_scan_parameters = None
        options.child_scan_parameters = None
        options.describe_list = []
        options.total_loop = None
        options.loop_list = []
        options.loop_label_list = []
        options.parent_scan_value_list = []
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.plot_2d = True
        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "describe_list": self.run_options.describe_list,
            "loop_list": self.run_options.loop_list,
            "loop_label_list": self.run_options.loop_label_list,
        }
        return metadata

    def _check_options(self):
        super()._check_options()

        # check scan parameter
        self.experiment_options.scan_parameters = self._check_parameters(
            self.experiment_options.scan_parameters
        )

        # check const parameter
        self.experiment_options.const_parameters = self._check_parameters(
            self.experiment_options.const_parameters
        )

        # check name/obj map
        self.run_options.unit_name2obj = self.physical_units_map()
        self.child_experiment.run_options.unit_name2obj = self.run_options.unit_name2obj

        # check scan parameters
        self._validate_scan_parameters()

        # check const parameters
        self._validate_const_parameters()

        # set child exp run mode
        self.child_experiment.run_options.run_mode = self.experiment_options.run_mode

    def _check_parameters(self, parameters):
        if parameters:
            if not isinstance(parameters, List):
                parameters = [parameters]

            for i in range(len(parameters)):
                cur_parameter = parameters[i]
                if isinstance(cur_parameter, dict):
                    parameter_type = cur_parameter.get("name")
                    if parameter_type == ParameterType.PULSE.value:
                        parameters[i] = PulseParameter.from_dict(cur_parameter)
                    elif parameter_type == ParameterType.INST.value:
                        parameters[i] = InstrumentParameter.from_dict(cur_parameter)
                    elif parameter_type == ParameterType.QUBIT.value:
                        parameters[i] = QubitParameter.from_dict(cur_parameter)
                        assert parameters[i].level > 0
                    elif parameter_type == ParameterType.EXP.value:
                        parameters[i] = ExperimentParameter.from_dict(cur_parameter)
                        assert parameters[i].level > 0
                    else:
                        raise ExperimentOptionsError(
                            self, msg=f"Unknown parameter {cur_parameter}"
                        )
                if not isinstance(parameters[i], Parameter):
                    raise ExperimentOptionsError(
                        self, msg="scan parameter must be `ScanParameter` class."
                    )

            for p in parameters:
                if not p.qubit:
                    p.qubit = self.qubits[0].name

            return parameters

        return []

    def _validate_scan_parameters(self):
        same_level_parameter_map = self.check_same_level_parameters(
            self.experiment_options.scan_parameters
        )

        loop_list = []  # 每个级别的扫描 loop 数
        loop_label_list = []  # 每个级别的扫描描述
        for parameters in same_level_parameter_map.values():
            loop_list.append(self._check_same_loop(parameters))
            loop_label_list.append(self._scan_parameters_info(parameters))

        all_level_parameters = list(same_level_parameter_map.values())

        self.run_options.total_loop = reduce(operator.mul, loop_list)
        self.run_options.father_scan_parameters = all_level_parameters[:-1]
        self.run_options.child_scan_parameters = all_level_parameters[-1]
        self.run_options.loop_list = loop_list
        self.run_options.loop_label_list = loop_label_list
        self.child_experiment.analysis_options.x_label = loop_label_list[-1]

        # set x data and analysis class
        self.run_options.x_data = list(
            range(self.run_options.total_loop // loop_list[-1])
        )
        self.run_options.analysis_class = SchedulerAnalysis

        if self.run_options.father_scan_parameters:
            parent_scan_value_list = []
            for parameters in self.run_options.father_scan_parameters:
                parent_scan_value_list.append(list(zip(*[p.value for p in parameters])))
            self.run_options.parent_scan_value_list = list(
                itertools.product(*parent_scan_value_list)
            )
            if len(all_level_parameters) > 2:
                top_parameters = all_level_parameters[:-2]
                value_list = []
                for parameters in top_parameters:
                    value_list.append(list(zip(*[p.value for p in parameters])))
                scan_values = list(itertools.product(*value_list))
                for _, one_scan_value in enumerate(scan_values):
                    msg = self._sub_experiment_class.__name__
                    for i, same_level_values in enumerate(one_scan_value):
                        for j, v in enumerate(same_level_values):
                            parameter = top_parameters[i][j]
                            msg += f" {parameter.key}({v})"
                    self.run_options.describe_list.append(msg)
            else:
                self.run_options.describe_list.append(
                    self._sub_experiment_class.__name__
                )

    @staticmethod
    def check_same_level_parameters(parameters: List[Parameter]):
        """将扫描 parameters 按照 level 从大到小排序

        Args:
            parameters (List[Parameter]): _description_

        Returns:
            _type_: _description_
        """
        same_level_parameter_map = defaultdict(list)

        for parameter in parameters:
            same_level_parameter_map[parameter.level].append(parameter)

        same_level_parameter_map = dict(
            sorted(same_level_parameter_map.items(), key=lambda x: x[0], reverse=True)
        )
        return same_level_parameter_map

    @staticmethod
    def _check_same_loop(parameters: List[Parameter]):
        """判断同一级别的扫描参数 loop 数是否一致,并返回 loop 数

        Args:
            parameters (List[Parameter]): _description_

        Raises:
            ValueError: _description_

        Returns:
            _type_: _description_
        """
        scan_length_list = list(set([len(p.value) for p in parameters]))

        if len(scan_length_list) != 1:
            raise ValueError("loop num no same!")

        return scan_length_list[0]

    @staticmethod
    def _scan_parameters_info(parameters: List[Parameter]):
        """生成当前级别的扫描名称

        Args:
            parameters (List[Parameter]): _description_

        Returns:
            _type_: _description_
        """
        p_infos = [str(p) for p in parameters if p.is_plot is True] or [
            str(parameters[0])
        ]
        return " ".join(p_infos)

    def _validate_const_parameters(self):
        if self.experiment_options.const_parameters:
            new_parameters = []
            for parameter in self.experiment_options.const_parameters:
                if isinstance(parameter, QubitParameter):
                    self._set_qubit_parameter(parameter)
                else:
                    new_parameters.append(parameter)
            self.experiment_options.const_parameters = new_parameters

    def _set_qubit_parameter(self, parameter: QubitParameter, exp=None):
        if exp:
            qubit = exp.run_options.unit_name2obj.get(parameter.bit_name)
        else:
            qubit = self.run_options.unit_name2obj.get(parameter.bit_name)
        if qubit:
            deepin_set_param_value(qubit, parameter.key, parameter.value)
            pyqlog.log("EXP", f"Set QubitParameter {parameter}")

    def _setup_child_experiment(
        self, child_exp: ScheduleTopExperiment, index: int, value: float
    ):
        child_exp.run_options.index = index
        total = len(self.run_options.x_data)
        describe, exp_options, run_options = self._generate_child_exp_options(
            index, child_exp
        )
        child_exp.set_parent_file(self, describe, index, total)
        child_exp.set_experiment_options(**exp_options)
        child_exp.set_run_options(**run_options)
        self._check_simulator_data(child_exp, index)

    def _generate_child_exp_options(self, index: int, child_exp):
        const_parameters = deepcopy(self.experiment_options.const_parameters or [])
        child_scan_parameters = deepcopy(self.run_options.child_scan_parameters)
        father_scan_parameters = self.run_options.father_scan_parameters

        describe = ""
        experiment_options = {}
        run_options = {}

        if self.run_options.parent_scan_value_list:
            one_scan_value = self.run_options.parent_scan_value_list[index]
            once_child_parameters = const_parameters
            for i, same_level_values in enumerate(one_scan_value):
                for j, v in enumerate(same_level_values):
                    parameter = deepcopy(father_scan_parameters[i][j])
                    parameter.value = v
                    if isinstance(parameter, QubitParameter):
                        self._set_qubit_parameter(parameter, child_exp)
                    elif isinstance(parameter, ExperimentParameter):
                        experiment_options.update(parameter.once_value)
                    else:
                        once_child_parameters.append(parameter)
            describe = self._scan_parameters_info(once_child_parameters)
            run_options.update(
                dict(
                    const_parameters=once_child_parameters,
                    scan_parameters=child_scan_parameters,
                )
            )
        else:
            run_options.update(
                dict(
                    const_parameters=const_parameters,
                    scan_parameters=child_scan_parameters,
                )
            )
            self.analysis_options.pure_exp_mode = True
        return describe, experiment_options, run_options
