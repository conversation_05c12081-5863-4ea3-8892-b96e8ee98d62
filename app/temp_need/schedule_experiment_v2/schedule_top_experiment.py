# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/03/19
# __author:       <PERSON><PERSON><PERSON>

import asyncio
from collections import defaultdict

from bson import ObjectId
from experiment_builder_v2 import build_experiment_message_v2
from structure import Enum

from pyQCat.analysis.standard_curve_analyis import StandardCurveAnalysis
from pyQCat.experiments.single.two_qubit_gate.swap_once import (
    validate_qubit_pair_cz_std,
    validate_two_qubit_exp_read_options,
)
from pyQCat.experiments.top_experiment_v1 import (
    CompilerExperimentError,
    ConcurrentCR,
    CourierError,
    ExperimentCompiler,
    ExperimentRegisterError,
    TopExperimentV1,
    get_async_win_size,
    send_execute_data_to_courier,
    send_exp_data_to_service,
    send_to_chimera,
    CompileSatus
)
from pyQCat.log import pyqlog
from pyQCat.qubit import Qubit
from pyQCat.structures import Options, QDict
from pyQCat.tools import cz_flow_options_adapter
from pyQCat.types import ExperimentRunMode


class ExperimentAdapterMode(Enum):
    normal = 0
    adapter_coupler = 1
    adapter_qubit_pair = 2


class ScheduleTopExperiment(TopExperimentV1):
    mode_adapter = ExperimentAdapterMode.normal

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.plot_raw_data = True
        options.raw_data_format = "plot"
        options.x_label = ""
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.scan_parameters = None
        options.const_parameters = None
        options.unit_name2obj = {}
        options.xy_pulse_map = defaultdict(list)
        options.z_pulse_map = defaultdict(list)
        options.parameter_pulse_map = None
        options.same_level_parameter_map = None
        options.coupler_cali_options = None
        options.pair_options = None
        options.total_loop = 0
        options.x_data = None
        options.run_mode = ExperimentRunMode.sync_mode
        options.support_context = []
        return options

    def _check_options(self):
        super()._check_options()

        # check data type
        self.set_experiment_options(
            data_type="I_Q" if self.discriminator is not None else "amp_phase"
        )

        # check scan/const parameters qubit
        self._check_parameter_bit()

        # check coupler adapter
        if self.mode_adapter == ExperimentAdapterMode.adapter_coupler and self.couplers:
            qc = self.coupler
            probe_q, drive_q = f"q{qc.probe_bit}", f"q{qc.drive_bit}"
            if (
                probe_q in self.run_options.unit_name2obj
                and drive_q in self.run_options.unit_name2obj
            ):
                qd: Qubit = self.run_options.unit_name2obj[drive_q]
                qp: Qubit = self.run_options.unit_name2obj[probe_q]
                self.run_options.coupler_cali_options = QDict(qd=qd, qp=qp, qc=qc)
                self.qubit = qd
                pyqlog.log(
                    "EXP", f"Coupler calibration adapter: qb({qp.name}) | qd({qd.name})"
                )

        # check pair adapter
        if (
            self.mode_adapter == ExperimentAdapterMode.adapter_qubit_pair
            and self.qubit_pair
        ):
            cz_flow_options_adapter(self)
            pair_options = validate_qubit_pair_cz_std(self)
            readout_type = validate_two_qubit_exp_read_options(self)
            pair_options.readout_type = readout_type
            self.run_options.pair_options = pair_options
            pyqlog.log("EXP", f"Qubit Pair Calibration adapter: {self.qubit_pair}")

    def _check_parameter_bit(self):
        parameters = []

        if self.run_options.scan_parameters:
            parameters.extend(self.run_options.scan_parameters)

        if self.run_options.const_parameters:
            parameters.extend(self.run_options.const_parameters)

        for parameter in parameters:
            if not parameter.qubit:
                parameter.qubit = self.qubits[0]
            elif isinstance(parameter.qubit, str):
                parameter.qubit = self.run_options.unit_name2obj.get(parameter.qubit)

        x_data = None
        for parameter in self.run_options.scan_parameters:
            if parameter.is_plot is True:
                x_data = parameter.value
        
        if x_data is None:
            x_data = list(range(len(parameter.value)))

        self.set_run_options(
            x_data=x_data, analysis_class=StandardCurveAnalysis
        )


    async def _async_compile(self) -> str:
        # feature: limit async task number
        while self.run_options.parallel_count.flow_count > get_async_win_size():
            await asyncio.sleep(0.001)
        self.run_options.parallel_count.flow_count += 1
        self.record_flow_count_complete = True

        task_id = ""
        exp_env = self._build_experiment_environment()

        if self.run_options.parallel:
            # parallel mode use processPool accelerate
            # PulseComponent._fake = self._experiment_options.fake_pulse
            program = await ConcurrentCR().run_concurrent_job(
                build_experiment_message_v2, exp_env
            )
            # zyc: 2024/06/24, record exp options
            self._tackle_program(program)
            task_id = await self._send_to_parallel_server(program)
        else:
            pyqlog.log("EXP", f"{self} compile start ... ")
            program = build_experiment_message_v2(exp_env)
            self._tackle_program(program)
            common = self._build_common_message()
            compiler = ExperimentCompiler([program.experiment], common)
            compiler.run()
            if compiler.result.status == CompileSatus.DONE:
                self._exp_task_status = compiler.result.task.status
                self._chimera_data = compiler.result.chimera_data
                task_id = send_exp_data_to_service(
                    compiler.result, self.run_options.parent_id or ""
                )
                pyqlog.log("FLOW", f"{self} register success, ID({task_id})")
                await send_execute_data_to_courier(
                    compiler.result,
                    task_id,
                    common.physical_units,
                    self.experiment_options.use_simulator,
                    options=program.experiment.extra["options"],
                )
                if not self.experiment_options.use_simulator:
                    response = await send_to_chimera(compiler.result.chimera_data)
                    pyqlog.debug(
                        f"Send task {task_id} to pyqcat-chimera response: {response}"
                    )
                    res_code = response.get("code")
                    if res_code != 200:
                        task_id = CourierError(
                            f"Send task {task_id} to pyqcat-chimera service failed!",
                            response,
                        )
            else:
                raise CompilerExperimentError(msg=str(compiler.result.message))

        # Validate Task ID format
        if not ObjectId.is_valid(task_id):
            raise ExperimentRegisterError(self, f"Invalid Task ID `{task_id}`")

        return task_id
