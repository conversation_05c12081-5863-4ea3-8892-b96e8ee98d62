# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/27
# __author:       <PERSON><PERSON><PERSON>

"""
Test Two Qubit ProcessTomography Experiment.
"""

from pyQCat.experiments.single.single_process_tomography import SingleProcessTomography
from pyQCat.tools import qarange
from pyQCat.executor import Backend


def qarange_adapter(options):
    new_options = {}
    for k, v in options.items():
        if k.endswith("*qarange"):
            new_options[k.split("*")[0]] = qarange(*v)
        else:
            new_options[k] = v

    return new_options


def two_bit_qpt_test():
    backend = Backend(
        username="tuple_01",
        password="123456",
        config_file=r"F:\10\new_conf.conf",
    )
    backend.refresh()

    backend.context_manager.set_global_options(
        env_bits=["q1", "q2", "q3", "q4", "q5", "q6"],
        working_type="awg_bias",
        divide_type="character_idle_point",
        online=False,
        crosstalk=False,
        xy_crosstalk=False,
        max_point_unit=[],
        online_unit=[],
        f12_opt_bits=[],
    )

    data = {
        "context": {
            "name": "cz_gate_calibration",
            # "name": "qubit_calibration",
            # "name": "union_read_measure",
            "physical_unit": "q1q2",
            # "physical_unit": "q1",
            "readout_type": "union-01-01",
            # "readout_type": "01",
        },
        "experiment_options": {
            "goal_gate": "CZ",
            # "goal_gate": "I",
            "use_simulator": True,
            "simulator_data_path": r"F:\2024-01\test\data\single_process_tm\SingleProcessTomography(probability).dat"
        },
        "analysis_options": {
            "use_mle": False
            # "child_ana_options": {
            #     "quality_bounds": [0.98, 0.93, 0.81],
            #     "factor": 3.5,
            # },
        },
    }

    context = backend.context_manager.generate_context(**data.get("context"))
    exp = SingleProcessTomography.from_experiment_context(context)

    experiment_options = data.get("experiment_options")
    analysis_options = data.get("analysis_options")

    if experiment_options:
        child_exp_options = experiment_options.pop("child_exp_options", {})
        experiment_options = qarange_adapter(experiment_options)
        exp.set_experiment_options(**experiment_options)

        if child_exp_options:
            child_exp_options = qarange_adapter(child_exp_options)
            exp.set_child_exp_options(**child_exp_options)

    if data.get("analysis_options"):
        child_ana_options = analysis_options.pop("child_ana_options", {})
        analysis_options = qarange_adapter(analysis_options)
        exp.set_analysis_options(**analysis_options)

        if child_ana_options:
            child_ana_options = qarange_adapter(child_ana_options)
            exp.set_child_ana_options(**child_ana_options)

    exp.run()


if __name__ == '__main__':
    two_bit_qpt_test()
