# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/02/24
# __author:       <PERSON><PERSON><PERSON>

import itertools
from collections import defaultdict
from copy import deepcopy
from typing import List

from ..top_experiment import TopExperiment
from ...analysis.algorithms import tensor_combinations
from ...analysis.tomography_analysis import SingleProcessTomographyAnalysis
from ...errors import ExperimentOptionsError, ExperimentFieldError
from ...gate import GateBucket, GateCollection
from ...pulse import Constant
from ...structures import MetaData, Options
from ...tools import get_multi_readout_channels


class SingleProcessTomography(TopExperiment):

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options for AC spectrum experiment.

        Experiment options:
            init_fringe (float): The init value of fringe.
            delays (Union[List, np.ndarray]): Delay time scanned when performing Ramsey
                                              experiments.
            z_amp_list (Union[List, np.ndarray]): Z line amplitude (flux pulse) sweep list.
            freq_bound (Optional[float], optional): Experiment will be stopped when qubit
                                                    frequency delta value lower than this value.
                                                    Defaults to 800MHz.
            osc_freq_limit (Optional[float], optional): [description]. Defaults to 2.5.
        """
        options = super()._default_experiment_options()

        options.set_validator("base_gates", list)
        options.set_validator("qst_base_gates", list)
        options.set_validator("goal_gate", GateCollection.gate_infos())

        options.base_gates = ["I", "X/2", "Y/2", "-X/2"]
        options.base_gate = ["I", "X/2", "Y/2"]
        options.qst_base_gates = ["I", "X/2", "Y/2"]
        options.goal_gate = None
        options.goal_matrix = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("use_mle", bool)
        options.set_validator("fidelity_accuracy", (1, 10, 0))

        options.use_mle = True
        options.sigma_basis = None
        options.goal_gate_matrix = None
        options.base_ops = None
        options.qst_base_ops = None
        options.labels = None
        options.qubit_nums = None
        options.single_ops = None

        # accuracy of result calculation fidelity
        options.fidelity_accuracy = 3

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Set run experiment operate options.

        Options:
            parking_qubits (List): List of BaseQubit,
                when name in parking_bits.

        """
        options = super()._default_run_options()

        options.gate_bucket = GateBucket()
        options.pre_gate_list = None
        options.exp_gates = None
        options.qh = None
        options.ql = None
        options.parking_qubits = None

        return options

    def _check_options(self):
        super()._check_options()
        goal_gate = self.experiment_options.goal_gate
        base_gates = self.experiment_options.base_gates

        qubit_nums = 2 if self.qubit_pair else 1
        gate_bucket = self.run_options.gate_bucket
        self._label += f"{qubit_nums}Q"

        goal_matrix = gate_bucket.get_matrix(goal_gate)
        if self.experiment_options.goal_matrix is not None:
            goal_matrix = self.experiment_options.goal_matrix
        self.set_analysis_options(
            goal_gate_matrix=goal_matrix,
            base_ops=[gate_bucket.get_matrix(gate) for gate in base_gates],
            qst_base_ops=[
                gate_bucket.get_matrix(gate)
                for gate in self.experiment_options.base_gate
            ],
            labels=list(gate_bucket.pauli_matrix.keys()),
            sigma_basis=list(gate_bucket.pauli_matrix.values()),
            qubit_nums=qubit_nums,
        )

        if qubit_nums == 2:
            self.set_analysis_options(result_name=self.qubit_pair.name)

        # check qubit
        if self.qubit:
            # one qubit qst
            gate_bucket.bind_single_gates(self.qubit)
        else:
            # two qubit qst
            pair = self.qubit_pair
            if pair is None:
                raise ExperimentFieldError(
                    self.label, "qubit pair is none, please check!"
                )
            parking_qubits = []
            base_qubits = []
            base_qubits.extend(self.qubits)
            base_qubits.extend(self.couplers)
            for qubit in base_qubits:
                if qubit.name == pair.ql:
                    gate_bucket.bind_single_gates(qubit)
                    self.set_run_options(ql=qubit)
                elif qubit.name == pair.qh:
                    gate_bucket.bind_single_gates(qubit)
                    self.set_run_options(qh=qubit)
                if qubit.name in pair.parking_bits:
                    parking_qubits.append(qubit)
            self.set_run_options(parking_qubits=parking_qubits)
            gate_bucket.bind_cz_gates(pair, base_qubits)

        pre_gate_list = self._generate_pre_gates()
        self.set_run_options(pre_gate_list=pre_gate_list)
        base_gate = self.experiment_options.base_gate

        single_ops = self.analysis_options.single_ops

        if single_ops is None:
            single_ops = [gate_bucket.get_matrix(gate) for gate in base_gate]
            if self.qubit_pair is not None:
                single_ops = tensor_combinations(single_ops, repeat=2)
            self.set_analysis_options(single_ops=single_ops)

        if self.qubit_pair is not None and isinstance(base_gate[0], str):
            base_gate = list(itertools.product(base_gate, repeat=2))
            self.set_experiment_options(base_gate=base_gate)

        # check dcm
        if self.qubit_pair:
            if not isinstance(self.discriminator, List) or len(self.discriminator) != 2:
                raise ExperimentFieldError(self.label, f"QST must support dcm")

            # bug fix 2023/04/27: only two qubit qst need to set mul readout channel
            qh = self.run_options.qh
            ql = self.run_options.ql
            multi_readout_channels = get_multi_readout_channels([qh, ql])
            self.set_experiment_options(
                multi_readout_channels=multi_readout_channels,
            )

        self.set_experiment_options(data_type="I_Q", is_dynamic=0)

    def _metadata(self) -> MetaData:
        """Return experiment metadata for ExperimentData."""
        metadata = super()._metadata()

        metadata.draw_meta = {"Gate": self.experiment_options.goal_gate}

        return metadata

    def _set_single_gate_xy_pulse(self):
        gate_bucket = self.run_options.gate_bucket
        pre_gate_list = self.run_options.pre_gate_list
        gates = []
        for pre_gate in pre_gate_list:
            for gate in self.experiment_options.base_gate:
                pre_gates = deepcopy(pre_gate)
                pre_gates.append(gate)
                gates.append(pre_gates)

        xy_pulses = []
        for gate in gates:
            xy_pulses.append(gate_bucket.get_xy_pulse(self.qubit, gate))
        self.play_pulse("XY", self.qubit, xy_pulses)

    def _set_double_gate_xy_pulse(self):
        pre_gate_list = self.run_options.pre_gate_list
        gate_bucket = self.run_options.gate_bucket
        qh = self.run_options.qh
        ql = self.run_options.ql

        # generate exp gates
        exp_gates = []
        for pre_gate in pre_gate_list:
            for gate in self.experiment_options.base_gate:
                pre_gates = deepcopy(pre_gate)
                pre_gates[0].append(gate[0])
                pre_gates[1].append(gate[1])
                exp_gates.append(pre_gates)
        self.set_run_options(exp_gates=exp_gates)

        # generate qh and ql exp pulse
        qh_xy_pulse = []
        ql_xy_pulse = []
        for one_loop_gates in exp_gates:
            qh_gates, ql_gates = one_loop_gates
            qh_xy_pulse.append(gate_bucket.get_xy_pulse(qh, qh_gates))
            ql_xy_pulse.append(gate_bucket.get_xy_pulse(ql, ql_gates))
        self.play_pulse("XY", qh, qh_xy_pulse)
        self.play_pulse("XY", ql, ql_xy_pulse)

        # generate parking qubit exp pulse
        for qubit in self.run_options.parking_qubits:
            if qubit.name.startswith("q"):
                pulse_list = []
                for pulse in ql_xy_pulse:
                    xy_p = Constant(pulse.width, 0, name="XY")()
                    pulse_list.append(xy_p)
                self.play_pulse("XY", qubit, pulse_list)

    def _set_xy_pulses(self):
        if self.qubit_pair is None:
            self._set_single_gate_xy_pulse()
        else:
            self._set_double_gate_xy_pulse()

    def _set_z_pulses(self):
        if self.qubit_pair:
            exp_gates = self.run_options.exp_gates
            qh = self.run_options.qh
            ql = self.run_options.ql
            gate_bucket = self.run_options.gate_bucket

            # generate exp pulse
            qh_z_pulse = []
            ql_z_pulse = []
            for one_loop_gates in exp_gates:
                qh_gates, ql_gates = one_loop_gates
                qh_z_pulse.append(gate_bucket.get_z_pulse(qh, qh_gates))
                ql_z_pulse.append(gate_bucket.get_z_pulse(ql, ql_gates))
            self.play_pulse("Z", qh, qh_z_pulse)
            self.play_pulse("Z", ql, ql_z_pulse)

            # generate parking pulse
            parking_qubit_pulses = defaultdict(list)
            for one_loop_gates in exp_gates:
                gates, _ = one_loop_gates
                for qubit in self.run_options.parking_qubits:
                    pulse = gate_bucket.get_z_pulse(qubit, gates)
                    parking_qubit_pulses[qubit].append(pulse)
            for qubit, pulses in parking_qubit_pulses.items():
                self.play_pulse("Z", qubit, pulses)

    def _set_measure_pulses(self):
        """Set readout pulse."""
        if self.qubit:
            self._set_single_readout_pulse(qubit=self.qubit)
        else:
            qh = self.run_options.qh
            ql = self.run_options.ql
            self._set_union_readout_pulse(qubits=[qh, ql])

    def run(self):
        super().run()
        pre_gate_list = self.run_options.pre_gate_list
        x_data = [
            i
            for i in range(len(pre_gate_list) * len(self.experiment_options.base_gate))
        ]
        self._run_analysis(
            x_data=x_data, analysis_class=SingleProcessTomographyAnalysis
        )

        self.file.save_text(
            self.analysis.results.ideal_chi_matrix.__str__(), name="ideal_chi_matrix"
        )
        self.file.save_text(
            self.analysis.results.exp_chi_matrix.__str__(), name="exp_chi_matrix"
        )
        self.file.save_data(
            self.analysis.results.ideal_chi_matrix.value,
            name="ideal_chi_matrix",
        )
        self.file.save_data(
            self.analysis.results.exp_chi_matrix.value,
            name="exp_chi_matrix",
        )

        self._set_result_path()

    def _generate_pre_gates(self):
        qubit_nums = 2 if self.qubit_pair else 1
        base_gates = self.experiment_options.base_gates
        goal_gate = self.experiment_options.goal_gate

        if qubit_nums == 1:
            pre_gate_list = []
            for index in range(2):
                if index == 0:
                    pre_gate_list.extend(
                        [[gate] for gate in self.experiment_options.base_gates]
                    )
                else:
                    pre_gate_list.extend(
                        [
                            [gate, self.experiment_options.goal_gate]
                            for gate in self.experiment_options.base_gates
                        ]
                    )
        else:
            if goal_gate == "CZ":
                goal_gate = [["CZ"], ["CZ"]]
            else:
                raise ExperimentOptionsError(
                    self,
                    key="goal_gate",
                    value=goal_gate,
                    msg="Current only support cz gate!",
                )
            gates_left = []
            gates_right = []
            for item in itertools.product(base_gates, repeat=2):
                gates_left.append([[item[0]], [item[1]]])
                r_l = [item[0]]
                r_l.extend(goal_gate[0])
                r_r = [item[1]]
                r_r.extend(goal_gate[1])
                gates_right.append([r_l, r_r])
            gates_left.extend(gates_right)
            pre_gate_list = gates_left

        return pre_gate_list

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        for key, result in self.analysis.results.items():
            if key == "fidelity":
                result.extra["path"] = f"QubitPair.metadata.std.fidelity.qpt_fidelity"
            elif key == "process_fidelity":
                result.extra["path"] = (
                    f"QubitPair.metadata.std.fidelity.qpt_process_fidelity"
                )
