# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/05/18
# __author:       <PERSON><PERSON><PERSON>

from .circuit_experiment import QuantumCircuitExperiment, QuantumCircuitAnalysis
from pyQCat.experiments.composite_experiment import CompositeExperiment


class ECCircuitBase(QuantumCircuitExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.validate_fields("cycle_num", int)
        options.cycle_num = 3
        return options


class ECCircuit1(ECCircuitBase):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.bits = list(range(9))
        return options

    def _circuit(self):
        for _ in range(self.experiment_options.cycle_num):
            for i in [4, 5, 8, 6, 7, 0, 3]:
                self.h(i)
            self.barrier()
            for i, j in [(1, 8), (0, 5), (3, 7)]:
                self.cz(i, j)
            self.barrier()
            for i in [0, 1, 2, 3]:
                self.h(i)
            self.barrier()
            for i, j in [(0, 8), (2, 5), (1, 4)]:
                self.cz(i, j)
            self.barrier()
            for i, j in [(3, 8), (2, 7), (1, 6)]:
                self.cz(i, j)
            self.barrier()
            for i in [0, 1, 2, 3]:
                self.h(i)
            self.barrier()
            for i, j in [(2, 8), (0, 4), (3, 6)]:
                self.cz(i, j)
            self.barrier()
            for i in [4, 5, 8, 6, 7, 0, 3]:
                self.h(i)
            self.barrier()
            for i in [4, 5, 7, 6, 8]:
                self.measure(i)
            self.barrier()


class ECCircuit2(ECCircuitBase):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.bits = list(range(11))
        return options

    def _circuit(self):
        cycle_num = self.experiment_options.cycle_num
        for k in range(cycle_num):
            for i in [4, 5, 8, 6, 7, 0, 3]:
                self.h(i)
            self.barrier()
            for i, j in [(1, 8), (0, 5), (3, 7)]:
                self.cz(i, j)
            self.barrier()
            for i in [0, 1, 2, 3]:
                self.h(i)
            self.barrier()
            for i, j in [(0, 8), (2, 5), (1, 4)]:
                self.cz(i, j)
            self.barrier()
            for i, j in [(3, 8), (2, 7), (1, 6)]:
                self.cz(i, j)
            self.barrier()
            for i in [0, 1, 2, 3]:
                self.h(i)
            self.barrier()
            for i, j in [(2, 8), (0, 4), (3, 6)]:
                self.cz(i, j)
            self.barrier()
            for i in [8, 0, 3, 4, 6, 2, 5, 9, 7, 10]:
                self.h(i)
            self.barrier()
            for i, j in [(4, 0), (6, 3), (8, 2), (5, 9), (7, 10)]:
                self.cz(i, j)
            self.barrier()
            for i in [9, 10, 2]:
                self.h(i)
            self.barrier()
            if k < cycle_num - 1:
                for i in [4, 5, 7, 6, 9, 10, 8]:
                    self.measure(i)
                self.barrier()


class ECCircuit3(ECCircuitBase):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.bits = list(range(11))
        return options

    def _circuit(self):
        cycle_num = self.experiment_options.cycle_num
        for _ in range(cycle_num):
            for i in [9, 10, 2]:
                self.h(i)
            self.barrier()
            for i, j in [(2, 8), (3, 6), (0, 4), (7, 10), (5, 9)]:
                self.cz(i, j)
            self.barrier()
            for i in [8, 0, 3, 4, 6, 2, 5, 9, 7, 10]:
                self.h(i)
            self.barrier()
            for i, j in [(2, 8), (3, 6), (0, 4)]:
                self.cz(i, j)
            self.barrier()
            for i in [0, 1, 2, 3]:
                self.h(i)
            self.barrier()
            for i, j in [(3, 8), (2, 7), (1, 6)]:
                self.cz(i, j)
            self.barrier()
            for i, j in [(0, 8), (2, 5), (1, 4)]:
                self.cz(i, j)
            self.barrier()
            for i in [0, 1, 2, 3]:
                self.h(i)
            self.barrier()
            for i, j in [(1, 8), (3, 7), (0, 5)]:
                self.cz(i, j)
            self.barrier()
            for i in [7, 6, 8, 5, 4, 3, 0]:
                self.h(i)
            self.barrier()
            for i in [4, 5, 7, 6, 9, 10, 8]:
                self.measure(i)
            self.barrier()


class ECCircuit4(ECCircuitBase):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.bits = list(range(12))
        return options

    def _circuit(self):
        cycle_num = self.experiment_options.cycle_num
        for k in range(cycle_num):
            if k % 2 == 0:
                for i in [6, 11, 7, 10, 5, 9, 1, 4]:
                    self.h(i)
                self.barrier()
                for i, j in [(6, 11), (7, 10), (5, 9), (1, 4)]:
                    self.cz(i, j)
                self.barrier()
                for i in [6, 11, 7, 10, 5, 9, 1, 4]:
                    self.h(i)
                self.barrier()
                for i, j in [(6, 11), (7, 10), (5, 9), (1, 4)]:
                    self.cz(i, j)
                self.barrier()
                for i in [6, 11, 7, 10, 5, 9, 1, 4, 8]:
                    self.h(i)
                self.barrier()
                for i, j in [(1, 8)]:
                    self.cz(i, j)
                self.barrier()
                for i in [4, 5, 8, 6, 7, 0, 3, 1]:
                    self.h(i)
                self.barrier()
                for i, j in [(1, 8), (0, 5), (3, 7)]:
                    self.cz(i, j)
                self.barrier()
                for i in [0, 8, 2, 3]:
                    self.h(i)
                self.barrier()
                for i, j in [(3, 8), (2, 7), (1, 6)]:
                    self.cz(i, j)
                self.barrier()
                for i, j in [(0, 8), (2, 5), (1, 4)]:
                    self.cz(i, j)
                self.barrier()
                for i in [4, 1, 2, 3]:
                    self.h(i)
                self.barrier()
                for i, j in [(2, 8), (0, 4), (3, 6)]:
                    self.cz(i, j)
                self.barrier()
                for i in [4, 8, 0, 3, 5, 6, 7]:
                    self.h(i)
                self.barrier()
                for i, j in [(0, 4)]:
                    self.cz(i, j)
                self.barrier()
                for i in [0, 5, 7, 6, 9, 11, 10, 8]:
                    self.measure(i)
                self.barrier()
            else:
                for i in [6, 11, 7, 10, 5, 9, 0, 8]:
                    self.h(i)
                self.barrier()
                for i, j in [(6, 11), (7, 10), (5, 9), (0, 8)]:
                    self.cz(i, j)
                self.barrier()
                for i in [6, 11, 7, 10, 5, 9, 0, 8]:
                    self.h(i)
                self.barrier()
                for i, j in [(6, 11), (7, 10), (5, 9), (0, 8)]:
                    self.cz(i, j)
                self.barrier()
                for i in [6, 11, 7, 10, 5, 9, 8, 0]:
                    self.h(i)
                self.barrier()
                for i, j in [(4, 0)]:
                    self.cz(i, j)
                self.barrier()
                for i in [4, 5, 8, 6, 7, 0, 3]:
                    self.h(i)
                self.barrier()
                for i, j in [(2, 8), (0, 4), (3, 6)]:
                    self.cz(i, j)
                self.barrier()
                for i in [4, 1, 2, 3]:
                    self.h(i)
                self.barrier()
                for i, j in [(0, 8), (2, 5), (1, 4)]:
                    self.cz(i, j)
                self.barrier()
                for i, j in [(3, 8), (2, 7), (1, 6)]:
                    self.cz(i, j)
                self.barrier()
                for i in [2, 8, 0, 3]:
                    self.h(i)
                self.barrier()
                for i, j in [(1, 8), (0, 5), (3, 7)]:
                    self.cz(i, j)
                self.barrier()
                for i in [4, 5, 6, 7, 1, 8, 0, 3]:
                    self.h(i)
                self.barrier()
                for i, j in [(8, 1)]:
                    self.cz(i, j)
                self.barrier()
                for i in [8]:
                    self.h(i)
                self.barrier()
                for i in [4, 5, 7, 6, 9, 11, 10, 1]:
                    self.measure(i)
                self.barrier()


class ECCCompositeBase(CompositeExperiment):
    _sub_experiment_class = ECCircuitBase

    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.validate_fields("cycle_num_list", list)
        options.cycle_num_list = [3, 5, 7, 9, 11]
        options.is_sub_merge = False
        return options

    def _check_options(self):
        bits = self.experiment_options.child_exp_options.bits
        # describe = "".join([f"q{bit}" for bit in bits])
        self.set_run_options(
            x_data=self.experiment_options.cycle_num_list,
            analysis_class=QuantumCircuitAnalysis,
            custom_unit_describe=f"q{bits[0]}",
        )
        super()._check_options()

    def _setup_child_experiment(self, circuit: ECCircuitBase, index: int, cycle: int):
        circuit.run_options.index = index
        total = len(self.run_options.x_data)
        describe = f"cycle= {cycle}"
        circuit.set_parent_file(self, describe, index, total)
        circuit.set_experiment_options(cycle_num=cycle)
        self._check_simulator_data(circuit, index)


class ECCCompositeCircuit1(ECCCompositeBase):
    _sub_experiment_class = ECCircuit1


class ECCCompositeCircuit2(ECCCompositeBase):
    _sub_experiment_class = ECCircuit2


class ECCCompositeCircuit3(ECCCompositeBase):
    _sub_experiment_class = ECCircuit3


class ECCCompositeCircuit4(ECCCompositeBase):
    _sub_experiment_class = ECCircuit4
