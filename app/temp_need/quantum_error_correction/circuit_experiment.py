# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/05/16
# __author:       <PERSON><PERSON><PERSON>

import csv
import os
import re
from copy import deepcopy
from enum import Enum

import numpy as np

from pyQCat.acquisition.acq_tackle import QubitMeasureMode
from pyQCat.analysis.base_analysis import BaseAnalysis
from pyQCat.errors import ExperimentOptionsError
from pyQCat.executor.structures import StandardContext
from pyQCat.experiments.top_experiment_v1 import TopExperimentV1
from pyQCat.gate import Rphi_gate
from pyQCat.pulse import Constant
from pyQCat.pulse_adjust import params_to_pulse
from pyQCat.qaio_property import QAIO
from pyQCat.qubit import NAME_PATTERN
from pyQCat.structures import QDict
from pyQCat.tools import check_readout_point
from pyQCat.tools.calculator import qubit_pair_validate


class QuantumCircuitAnalysis(BaseAnalysis):
    pass


class GateTag(str, Enum):
    H = "h"
    CZ = "cz"
    PH = ""
    M = "m"


class QuantumCircuitExperiment(TopExperimentV1):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.set_validator("bits", list, limit_null=True)
        options.set_validator("loop", int)
        options.bits = None
        options.loop = 1
        options.data_type = "I_Q"
        options.union_mode = "all union"
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.support_context = [StandardContext.CIRCUIT]
        options.analysis_class = QuantumCircuitAnalysis
        options.circuit = {}
        options.depth = 0
        options.xy_pulses = {}
        options.z_pulse = {}
        return options

    @property
    def circuit(self) -> dict:
        return self.run_options.circuit

    @property
    def depth(self):
        return self.run_options.depth

    @depth.setter
    def depth(self, value: int):
        self.run_options.depth = value

    def _check_options(self):
        self._check_bits()
        self._circuit()
        self._resource_filter()
        self._bind_resource()
        super()._check_options()

    def _check_bits(self):
        new_bits = []
        for index, bit in enumerate(self.experiment_options.bits):
            self.circuit[index] = []
            if not re.match(NAME_PATTERN.qubit, str(bit)):
                bit = f"q{bit}"
            if not re.match(NAME_PATTERN.qubit, str(bit)):
                raise ExperimentOptionsError(
                    self, key="bits", value=self.experiment_options.bits
                )
            new_bits.append(bit)
        self.experiment_options.bits = new_bits

    def _circuit(self):
        pass

    def _resource_filter(self):
        remove_bits = []
        for bit, gates in self.circuit.items():
            gate_set = set(gates)
            if len(gate_set) == 1 and "" in gate_set:
                remove_bits.append(bit)
        for bit in remove_bits:
            self.circuit.pop(bit)

    def _bind_resource(self):
        actual_bits = self.experiment_options.bits
        physical_units_map = self.physical_units_map()
        dcm_map = {dcm.name: dcm for dcm in self.discriminator}

        qubits = []
        couplers = []
        qubit_pairs = []
        compensates = {}
        new_circuit = {}
        cache_cz = {}
        measure_qubits = set()

        for bit, gates in self.circuit.items():
            qubit_name = actual_bits[bit]
            qubit = physical_units_map.get(qubit_name)
            qubits.append(qubit)
            new_circuit[qubit_name] = []

            for gate in gates:
                if gate.startswith("cz"):
                    if gate not in cache_cz:
                        _g, lb, rb = gate.split("-")
                        lb = actual_bits[int(lb)]
                        rb = actual_bits[int(rb)]
                        bit1, bit2 = (lb, rb) if int(lb[1:]) < int(rb[1:]) else (rb, lb)
                        pair_name = bit1 + bit2
                        qubit_pair = physical_units_map.get(pair_name)
                        qubit_pairs.append(qubit_pair)
                        coupler = physical_units_map.get(qubit_pair.qc)
                        couplers.append(coupler)
                        actual_cz = f"{_g}-{pair_name}"
                        cache_cz[gate] = actual_cz
                    else:
                        actual_cz = cache_cz[gate]
                    gate = actual_cz
                elif gate == GateTag.M:
                    measure_qubits.add(qubit_name)
                new_circuit[qubit_name].append(gate)

        for qubit in qubits:
            compensates[qubit] = self.compensates[qubit]
        for coupler in couplers:
            compensates[coupler] = self.compensates[coupler]

        self.qubits = qubits
        self.couplers = couplers
        self.qubit_pairs = qubit_pairs
        self.compensates = compensates
        self.discriminator = [dcm_map[qubit] for qubit in measure_qubits]

        self.run_options.circuit = new_circuit
        self.run_options.custom_unit_describe = actual_bits[0]
        self.run_options.physical_units_map = physical_units_map

        multi_readout_channels = [
            physical_units_map[qubit].readout_channel for qubit in measure_qubits
        ]
        self.experiment_options.multi_readout_channels = multi_readout_channels

        loop = self.experiment_options.loop
        if loop == 1:
            self.experiment_options.enable_one_sweep = True
        self.run_options.x_data = list(range(loop))

    def _bit_gate_length(self, bit: int):
        gates = self.circuit.get(bit)
        return len(gates) if gates else 0

    def _align_gate_to_clock(self):
        for _, gate_list in self.circuit.items():
            align_num = self.depth - len(gate_list)
            if align_num > 0:
                for _ in range(align_num):
                    gate_list.append(GateTag.PH.value)

    def _append_gate(self, gate: str, bit: str):
        self.circuit[bit].append(gate)
        self.depth = max(self.depth, self._bit_gate_length(bit))

    def _save_circuit_to_csv(self, name: str = ""):
        data = self.circuit
        qubit_keys = list(data.keys())
        max_cycles = max(len(data[k]) for k in qubit_keys)

        save_path = os.path.join(
            self.file.dirs, str(self) + (name or "quantum_circuit.csv")
        )
        with open(save_path, "w", newline="") as f:
            fieldnames = ["cycle"] + qubit_keys
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()

            for cycle in range(max_cycles):
                row = {"cycle": cycle}
                for k in qubit_keys:
                    row[k] = data[k][cycle] if cycle < len(data[k]) else None
                writer.writerow(row)

    def h(self, bit: int):
        self._append_gate(GateTag.H.value, bit)

    def cz(self, bit1: int, bit2: int):
        gate = f"cz-{bit1}-{bit2}"
        self._append_gate(gate, bit1)
        self._append_gate(gate, bit2)

    def measure(self, bit: int):
        self._append_gate(GateTag.M.value, bit)

    def barrier(self):
        self._align_gate_to_clock()

    @staticmethod
    def set_xy_pulses(builder):
        generate_circuit_pulse(builder)

        for qubit, pulses in builder.xy_pulses.items():
            builder.play_pulse("XY", qubit, pulses)

    @staticmethod
    def set_z_pulses(builder):
        for unit, pulses in builder.z_pulses.items():
            builder.play_pulse("Z", unit, pulses)

    def _alone_save_result(self):
        self._save_circuit_to_csv()


def generate_circuit_pulse(builder):
    """
    Generate XY and Z pulses for a quantum circuit based on the builder's configuration.

    Args:
        builder: A builder object containing circuit configuration, qubit pairs,
                 and other experiment options.
    """
    # Get circuit and physical units mapping from builder
    circuit = builder.run_options.circuit
    physical_units_map = builder.run_options.physical_units_map

    # Initialize gate phase map with default Hadamard gate phase
    gate_phase_map = {"h": np.pi}
    # Cache for storing generated pulses to avoid recomputation
    cache_pulse = {}
    # Set to store measurement operation indices
    m_index_set = set()

    # Process qubit pairs to populate gate phase map with CZ gate phases
    for qubit_pair in builder.qubit_pairs:
        qh, ql, qc = qubit_pair.qh, qubit_pair.ql, qubit_pair.qc
        base_bits = [physical_units_map.get(bit) for bit in [qh, ql, qc]]
        qubit_pair_validate(qubit_pair, qubits=base_bits)
        # Store CZ phase values for both high and low qubits in the pair
        gate_phase_map[(f"cz-{qubit_pair.name}", qh)] = qubit_pair.cz_value(qh, "phase")
        gate_phase_map[(f"cz-{qubit_pair.name}", ql)] = qubit_pair.cz_value(ql, "phase")

    # Generate pulses for each gate in the circuit
    for bit, gates in circuit.items():
        for idx, gate in enumerate(gates):
            # Handle Hadamard (H) gate
            if gate == GateTag.H:
                gate_key = (gate, bit)
                if gate_key not in cache_pulse:
                    # Create pulse for H gate if not cached
                    pulse = Rphi_gate(0).to_pulse(physical_units_map[bit])()
                    cache_pulse[gate_key] = pulse
                else:
                    pulse = cache_pulse[gate_key]
                # Add zero pulses for Z and XY channels if not present
                if ("", idx, "z") not in cache_pulse:
                    cache_pulse[("", idx, "z")] = Constant(pulse.width, 0)()
                    cache_pulse[("", idx, "xy")] = Constant(pulse.width, 0, "XY")()

            # Handle CZ gate
            elif gate.startswith("cz"):
                gate_key = (gate, bit)
                pair = physical_units_map[gate.split("-")[-1]]
                if gate_key not in cache_pulse:
                    # Create CZ pulse using parameters from metadata
                    s_gate_params = deepcopy(
                        pair.metadata.std.get("cz").params.get(bit)
                    )
                    s_gate_params.update({"width": pair.width()})
                    pulse = params_to_pulse(**s_gate_params)()
                    cache_pulse[gate_key] = pulse
                else:
                    pulse = cache_pulse[gate_key]
                # Add zero pulses for Z and XY channels if not present
                if ("", idx, "z") not in cache_pulse:
                    cache_pulse[("", idx, "z")] = Constant(pulse.width, 0)()
                    cache_pulse[("", idx, "xy")] = Constant(pulse.width, 0, "XY")()
                # Handle coupler pulse for CZ gate
                qc_key = (gate, pair.qc)
                if qc_key not in cache_pulse:
                    s_gate_params = deepcopy(
                        pair.metadata.std.get("cz").params.get(pair.qc)
                    )
                    s_gate_params.update({"width": pair.width()})
                    pulse = params_to_pulse(**s_gate_params)()
                    cache_pulse[qc_key] = pulse

            # Handle measurement (M) gate
            elif gate == GateTag.M:
                m_index_set.add(idx)
                gate_key = (gate, bit)
                if gate_key not in cache_pulse:
                    # Create measurement pulse using readout model
                    qubit = physical_units_map[bit]
                    model = builder._search_pulse_model(qubit.readout_point_model)
                    readout_point_param = check_readout_point(qubit)
                    pulse = model(qubit.Mwave.width, **readout_point_param)()
                    cache_pulse[gate_key] = pulse
                else:
                    pulse = cache_pulse[gate_key]
                # Add zero pulses for Z and XY channels if not present
                if ("", idx, "z") not in cache_pulse:
                    cache_pulse[("", idx, "z")] = Constant(pulse.width, 0)()
                    cache_pulse[("", idx, "xy")] = Constant(pulse.width, 0, "XY")()

    # Dictionary to store multiple measurement options
    multiple_measure_options = {}

    # Generate XY and Z pulse sequences for each qubit
    for bit, gates in circuit.items():
        qubit = physical_units_map[bit]
        cur_xy_pulse = Constant(0, 0, name="XY")()
        cur_z_pulse = Constant(0, 0)()
        sum_phase = 0  # Track accumulated phase
        v_z = [0]  # Track Z rotations

        for idx, gate in enumerate(gates):
            # Handle measurement timing alignment
            if idx in m_index_set:
                offset = round(
                    QAIO.delay_ceil(cur_xy_pulse.width) - cur_xy_pulse.width, 4
                )
                if offset:
                    cur_xy_pulse += Constant(offset, 0, "XY")()
                    cur_z_pulse += Constant(offset, 0)()
                    v_z.append(0)

            # Handle phase gate
            if gate in GateTag.PH:
                cur_xy_pulse += cache_pulse[(gate, idx, "xy")]
                cur_z_pulse += cache_pulse[(gate, idx, "z")]
                v_z.append(0)

            # Handle Hadamard gate
            elif gate == GateTag.H:
                cur_xy_pulse += cache_pulse[(gate, bit)]
                cur_z_pulse += cache_pulse[("", idx, "z")]
                v_z.append(-np.pi / 2 - sum_phase)
                sum_phase += gate_phase_map[gate]

            # Handle CZ gate
            elif gate.startswith("cz"):
                cur_xy_pulse += cache_pulse[("", idx, "xy")]
                v_z.append(0)
                sum_phase += gate_phase_map[(gate, bit)]
                cur_z_pulse += cache_pulse[(gate, bit)]

            # Handle measurement gate
            elif gate == GateTag.M:
                if qubit not in multiple_measure_options:
                    # First measurement for this qubit
                    m_pulse = builder._create_readout_pulse(qubit)
                    multiple_measure_options[qubit] = QDict(
                        measure_delay=[cur_xy_pulse.width], waveform=[m_pulse]
                    )
                else:
                    # Subsequent measurements for this qubit
                    measure_delay = multiple_measure_options[qubit].measure_delay
                    cur_delay = (
                        cur_xy_pulse.width
                        - sum(measure_delay)
                        - (len(measure_delay) * qubit.Mwave.width)
                    )
                    measure_delay.append(round(cur_delay, 4))
                cur_xy_pulse += cache_pulse[("", idx, "xy")]
                cur_z_pulse += cache_pulse[(gate, bit)]
                v_z.append(0)
            else:
                raise NameError(f"no support {gate}")

        # Store final pulses for this qubit
        builder.xy_pulses[qubit] = [cur_xy_pulse]
        builder.z_pulses[qubit] = cur_z_pulse

    # Generate Z pulses for couplers in qubit pairs
    for pair in builder.qubit_pairs:
        q1_gates = circuit[pair.qh]
        cur_z_pulse = Constant(0, 0)()
        coupler = physical_units_map[pair.qc]
        for index, gate in enumerate(q1_gates):
            if pair.name in gate:
                cur_z_pulse += cache_pulse[(gate, pair.qc)]
            else:
                cur_z_pulse += cache_pulse[("", index, "z")]
        builder.z_pulses[coupler] = cur_z_pulse

    # Create measurement modes for multiple measurements
    measure_modes = []
    for qubit, measure_options in multiple_measure_options.items():
        sample_count = len(measure_options.measure_delay)
        mode = QubitMeasureMode(
            name=qubit.name,
            measure_total=sample_count,
            measure_num_list=list(range(sample_count)),
            base_label_list=["" for _ in range(sample_count)],
        )
        measure_modes.append(mode)

    # Prepare measurement options for experiment loops
    loop = builder.experiment_options.loop
    for qubit, measure_options in multiple_measure_options.items():
        measure_delay = measure_options.measure_delay
        measure_options.measure_delay = [
            [v for _ in range(loop)] for v in measure_delay
        ]

    # Store final options in builder
    builder.run_options.multiple_measure_options = multiple_measure_options
    builder.run_options.measure_modes = measure_modes
