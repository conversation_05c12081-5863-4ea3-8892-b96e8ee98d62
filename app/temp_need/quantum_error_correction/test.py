# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/05/17
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.batch_test.dynamic_injection import injection_experiment_libs
    from app.config import init_backend
    from app.temp_need.quantum_error_correction.ecc import (
        ECCircuit1,
        ECCircuit2,
        ECCircuit3,
        ECCircuit4,
        ECCCompositeCircuit1,
        ECCCompositeCircuit2,
        ECCCompositeCircuit3,
        ECCCompositeCircuit4,
    )
    from pyQCat.experiments.batch import BatchRunner

    injection_experiment_libs(
        ECCircuit1,
        ECCircuit2,
        ECCircuit3,
        ECCircuit4,
        ECCCompositeCircuit1,
        ECCCompositeCircuit2,
        ECCCompositeCircuit3,
        ECCCompositeCircuit4,
    )

    backend = init_backend()
    batch = BatchRunner(backend)
    batch.set_experiment_options(
        param_path=r"/home/<USER>/code/pyqcat-apps/app/temp_need/quantum_error_correction/options.json",
        exp_retry=0,
        record_batch=True,
        quality_filter=False,
        use_config_unit=True,
        unified_dir=False,
        refresh_context=False,
        flows=[
            # "ECCircuit1",
            # "ECCircuit2",
            # "ECCircuit3",
            # "ECCircuit4",
            # "ECCCompositeCircuit1",
            # "ECCCompositeCircuit2",
            # "ECCCompositeCircuit3",
            # "ECCCompositeCircuit4"
        ],
    )
    batch.run()
