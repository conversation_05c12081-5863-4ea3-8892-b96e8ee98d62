# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/08/10
# __author:       SS Fang

""" Test XYCrosstalk Experiment
"""

import numpy as np

from pyQCat.tools.utilities import qarange
from scripts.temp_need.xy_crosstalk_source.cross_rm_assist import CrosstalkRamseyAssist

from scripts.simulator import builder


def xy_cross_ramsey_test():
    t_name = "q1"
    b_name = "q0"

    # group_names = f"{t_name}{b_name}"
    union_bits = [1, 0]
    context = builder.union_readout_env(
        union_bits,
        working_type="awg_bias",
        divide_type="character_idle_point",
        readout_type="01"
    )

    cross_ramsey_com_exp = CrosstalkRamseyAssist.from_experiment_context(context)

    cross_ramsey_com_exp.set_experiment_options(use_simulator=True)
    cross_ramsey_com_exp.set_child_exp_options(
        # target_name="q0",
        # bias_name="q1",
        x_num_list=qarange(1, 2, 1),
        use_simulator=True,
        simulator_data_path=r"F:\monster8\monster\impa_exp\pyqcat-monster\scripts\simulator\data\ConditionalPhaseFix"
                            r"\CPhase_raw_data-0.dat",
    )

    cross_ramsey_com_exp.run()


if __name__ == "__main__":
    xy_cross_ramsey_test()
