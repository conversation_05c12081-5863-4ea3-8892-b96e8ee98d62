# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/08/10
# __author:       SS Fang

""" Test XYCrosstalk Experiment
"""

import numpy as np

from pyQCat.tools.utilities import qarange
from scripts.temp_need.xy_cross_source.xy_cross_rm import XYCrosstalkRamsey

from scripts.simulator import builder


def xy_cross_ramsey_test():
    t_name = "q1"
    b_name = "q0"

    # group_names = f"{t_name}{b_name}"
    group_names = f"q1q0q2"
    context = builder.xy_crosstalk_env(
        group_names=group_names,
        working_type="awg_bias",
        divide_type="character_idle_point",
        use_dcm=True,
        ac_switch=False,
        max_point_qubits=[t_name, b_name],
    )

    xy_cross_ramsey_exp = XYCrosstalkRamsey.from_experiment_context(context)

    xy_cross_ramsey_exp.set_experiment_options(
        target_name="q0",
        bias_name="q1",
        scan_param="phase",
        x_num_list=qarange(1, 2, 1),
        use_simulator=True,
        simulator_data_path=r"F:\monster8\monster\impa_exp\pyqcat-monster\scripts\simulator\data\Ramsey",
    )

    xy_cross_ramsey_exp.run()


if __name__ == "__main__":
    xy_cross_ramsey_test()
