# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/08/09
# __author:       SS Fang

"""
Rough test XY Crosstalk base experiment.
"""

from copy import deepcopy

import numpy as np

from pyQCat.analysis import RamseyAnalysis, OscillationAnalysis
from pyQCat.errors import ExperimentFieldError, ExperimentOptionsError
from pyQCat.structures import Options, MetaData
from pyQCat.pulse.pulse_lib import Constant
from pyQCat.pulse.pulse_function import (
    half_pi_pulse,
    pi_pulse,
    zero_pulse,
    stimulate_state_pulse,
)
from pyQCat.experiments.top_experiment import TopExperiment
from pyQCat.tools import qarange


class XYCrosstalkRamsey(TopExperiment):
    """XY Crosstalk Once experiment."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default experiment options.

        Experiment Options:
            target_name (str): Target bit name.

        """
        options = super()._default_experiment_options()

        options.set_validator("target_name", str)
        options.set_validator("bias_name", str)
        options.set_validator("b_amp", float)
        options.set_validator("b_drive_power", float)
        options.set_validator("x_num_list", list)
        options.set_validator("phase_list", list)
        options.set_validator("add_pi_pulse", bool)
        options.set_validator("scan_param", ["x_num", "phase"])
        options.set_validator("control_gate", ["I", "X"])
        options.set_validator("delay", float)

        options.target_name = None
        options.bias_name = None

        options.b_amp = None
        options.b_drive_power = None
        options.x_num_list = qarange(1, 20, 1)
        options.add_pi_pulse = False
        options.control_gate = "I"
        options.scan_param = "x_num"
        # options.phase_list = np.linspace(0, 2 * np.pi, 20).tolist()
        options.phase_list = np.linspace(0, 26, 20).tolist()
        options.delay = 50
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default run options.

        Run Options:
            target_qubit (Qubit): Target Qubit object.
            bias_qubit (Qubit): Crosstalk Bias Qubit object.

        """
        options = super()._default_run_options()
        options.target_qubit = None
        options.bias_qubit = None
        options.scan_list = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options.

        Analysis Options:
            is_plot (bool): Plot or not analysis fit photo.
            data_key (List[str]): List of mark select data.

        """
        options = super()._default_analysis_options()

        options.is_plot = True
        options.data_key = ["P1"]
        options.x_label = "x_num"

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "target": self.run_options.target_qubit.name,
            "bias": self.run_options.bias_qubit.name,
            "b_amp": self.experiment_options.b_amp,
            "b_drive_power": self.experiment_options.b_drive_power,
        }
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        eop = self.experiment_options
        target_name = eop.target_name
        bias_name = eop.bias_name
        b_drive_power = eop.b_drive_power
        b_amp = eop.b_amp

        qubit_map = {}
        for qubit in self.qubits:
            qubit_map[qubit.name] = qubit
        q_names = list(qubit_map.keys())

        if target_name not in q_names:
            raise ExperimentOptionsError(
                self,
                f"Set target {target_name} not in all bit names!",
                "target_name",
                target_name,
            )
        target_qubit = qubit_map.get(target_name)
        bias_qubit = qubit_map.get(bias_name)
        self.qubits = [target_qubit, bias_qubit]
        self.z_pulses = {qubit: [] for qubit in self.qubits}
        self.xy_pulses = {qubit: [] for qubit in self.qubits}

        if b_amp is None:
            b_amp = bias_qubit.XYwave.Xpi
        if b_drive_power is None:
            b_drive_power = bias_qubit.drive_power
        if b_drive_power < -40.0 or b_drive_power > -10.0:
            raise ValueError(
                f"Set bias drive_power {b_drive_power}, out of [-40.0, -10.0]"
            )

        # drag_time = bias_qubit.XYwave.time + bias_qubit.XYwave.offset * 2

        if eop.scan_param == "x_num":
            scan_list = np.array(eop.x_num_list)
            # scan_list = np.asarray(scan_list) * drag_time
        else:
            scan_list = eop.phase_list
            self.set_analysis_options(x_label="Phase")

        self.set_run_options(scan_list=scan_list)

        multi_readout_channels = [target_qubit.readout_channel]

        self.set_experiment_options(
            multi_readout_channels=multi_readout_channels,
            data_type="I_Q",
            b_drive_power=b_drive_power,
            b_amp=b_amp,
        )

        self.set_run_options(target_qubit=target_qubit, bias_qubit=bias_qubit)

    def _set_xy_pulses(self):
        """Set experiment XY pulses."""

        eop = self.experiment_options
        rop = self.run_options

        b_amp = eop.b_amp
        add_pi_pulse = eop.add_pi_pulse
        control_gate = eop.control_gate
        scan_param = eop.scan_param
        delay = eop.delay

        scan_list = rop.scan_list
        target_qubit = rop.target_qubit
        bias_qubit = rop.bias_qubit
        drag_time = bias_qubit.XYwave.time + bias_qubit.XYwave.offset * 2

        for qubit in [target_qubit, bias_qubit]:
            xy_pulse_list = []
            for param in scan_list:
                if qubit.name == target_qubit.name:
                    if add_pi_pulse:
                        pulse_1 = pi_pulse(qubit)
                    else:
                        pulse_1 = half_pi_pulse(qubit)
                    pulse_3 = deepcopy(pulse_1)

                    if scan_param == "phase":
                        pulse_2 = Constant(delay, 0, "XY")
                        pulse_3.phase = param
                    else:
                        # phase = 10 * param * 2 * np.pi * 10e-3
                        pulse_2 = Constant(param * drag_time, 0, "XY")
                        # pulse_3.phase = phase
                    pulse_2 = pulse_2()
                else:
                    pulse_1 = zero_pulse(target_qubit, name="XY")
                    c_pulse = stimulate_state_pulse(control_gate, qubit)
                    if scan_param == "x_num":
                        if control_gate == "I":
                            b_amp = 0
                        pulse_2 = c_pulse(amp=b_amp, time=bias_qubit.XYwave.time) * param
                    else:
                        if control_gate == "I":
                            b_amp = 0
                        pulse_2 = c_pulse(time=delay, amp=b_amp)
                    pulse_3 = deepcopy(pulse_1)
                ramsey_pulse = pulse_1() + pulse_2 + pulse_3()
                xy_pulse_list.append(ramsey_pulse)
            self.play_pulse("XY", qubit, xy_pulse_list)

    def _set_z_pulses(self):
        """Set experiment Z pulses."""

    def _set_measure_pulses(self):
        """Set readout pulse."""
        target_qubit = self.run_options.target_qubit
        self._set_single_readout_pulse(qubit=target_qubit)

    def _update_instrument(self):
        """Update instrument."""
        b_drive_power = self.experiment_options.b_drive_power
        target_qubit = self.run_options.target_qubit
        if self.experiment_options.add_pi_pulse:
            bias_qubit = self.run_options.bias_qubit
            self.inst.set_power("XY_control", bias_qubit.xy_channel, b_drive_power)
        self._bind_probe_inst(target_qubit)

    def _special_run_analysis(self):
        """Experiment special analysis run logic."""
        if self.experiment_options.scan_param == "phase":
            analysis_class = XYCrosstalkRamseyAnalysis
            x_data = self.run_options.scan_list
        else:
            analysis_class = RamseyAnalysis
            bias_qubit = self.run_options.bias_qubit
            time = bias_qubit.XYwave.time + bias_qubit.XYwave.offset * 2
            x_data = self.run_options.scan_list * time
        self._run_analysis(
            x_data=x_data,
            analysis_class=analysis_class,
        )

    def run(self):
        """run"""
        super().run()
        self._special_run_analysis()


class XYCrosstalkRamseyAnalysis(OscillationAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.x_label = "Phase"
        options.quality_bounds = [0.9, 0.65, 0.55]

        options.curve_fit_extra.update({"ftol": 1.49012e-8,
                                        "xtol": 1.49012e-8})

        options.result_parameters = ["phase"]
        return options

    def _extract_result(self, data_key: str, quality=None):
        """Extract Xpi from fitted data.

        Args:
            data_key (str): The basis for selecting data.
            quality : Quality of fit outcome.
        """

        analysis_data = self.analysis_datas[data_key]
        x = analysis_data.x
        # y_fit = analysis_data.fit_data.y_fit
        # target_index = np.argmax(y_fit)
        #
        # xpi_x = x[target_index]
        # self.results.phase.value = xpi_x
        amp, freq, phase, base = analysis_data.fit_data.popt

        if amp < 0:
            phase += 1 / freq / 2

        # bug solve: f(x) = A * cos(x + phase), if we want to find the max point,
        # the x must be equal -phase
        phase = -phase

        if data_key == 'P0':
            phase += 1 / freq / 4

        while phase <= x[0]:
            phase = phase + 1 / freq

        while phase >= x[-1]:
            phase = phase - 1 / freq

        self.results.phase.value = phase