# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/27
# __author:       <PERSON>, <PERSON><PERSON><PERSON>

"""
Normal Leakage, LeakageNGate Experiment.
"""
import json
from copy import deepcopy
from typing import Union, List, Dict

import numpy as np
import pandas as pd

from pyQCat.analysis import CurveAnalysis, TopAnalysis, ParameterRepr
from pyQCat.experiments.composite_experiment import CompositeExperiment
from pyQCat.log import pyqlog
from pyQCat.structures import Options, MetaData, ExperimentData
from pyQCat.tools import format_results, qarange
from scripts.temp_need.xy_crosstalk_source.xy_cross_rm import XYCrosstalkRamsey


# from pyqcat_developer.xy_cross_rm import XYCrosstalkRamsey


class CrosstalkRamseyAssist(CompositeExperiment):
    _sub_experiment_class = XYCrosstalkRamsey

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()
        options.set_validator("x_num_list", list)

        options.x_num_list = qarange(1, 20, 1)
        options.diff_freq = 350
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options."""
        options = super()._default_analysis_options()
        options.y_label = ["osc_freq", "probility"]
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.q_map = None
        options.osc_I_freq = ([],)
        options.osc_I_phase = ([],)
        options.osc_X_freq = ([],)
        options.osc_X_phase = ([],)
        options.p1_I_list = ([],)
        options.p1_X_list = ([],)
        options.exp1_bias_q = ([],)
        options.exp2_bias_q = ([],)
        options.cross_q = []
        options.scan_param = None
        options.save_mark = None
        options.dcm = None
        options.working_dc = {}
        options.ac_bias = {}
        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        diff_freq = self.experiment_options.diff_freq
        qubit_map = {}
        for qubit in self.qubits:
            filter_q = [
                q
                for q in self.qubits
                if abs(q.drive_freq - qubit.drive_freq) < diff_freq and q != qubit
            ]
            if len(filter_q) > 0:
                qubit_map[qubit] = filter_q
            if not qubit_map:
                raise ValueError(f"There is no crosstalk between bits!")
        self.set_run_options(q_map=qubit_map)

    def check_working(self, tq):
        qubit_map = self.run_options.q_map

        def change_dict(mode: str, data: Dict) -> Dict:
            """Change ac_bias or working_dc value."""
            for qubit in qubit_map:
                if qubit.name != tq.name:
                    data[qubit.name][1] = qubit.dc_min
                else:
                    data[qubit.name][1] = qubit.dc_max + qubit.idle_point

            df = pd.DataFrame(data=data).T
            df.columns = ["channel", "value"]
            pyqlog.info(f"Target {tq.name}, so change {mode} values:\n{df}")
            return data

        # Set bias bits in min point.
        if self.working_dc:
            new_working_dc = change_dict("working_dc", self.working_dc)
        else:
            new_working_dc = {}
        if self.ac_bias:
            new_ac_bias = change_dict("ac_bias", self.ac_bias)
        else:
            new_ac_bias = {}

        self.set_run_options(working_dc=new_working_dc, ac_bias=new_ac_bias)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        return metadata

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""

    def _run_analysis(
        self, x_data: Union[List, np.ndarray], analysis_class: type(TopAnalysis)
    ):
        metadata = self._metadata()
        exp_data = ExperimentData(
            x_data=x_data,
            y_data={
                "osc_I_freq": np.array(self.run_options.osc_I_freq),
                "osc_I_phase": np.array(self.run_options.osc_I_phase),
                "osc_X_freq": np.array(self.run_options.osc_X_freq),
                "osc_X_phase": np.array(self.run_options.osc_X_phase),
                "p1_I_list": np.array(self.run_options.p1_I_list),
                "p1_X_list": np.array(self.run_options.p1_X_list),
                "exp1_bias_q": self.run_options.exp1_bias_q,
                "exp2_bias_q": self.run_options.exp2_bias_q,
            },
            experiment_id=self.id,
            metadata=metadata,
        )
        self._analysis = analysis_class(exp_data)

        # update options.
        self.analysis.set_options(**self._analysis_options)

        # run analysis.
        self.analysis.run_analysis()
        self._save_curve_analysis_plot(save_mark=self.run_options.save_mark)

    def check_dcm(self, tq):
        # Get target bit discriminator.
        if self.discriminator:
            if isinstance(self.discriminator, list):
                dcm_list = self.discriminator
            else:
                dcm_list = [self.discriminator]
            new_dcm = None
            for dcm in dcm_list:
                if tq.name == dcm.name:
                    new_dcm = dcm
                    break
            pyqlog.info(f"Add {new_dcm.name} discriminator: {new_dcm}")
        else:
            new_dcm = None

        self.set_run_options(dcm=new_dcm)

    def special_run(self, tq):
        exp1_I_phase = []
        exp1_I_freq = []
        p1_I_list = []
        for flag in [True, False]:
            exp = deepcopy(self.child_experiment)
            exp.discriminator = self.run_options.dcm
            exp.working_dc = self.run_options.working_dc
            exp.ac_bias = self.run_options.ac_bias

            exp.set_experiment_options(
                target_name=tq.name,
                bias_name=tq.name,  # bq[0].name
                add_pi_pulse=flag,
                control_gate="I",
            )
            if flag is True:
                exp.set_experiment_options(
                    scan_param="x_num", x_num_list=self.experiment_options.x_num_list
                )
                scan_param = "x_num"
            else:
                scan_param = "phase"
            self._check_simulator_data(exp, 0)
            tail_name = f"target_name={tq.name}-add_pi={flag}-scan_param={scan_param}-control_gate='I'"
            exp.set_parent_file(self, tail_name)
            exp.run()

            if flag:
                p1_list = exp.experiment_data.y_data.get("P1")
                p1_I_list.append(p1_list[-1])

            if "phase" in exp.analysis.results and flag is False:
                phase = exp.analysis.results.phase.value
                exp1_I_phase.append(phase)
            elif "freq" in exp.analysis.results and flag is False:
                freq = exp.analysis.results.freq.value
                exp1_I_freq.append(freq)

            exp.clear_params()
        self.set_run_options(
            osc_I_freq=exp1_I_freq, osc_I_phase=exp1_I_phase, p1_I_list=p1_I_list
        )

    def run(self):
        super().run()
        qubit_map = self.run_options.q_map
        x_num_list = self.experiment_options.x_num_list
        for tq, bq in qubit_map.items():
            exp1_osc_phase = []
            exp1_osc_freq = []
            exp2_p1_list = []
            x_data = []
            exp1_bias_q = []
            exp2_bias_q = []
            bq_index = 0
            iteration_times = 0
            self.check_dcm(tq)
            self.check_working(tq)
            self.special_run(tq)
            while bq_index < len(bq):
                bq_freq = bq[bq_index].drive_freq
                diff_freq = abs(tq.drive_freq - bq_freq)
                bias_num = int(bq[bq_index].name[1:])
                x_data.append(bias_num)
                exp = deepcopy(self.child_experiment)
                exp.discriminator = self.run_options.dcm
                exp.working_dc = self.run_options.working_dc
                exp.ac_bias = self.run_options.ac_bias
                if diff_freq < np.abs(tq.drive_freq + tq.anharmonicity - bq_freq):
                    exp.set_experiment_options(
                        target_name=tq.name,
                        bias_name=bq[bq_index].name,
                        add_pi_pulse=False,
                        control_gate="X",
                    )
                else:
                    scan_param = "x_num"
                    exp.set_experiment_options(
                        target_name=tq.name,
                        bias_name=bq[bq_index].name,
                        add_pi_pulse=True,
                        scan_param=scan_param,
                        x_num_list=x_num_list,
                        control_gate="X",
                    )
                scan_param = exp.experiment_options.scan_param
                tail_name = f"target_name={tq.name}-bias_name={bq[bq_index].name}-scan_param={scan_param}-control_gate='X'"
                exp.set_parent_file(self, tail_name, iteration_times, len(x_data))
                self._check_simulator_data(exp, bq_index)
                exp.run()
                add_pi_pulse = exp.experiment_options.add_pi_pulse
                if add_pi_pulse:
                    p1_list = exp.experiment_data.y_data.get("P1")
                    exp2_p1_list.append(p1_list[-1])
                    exp2_bias_q.append(bias_num)
                if "phase" in exp.analysis.results and add_pi_pulse is False:
                    phase = exp.analysis.results.phase.value
                    exp1_osc_phase.append(phase)
                    exp1_bias_q.append(bias_num)
                elif "freq" in exp.analysis.results and add_pi_pulse is False:
                    freq = exp.analysis.results.freq.value
                    exp1_osc_freq.append(freq)
                    exp1_bias_q.append(bias_num)

                exp.clear_params()
                bq_index += 1
                iteration_times += 1

            self.set_run_options(
                p1_X_list=exp2_p1_list,
                osc_X_freq=exp1_osc_freq,
                osc_X_phase=exp1_osc_phase,
                exp1_bias_q=exp1_bias_q,
                exp2_bias_q=exp2_bias_q,
                save_mark=tq.name,
            )
            self._run_analysis(x_data=x_data, analysis_class=CrosstalkRamseyComAnalysis)
            bias_qubit = self.analysis.results.bias_qubit.value
            diff_freq = self.analysis.results.diff_freq.value
            self.file.save_data(bias_qubit, diff_freq, name=f"target_qubit={tq.name}")


class CrosstalkRamseyComAnalysis(CurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        Options:
            plot_key(str): Process sub analysis y_data key.

        """
        options = super()._default_options()

        options.subplots = (2, 1)
        options.x_label = "bias qubit"
        options.y_label = ["osc_freq", "probility"]
        options.result_parameters = [
            ParameterRepr(name="diff_freq", repr="freq", unit="MHz"),
            ParameterRepr(name="bias_qubit", repr="num", unit=""),
        ]
        return options

    def _extract_result(self, data_key: str, quality=None):
        """Extract Xpi from fitted data.

        Args:
            data_key (str): The basis for selecting data.
            quality : Quality of fit outcome.
        """

        osc_X_freq = self.analysis_datas.osc_X_freq.y
        osc_I_freq = self.analysis_datas.osc_I_freq.y
        exp1_bias_q = self.analysis_datas.exp1_bias_q.y
        if len(osc_X_freq) > 0:
            diff_freq = np.abs(osc_X_freq - osc_I_freq)
            self.results.diff_freq.value = diff_freq
            indices = np.where(diff_freq > 0.1)
            res_bq = exp1_bias_q[indices]
            self.results.bias_qubit.value = res_bq

    def _visualization(self):
        """Plot visualization."""

        self.drawer.set_options(raw_data_format="plot")

        analysis_data = self.analysis_datas
        x_data = self.experiment_data.x_data
        osc_I_freq = analysis_data.osc_I_freq.y
        osc_I_phase = analysis_data.osc_I_phase.y
        osc_X_freq = analysis_data.osc_X_freq.y
        osc_X_phase = analysis_data.osc_X_phase.y
        p1_I_list = analysis_data.p1_I_list.y
        p1_X_list = analysis_data.p1_X_list.y
        exp1_bias_q = analysis_data.exp1_bias_q.y
        exp2_bias_q = analysis_data.exp2_bias_q.y

        base_freq = osc_I_freq
        base_phase = osc_I_phase
        if np.any(base_phase):
            self.drawer.set_options(ylabel=["phase", "probility"])
            base_y1 = base_phase
        else:
            base_y1 = base_freq

        self.drawer.draw_axh_line(y=base_y1[0], ax_index=0)
        self.drawer.draw_axh_line(y=p1_I_list[0], ax_index=1)

        start = min(x_data)
        end = max(x_data)
        if start == end:
            start = 0
        dash_data = np.linspace(start, end, 20)

        dash_line = np.ones(len(dash_data))

        draw_ops = {
            "markersize": 5,
            "marker": "o",
            "alpha": 0.8,
            "linewidth": 2.5,
            "color": None,
        }

        if np.any(osc_X_freq):
            y1_data = osc_X_freq
            base_y = 0
        else:
            y1_data = osc_X_phase
            base_y = base_y1[0]

        self.drawer.draw_raw_data(
            x_data=exp1_bias_q, y_data=y1_data, ax_index=0, **draw_ops
        )

        self.drawer.draw_raw_data(
            x_data=exp2_bias_q, y_data=p1_X_list, ax_index=1, **draw_ops
        )

        draw_ops.update(
            {"linewidth": 0.8, "marker": "_", "color": "blue", "label": None}
        )
        if np.any(y1_data):
            self.drawer.draw_raw_data(
                x_data=dash_data,
                y_data=(base_y + 0.1) * dash_line,
                ax_index=0,
                **draw_ops,
            )

            self.drawer.draw_raw_data(
                x_data=dash_data,
                y_data=(base_y - 0.1) * dash_line,
                ax_index=0,
                **draw_ops,
            )
        if np.any(p1_X_list):
            self.drawer.draw_raw_data(
                x_data=dash_data,
                y_data=(p1_I_list[0] + 0.04) * dash_line,
                ax_index=1,
                **draw_ops,
            )

            self.drawer.draw_raw_data(
                x_data=dash_data,
                y_data=(p1_I_list[0] - 0.04) * dash_line,
                ax_index=1,
                **draw_ops,
            )
        self.drawer.format_canvas()
