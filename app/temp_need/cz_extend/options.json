{"LeakageOnceV2": {"meta": {"username": "", "monster_version": "0.22.4", "device": "B", "exp_class_name": "LeakageOnceV2", "export_datetime": "2025-03-03 17:57:49", "description": ""}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q1q2"}, "options_for_regular_exec": {"experiment_options": {"simulator_data_path": null, "repeat": 1000, "period": 100, "save_label": null, "fake_pulse": true, "fidelity_correct_type": "ibu", "is_amend": false, "swap_state": "11", "scan_name": "qc", "z_amp_list": null, "cz_num": 5, "scope": {"l": 0.1, "r": 0.1, "p": 31}, "label": "cz", "readout_type": "ql-01", "freq_list": [], "ac_branch": "right"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "LeakageNumV2": {"meta": {"username": "", "monster_version": "0.22.4", "device": "B", "exp_class_name": "LeakageNumV2", "export_datetime": "2025-03-03 17:57:49", "description": ""}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"simulator_data_path": null, "child_exp_options": {"simulator_data_path": null, "repeat": 1000, "period": 100, "save_label": null, "fake_pulse": true, "fidelity_correct_type": "ibu", "is_amend": false, "swap_state": "11", "scan_name": "qc", "z_amp_list": null, "cz_num": 1, "scope": {"l": 0.1, "r": 0.1, "p": 31}, "label": "cz", "readout_type": "ql-01", "freq_list": [], "ac_branch": "right"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "cz_num_list": "Points(40) | qarange | (1, 20, 1)"}, "analysis_options": {"child_ana_options": {}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CPhaseTMSEV2": {"meta": {"username": "", "monster_version": "0.22.4", "device": "B", "exp_class_name": "CPhaseTMSEV2", "export_datetime": "2025-03-04 14:28:36", "description": ""}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"simulator_data_path": null, "repeat": 1000, "period": 100, "save_label": null, "fake_pulse": true, "fidelity_correct_type": "ibu", "is_amend": false, "mode": "SE-TM", "ramsey_bit": "qh", "phase_mode": "control", "scan_name": "qh", "z_amp_list": null, "k": 1, "cz_num": 5, "scope": {"l": 30, "r": 30, "p": 31}, "readout_type": null}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CPhaseTMSENumV2": {"meta": {"username": "", "monster_version": "0.22.4", "device": "B", "exp_class_name": "CPhaseTMSENumV2", "export_datetime": "2025-03-04 17:25:16", "description": ""}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"simulator_data_path": null, "child_exp_options": {"simulator_data_path": null, "repeat": 1000, "period": 100, "save_label": null, "fake_pulse": true, "fidelity_correct_type": "ibu", "is_amend": false, "mode": "SE-TM", "ramsey_bit": "qh", "phase_mode": "control", "scan_name": "qh", "z_amp_list": null, "k": 5, "cz_num": 1, "scope": {"l": 30, "r": 30, "p": 31}, "readout_type": null, "freq_list": [], "ac_branch": "right"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "cz_num_list": "Points(40) | qarange | (1, 20, 1)"}, "analysis_options": {"child_ana_options": {}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}