# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/13
# __author:       <PERSON><PERSON><PERSON>


import os
from glob import glob

from loguru import logger

from app.analysis_test.standard_analysis_demo import AnalysisTest
from app.temp_need.cz_extend.exp import (
    CPhaseTMSENumAnalysisV2,
    LeakageNumAnalysisV2,
    LeakageNumAnalysisV3,
)


def find_files_with_suffix(parent_path, suffix):
    """Searches for files with a specific suffix in a parent directory and its subdirectories.

    Args:
        parent_path (str): The parent directory to search in.
        suffix (str): The suffix of the files to search for (e.g., 'LeakageNumV2.epd').

    Returns:
        List[str]: A list of file paths that match the suffix.
    """
    # Use glob to recursively search for files with the given suffix
    search_pattern = os.path.join(parent_path, "**", f"*{suffix}")
    matching_files = glob(search_pattern, recursive=True)

    return matching_files


def leakage_v2(path):
    test1 = AnalysisTest()
    test1.options.ana_cls_name = LeakageNumAnalysisV2
    test1.options.epd_path = path
    test1.options.analysis_options.update(
        dict(plot_2d=True, y_label=["QC Amp (v)"], sub_title=["P10"])
    )
    try:
        test1.run()
    except Exception as e:
        import traceback

        logger.error(f"V2 error {e}\n{traceback.format_exc()}")


def leakage_v3(path):
    test1 = AnalysisTest()
    test1.options.ana_cls_name = LeakageNumAnalysisV3
    test1.options.epd_path = path
    test1.options.analysis_options.update(
        dict(
            plot_2d=False,
            raw_data_format="plot",
            merge_y_data=False,
        )
    )
    try:
        test1.run()
    except Exception as e:
        import traceback

        logger.error(f"V3 error {e}\n{traceback.format_exc()}")


def cz_phase_v2(path):
    test1 = AnalysisTest()
    test1.options.ana_cls_name = CPhaseTMSENumAnalysisV2
    test1.options.epd_path = path
    test1.options.analysis_options.update(
        dict(
            plot_2d=True,
            raw_data_format="plot",
        )
    )
    try:
        test1.run()
    except Exception as e:
        import traceback

        logger.error(f"CZ phase V2 error {e}\n{traceback.format_exc()}")


def test_leakage(parent_path, suffix):
    files = find_files_with_suffix(parent_path, suffix)
    for epd in files:
        leakage_v2(epd)
        leakage_v3(epd)


def test_cz_phase(parent_path, suffix):
    files = find_files_with_suffix(parent_path, suffix)
    for epd in files:
        cz_phase_v2(epd)


if __name__ == "__main__":
    # test_leakage(
    #     parent_path=r"C:\Users\<USER>\Downloads\BatchRunner",
    #     suffix="LeakageNumV2.epd",
    # )

    test_cz_phase(
        parent_path=r"C:\Users\<USER>\Downloads\BatchRunner",
        suffix="CPhaseTMSENumV2.epd",
    )
