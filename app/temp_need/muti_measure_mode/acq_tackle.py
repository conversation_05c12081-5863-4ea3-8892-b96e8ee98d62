# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/30
# __author:       <PERSON><PERSON><PERSON>

import csv
import enum
import itertools
from collections import defaultdict
from dataclasses import dataclass, field
from enum import Enum
from functools import reduce
from operator import mul
from pathlib import Path
from typing import List, Union, Dict

import numpy as np
from prettytable import PrettyTable

from pyQCat.analysis.algorithms import IQdiscriminator
from pyQCat.log import pyqlog
from pyQCat.tools import time_cal


class UnionMode(Enum):

    U = "union"
    NU = "not union"


@dataclass
class QubitMeasureMode:

    name: str
    measure_total: int
    measure_num_list:  list = field(default_factory=list)
    base_label_list:  list = field(default_factory=list)

    def divide(self):

        if len(self.measure_num_list) != len(self.base_label_list):
            raise ValueError("measure_num 和 base_label 长度不一致！")

        result = []
        for index, num in enumerate(self.measure_num_list):
            once = OnceQubitMeasureMode(
                name=self.name,
                measure_total=self.measure_total,
                measure_num=num,
                base_label=self.base_label_list[index]
            )
            once.validator()
            result.append(once)

        return result


@dataclass
class OnceQubitMeasureMode:

    name: str
    measure_total: int
    measure_num: int
    base_label: str = ""
    remove_index: list = field(default_factory=list)
    goal_label: list = field(default_factory=list)

    def __repr__(self):
        return f"{self.name}-M({self.measure_num})-Base({self.base_label})"

    def validator(self):

        if self.measure_num == -1:
            self.measure_num = self.measure_total - 1

        self.remove_index = [self.measure_num]
        self.goal_label = []

        for idx, label in enumerate(self.base_label):
            if label == "x":
                self.remove_index.append(
                    idx if idx < self.measure_num else idx + 1
                )
            else:
                self.goal_label.append(int(label))

    @property
    def need_filter(self):
        return self.measure_total > 1 and self.base_label


class MultipleMeasureWrapper:

    def __init__(
        self,
        repeat: int,
        dcms: Union[IQdiscriminator, List[IQdiscriminator]],
        measure_modes: Union[QubitMeasureMode, List[QubitMeasureMode]],
        union_mode: enum.Enum = UnionMode.U
    ):
        self._repeat = repeat
        self._union_mode = union_mode

        if not isinstance(dcms, list):
            dcms = [dcms]

        if not isinstance(measure_modes, list):
            measure_modes = [measure_modes]

        if len(dcms) != len(measure_modes):
            raise ValueError("判据个数和处理类型个数不匹配")

        tackle_type_map: Dict[str, List[OnceQubitMeasureMode]] = {}

        for tt in measure_modes:
            tackle_type_map[tt.name] = tt.divide()

        for dcm in dcms:
            if dcm.name not in tackle_type_map:
                raise ValueError("无法找到此判据的结果处理类型")

        self._dcm_list = dcms
        self._measure_mode_map: Dict[str, List[OnceQubitMeasureMode]] = tackle_type_map

        self._data_i = None
        self._data_q = None
        self._label_map = None

        # cache iq order
        self._cache_std_index = {}

        # record data
        self._record_data = defaultdict(list)

        self.result_labels = self.get_data_labels()

    @property
    def qubit_num(self):
        return len(self._dcm_list)

    @property
    def record_data(self):
        return self._record_data

    @time_cal
    def tackle(self, measure_result: List):
        """

        Args:
            measure_result:

        Returns:

        """
        label_map = {}

        for idx, once_result in enumerate(measure_result):
            dcm = self._dcm_list[idx]
            measure_mode = self._measure_mode_map.get(dcm.name)[0]

            labels = dcm.get_predict_label(once_result.I, once_result.Q)
            labels = np.array(labels).reshape((measure_mode.measure_total, self._repeat))
            label_map[dcm.name] = labels

        self._label_map = label_map

        result_labels = []
        result_values = []
        tackles = [self._measure_mode_map.get(dcm.name) for dcm in self._dcm_list]

        if self._union_mode == UnionMode.U:
            for once_modes in itertools.product(*tackles):
                describe = " | ".join([str(one) for one in once_modes])
                result_labels.append(describe)
                result_values.append(self._once_run(list(once_modes)))
        else:
            for idx, qubit_tackles in enumerate(tackles):
                dcm = self._dcm_list[idx]
                for tackle in qubit_tackles:
                    result_values.append(self._once_run([tackle], [dcm]))
                    result_labels.append(str(tackle))

        return result_labels, result_values

    def _once_run(self, once_mode_list: List[OnceQubitMeasureMode], dcms: List[IQdiscriminator] = None):

        repeat_indexes = None
        label_map = {}
        dcm_list = dcms or self._dcm_list

        for idx, tackle_type in enumerate(once_mode_list):
            dcm = dcm_list[idx]
            labels = self._label_map.get(dcm.name)
            label_map[dcm.name] = labels

            ok_indexes = self._filter_index(tackle_type, labels)

            if repeat_indexes is None:
                repeat_indexes = ok_indexes
            else:
                repeat_indexes = np.intersect1d(repeat_indexes, ok_indexes)

        final_label = None
        for idx, dcm in enumerate(dcm_list):
            labels = label_map.get(dcm.name)
            tackle_type = once_mode_list[idx]
            labels = labels[tackle_type.measure_num, :][list(repeat_indexes)]
            if final_label is None:
                final_label = labels
            else:
                final_label = np.vstack((final_label, labels))
        total = final_label.shape[-1] or 1

        qubit_num = len(dcm_list)
        prob_status = np.zeros(1 << qubit_num)
        label_matrix = final_label.T
        if qubit_num == 1:
            for status_array in label_matrix:
                prob_status[int(status_array)] += 1
        else:
            for status_array in label_matrix:
                new_array = [str(int(v)) for v in status_array]
                prob_status[int("".join(new_array), 2)] += 1
        result = np.round(prob_status / total, 4)

        std_index = self._cache_dcm_std_index(dcm_list)
        if std_index:
            result = [result[ix] for ix in std_index]

        unit_key = "-".join([str(mode) for mode in once_mode_list])
        for idx, v in enumerate(result):
            self._record_data[f"{unit_key}-P{idx}"].append(v)

        return result

    def _filter_index(self, tackle_type: OnceQubitMeasureMode, labels):
        if tackle_type.need_filter:
            new_labels = np.delete(labels, tuple(tackle_type.remove_index), axis=0)
            if new_labels.shape[0] == 0:
                return np.arange(self._repeat)
            else:
                return np.where((new_labels.T == tuple(tackle_type.goal_label)).all(axis=1))[0]
        else:
            return np.arange(self._repeat)

    def _cache_dcm_std_index(self, dcms: List[IQdiscriminator]):
        name = "".join([dcm.name for dcm in dcms])
        if name not in self._cache_std_index and dcms[0].label is not None:
            pre_order = list(itertools.product(*[dcm.label for dcm in dcms]))
            sort_order = sorted(pre_order, key=lambda item: sum(item))
            if pre_order == sort_order:
                self._cache_std_index[name] = None
            else:
                am = {}
                for k in pre_order:
                    am[k] = sort_order.index(k)
                std_index = [am[k] for k in pre_order]
                self._cache_std_index[name] = std_index
        else:
            return self._cache_std_index.get(name)

    def save_record_data(self, save_path: str, x_data=None):

        csv_data = []
        if x_data is not None:
            csv_data.append(["X-Data"] + list(x_data))
        for key, value in self._record_data.items():
            csv_row = [key]
            csv_row.extend(value)
            csv_data.append(csv_row)

        with open(str(Path(save_path, "origin_data1.csv")), mode='w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            for row in csv_data:
                writer.writerow(row)

    def get_data_labels(self):
        tackles = [self._measure_mode_map.get(dcm.name) for dcm in self._dcm_list]
        dcm_map = {dcm.name: dcm for dcm in self._dcm_list}
        result_labels = []
        if self._union_mode == UnionMode.U:
            for once_modes in itertools.product(*tackles):
                label_nums = [dcm_map.get(one.name).n_clusters for one in once_modes]
                total = reduce(mul, label_nums, 1)
                describe = "-".join([str(one) for one in once_modes])
                result_labels.extend([f"{describe}-P{i}" for i in range(total)])
        else:
            for idx, qubit_tackles in enumerate(tackles):
                dcm = self._dcm_list[idx]
                for tackle in qubit_tackles:
                    result_labels.extend([f"{str(tackle)}-P{i}" for i in range(dcm.n_clusters)])

        table = PrettyTable()
        table.field_names = ["Index", "Label"]
        for index, label in enumerate(result_labels):
            table.add_row([str(index), label])
        pyqlog.log("EXP", f"Multiple Measure Label Results: \n{table}")

        return result_labels


if __name__ == "__main__":
    from pyQCat.structures import QDict

    backend = MultipleMeasureWrapper(
        repeat=1000,
        dcms=[
            IQdiscriminator([], [], name="q1"),
            IQdiscriminator([], [], name="q2"),
        ],
        measure_modes=[
            QubitMeasureMode(name="q1", measure_total=3, measure_num_list=[-1], base_label_list=["00"]),
            QubitMeasureMode(name="q2", measure_total=3, measure_num_list=[-1], base_label_list=["00"])
        ],
        union_mode=UnionMode.NU
    )

    res = backend.tackle(
        measure_result=[
            QDict(I=np.zeros(3000), Q=np.zeros(3000)),
            QDict(I=np.zeros(3000), Q=np.zeros(3000))
        ]
    )

    backend.save_record_data(
        r"F:\data",
        x_data=[0]
    )
