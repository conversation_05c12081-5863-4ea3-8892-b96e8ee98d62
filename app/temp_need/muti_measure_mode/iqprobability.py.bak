# -*- coding: utf-8 -*-
# This module provides a k-means algorithm to get the probability with I and Q

import os
import random
import time
from collections import Counter
from typing import List, Union, Tuple

import matplotlib.pyplot as plt
import numpy as np
from scipy.constants import Plan<PERSON>, <PERSON><PERSON><PERSON>
from scipy.integrate import quad
from scipy.interpolate import interp1d
from scipy.optimize import minimize
from sklearn.cluster import KMeans
from sklearn.mixture import GaussianMixture

from ..visualization.base_drawer import get_ax
from ...errors import AnalysisIQDcmError, AnalysisFieldError
from ...structures import QDict


def _select_regulation(cluster, state):
    result = None
    # state = 0表示分布情况： 1 | 0
    # c_0 -> 0标签 中心点 横坐标值
    # c_1 -> 1标签 中心点 横坐标值
    c_0, c_1 = cluster[:, 0]
    if state == 0:
        if c_0 > c_1:
            result = 1
        if c_0 < c_1:
            result = 0
    # state = 1表示分布情况： 0 | 1
    elif state == 1:
        if c_1 > c_0:
            result = 1
        if c_0 > c_1:
            result = 0
    else:
        raise Exception("state error!")
    return result


def _select_state(labels, cluster, wave_type):
    first_label, *__ = labels
    c_0, c_1 = cluster[:, 0]
    state = None
    if wave_type == first_label:
        if c_0 > c_1:
            state = 0
        if c_0 < c_1:
            state = 1
    else:
        if c_0 > c_1:
            state = 1
        if c_0 < c_1:
            state = 0
    return state


def _tabulate(seq):
    seq_list = list(seq)
    length = len(seq_list)
    prob_0 = seq_list.count(0) / length
    prob_1 = seq_list.count(1) / length
    return prob_0, prob_1


def get_iqprob(i, q, state=0, wave_type=None):
    """
    get the probability of 0 and 1 by i/q couple.
    :param i: ndarray type data sequence.
    :param q: ndarray type data sequence.
    :param state: only can be set to 0 or 1.
    :param wave_type: only can be set to 0 or 1.
    :return: 0 and 1 probability. such as 0.45, 0.55.
    """
    X = np.column_stack((i, q))
    kmeans = KMeans(n_clusters=2)
    y_pre = kmeans.fit_predict(X)
    prob0, prob1 = _tabulate(y_pre)
    C = kmeans.cluster_centers_
    if wave_type is None:
        # 直接采用指定的state判断
        update_state = state
    else:
        update_state = _select_state(labels=y_pre, cluster=C, wave_type=wave_type)
    if _select_regulation(cluster=C, state=update_state):
        return prob0, prob1
    else:
        return prob1, prob0


def _tabulate_multiple(seq):
    seq_list = list(seq)
    length = round(len(seq_list) / 2)
    seq0_list = seq_list[0:length]
    seq1_list = seq_list[length:]
    prob_00 = seq0_list.count(0) / length
    prob_01 = seq0_list.count(1) / length
    prob_10 = seq1_list.count(0) / length
    prob_11 = seq1_list.count(1) / length
    return prob_00, prob_01, prob_10, prob_11


def probability_multiple(i, q):  # 去除了state标志位判断，采用把0态的值放在第一位判断概率
    """
    get the probability of 0 and 1 by i/q couple.
    Please put "0"state first
    :param i: ndarray type data sequence.
    :param q: ndarray type data sequence.
    :return: 0 and 1 probability. such as 0.45, 0.55.
    """
    X = np.column_stack((i, q))
    kmeans = KMeans(n_clusters=2)
    y_pre = kmeans.fit_predict(X)
    prob00, prob01, prob10, prob11 = _tabulate_multiple(y_pre)
    c = kmeans.cluster_centers_
    if prob00 > prob01:
        prob0 = prob00
        prob1 = prob11
    else:
        prob0 = prob01
        prob1 = prob10
    return prob0, prob1, c


def probability_single(i, q):
    X = np.column_stack((i, q))
    kmeans = KMeans(n_clusters=2)
    y_pre = kmeans.fit_predict(X)
    probability = _tabulate(y_pre)
    c = kmeans.cluster_centers_
    prob0, prob1 = None, None
    if c[0, 0] < c[1, 0]:
        prob1 = probability[0]
        prob0 = probability[1]
    elif c[0, 0] > c[1, 0]:
        prob1 = probability[1]
        prob0 = probability[0]
    return prob0, prob1, c


def _get_rate_and_intercept(x1, y1, x2, y2):
    mean_x, mean_y = (x1 + x2) / 2, (y1 + y2) / 2
    # 如果垂直平分线与X轴平行
    if x1 == x2:
        return 0, mean_y
    rate = -1 / ((y1 - y2) / (x1 - x2))
    # 分别返回斜率k和截距b
    return rate, mean_y - rate * mean_x


def get_iq_threshold(i, q):
    X = np.column_stack((i, q))
    kmeans = KMeans(n_clusters=2, random_state=0).fit(X)
    axis = kmeans.cluster_centers_
    x1, y1 = axis[0]
    x2, y2 = axis[1]
    return _get_rate_and_intercept(x1, y1, x2, y2)


def get_iq_threshold_probability(k, b, i, q):
    # 如果传入一个坐标，即i和q分别是数值，则返回1标签或者0标签
    # 如果传入一组坐标，即i和q分别是list，则返回1态概率和0态概率
    I0, Q0, I1, Q1 = [], [], [], []
    if isinstance(i, list) and isinstance(q, list):
        iq_len = len(i)
        for single_i, single_q in zip(i, q):
            if single_q > single_i * k + b:
                I1.append(single_i)
                Q1.append(single_q)
            else:
                I0.append(single_i)
                Q0.append(single_q)
        return len(I0) / iq_len, len(I1) / iq_len, I0, Q0, I1, Q1
    elif isinstance(i, (int, float)) and isinstance(q, (int, float)):
        if q > i * k + b:
            return 1
        else:
            return 0


def get_multi_iq_threshold(i, q):
    x = np.column_stack((i, q))
    kmeans = KMeans(n_clusters=3, random_state=0).fit(x)
    axis = kmeans.cluster_centers_
    data = sorted(axis, key=_apart_from_axis)
    x0, y0 = data[0]
    x1, y1 = data[1]
    x2, y2 = data[2]
    k01, b01 = _get_rate_and_intercept(x0, y0, x1, y1)
    k12, b12 = _get_rate_and_intercept(x1, y1, x2, y2)
    k20, b20 = _get_rate_and_intercept(x2, y2, x0, y0)
    return k01, b01, k12, b12, k20, b20


def get_multi_iq_threshold_probability(k01, b01, k12, b12, k20, b20, i, q):
    # 如果传入一个坐标，即i和q分别是数值，则返回1标签或者0标签
    # 如果传入一组坐标，即i和q分别是list，则返回1态概率和0态概率
    I0, Q0, I1, Q1, I2, Q2 = [], [], [], [], [], []
    if isinstance(i, list) and isinstance(q, list):
        iq_len = len(i)
        for single_i, single_q in zip(i, q):
            if single_i * k01 + b01 <= single_q:
                if single_i * k20 + b20 <= single_q:
                    I0.append(single_i)
                    Q0.append(single_q)
            if single_i * k20 + b20 >= single_q:
                if single_i * k12 + b12 >= single_q:
                    I2.append(single_i)
                    Q2.append(single_q)
            if single_i * k01 + b01 >= single_q:
                if single_i * k12 + b12 <= single_q:
                    I1.append(single_i)
                    Q1.append(single_q)
        return (
            len(I0) / iq_len,
            len(I1) / iq_len,
            len(I1) / iq_len,
            I0,
            Q0,
            I1,
            Q1,
            I2,
            Q2,
        )
    elif isinstance(i, (int, float)) and isinstance(q, (int, float)):
        if i * k01 + b01 <= q:
            if i * k20 + b20 <= q:
                return 0
        if i * k20 + b20 >= q:
            if i * k12 + b12 >= q:
                return 2
        if i * k01 + b01 >= q:
            if i * k12 + b12 <= q:
                return 1


def _apart_from_axis(axis):
    x, y = axis
    return x**2 + y**2


class IQdiscriminator:
    """
    IQdiscriminator is used to diagnose the probability of P0 and P1 for the I and Q data
    collected by the experiment. Usually, the object is saved in the database or local
    bin file in binary form. Therefore, when obtaining, first query from the database,
    if not found, read from the local configuration file directory.
    """

    def __init__(
        self,
        I_list: List,
        Q_list: List,
        n_clusters: int = 2,
        method: str = "KMeans",
        name: str = None,
        level_str: str = "01",
        set_proportion: bool = False,
        heat_stimulate: bool = False,
    ):
        """Create a new IQdiscriminator.

        Args:
            I_list (List): Demodulated I data.
            Q_list (List): Demodulated Q data.
            n_clusters (int, optional): Number of taxonomic clusters.
            method (str, optional): Clustering algorithm, KMeans or GMM.
            name (str): Mark belong to which qcomponent.
            level_str (str): Mark energy level string, `01`, `02`,`012`
        """
        self.I_list = I_list
        self.Q_list = Q_list
        self.n_clusters = n_clusters
        self.method = method
        self.discriminator = None
        self.qubit = None

        self._iq_status = None
        self._trained = False
        self._label = None
        self._fit_centers = None
        self._discriminator_dict = {
            "KMeans": KMeans(n_clusters=self.n_clusters),
            "GMM": GaussianMixture(
                n_components=self.n_clusters, covariance_type="spherical"
            ),
        }
        self._fidelity = None
        self._probability = None
        self._radius = None
        self._std_deviation = np.array([])
        self._outlier = None
        self._iq_outlier = None
        self._k_recommend = None
        self._name = name
        self._level_str = level_str
        self._set_proportion = set_proportion
        self._proportion = None
        self._fidelity_matrix = None

        # Adjust calculate heat stimulate.
        self._cal_heat_stimulate = heat_stimulate
        self._heat_stimulate = 0.0
        self._equivalent_temp = 0.0

    def __repr__(self):
        return f"{self.name} IQdiscriminator<center={self.centers}>"

    def __str__(self):
        if self.level_str == "01":
            F0, F1 = self.fidelity
            return f"K={self.k_recommend} F0={F0} F1={F1} OL={self.outlier}"
        elif self.level_str == "02":
            F0, F2 = self.fidelity
            return f"K={self.k_recommend} F0={F0} F2={F2} OL={self.outlier}"
        elif self.level_str == "012":
            F0, F1, F2 = self.fidelity
            return f"K={self.k_recommend} F0={F0} F1={F1} F2={F2} OL={self.outlier}"
        else:
            return "Unknown label"

    def __len__(self):
        """Return length of self."""
        # bug fixed: before pickle.dumps(self), clear self.I_list data.
        return self.n_clusters

    def to_dict(self):
        show_infos = QDict()

        show_infos.K = int(self._k_recommend)
        show_infos.fidelity = self.fidelity
        show_infos.fidelity_matrix = self.fidelity_matrix
        show_infos.probability = self.probability
        show_infos.radius = self.radius
        show_infos.outlier = self.outlier
        show_infos.centers = self.centers
        show_infos.FM = float(np.mean(self.fidelity) if self.fidelity else 0)

        return show_infos.to_dict()

    @property
    def centers(self):
        """Model centers."""
        return self._fit_centers

    @property
    def fidelity(self):
        """Model fidelity."""
        return self._fidelity

    @property
    def probability(self):
        """Predict probability."""
        return self._probability

    @property
    def radius(self):
        """Model radius value."""
        return self._radius

    @property
    def std_deviation(self):
        """Model standard deviation value."""
        return self._std_deviation

    @property
    def outlier(self):
        """Outlier value."""
        return self._outlier

    @property
    def k_recommend(self) -> int:
        """Model ideal clusters."""
        return self._k_recommend

    @property
    def name(self) -> str:
        """Qcomponent name."""
        return self._name

    @property
    def level_str(self) -> str:
        """Qcomponent level_str."""
        return self._level_str

    @property
    def set_proportion(self) -> bool:
        """Qcomponent dcm type."""
        return self._set_proportion

    @property
    def proportion(self) -> str:
        """Gaussian distribution criterion proportion value"""
        return self._proportion

    @property
    def fidelity_matrix(self) -> np.ndarray:
        """Model fidelity matrix."""
        res = None
        if hasattr(self, "_fidelity_matrix"):
            res = self._fidelity_matrix
        return res

    @staticmethod
    def calculate_radius(
        x: np.ndarray,
        y: np.ndarray,
        center_x: float,
        center_y: float,
        n_multiple: float = 3.0,
    ) -> float:
        """Calculate radius.
        The radius calculated from the plane coordinate system to the center.

        Args:
            x: one dimension array
            y: one dimension array
            center_x (float): value of center's x direction
            center_y (float): value of center's y direction
            n_multiple (float): multiple of standard deviation

        Returns:
            radius (float): radius value
        """
        distance_arr = np.sqrt(np.square(x - center_x) + np.square(y - center_y))
        distance_mean = np.mean(distance_arr)
        distance_std = np.std(distance_arr)
        radius = distance_mean + n_multiple * distance_std
        return radius

    def _get_radius(self, fit_label: np.ndarray, n_multiple: float = 3.0):
        """Get model radius, and assign to self._radius.

        Args:
            fit_label: label array, one dimension array
            n_multiple (float): multiple of standard deviation

        """
        if isinstance(self.discriminator, KMeans):
            radius_list = []
            fit_label_arr = fit_label.reshape((len(self.I_list), -1))
            for c in self._fit_centers:
                center_x, center_y = c
                distance_list = []
                for I, Q in zip(self.I_list, self.Q_list):
                    I_mean, Q_mean = np.mean(I), np.mean(Q)
                    distance = np.sqrt(
                        np.square(I_mean - center_x) + np.square(Q_mean - center_y)
                    )
                    distance_list.append(distance)
                target_index = np.argmin(distance_list)
                target_label_arr = fit_label_arr[target_index]

                counts = np.bincount(target_label_arr)
                label = int(np.argmax(counts))
                label_index_arr = target_label_arr == label

                x = self.I_list[target_index][label_index_arr]
                y = self.Q_list[target_index][label_index_arr]

                radius = self.calculate_radius(
                    x, y, center_x, center_y, n_multiple=n_multiple
                )
                radius_list.append(radius)

            self._radius = np.array(radius_list)
            self._std_deviation = self._radius / n_multiple
        elif isinstance(self.discriminator, GaussianMixture):
            covariances = self.discriminator.covariances_
            self._std_deviation = np.sqrt(covariances)
            self._radius = self._std_deviation * n_multiple

    def _get_index_arr(
        self, i: Union[List, np.ndarray], q: Union[List, np.ndarray], mode: str = "iq"
    ) -> np.ndarray:
        """Get target data bool index.

        Args:
            i: i data, need of filter, one dimension array.
            q: q data, need of filter, one dimension array.
            mode (str): mark target data,
                        iq: default value, means get iq data within model radius;
                        outlier: means get iq data without model radius.

        Returns:
            target_index_arr: target data bool index, one dimension array.

        """
        index_list = []
        for c, radius in zip(self._fit_centers, self._radius):
            center_x, center_y = c
            distance_arr = np.sqrt(np.square(i - center_x) + np.square(q - center_y))
            index_arr = distance_arr <= radius
            index_list.append(index_arr)
        ret = np.sum(index_list, axis=0)
        if mode == "iq":
            target_index_arr = ret > 0
        elif mode == "outlier":
            target_index_arr = ret == 0
        else:
            print(f"Now, `mode` just support `iq` or `outlier`!")
            target_index_arr = np.array([True] * len(ret))
        return target_index_arr

    def get_linear(self):
        """Get the parameters of the center line equation."""
        center_point1, center_point2, *_ = self.centers
        A = (center_point2[1] - center_point1[1]) / (
            center_point2[0] - center_point1[0]
        )
        C = center_point1[1] - A * center_point1[0]
        return [A, -1, C]

    def _get_proportion(self):
        """Get the Gaussian criterion proportion,that is,
        the proportion of data on both sides of the line."""
        I_arr = np.asarray(self.I_list).flatten()
        Q_arr = np.asarray(self.Q_list).flatten()
        A, B, C = self.get_linear()
        L = A * I_arr + B * Q_arr + C
        right_point_arr = np.where(L > 0, True, False)
        right_point = I_arr[right_point_arr]
        left_point_arr = np.where(L < 0, True, False)
        left_point = I_arr[left_point_arr]
        right_data_rate = round(len(right_point) / len(I_arr), 4)
        left_data_rate = round(len(left_point) / len(I_arr), 4)
        self._proportion = abs(right_data_rate - left_data_rate)

    def get_outlier(self):
        """
        The distance between the data points other than 3σ and
        the straight line determined by the center of the circle
        is greater than the data of [1 state] circle spot distribution radius.
        """
        i_outlier, q_outlier = self._iq_outlier
        A, B, C = self.get_linear()
        l_outlier = A * i_outlier + B * q_outlier + C
        distance = abs(l_outlier) / np.sqrt(A**2 + B**2)
        target_index_arr = np.where(distance > self.radius[1], True, False)
        new_i_outlier = i_outlier[target_index_arr]
        return new_i_outlier

    def _screen_outlier(self):
        """Screen i_outlier, q_outlier data, without model radius,
        and assign to self._iq_outlier.
        Calculate rate of outlier, update self._outlier value.

        """
        if self._radius is not None:
            I_arr = np.asarray(self.I_list).flatten()
            Q_arr = np.asarray(self.Q_list).flatten()
            target_index_arr = self._get_index_arr(I_arr, Q_arr, mode="outlier")
            i_outlier, q_outlier = I_arr[target_index_arr], Q_arr[target_index_arr]
            self._iq_outlier = [i_outlier, q_outlier]
            self._outlier = round(len(i_outlier) / len(I_arr), 4)

    def screen_iq(
        self, i: Union[List, np.ndarray], q: Union[List, np.ndarray]
    ) -> Tuple[Union[List, np.ndarray], Union[List, np.ndarray]]:
        """Screen i, q data, within model radius.

        Args:
            i: original i data, one dimension array
            q: original q data, one dimension array

        Returns:
            (i, q): tuple of i, q
        """
        if self._radius is not None:
            target_index_arr = self._get_index_arr(i, q)
            i, q = i[target_index_arr], q[target_index_arr]

        return i, q

    def _inside_k_recommend(self, X: np.ndarray):
        """Simple and easy get X data k recommend value.

        Args:
            X (np.ndarray):  np.array(I, Q)

        """
        n_components = np.arange(1, 4)
        models = [GaussianMixture(n, random_state=0).fit(X) for n in n_components]
        ylist = [m.bic(X) for m in models]
        ylist = ylist / np.min(ylist)
        self._k_recommend = n_components[np.argmin(ylist)]

    def train(self, n_multiple: float = 3.0):
        """Train model.

        Args:
            n_multiple (float): multiple of standard deviation,
                                calculate model radius.

        """
        self._validate_params()

        I_list = np.asarray(self.I_list).flatten()
        Q_list = np.asarray(self.Q_list).flatten()

        X = np.column_stack((I_list, Q_list))

        fit_label = self._fit_predict(X)

        self._distinguish(fit_label)

        self._get_radius(fit_label, n_multiple=n_multiple)
        if self.set_proportion is True:
            self._get_proportion()
        self._screen_outlier()
        self._inside_k_recommend(X)
        self._check_center()

        if self._cal_heat_stimulate is True and self.n_clusters == 2:
            self._calculate_hs()

    def _calculate_hs(self):
        """Heat stimulate calculate."""
        c0, c1 = self.centers
        r0, r1 = self.radius
        std0, std1 = self.std_deviation
        p0, p1 = self.fidelity

        c0_x, c0_y = c0
        c1_x, c1_y = c1
        ab = np.sqrt(np.square(c0_x - c1_x) + np.square(c0_y - c1_y))
        ao = (r0 / (r0 + r1)) * ab
        bo = (r1 / (r0 + r1)) * ab

        def sn_func(x, miu, theta):
            """Standard-Normal distribution function."""
            y = (1 / (theta * np.sqrt(2 * np.pi))) * np.exp(
                (-1 / 2) * (((x - miu) / theta) ** 2)
            )
            return y

        def mb_func(t):
            """Maxwell-Boltzmann distribution function."""
            h = Planck
            k = Boltzmann
            level_freq_list = [4.9692, 4.6944, 4.3855, 4.0280]
            energy_list = [h * freq * 1e9 for freq in level_freq_list]

            e_i = energy_list[0]
            pe_i = np.exp(-e_i / (k * t)) / sum(
                [np.exp(-el / (k * t)) for el in energy_list]
            )
            return pe_i

        quad_v1, *_ = quad(sn_func, -np.inf, ao, args=(0, std0))
        quad_v2, *_ = quad(sn_func, -np.inf, ao, args=(ab, std1))
        hs = (quad_v1 - p0) / (quad_v1 - quad_v2)
        self._heat_stimulate = round(hs, 6)

        if hs > 0:
            tamp_arr = np.arange(10, 65) * 1e-3
            hs_arr = mb_func(tamp_arr)
            ipd_func = interp1d(
                hs_arr, tamp_arr, kind="cubic", fill_value="extrapolate"
            )
            temp = ipd_func(hs) * 1e3  # mK
            self._equivalent_temp = round(temp, 3)
            print(
                f"{self.name} heat stimulate: {hs:.2%}, "
                f"equivalent temp: {temp:.3f} mK"
            )
        else:
            print(f"{self.name} heat stimulate: {hs:.2%}")

    def _check_center(self):
        """Check center match label."""
        act_centers = []  # two-dimensional array
        act_radius = []  # one-dimensional array
        act_std = []  # one-dimensional array

        for label in self._label:
            center = self.centers[label, :]
            radius = self.radius[label]
            std_val = self.std_deviation[label]
            act_centers.append(center)
            act_radius.append(radius)
            act_std.append(std_val)

        self._fit_centers = np.array(act_centers)
        self._radius = np.array(act_radius)
        self._std_deviation = np.array(act_std)

    def predict(
        self,
        i: Union[List, np.ndarray],
        q: Union[List, np.ndarray],
        screen_flag: bool = False,
    ):
        """Predict state label.

        Args:
            i (Union[List, np.ndarray]): Input I data.
            q (Union[List, np.ndarray]): Input Q data.
            screen_flag (bool): Screen iq or not.

        Returns:
            np.ndarray: Label array.
        """
        if screen_flag is True:
            i, q = self.screen_iq(i, q)
        labels = self.discriminator.predict(np.column_stack((i, q)))
        return self._convert_label(labels)

    def get_probability(
        self,
        i: Union[List, np.ndarray],
        q: Union[List, np.ndarray],
        screen_flag: bool = False,
    ):
        """Calculate probability.

        Args:
            i (Union[List, np.ndarray]): Input I data.
            q (Union[List, np.ndarray]): Input Q data.
            screen_flag (bool): Screen iq or not.

        """
        len_i = len(i)
        len_q = len(q)

        prob = []

        if len_i != len_q:
            raise AnalysisIQDcmError(
                f"I list and Q list have different lengths, I({len_i}), Q({len_q})!"
            )

        if screen_flag is True:
            i, q = self.screen_iq(i, q)

        predict_label = self.get_predict_label(i, q)

        for label in self._label:
            label_counter = Counter(predict_label)
            P = label_counter[label] / len(predict_label)
            prob.append(P)

        self._probability = prob

    def get_predict_label(
        self,
        i: Union[List, np.ndarray],
        q: Union[List, np.ndarray],
        simulator: bool = True
    ):
        data = np.column_stack((i, q))

        if simulator:
            predict_label = [random.randint(0, 1) for _ in range(i.shape[0])]
        elif self.discriminator is not None:
            predict_label = self.discriminator.predict(data)
        else:
            raise AnalysisIQDcmError("Please train IQ discriminator before using!")

        return predict_label

    def plot(
        self,
        dirs: str = None,
        name: str = None,
        is_save: bool = False,
        bit: int = None,
        power: float = None,
        freq: float = None,
        figsize=(8, 7),
    ):
        """Plot IQ round spot photos."""
        if not self._trained:
            raise AnalysisIQDcmError("Please train IQ discriminator before using!")

        if bit is not None:
            title = f"Qubit{bit} I/Q probability distribution"
        else:
            title = f"{self.name} I/Q probability distribution"

        if self._cal_heat_stimulate is True:
            title = (
                f"{title}\nprobe_power={power}db, freq={freq}MHz, "
                f"k_recommend={self.k_recommend}\n"
                f"heat_stimulate={self._heat_stimulate:.2%}, "
                f"equivalent_temp={self._equivalent_temp} mK"
            )
        else:
            title = (
                f"{title}\nprobe_power={power}db, freq={freq}MHz, "
                f"k_recommend={self.k_recommend}"
            )

        fig, ax = get_ax()
        fig.set_size_inches(*figsize)

        ax.grid(True)
        ax.axis("equal")
        ax.set_xlabel("I", fontsize=10)
        ax.set_ylabel("Q", fontsize=10)
        ax.set_title(title, fontsize=10)
        ax.tick_params(labelsize=10)

        cs = {"0": "blueviolet", "1": "orangered", "2": "#00A0E9"}
        label_list = []
        for i, level in enumerate(self.level_str):
            ax.scatter(x=self.I_list[i], y=self.Q_list[i], c=cs[level], s=10)
            label = f"|{level}> {round(self.fidelity[i], 4)}"
            label_list.append(label)

        if self._radius is not None and self._iq_outlier is not None:
            i_outlier, q_outlier = self._iq_outlier
            ax.scatter(x=i_outlier, y=q_outlier, c="#00CC00", s=10)
            outlier_label = f"|outlier> = {round(self._outlier, 4)}"
            label_list.append(outlier_label)

        ax.legend(labels=label_list, loc="upper right", fontsize=10)

        i_mean = np.mean(self.I_list)
        q_mean = np.mean(self.Q_list)

        bbox = dict(boxstyle="round, pad=1", fc="yellow")
        arrowprops = dict(arrowstyle="->", connectionstyle="arc3", color="lawngreen")

        for i, c in enumerate(self._fit_centers):
            x, y = c
            ax.scatter(x, y, marker="*", c="lawngreen", linewidths=8)

            ax_text = "Center({:.2f}, {:.2f})".format(x, y)
            if self._radius is not None:
                radius = self.radius[i]
                ax_text = "Center({:.2f}, {:.2f})\nRadius({:.2f})".format(x, y, radius)
                draw_circle = plt.Circle(
                    (x, y),
                    radius=self.radius[i],
                    fill=False,
                    edgecolor="lawngreen",
                    linewidth=2.0,
                )
                ax.add_artist(draw_circle)
            ax.annotate(
                ax_text,
                xy=(x, y),
                textcoords="offset points",
                xytext=(x - i_mean, y - q_mean),
                weight="extra bold",
                bbox=bbox,
                arrowprops=arrowprops,
            )

        if dirs is None:
            dirs = os.getcwd()
        else:
            if not os.path.exists(dirs):
                os.makedirs(dirs, exist_ok=True)

        cur_time = time.strftime("%Y%m%d%H%M%S", time.localtime(time.time()))
        if name is None:
            name = f"{self.name}-{cur_time}"
        else:
            name = name + "-" + cur_time

        if is_save:
            fig.savefig(dirs + "/" + "{}_I-Q.png".format(name))

        return fig

    def _validate_params(self):
        length = len(self.I_list)

        if length != len(self.Q_list):
            raise AnalysisIQDcmError(
                f"I list and Q list have different lengths, I({length}), Q({len(self.Q_list)})!"
            )

        self._iq_status = [status for status in range(length)]

        if length > self.n_clusters:
            raise AnalysisIQDcmError(
                "Number of cluster should not be smaller than number of input status"
            )

    def _get_cluster_centers(self):
        if isinstance(self.discriminator, KMeans):
            return self.discriminator.cluster_centers_
        if isinstance(self.discriminator, GaussianMixture):
            return self.discriminator.means_

    def _fit_predict(self, X: np.ndarray) -> np.ndarray:
        self.discriminator = self._discriminator_dict.get(self.method)

        if not self.discriminator:
            raise AnalysisFieldError("method", self.method, expect=["KMeans", "GMM"])

        label = self.discriminator.fit(X).predict(X)

        self._fit_centers = self._get_cluster_centers()
        self._trained = True

        return label

    def _check_repeat(self, exsist_label: int, new_label: int) -> bool:
        ret = False
        if exsist_label == new_label:
            print(
                "status |{}> and |{}> are mixed and "
                "cannot be distinguished".format(exsist_label, new_label)
            )
            ret = True
        return ret

    def _get_fidelity(self, label_list: np.ndarray, label: int, total: int):
        if self._fidelity is None:
            self._fidelity = []
        label_counter = Counter(label_list)
        f = label_counter[label] / total
        self._fidelity.append(f)

    @staticmethod
    def _get_fidelity_matrix(counts_arr: np.ndarray, fidelity: List[float]) -> np.ndarray:
        """According to label counts array, calculate fidelity matrix.

        Args:
            counts_arr (array): 2D-array, normal like:
                n_clusters = 2:
                    [[ 9417   583]
                     [2958  7042]]
                n_clusters = 3:
                     [[ 232 4707   61]
                      [4179  632  189]
                      [ 706  246 4048]]
            fidelity (list): Fidelity list, use to reference select count.

        Returns:
            fidelity_matrix: 2D-array.

        """
        rows = counts_arr.shape[0]
        new_arr = np.zeros_like(counts_arr)
        fidelity_matrix = np.zeros_like(counts_arr, dtype=np.float64)

        idx_list = []
        for row in range(rows):
            fd = fidelity[row]
            if fd > 0.5:
                idx = np.argmax(counts_arr[row, :])
            else:
                idx = np.argmin(counts_arr[row, :])
            idx_list.append(idx)
        for i, idx in enumerate(idx_list):
            new_arr[:, i] = counts_arr[:, idx]
        for row in range(rows):
            row_sum = np.sum(new_arr[row, :])
            fidelity_matrix[row, :] = new_arr[row, :] / row_sum
        return fidelity_matrix.T

    def _distinguish(self, fitted_label: np.ndarray):
        self._label = []
        status_len_list = [len(i_list) for i_list in self.I_list]  # 获得每个输入状态的数组长度
        start_index = 0
        counts_list = []
        # 对输入的状态进行遍历，确定每个状态下的标签
        for status in self._iq_status:
            current_label_len = status_len_list[status]
            end_index = start_index + current_label_len
            raw_label = fitted_label[start_index:end_index]  # 从分好类的标签列表中取出当前输入状态的标签列表

            counts = np.bincount(raw_label)  # 统计出标签集合中的各个标签出现次数

            # bugfix: 2023/10/24 count error
            # np.bincount maybe cause result shape different
            # like np.bincount([0, 1, 2]) = [1, 1, 1], np.bincount([0, 2, 2]) = [1, 0, 2]
            # but np.bincount([0, 1, 1) = [1, 2], lose state-2 count, cause shape diff
            if counts.shape[0] < self.n_clusters:
                num = self.n_clusters - counts.shape[0]
                counts = np.hstack([counts, np.zeros(num, dtype=int)])

            true_label = int(np.argmax(counts))  # 取出现次数最多的标签作为真实标签
            counts_list.append(counts)

            for i, label in enumerate(self._label):
                if self._check_repeat(label, true_label):
                    true_label, *_ = list(set(self._label) ^ set(self._iq_status))

            self._label.append(true_label)

            self._get_fidelity(raw_label, true_label, current_label_len)

            start_index += status_len_list[status]

        self._fidelity_matrix = self._get_fidelity_matrix(np.array(counts_list), self._fidelity)

        diff = self.n_clusters - len(self._iq_status)
        if diff:
            for i in range(diff):
                diff_label, *_ = list(set(self._label) ^ set(self._iq_status))
                self._label.append(diff_label)

    def _convert_label(self, labels: np.ndarray):
        convert_shape = len(labels)
        convert_label = np.zeros(convert_shape, dtype=int)
        expect_label_list = [i for i in range(self.n_clusters)]

        for label in expect_label_list:
            index_list = [i for i, x in enumerate(labels) if x == label]
            np.put(convert_label, index_list, self._label.index(label))

        return convert_label

    def get_k_recommend(self, name: str, power: float, dirs: str = None):
        """Get k_recommend, by `self.I_list` and `self.I_list`.

        Args:
            name (int): Qubit or Coupler name.
            power (float): Get IQ data, the readout power.
            dirs (str): Save plot figure path.

        Returns:
            int: k_recommend
        """
        I_list = np.asarray(self.I_list).flatten()
        Q_list = np.asarray(self.Q_list).flatten()
        Xmoon = np.column_stack((I_list, Q_list))
        n_components = np.arange(1, 4)
        models = [
            GaussianMixture(n, covariance_type="full", random_state=0).fit(Xmoon)
            for n in n_components
        ]
        ylist = [m.bic(Xmoon) for m in models]
        ylist = ylist / np.min(ylist)
        k_recommend = n_components[np.argmin(ylist)]
        fig, ax = plt.subplots()
        ax.plot(n_components, ylist, "-o")
        # plt.plot(n_components, [m.aic(Xmoon) for m in models], label='AIC')
        ax.set_ylabel("BIC(a.u.)", fontsize=18)
        ax.set_xlabel("k clusters", fontsize=18)
        ax.set_title(f"power={power} recommend k={k_recommend}", fontsize=18)

        if dirs is not None:
            if not os.path.exists(dirs):
                os.makedirs(dirs, exist_ok=True)
            png_name = os.path.join(dirs, f"{name}_power{power}k_clusters.png")
            fig.savefig(png_name)
        plt.close(fig)
        return k_recommend

    def mean_center_distance(self):
        def _distance(i0, q0, i1, q1):
            return np.sqrt(np.square(i0 - i1) + np.square(q0 - q1))

        centers = []
        for i in range(self.centers.shape[0]):
            centers.append(list(self.centers[i, :]))

        distances = []
        for i in range(len(centers) - 1):
            for j in range(i + 1, len(centers)):
                distances.append(_distance(*centers[i], *centers[j]))

        return np.mean(distances)


def get_qubits_probability(
    discriminator_list: List[IQdiscriminator],
    i_list: List,
    q_list: List,
    repeat: int,
) -> List:
    """Convert IQ data into P0 and P1 data.

    Args:
        discriminator_list: IQ discriminator for each qubit.
        i_list: i data for each qubit.
        q_list: q data for each qubit.
        repeat: single repeat time.

    Returns: Probability data.

    Notes: At present, this interface can only calculate the
        probability of I/Q for two quantum states: 0 and 1.
    """
    qubits_num = len(discriminator_list)
    label_array = None
    prob_status = np.zeros(1 << qubits_num)

    i_len = len(i_list)
    q_len = len(q_list)
    if not (qubits_num == i_len == q_len):
        raise AnalysisIQDcmError(
            f"iq probability input arguments dimensionality error!"
            f" qubit_num({qubits_num}) | I({i_len}) | Q({q_len})"
        )

    for i, discriminator in enumerate(discriminator_list):
        label = discriminator.predict(i_list[i], q_list[i])
        if label_array is None:
            label_array = label
        else:
            label_array = np.vstack((label_array, label))

    label_matrix = label_array.T

    # bugfix: maybe one qubit case
    if qubits_num == 1:
        for status_array in label_matrix:
            prob_status[int(status_array)] += 1
    else:
        for status_array in label_matrix:
            new_array = [str(int(v)) for v in status_array]
            prob_status[int("".join(new_array), 2)] += 1

    prob_list = prob_status / repeat

    return prob_list.tolist()


def correct_fidelity(
    bit_num: int, index_list: List, std_p: List, f_matrix: np.ndarray, method: str
):
    # Fidelity calibration function, the returned probability list works better after f_matrix correction.
    prob0 = np.random.rand(len(std_p))
    prob0 = prob0 / np.sum(prob0)
    if method == "inv":
        try:
            inv_matrix = np.linalg.inv(f_matrix)
        except np.linalg.LinAlgError:
            raise AnalysisFieldError(
                "fidelity_matrix",
                f_matrix,
                msg="fidelity matrix is not an invertible matrix",
            )
        after_correct_f = np.dot(
            inv_matrix, np.array([std_p]).reshape((-1, 1))
        ).flatten()

        # normalization
        min_value = np.min(after_correct_f)
        if min_value < 0:
            after_correct_f = np.add(after_correct_f, 2 * abs(min_value))
        std = np.sum(after_correct_f)
        for i, f in enumerate(after_correct_f):
            after_correct_f[i] = f / std

    elif method == "ibu":
        after_correct_f = iter_bayesian_unfold(
            std_p, prob0, f_matrix, max_iter=50, max_rmse=-1
        )
    elif method == "least-sq":
        cons = {"type": "eq", "fun": lambda x: np.sum(std_p) - np.sum(x)}
        bnds = tuple((0, 1) for _ in std_p)
        res = minimize(
            fidelity_error,
            prob0,
            method="SLSQP",
            constraints=cons,
            bounds=bnds,
            args=(std_p, f_matrix),
        )
        after_correct_f = res.x
        after_correct_f = after_correct_f.flatten()
    else:
        raise AnalysisFieldError(
            "fidelity-correct-method",
            method,
            expect=["ibu", "inv", "least-sq"],
        )

    q_len = index_list.count(True)
    if q_len == bit_num:
        prob_arr = after_correct_f
    else:
        # Optimize, 2023-06-06.
        # Projection extraction results no support 3 classification,
        # and this operation is generally used for cloud platforms.
        prob_arr = np.zeros(1 << q_len)
        ma = simulate_qgate(bit_num)
        qm = ma[:, index_list]
        for i, q in enumerate(qm):
            s = ""
            for single_num in q:
                s += str(int(single_num))
            decimal_num = int(s, 2)
            index = decimal_num
            prob_arr[index] += after_correct_f[i]

    return prob_arr.tolist()


def simulate_qgate(qubit_num: int) -> np.ndarray:
    simulate_gate = np.zeros((1 << qubit_num, qubit_num))
    for i in range(1 << qubit_num):
        s = np.binary_repr(i, width=qubit_num)
        for j in range(qubit_num):
            simulate_gate[i][j] = int(s[j])
    return simulate_gate


def get_p_labels(
    discriminator: Union[IQdiscriminator, List[IQdiscriminator]]
) -> List[str]:
    """Get P labels."""
    if discriminator is None:
        dcm_list = []
    elif isinstance(discriminator, IQdiscriminator):
        dcm_list = [discriminator]
    else:
        dcm_list = discriminator

    prob_labels = None
    for dcm in dcm_list:
        if dcm and isinstance(dcm, IQdiscriminator):
            level_str = dcm.level_str
            if prob_labels is None:
                prob_labels = [str(s) for s in level_str]
            else:
                new_labels = []
                for label in prob_labels:
                    new_labels.extend([f"{label}{s}" for s in level_str])
                prob_labels = new_labels
    if prob_labels is None:
        p_labels = []
    else:
        p_labels = [f"P{label}" for label in prob_labels]
    return p_labels


def get_multi_bits_probability(
    discriminator_list: List[IQdiscriminator],
    i_list: List,
    q_list: List,
    prob_dict: dict = None
) -> Tuple[List[str], List[int], List[float]]:
    """Get multi bits probability list.
    Support IQ discriminator classification `0`, `1`, `2`.
    """
    qubits_num = len(discriminator_list)
    i_len = len(i_list)
    q_len = len(q_list)
    if not (qubits_num == i_len == q_len):
        raise AnalysisIQDcmError(
            f"iq probability input arguments dimensionality error!"
            f" qubit_num({qubits_num}) | I({i_len}) | Q({q_len})"
        )

    label_array = None
    for i, discriminator in enumerate(discriminator_list):
        label = np.asarray(discriminator.predict(i_list[i], q_list[i]))
        if label_array is None:
            label_array = np.vstack((label,))
        else:
            label_array = np.vstack((label_array, label))
    label_matrix = label_array.T

    prob_dict = prob_dict or {}
    for status_array in label_matrix:
        label = "".join([str(int(s)) for s in status_array])
        if label in prob_dict:
            prob_dict[label] += 1
        else:
            prob_dict[label] = 1

    prob_labels = list(prob_dict.keys())
    count_list = list(prob_dict.values())
    sum_times = sum(count_list)
    prob_list = [prob / sum_times for prob in count_list]
    return prob_labels, count_list, prob_list


def post_selection(bit_num: int, index_list: List, prob0: List, select_type: str):
    prob0 = np.asarray(prob0)
    if select_type == "g":
        mask = 0
        prob0[mask] = 0
        after_select = prob0 / np.sum(prob0)
    elif select_type == "g&e":
        probe_q_lenth = 1
        idx_e1 = 1 << np.arange(probe_q_lenth)
        idx_all = np.arange(2**probe_q_lenth)
        mask = np.delete(idx_all, idx_e1)
        prob0[mask] = 0
        after_select = prob0 / np.sum(prob0)
    else:
        raise AnalysisFieldError("select_type", select_type, expect=["g", "g&e"])

    ma = simulate_qgate(bit_num)

    q_len = index_list.count(True)
    prob_list = np.zeros(1 << q_len)

    qm = ma[:, index_list]

    for i, q in enumerate(qm):
        s = ""
        for single_num in q:
            s += str(int(single_num))
        decimal_num = int(s, 2)
        index = decimal_num
        prob_list[index] += after_select[i]
    return prob_list.tolist()


def iter_bayesian_unfold(
    pmea, ptrue0, fmat, max_iter: int = 20, max_rmse: float = 1e-5
):
    """
    iterative bayesian unfolding method[npj Quantum Information (2020) 6:84]:
    ti(n+1) = sum_j(Pr(truth is i|measure j) * mj)
            = sum_j((Rji * ti(n)) * mj / sum_k(Rjk*tk(n)))
    numerator = ti(n) * Rji * mj
    denominator =  sum_k(Rjk*tk(n))

    The matrix form of each element of the molecule is represented as follows,
    and it can be multiplied directly by algebraic multiplication, and the result is a dim*dim matrix:
    ti(n)->[[t0, t1, ..., tend],
            [...],
           [t0, t1, ..., tend]](dim*dim)
    Rji->[[R00, R01, ..., R0end],
          [..., Rji, ..., Rjend],
          [Re0, Re1, ..., Reend]]
    mj->[[m0, m0, ..., m0],
         [mj, mj, ..., mj],
         [me, me, ..., meend]]

    The denominator is equivalent to multiplying the matrix and column vectors,
    and the result is a dim*1 row vector.

    After dividing the numerator and denominator [numerator/denominator],
    sum the row index [np.sum(...,axis=0)],
    and finally return a column vector, which is the true value obtained by iterating using the IBU method.

    :param pmea: Value of layout measurements, i.e. experimental data.
    :param ptrue0: An estimate of the actual number of layouts.
    :param fmat: Fidelity matrix, fmat_ji represents the probability that the true value is i
     and the measured value is j (row index corresponds to measurement, column index corresponds to true value)
    :param max_iter: Maximum number of iterations.
    :param max_rmse: The maximum allowed RMSE, if less than max_rmse before the maximum number of iterations,
     it jumps out of the loop.
    :return:
    """
    ptruen = np.asarray(ptrue0)
    pmea = np.asarray(pmea)
    fmat = np.asarray(fmat)

    n, rmse = 0, np.inf
    while (n < max_iter) and (rmse > max_rmse):
        dim = len(pmea)
        # pmea copies by column.
        pmea_mat = np.tile(pmea.reshape(dim, 1), (1, dim))
        # ptruen copies by row.
        ptruen_mat = np.tile(ptruen, (dim, 1))
        # Molecules use multiplication.
        numerator_mat = ptruen_mat * fmat * pmea_mat
        # The denominator uses matrix multiplication.
        denominator = fmat @ ptruen
        denominator_mat = np.tile(denominator.reshape(dim, 1), (1, dim))
        ptruen1 = np.sum(numerator_mat / denominator_mat, axis=0)
        ptruen = ptruen1
        n += 1
        rmse = fidelity_error(ptruen, pmea, fmat)
    return ptruen.flatten()


def fidelity_error(ptrue, pmea, fmat):
    """
    Calculate the RMSE between pmea and fmat@ptrue.
    :param ptrue: Actual value of layout count.
    :param pmea: Measured value of layout count, i.e. experimental data.
    :param fmat: Fidelity matrix, where fmat_ji represents the probability of measuring
     the value j when the true value is i (the row index corresponds to the measurement value,
     and the column index corresponds to the true value).
    :return:
    """
    ptrue = np.asarray(ptrue)
    pmea = np.asarray(pmea)
    fmat = np.asarray(fmat)
    mat_dot_ptrue = np.ravel(fmat @ ptrue)
    rmse = np.sum((pmea - mat_dot_ptrue) ** 2) / len(pmea)
    return rmse
