# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/14
# __author:       <PERSON><PERSON><PERSON>

import asyncio

import exp_libs
from exp_base import ParallelExperiment, CompositeExperiment, start_parallel_thread_service


def parallel_test(
    exp_name: str,
    run_mode: str = "sync",
    parallel_num: int = 3
):
    exp_class = getattr(exp_libs, exp_name)
    parallel_exp_list = []
    for i in range(parallel_num):
        pc_exp = exp_class(physical_unit=f"Q{i + 1}")
        pc_exp.exp_options.run_mode = run_mode
        pc_exp.child_exp.exp_options.run_mode = run_mode
        parallel_exp_list.append(pc_exp)
    parallel_exp = ParallelExperiment(parallel_exp_list)
    asyncio.run(parallel_exp.run())


if __name__ == '__main__':
    start_parallel_thread_service()
    parallel_test(
        exp_name="DetuneCalibration",
        run_mode="async",
        parallel_num=3
    )
