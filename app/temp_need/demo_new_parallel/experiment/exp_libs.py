# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/14
# __author:       <PERSON><PERSON><PERSON>

import asyncio
import random
from copy import deepcopy

from loguru import logger

from exp_base import TopExperiment, CompositeExperiment


class APE(TopExperiment):
    pass


class RamseyCrosstalk(TopExperiment):
    pass


class APEComposite(CompositeExperiment):

    _sub_exp_class = APE

    async def sync_run(self):
        sweep_num = random.randint(3, 5)
        for idx in range(sweep_num):
            child_exp = deepcopy(self.child_exp)
            child_exp.set_parent(self, idx)
            child_exp.set_options(N=idx)
            child_exp = await self.child_exp_run(child_exp)
            self._experiments.append(child_exp)

    async def async_run(self):
        task_list = []
        exp_list = []
        sweep_num = random.randint(3, 5)
        for idx in range(sweep_num):
            child_exp = deepcopy(self.child_exp)
            child_exp.set_parent(self, idx)
            child_exp.set_options(N=idx)
            task = await self.child_exp_run(child_exp)
            task_list.append(task)
            exp_list.append(child_exp)
        try:
            await asyncio.gather(*task_list)
            for ce in exp_list:
                self._experiments.append(ce)
        except asyncio.CancelledError:
            logger.error("Async run experiment cancelled.")


class DetuneCalibration(CompositeExperiment):

    _sub_exp_class = APEComposite

    async def sync_run(self):
        sweep_num = random.randint(3, 5)
        for idx in range(sweep_num):
            child_exp = deepcopy(self.child_exp)
            child_exp.set_parent(self, idx)
            child_exp.set_options(N=idx)
            child_exp = await self.child_exp_run(child_exp)
            self._experiments.append(child_exp)

    async def async_run(self):
        task_list = []
        exp_list = []
        sweep_num = random.randint(3, 5)
        for idx in range(sweep_num):
            child_exp = deepcopy(self.child_exp)
            child_exp.set_parent(self, idx)
            child_exp.set_options(N=idx)
            task = await self.child_exp_run(child_exp)
            task_list.append(task)
            exp_list.append(child_exp)
        try:
            await asyncio.gather(*task_list)
            for ce in exp_list:
                self._experiments.append(ce)
        except asyncio.CancelledError:
            logger.error("Async run experiment cancelled.")


# class ACCrosstalk(CompositeExperiment):
#     _sub_exp_class = RamseyCrosstalk
#
#     async def sync_run(self):
#         pass
#
#     def run(self):
#         super().run()
#

