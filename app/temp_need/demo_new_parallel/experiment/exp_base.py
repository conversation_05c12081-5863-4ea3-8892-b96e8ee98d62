# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/04/14
# __author:       <PERSON><PERSON><PERSON>

from __future__ import annotations

import asyncio
import random
import time
import uuid
from collections import defaultdict
from dataclasses import dataclass
from enum import Enum
from threading import Thread, Event
from typing import List, Union
from prettytable import PrettyTable

from loguru import logger

from pyQCat.errors import PyQCatError
from pyQCat.structures import QDict

logger.level("CS", no=21, color="<cyan><bold>", icon="🐍")
logger.level("TS", no=22, color="<blue>", icon="🐍")

ProcessCache = QDict(merge_thread=None)
ERROR_RATE = 0.05


class RunMode(Enum):

    async_mode = "async"
    sync_mode = "sync"


class MergeThreadState(Enum):

    error = 0x00
    suc = 0x01


class ExperimentState(Enum):

    error = 0x10
    suc = 0x11


class MergeError(PyQCatError):
    pass


class BuildMessageError(PyQCatError):
    pass


class AnalysisError(PyQCatError):
    pass


@dataclass
class ParallelRequestMessage:
    parallel_num: int
    experiment_name: str
    token: str = None

    def __repr__(self):
        return f"Parallel-{self.experiment_name}-Total({self.parallel_num})"


class MergerThread(Thread):
    def __init__(self, name: str = "merger", daemon: bool = True):
        super().__init__(name=name, daemon=daemon)

        self._flag_stop = True
        self._flag_stop_ed = False
        self._flag_pause = False

        self.request_message: ParallelRequestMessage | None = None
        self.task_map = defaultdict(list)
        self.task_id = {}
        self.is_merge_map = {}

        self._event = Event()

    def is_alive(self):
        return not self._flag_stop

    def sleep(self, sleep_time: float):
        self._event.wait(sleep_time)

    def stop(self):
        self._flag_stop = True

    def pause(self):
        self._flag_pause = True

    def recover(self):
        self._flag_pause = False

    def clear(self):
        logger.log("TS", f"{self.request_message} end... clear data")
        self.request_message = None
        self.task_map.clear()
        self.task_id.clear()
        self.is_merge_map.clear()

    def pre_run(self):
        pass

    def run_end(self):
        pass

    def work(self):
        if self.request_message:
            for key, value in self.task_map.items():
                if (
                    len(value) == self.request_message.parallel_num
                    and key not in self.is_merge_map
                ):
                    self._merge(key)
                    break

    def run(self) -> None:
        try:
            self._flag_stop = False
            self.pre_run()
            while True:
                if self._flag_stop:
                    self._flag_stop_ed = True
                    self.run_end()
                    return

                if self._flag_pause:
                    self._event.wait(1)
                    continue

                self.work()
                self._event.wait(0.001)
        except Exception:
            import traceback

            logger.error(traceback.format_exc())

    def _merge(self, token_str: str):
        if not self.is_merge_map.get(token_str):

            messages = self.task_map.get(token_str)

            table = PrettyTable()
            field_names = ["KEY", "VALUE"]
            table.field_names = field_names
            table.add_row(["验证码", token_str])
            table.add_row(["并行数", len(messages)])
            table.add_row(["消息体", "\n".join(messages)])

            if random.random() > ERROR_RATE:
                time.sleep(1)
                _id = str(uuid.uuid4())
                self.task_id[token_str] = _id
                self.is_merge_map[token_str] = True
                table.add_row(["合并ID", _id])
                logger.log(
                    "TS",
                    f"{self.request_message} 模拟并行服务合并流程:\n{table}",
                )
            else:
                self.task_id[token_str] = MergeThreadState.error
                self.is_merge_map[token_str] = True
                table.add_row(["合并ID", MergeThreadState.error])
                logger.error(f"{self.request_message} 模拟并行服务合并流程:\n{table}")


class BaseExperiment:
    def __init__(self, physical_unit: Union[int, str] = None):
        self._label = self.__class__.__name__
        self._physical_unit = physical_unit
        self.exp_options = QDict(
            is_parallel=False,
            run_mode=RunMode.sync_mode.value
        )
        self.run_options = QDict(
            counter=QDict(index=0),
            has_parent=False,
            parent_label=None,
            child_index=0,
            exp_state=ExperimentState.suc
        )

    def __repr__(self):

        return (
            f"{self._label}-{self._physical_unit} | Parent({self.run_options.parent_label})"
            # f"-Index({self.run_options.child_index})"
        )

    @property
    def label(self):
        return self._label

    def set_parent(self, parent_exp, index: int):
        self.run_options.child_index = index
        self.run_options.counter = parent_exp.run_options.counter
        self.run_options.has_parent = True

        if parent_exp.run_options.parent_label:
            pl = f"{parent_exp.run_options.parent_label}-{parent_exp.label}({index})"
        else:
            pl = f"{parent_exp.label}({index})"

        self.run_options.parent_label = pl

    def set_options(self, **kwargs):
        self.exp_options.update(kwargs)
        logger.log("CS", f"{self} 模拟设置实验选项 | (set options)")

    def _check_options(self):
        logger.log("CS", f"{self} 模拟校验实验选项 | (check options)")

    def _analysis(self):
        if random.random() < ERROR_RATE:
            raise AnalysisError(f"{self}, 模拟分析报错！")

        logger.log("CS", f"{self} 模拟分析过程 | (analysis)")
        time.sleep(1)

    async def run_experiment(self):
        pass


class TopExperiment(BaseExperiment):

    def __init__(self, physical_unit: Union[int, str] = None):
        super().__init__(physical_unit)
        self._exp_id = None
        self._token = None
        self.run_options.is_parallel_send = False

    def _set_special_message(self):

        if random.random() < ERROR_RATE:
            raise BuildMessageError(f"{self} 模拟构造实验消息体 Error！")

        msg = f"Protocol({self})"
        logger.log("CS", f"{self} 模拟构建实验消息体 | (build exp message) | {msg}")
        return msg

    def _register(self):
        self._exp_id = str(uuid.uuid4())
        logger.log("CS", f"{self} 模拟实验注册 | (register)")

    def _data_acquisition(self):
        logger.log("CS", f"{self} 模拟采集过程 | (data acq) | ID-{self._exp_id}")
        time.sleep(1)

    async def _async_data_acquisition(self):
        self._data_acquisition()

    async def _async_analysis(self):
        self._analysis()

    def run(self):
        self._check_options()
        self._set_special_message()
        self._register()
        self._data_acquisition()
        self._analysis()

    async def async_run(self):
        self._check_options()
        self._set_special_message()
        self._register()
        await asyncio.create_task(self._async_data_acquisition())
        await asyncio.create_task(self._async_analysis())

    async def parallel_run(self):
        self._check_options()
        self._exp_id = await self.send_to_parallel_server()
        if not self._exp_id == MergeThreadState.error:
            if self.exp_options.run_mode == RunMode.sync_mode.value:
                self._data_acquisition()
                self._analysis()
            else:
                await asyncio.create_task(self._async_data_acquisition())
                await asyncio.create_task(self._async_analysis())
        else:
            raise MergeError("Parallel Merge Error!")

    async def send_to_parallel_server(self):
        message = self._set_special_message()
        self.run_options.is_parallel_send = True
        token_str = f"{ProcessCache.merge_thread.request_message.token}-{self.run_options.counter.index}"
        self.run_options.counter.index += 1
        ProcessCache.merge_thread.task_map[token_str].append(message)
        while not ProcessCache.merge_thread.task_id.get(token_str):
            await asyncio.sleep(0.1)
        return ProcessCache.merge_thread.task_id.get(token_str)

    async def run_experiment(self):
        try:
            if self.exp_options.is_parallel:
                await self.parallel_run()
            elif self.exp_options.run_mode == RunMode.sync_mode:
                self.run()
            else:
                await self.async_run()
        except Exception as e:
            logger.error(f"{self} error: {e}")
            self.run_options.exp_mode = ExperimentState.error
            if self.run_options.is_parallel_send is False and self.exp_options.is_parallel:
                logger.warning(f"{self} 发送至并行服务之前出错, 所以在结束之前发送展位的异常消息给合并线程，否则合并线程达不到并行数无法退出！")
                token_str = f"{ProcessCache.merge_thread.request_message.token}-{self.run_options.counter.index}"
                self.run_options.counter.index += 1
                ProcessCache.merge_thread.task_map[token_str].append(f"Client Error!")


class CompositeExperiment(BaseExperiment):
    _sub_exp_class = TopExperiment

    def __init__(self, physical_unit: Union[int, str] = None):
        super().__init__(physical_unit)
        self._experiments = []
        self.exp_options.run_mode = "sync"
        self.child_exp = self._sub_exp_class(physical_unit)
        self.exp_options.limit_exp_error = True

    def _check_options(self):
        super()._check_options()
        self.child_exp.exp_options.is_parallel = self.exp_options.is_parallel
        if isinstance(self.child_exp, TopExperiment):
            self.child_exp.exp_options.run_mode = self.exp_options.run_mode

    def run(self):
        self._check_options()

    async def sync_run(self):
        pass

    async def async_run(self):
        pass

    async def child_exp_run(self, child_exp: BaseExperiment):
        if self.exp_options.run_mode == RunMode.sync_mode.value:
            await child_exp.run_experiment()
            return child_exp
        else:
            task = child_exp.run_experiment()
            return task

    async def run_experiment(self):
        self.run()

        if self.exp_options.run_mode == RunMode.sync_mode.value:
            await self.sync_run()
        else:
            await self.async_run()

        logger.log("CS", f"{self} 子实验任务执行结束...")
        if self.exp_options.is_parallel and not self.run_options.has_parent:
            ProcessCache.merge_thread.request_message.parallel_num -= 1
            logger.log("CS", f"{self} 并行数目减 1")

        self._analysis()


class ParallelExperiment:
    def __init__(self, experiments: List[Union[TopExperiment, CompositeExperiment]]):
        self._experiments = experiments

    def _check_parallel_task(self):
        self._check_merge_service()
        self._check_parallel_num()
        self._check_parallel_exp_name()

        for exp in self._experiments:
            exp.exp_options.is_parallel = True

    @staticmethod
    def _check_merge_service():
        assert ProcessCache.merge_thread is not None
        assert ProcessCache.merge_thread.is_alive() is True

    def _check_parallel_num(self):
        assert len(self._experiments) > 0

    def _check_parallel_exp_name(self):
        assert len(set([ce.__class__.__name__ for ce in self._experiments])) == 1

    def _get_parallel_request_message(self):
        request_message = ParallelRequestMessage(
            parallel_num=len(self._experiments),
            experiment_name=self._experiments[0].__class__.__name__,
        )
        request_message.token = str(
            hash(
                (
                    request_message.parallel_num,
                    request_message.experiment_name,
                    time.time_ns(),
                )
            )
        )
        return request_message

    def _get_parallel_token(self):
        request_message = self._get_parallel_request_message()
        ProcessCache.merge_thread.request_message = request_message

    async def run(self):
        self._check_parallel_task()
        self._get_parallel_token()

        task_list = []
        for exp in self._experiments:
            task = exp.run_experiment()
            task_list.append(task)
        try:
            await asyncio.gather(*task_list)
            ProcessCache.merge_thread.clear()
        except asyncio.CancelledError:
            logger.error("Async run parallel experiment cancelled.")


async def build_experiment_special_message(experiment: TopExperiment):
    """
    1. 使用资源加速器构建私有消息体
    2. 将私有消息体给到任务合并线程
    3. 等待合并后的 ID 返回
    """
    logger.log("CS", "模拟资源加速器构建私有消息体...")
    await asyncio.sleep(1)
    logger.log("CS", "模拟将私有消息体给到并行合并服务...")
    await asyncio.sleep(1)
    logger.log("CS", "模拟等待合并服务合并完成，返回任务 ID...")
    await asyncio.sleep(1)
    return str(uuid.uuid4())


async def accelerate_analysis(experiment: TopExperiment):
    """
    1. 使用资源加速器加速分析过程
    """
    logger.log("CS", "模拟资源加速器加速分析过程...")
    await asyncio.sleep(1)


def start_parallel_thread_service():
    ProcessCache.merge_thread = MergerThread()
    ProcessCache.merge_thread.start()
    time.sleep(1)
