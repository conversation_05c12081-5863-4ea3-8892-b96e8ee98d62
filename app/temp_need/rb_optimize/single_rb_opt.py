# # -*- coding: utf-8 -*-
#
# # This code is part of pyQCat.
# #
# # Copyright (c) 2017-2027 Origin Quantum Computing. All Right Reserved.
# # Unless required by applicable law or agreed to in writing, software
# # distributed under the License is distributed on an "AS IS" BASIS,
# # WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#
# # __date:         2022/06/29
# # __author:       <PERSON><PERSON><PERSON>
#
# """
# Standard RB Experiment class.
# """
#
# import random
#
# import numpy as np
#
# from pyQCat.analysis import RBAnalysis
# from pyQCat.experiments.top_experiment import TopExperiment
# from pyQCat.gate.notable_gate import (
#     CLIFFORD_GATE_SET,
#     GateBucket,
#     GateCollection,
# )
# from pyQCat.structures import MetaData, Options
# from pyQCat.tools import qarange, time_cal
#
#
# class RBSingleOpt(TopExperiment):
#     """Standard single qubit randomized benchmarking experiment."""
#
#     @classmethod
#     def _default_experiment_options(cls) -> Options:
#         """Default Experiment Options"""
#         options = super()._default_experiment_options()
#
#         interleaved_gates = [None]
#         interleaved_gates.extend(GateCollection.single_gate_infos())
#
#         options.set_validator("times", (1, 50, 0))
#         options.set_validator("depth1", list)
#         options.set_validator("depth2", list)
#         options.set_validator("depth3", list)
#         options.set_validator("depth4", list)
#         options.set_validator("interleaved_gate", interleaved_gates)
#         options.set_validator("gate_split", bool)
#
#         options.times = 30
#         options.depth1 = qarange(2, 10, 2)
#         options.depth2 = qarange(15, 50, 5)
#         options.depth3 = qarange(60, 100, 10)
#         options.depth4 = qarange(120, 200, 20)
#         options.interleaved_gate = None
#         options.gate_split = False
#
#         return options
#
#     @classmethod
#     def _default_analysis_options(cls) -> Options:
#         options = super()._default_analysis_options()
#
#         options.set_validator("quality_bounds", list)
#
#         options.depths = None
#         options.k = None
#         options.rate = None
#         options.data_key = None
#         options.quality_bounds = [0.9, 0.85, 0.77]
#
#         return options
#
#     @classmethod
#     def _default_run_options(cls) -> Options:
#         options = super()._default_run_options()
#         options.gate_bucket = GateBucket()
#         options.standard_clifford_set = CLIFFORD_GATE_SET
#         options.depths = []
#         options.clifford_compose_1q = np.load(r"/pyQCat/experiments/single/clifford_compose_1q.npz")["table"]
#         options.clifford_inverse_1q = np.load(r"/pyQCat/experiments/single/clifford_inverse_1q.npz")["table"]
#         return options
#
#     def _update_instrument(self):
#         self.sweep_readout_trigger_delay(
#             self.qubit.readout_channel, self._pulse_time_list
#         )
#
#     def _check_options(self):
#         """Check Options."""
#         super()._check_options()
#         self.run_options.gate_bucket.bind_single_gates(self.qubit)
#
#         depths = []
#         depth1 = self.experiment_options.depth1
#         depth2 = self.experiment_options.depth2
#         depth3 = self.experiment_options.depth3
#         depth4 = self.experiment_options.depth4
#         for ds in [depth1, depth2, depth3, depth4]:
#             if ds:
#                 depths.extend(ds)
#
#         if self.experiment_options.gate_split is True:
#             rate = 53 / 24
#         else:
#             rate = 1.875
#
#         interleaved_gate = self.experiment_options.interleaved_gate
#         if interleaved_gate is None or interleaved_gate == "None":
#             interleaved_gate = None
#
#         self.set_run_options(depths=depths)
#         self.set_experiment_options(data_type="I_Q", interleaved_gate=interleaved_gate)
#         self.set_analysis_options(
#             depths=depths, k=self.experiment_options.times, rate=rate
#         )
#
#     def _special_run_analysis(self):
#         """Experiment special analysis run logic."""
#         depths = self.run_options.depths
#         x_data = np.asarray(depths).repeat(self.experiment_options.times)
#         self._run_analysis(x_data=x_data, analysis_class=RBAnalysis)
#
#     def run(self):
#         """Initialize a standard randomized benchmarking experiment."""
#         super().run()
#         self._special_run_analysis()
#         self._set_result_path()
#
#     def _metadata(self) -> MetaData:
#         """Set RB experiment metadata."""
#         metadata = super()._metadata()
#         metadata.draw_meta = {"K": self.experiment_options.times}
#         return metadata
#
#     def _set_xy_pulses(self):
#         # clifford_matrix_set = self._get_clifford_matrix_set()
#         quantum_circuits = self._get_quantum_circuits()
#         with open("gate_map.txt", mode='w', encoding="utf-8") as f:
#             f.write("\n".join(quantum_circuits))
#         pulse_list = []
#         for circuits in quantum_circuits:
#             pulse_list.append(
#                 self.run_options.gate_bucket.get_xy_pulse(self.qubit, circuits)
#             )
#         self.play_pulse("XY", self.qubit, pulse_list)
#
#     @time_cal
#     def _get_quantum_circuits(self):
#         """todo"""
#         random_gate_max_value = len(self.run_options.standard_clifford_set) - 1
#         clifford_compose_1q = self.run_options.clifford_compose_1q
#         clifford_inverse_1q = self.run_options.clifford_inverse_1q
#         standard_clifford_set = self.run_options.standard_clifford_set
#         times = self.experiment_options.times
#
#         if self.experiment_options.gate_split:
#             self._split_gate()
#
#         quantum_circuit_list = []
#
#         for circuit_depth in self.run_options.depths:
#             for _ in range(times):
#                 cliff_index = None
#                 quantum_circuit_cache = []
#                 for __ in range(int(circuit_depth)):
#                     random_gate_index = random.randint(0, random_gate_max_value)
#
#                     if cliff_index is None:
#                         cliff_index = random_gate_index
#                     else:
#                         cliff_index = clifford_compose_1q[cliff_index, random_gate_index]
#
#                     random_gate = standard_clifford_set[random_gate_index]
#                     quantum_circuit_cache += random_gate
#
#                     if self.experiment_options.interleaved_gate:
#                         pass
#
#                 quantum_circuit_cache += standard_clifford_set[clifford_inverse_1q[cliff_index]]
#                 quantum_circuit_list.append(quantum_circuit_cache)
#
#         return quantum_circuit_list
#
#     def _split_gate(self):
#         for gate_set in self.run_options.standard_clifford_set:
#             for i, gate in enumerate(gate_set):
#                 if gate == "X":
#                     gate_set.pop(i)
#                     gate_set.insert(i, "X/2")
#                     gate_set.insert(i + 1, "X/2")
#                 if gate == "-X":
#                     gate_set.pop(i)
#                     gate_set.insert(i, "-X/2")
#                     gate_set.insert(i + 1, "-X/2")
#                 if gate == "Y":
#                     gate_set.pop(i)
#                     gate_set.insert(i, "Y/2")
#                     gate_set.insert(i + 1, "Y/2")
#                 if gate == "-Y":
#                     gate_set.pop(i)
#                     gate_set.insert(i, "-Y/2")
#                     gate_set.insert(i + 1, "-Y/2")
#
#     def _set_result_path(self):
#         """Set path to save parameter of Qubit."""
#         for key, result in self.analysis.results.items():
#             if key == "fidelity":
#                 result.extra["path"] = "Qubit.fidelity"
#             elif key == "depth":
#                 result.extra["path"] = "Qubit.depth"
