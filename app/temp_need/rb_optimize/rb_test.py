# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/09/01
# __author:       <PERSON><PERSON><PERSON>
# __corporation:  OriginQuantum

# from pyQCat.experiments import RBSingle
# from pyQCat.tools import qarange
# from scripts.simulator import builder
# from scripts.temp_need.rb_optimize.single_rb_opt import RBSingleOpt
#
#
# def rb_test(qubit_name: str):
#     context = builder.single_qubit_cali_context(qubit_name, use_dcm=True)
#
#     rb_exp = RBSingleOpt.from_experiment_context(context)
#     # rb_exp = RBSingle.from_experiment_context(context)
#     rb_exp.set_experiment_options(
#         depth1=qarange(2, 10, 2),
#         # depth2=qarange(15, 50, 5),
#         # depth3=qarange(60, 100, 10),
#         # depth4=qarange(120, 320, 20),
#         times=50,
#         gate_split=False,
#         interleaved_gate=None,
#         simulator_data_path="../data/RB/",
#         schedule_measure=False,
#         schedule_show_measure=1000,
#         is_dynamic=0
#     )
#
#     rb_exp.run()
#
#
# if __name__ == "__main__":
#     rb_test("q0")
