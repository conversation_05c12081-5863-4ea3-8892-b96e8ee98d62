import itertools
from copy import deepcopy
from typing import List

import numpy as np
import scipy

from pyQCat.gate import GateBucket
from pyQCat.tools import time_cal

gate_bucket = GateBucket()
NUM_CLIFFORD_1Q = 24
NUM_CLIFFORD_2Q = 11520


class Clifford:

    def __init__(self, gate_sequence, qubit_nums):
        self._gate_sequence = gate_sequence
        self._qubit_nums = qubit_nums
        self._matrix = None
        self._initial()

    def _initial(self):

        matrix_temp = np.eye(1 << self._qubit_nums)

        if self._qubit_nums == 1:
            for gate in self._gate_sequence:
                matrix_temp = np.around(
                    np.dot(gate_bucket.get_matrix(gate), matrix_temp), decimals=8
                )
        else:
            gate0, gate1 = self._gate_sequence
            for g0, g1 in zip(gate0, gate1):
                if g0 == g1 == "CZ":
                    matrix = gate_bucket.get_matrix(g0)
                else:
                    matrix = np.kron(
                        gate_bucket.get_matrix(g0),
                        gate_bucket.get_matrix(g1)
                    )
                matrix_temp = np.around(
                    np.dot(matrix, matrix_temp), decimals=8
                )

        self._matrix = matrix_temp

    @property
    def matrix(self):
        return self._matrix


class CliffordUtil:

    CLIFFORD_GATE_SET = [
        ["I"],
        ["X"],
        ["Y"],
        ["Y", "X"],
        ["X/2"],
        ["-X/2"],
        ["Y/2"],
        ["-Y/2"],
        ["-X/2", "Y/2", "X/2"],
        ["-X/2", "-Y/2", "X/2"],
        ["X", "-Y/2"],
        ["X", "Y/2"],
        ["Y", "X/2"],
        ["Y", "-X/2"],
        ["X/2", "Y/2", "X/2"],
        ["-X/2", "Y/2", "-X/2"],
        ["Y/2", "X/2"],
        ["Y/2", "-X/2"],
        ["-Y/2", "X/2"],
        ["-Y/2", "-X/2"],
        ["-X/2", "-Y/2"],
        ["X/2", "-Y/2"],
        ["-X/2", "Y/2"],
        ["X/2", "Y/2"],
    ]

    S1_SET = [["I"], ["Y/2", "X/2"], ["-X/2", "-Y/2"]]

    S1_X2_SET = [["X/2"], ["X/2", "Y/2", "X/2"], ["-Y/2"]]

    S1_Y2_SET = [["Y/2"], ["Y", "X/2"], ["-X/2", "-Y/2", "X/2"]]

    def __init__(self):
        self._cliff_gate_2q = self._clifford_2q()

    def _clifford_2q(self):

        clifford_gate_set = self.CLIFFORD_GATE_SET
        s1_y2_set = self.S1_Y2_SET
        s1_x2_set = self.S1_X2_SET
        s1_set = self.S1_SET

        clifford = []

        # c1_set and c1_set assemble (24 * 24 = 576)
        c1_c1_iter = itertools.product(
            deepcopy(clifford_gate_set), deepcopy(clifford_gate_set)
        )
        c1_c1_gates = [list(t) for t in c1_c1_iter]
        clifford.extend(c1_c1_gates)

        # c1, cz, s1 and c1, cz, y2 assemble (576 * 3 * 3 = 5184) c-not-like class
        s1_y2_iter = itertools.product(deepcopy(s1_set), deepcopy(s1_y2_set))
        s1_y2_gates = [list(t) for t in s1_y2_iter]
        mid_part_c_not = [["CZ"], ["CZ"]]
        c_not = self._gate_assemble(
            deepcopy(c1_c1_gates), deepcopy(mid_part_c_not), deepcopy(s1_y2_gates)
        )
        clifford.extend(c_not)

        # assemble (576 * 3 * 3 = 5184) i-swap-like class
        y2_x2_iter = itertools.product(deepcopy(s1_y2_set), deepcopy(s1_x2_set))
        y2_x2_gates = [list(t) for t in y2_x2_iter]
        mid_part_i_swap = [["CZ", "Y/2", "CZ"], ["CZ", "-X/2", "CZ"]]
        i_swap = self._gate_assemble(
            deepcopy(c1_c1_gates), deepcopy(mid_part_i_swap), deepcopy(y2_x2_gates)
        )
        clifford.extend(i_swap)

        # assemble 576 swap-like classes
        mid_part_swap = [
            ["CZ", "-Y/2", "CZ", "Y/2", "CZ"],
            ["CZ", "Y/2", "CZ", "-Y/2", "CZ", "Y/2"],
        ]
        swap = self._gate_assemble(deepcopy(c1_c1_gates), deepcopy(mid_part_swap))
        clifford.extend(swap)

        # CZ door alignment
        final_clifford = []
        for gates in clifford:
            gates_l, gates_r = self._align_gate(gates)
            final_clifford.append([gates_l, gates_r])

        return final_clifford

    @classmethod
    def clifford_1_qubit(cls, num: int):
        return Clifford(cls.CLIFFORD_GATE_SET[num], 1)

    def clifford_2_qubit(self, num: int):
        return Clifford(self._cliff_gate_2q[num], 2)

    @staticmethod
    def _gate_assemble(head_gate: List, mid_gate: List, tail_gate: List = None):
        """gate assemble

        Assume we have:
            head_gate: [[[X, I], [Y, X]], [[I], [X, Y]]]
            mid_gate: [[CZ], [CZ]]
            tail_gate: [[[X, I], [Y, X]], [[I], [X, Y]]]

        gate assemble result as follow:
            1. [[X, I, CZ, X, I], [Y, X, CZ, Y, X]]
            2. [[X, I, CZ, I], [Y, X, CZ, X, Y]]
            3. [[I, CZ, X, I], [X, Y, CZ, Y, X]]
            4. [[I, CZ, I], [X, Y, CZ, X, Y]]

        Total length is len(head_gate) * len(tail)
        """
        gate_set = [
            [gate[0] + mid_gate[0], gate[1] + mid_gate[1]] for gate in head_gate
        ]
        if tail_gate:
            gate_res = []
            for tail in tail_gate:
                gate_res.extend(
                    [[gate[0] + tail[0], gate[1] + tail[1]] for gate in gate_set]
                )
            return gate_res
        return gate_set

    @staticmethod
    def _align_gate(clifford_gate: List):
        """CZ gate alignment operation

        In the clifford cluster of two qubit RB, the alignment of the first CZ
        gate must be ensured, and the length of the two gate operation lists
        must be equal.  The operation length between the two CZ gates in the
        two-bit Clifford cluster must be equal, so we only need to slave before
        the first CZ gate After the last CZ gate,  and fill I gate in the two
        bit door operation list. Let's say that our set of gates is:

        [X/2, Y/2, CZ, I, X/2, CZ, Y/2, Y/2]
        [CZ, I, I, CZ, Y/2]

        After alignment is:

        [X/2, Y/2, CZ, I, X/2, CZ, Y/2, Y/2]
        [  I,   I, CZ, I,   I, CZ, Y/2,   I]
        """
        gate_list = deepcopy(clifford_gate)
        num_c_zs = gate_list[0].count("CZ")
        if num_c_zs != gate_list[1].count("CZ"):
            raise Exception("dissimilar number of two qubit CZ gates")
        if num_c_zs > 0:
            gap = gate_list[0].index("CZ") - gate_list[1].index("CZ")
            if gap < 0:
                gate_list[0] = ["I" for _ in range(-gap)] + gate_list[0]
            elif gap > 0:
                gate_list[1] = ["I" for _ in range(gap)] + gate_list[1]
        len0 = len(gate_list[0])
        len1 = len(gate_list[1])
        if len0 < len1:
            gate_list[0].extend(["I" for _ in range(len1 - len0)])
        elif len0 > len1:
            gate_list[1].extend(["I" for _ in range(len0 - len1)])
        return gate_list[0], gate_list[1]


def _hash_cliff(cliff):
    """Produce a hashable value that is unique for each different Clifford.  This should only be
    used internally when the classes being hashed are under our control, because classes of this
    type are mutable."""
    return np.packbits(cliff.ma).tobytes()


cu = CliffordUtil()

_CLIFF_1Q = {i: CliffordUtil.clifford_1_qubit(i) for i in range(NUM_CLIFFORD_1Q)}
_TO_INT_1Q = {hash(cliff): i for i, cliff in _CLIFF_1Q.items()}
_CLIFF_2Q = {i: cu.clifford_2_qubit(i) for i in range(NUM_CLIFFORD_2Q)}
_TO_INT_2Q = {hash(cliff): i for i, cliff in _CLIFF_2Q.items()}


def gen_clifford_inverse_1q():
    inv = np.empty(NUM_CLIFFORD_1Q, dtype=int)
    for i, cliff_i in _CLIFF_1Q.items():
        for j, cliff_j in _CLIFF_1Q.items():
            num = abs(np.trace(np.dot(cliff_j.matrix, cliff_i.matrix))) / 2
            if num > 0.99:
                print("-----------", i, j)
                inv[i] = j
    assert all(sorted(inv) == np.arange(0, NUM_CLIFFORD_1Q))
    return inv


def gen_clifford_compose_1q():
    products = np.empty((NUM_CLIFFORD_1Q, NUM_CLIFFORD_1Q), dtype=int)
    for i, cliff_i in _CLIFF_1Q.items():
        for j, cliff_j in _CLIFF_1Q.items():
            matrix = np.dot(cliff_j.matrix, cliff_i.matrix)
            for k, cliff_k in _CLIFF_1Q.items():
                if np.allclose(cliff_k.matrix, matrix) or np.allclose(cliff_k.matrix, -matrix):
                    print("-----------", i, j, k)
                    products[i, j] = k
        assert all(sorted(products[i]) == np.arange(0, NUM_CLIFFORD_1Q))
    return products


@time_cal
def gen_clifford_inverse_2q():
    inv = np.zeros(11520, dtype=int)
    for i, cliff_i in _CLIFF_2Q.items():
        if inv[i] == 0:
            for j, cliff_j in _CLIFF_2Q.items():
                num = abs(np.trace(np.dot(cliff_j.matrix, cliff_i.matrix))) / 4
                if num > 0.99:
                    print("-----------", i, j)
                    inv[i] = j
                    inv[j] = i
                    break
    assert all(sorted(inv) == np.arange(0, NUM_CLIFFORD_2Q))
    return inv


def gen_clifford_compose_2q_gate():
    products = scipy.sparse.lil_matrix((NUM_CLIFFORD_2Q, NUM_CLIFFORD_2Q), dtype=int)
    for i, cliff_i in _CLIFF_2Q.items():
        for j, cliff_j in _CLIFF_2Q.items():
            matrix = np.dot(cliff_j.matrix, cliff_i.matrix)
            for k, cliff_k in _CLIFF_2Q.items():
                if np.allclose(cliff_k.matrix, matrix) or np.allclose(cliff_k.matrix, -matrix):
                    print("-----------", i, j, k)
                    products[i, j] = k
                    break
        assert all(sorted(products[i]) == np.arange(0, NUM_CLIFFORD_2Q))
    return products.tocsr()


if __name__ == "__main__":
    # np.savez_compressed("clifford_inverse_1q.npz", table=gen_clifford_inverse_1q())
    # np.savez_compressed("../../../pyQCat/experiments/single/clifford/clifford_compose_1q.npz", table=gen_clifford_compose_1q())

    np.savez_compressed("clifford_inverse_2q.npz", table=gen_clifford_inverse_2q())
    scipy.sparse.save_npz("clifford_compose_2q_sparse.npz", gen_clifford_compose_2q_gate())

    # for i, cliff in _CLIFF_1Q.items():
    #     print(f"{i}-{cliff._gate_sequence}\n{cliff.matrix}")
