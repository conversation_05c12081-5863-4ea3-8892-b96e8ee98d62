# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/18
# __author:       <PERSON><PERSON><PERSON>

import copy
from collections import defaultdict
from typing import Dict, List

from icecream import ic

from pyQCat.executor.batch.tools import divide_cz_parallel_group
from pyQCat.experiments.batch_experiment import BatchExperiment
from pyQCat.structures import QDict
from pyQCat.tools.utilities import get_bound_ac_spectrum


class BatchCZV2(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.cz_freq_cali_flows = [
            "FixedPointCalibration_cz_qh",
            "FixedPointCalibration_cz_ql",
            "FixedSwapFreqCaliCoupler2_cz",
            "Swap",
        ]
        options.cz_leakage_scan_flows = ["LeakageAmp_use_qc"]
        options.cz_flows = [
            "CPhaseTMSE",
            "SQPhaseTMSE",
            "XEBMultiple_1",
            "NMXEBMultiple",
            "XEBMultiple_2",
        ]
        options.sweep_step = 5  # 工作点扫描步进
        options.filter_count = 5  # 最优工作点提取个数
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.working_points = {}
        options.leakage_point_map = {}
        options.leakage_result = {}
        return options

    def _build_leakage_working_point(self, pairs: List[str]):
        def _add_point(_qh_freq, _ql_freq) -> bool:
            if qh_f_min <= _qh_freq <= qh_f_max and ql_f_min <= _ql_freq <= ql_f_max:
                working_points[pair].append([_qh_freq, _ql_freq])

        sweep_step = self.experiment_options.sweep_step
        working_points = defaultdict(list)
        max_loop = 0

        for pair in pairs:
            pair_obj = self.backend.chip_data.cache_qubit_pair.get(pair)
            qh, ql = pair_obj.qh, pair_obj.ql
            qh_f_max, qh_f_min = get_bound_ac_spectrum(
                self.backend.chip_data.cache_qubit.get(qh)
            )
            ql_f_max, ql_f_min = get_bound_ac_spectrum(
                self.backend.chip_data.cache_qubit.get(ql)
            )
            qh_freq = pair_obj.cz_value(qh, "freq")
            ql_freq = pair_obj.cz_value(ql, "freq")

            if not (qh_freq and ql_freq):
                continue

            _add_point(qh_freq, ql_freq)
            distance = 1
            while True:
                up = _add_point(
                    qh_freq + distance * sweep_step, ql_freq + distance * sweep_step
                )
                down = _add_point(
                    qh_freq - distance * sweep_step, ql_freq - distance * sweep_step
                )
                if up or down:
                    distance += 1
                else:
                    break
            max_loop = max(max_loop, len(working_points[pair]))

        self.run_options.working_points.update(working_points)
        self._save_data_to_json(self.run_options.working_points, "leakage_point")
        return working_points, max_loop

    def _change_leakage_point(self, working_points: Dict, index: int):
        work_pairs = []
        for pair_name, points in working_points.items():
            if index < len(points):
                work_pairs.append(pair_name)
                qh_freq, ql_freq = points[index]
                pair_obj = self.backend.chip_data.cache_qubit_pair.get(pair_name)
                pair_obj.set_cz_value(pair_obj.qh, "freq", qh_freq)
                pair_obj.set_cz_value(pair_obj.ql, "freq", ql_freq)
                self.run_options.leakage_point_map[pair_name] = (qh_freq, ql_freq)
                self.run_options.dir_describe[pair_name] = f"qh-{qh_freq} ql-{ql_freq}"
        return work_pairs

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)
        leakage_result = self.run_options.leakage_result
        leakage_point_map = self.run_options.leakage_point_map

        if "LeakageAmp" in exp.label:
            for unit, result in record.analysis_data.items():
                point = str(leakage_point_map[unit])
                if unit not in leakage_result:
                    leakage_result[unit] = {}
                leakage_result[unit][point] = result.get("result")
        elif "XEBMultiple" in exp.label and "NM" not in exp.label:
            for unit, result in record.analysis_data.items():
                point = str(leakage_point_map[unit])
                pair_obj = self.backend.chip_data.cache_qubit_pair.get(unit)
                leakage_result[unit][point].update(result.get("result"))
                leakage_result[unit][point]["params"] = copy.deepcopy(
                    pair_obj.to_dict()
                )

        return record

    def _record_leakage_result(self):
        self._save_data_to_json(self.run_options.leakage_result, "leakage_result")

    def _sort_leakage_result(self, physical_units: List[str]):
        leakage_result = self.run_options.leakage_result
        for unit in physical_units:
            leakage_result[unit] = dict(
                sorted(
                    leakage_result[unit].items(), key=lambda item: item[1].get("leakage", 1000)
                )
            )

    def _set_leakage_result(self, physical_units: List[str], index: int):
        work_pairs = []
        leakage_result = self.run_options.leakage_result
        for unit in physical_units:
            points = leakage_result.get(unit)
            if len(points) > index:
                work_pairs.append(unit)
                point, result = list(points.items())[index]
                point_value = eval(point)
                pair_obj = self.backend.chip_data.cache_qubit_pair.get(unit)
                pair_obj.set_cz_value(pair_obj.qh, "freq", point_value[0])
                pair_obj.set_cz_value(pair_obj.ql, "freq", point_value[1])
                pair_obj.metadata.std.process.min_leakage_point = QDict(
                    fit=QDict(qc=result.get("qc_fit"), qh=result.get("qh_fit")),
                    max=QDict(qc=result.get("qc_max"), qh=result.get("qh_max")),
                )
                self.run_options.leakage_point_map[unit] = (point_value[0], point_value[1])
                self.run_options.dir_describe[unit] = f"qh-{point_value[0]} ql-{point_value[1]}"
        return work_pairs

    def _run_batch(self):
        cz_freq_cali_flows = self.experiment_options.cz_freq_cali_flows
        cz_leakage_scan_flows = self.experiment_options.cz_leakage_scan_flows
        cz_flows = self.experiment_options.cz_flows
        filter_count = self.experiment_options.filter_count

        group_list = divide_cz_parallel_group(
            self.experiment_options.physical_units,
            self.context_manager.chip_data,
        )
        for _, group in group_list.items():
            if cz_freq_cali_flows:
                pass_pair = self._run_flow(cz_freq_cali_flows, group)
            else:
                pass_pair = group

            if not pass_pair:
                continue

            working_points, max_loop = self._build_leakage_working_point(pass_pair)
            for i in range(max_loop):
                work_pairs = self._change_leakage_point(working_points, i)

                if not work_pairs:
                    continue
                self._run_flow(cz_leakage_scan_flows, work_pairs)
                self._record_leakage_result()

            self._sort_leakage_result(group)

            for j in range(filter_count):
                work_units = self._set_leakage_result(group, j)
                if not work_units:
                    continue
                self._run_flow(cz_flows, work_units)
                self._record_leakage_result()


if __name__ == "__main__":
    from app.config import init_backend

    backend = init_backend()
    batch = BatchCZV2(backend)
    batch.set_experiment_options(
        param_path="app/batch_test/json_data/cz.json",
        physical_units=["q25q31"],
        sweep_step=5,  # 工作点扫描步进
        filter_count=5,  # 最优工作点提取个数
        # use_simulator=True,
        # simulator_pass_rate=0.95,
        # cz_freq_cali_flows=[],
    )
    batch.run()
