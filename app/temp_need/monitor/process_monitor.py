# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/18
# __author:       <PERSON><PERSON><PERSON>

"""
#####################################################
说明: 此脚本用于检查当前账户中的 Python 进程状态

运行此 py 会打印出当前系统中存活的所有 python 进程
可通过进程工作路径大致判断哪些进程可以 kill
需要 kill 的进程调用 kill_process_by_pid 接口杀死即可
#####################################################
"""

import subprocess

import psutil

all_processes = []
for proc in psutil.process_iter(['pid', 'name']):
    if "python" in proc.info['name']:
        all_processes.append(proc)


def kill_process_by_pid(pid):
    pid = str(pid)
    result = subprocess.run(["TASKKILL", "/F", "/PID", pid], stdout=subprocess.PIPE)
    if result.returncode == 0:
        print(f"kill process success, it's id:{pid!r}")
    else:
        print(f"kill process fail, it's id:{pid!r}")


print(f"Python 进程一共有 {len(all_processes)} | 进程信息如下：")
for process in all_processes:
    try:
        print(
            f"PID: {process.pid}, Name: {process.info['name']}, "
            f"CPU占用 {process.cpu_percent()}, "
            f"内存占用 {process.memory_info().rss / (1024 * 1024) } MB,"
            f"用户名 {process.username()},"
            f"工作路径 {process.cwd()},"
        )
        if process.cwd() not in [
            r"E:\code\dp\pyqcat-visage-0.4.8\pyqcat-visage",
        ]:
            # kill_process_by_pid(process.pid)
            pass
    except Exception:
        pass
