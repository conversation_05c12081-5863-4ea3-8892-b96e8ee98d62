import time
from pymongo import MongoClient


def monitor_connections(interval=10):
    """监控MongoDB的连接数，每隔interval秒执行一次"""

    # MongoDB连接字符串
    client_local = MongoClient(f'*************************************************/')
    client_y4 = MongoClient(f'*************************************************/')

    while True:
        # 获取服务器状态
        local_server_status = client_local.admin.command('serverStatus')
        # 提取当前连接数
        local_current_connections = local_server_status['connections']['current']

        # 获取服务器状态
        y4_server_status = client_y4.admin.command('serverStatus')
        # 提取当前连接数
        y4_current_connections = y4_server_status['connections']['current']
        # y4_current_connections = 0

        print(f"local 连接数： {local_current_connections} | y4 连接数： {y4_current_connections} ")

        time.sleep(interval)


if __name__ == '__main__':
    monitor_connections()
