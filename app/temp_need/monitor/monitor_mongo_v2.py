import time

from pymongo import MongoClient


def monitor_connections(ip="127.0.0.1", port=27017, interval=2):
    """监控MongoDB的连接数，每隔interval秒执行一次"""

    # MongoDB连接字符串
    client = MongoClient(f'mongodb://bylz:fjsaoJOIjiojj28hjj@{ip}:{port}/')

    while True:
        # 获取服务器状态
        server_status = client.admin.command('serverStatus')
        # 提取当前连接数
        current_connections = server_status['connections']['current']

        print(f"Mongo 连接数： {current_connections}")

        time.sleep(interval)


if __name__ == '__main__':
    monitor_connections()
