# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/10/24
# __author:       <PERSON><PERSON><PERSON>

import os
import re
import time
from datetime import datetime
from typing import List

import matplotlib.pyplot as plt
import psutil


def process_memory_monitor(process_id_list=None):
    process_list: List = process_id_list or [os.getpid()]
    print("process ", process_list)

    # 定义日志文件名格式，包含日期
    filename = f"memory_usage_{' '.join([str(p) for p in process_list])}.log"

    # 循环获取内存使用情况
    try:
        while True:
            # 获取当前时间

            for pid in process_list:
                # 获取内存信息
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                process = psutil.Process(pid)
                mem_info = process.memory_info()
                mem_percent = process.memory_percent()

                # 构建要写入的内容
                message = (
                    f"{current_time} | {pid} | RSS(物理内存): {mem_info.rss / (1024**2)} Mb, "
                    f"VMS: {mem_info.vms / (1024**2)} Mb, MEM: {mem_percent} %\n"
                )
                print(message)

                # 将内存使用情况追加写入到文件，指定编码为utf-8
                with open(filename, "a", encoding="utf-8") as file:
                    file.write(message)

            # 等待2秒
            time.sleep(2)
    except KeyboardInterrupt:
        print("Caught KeyboardInterrupt, exiting")


def process_memory_monitor_in_thread(process_id_list=None):
    import threading

    thread = threading.Thread(target=process_memory_monitor, args=(process_id_list,), daemon=True)
    thread.start()
    return thread


def viewer(log_path: str):
    # 定义正则表达式模式
    pattern = r"RSS\(物理内存\): ([\d\.]+) Mb, VMS: ([\d\.]+) Mb, MEM: ([\d\.]+) %"

    # 初始化空列表来存储数据
    times = []
    rss_values = []
    vms_values = []
    mem_values = []

    # 读取日志文件并逐行提取数据
    count = 0
    with open(log_path, 'r', encoding="utf-8") as file:
        for line in file:
            match = re.search(pattern, line)
            if match:
                rss, vms, mem = match.groups()
                times.append(count)
                rss_values.append(float(rss))
                vms_values.append(float(vms))
                mem_values.append(float(mem))
                count += 2

    # 绘制波动图
    plt.figure(figsize=(15, 5))
    plt.suptitle(os.path.basename(log_path))

    plt.subplot(1, 3, 1)
    plt.plot(times, rss_values, marker='o')
    plt.title('RSS')
    plt.xlabel("Time")
    plt.ylabel('RSS (Mb)')

    plt.subplot(1, 3, 2)
    plt.plot(times, vms_values, marker='o')
    plt.title('VMS')
    plt.xlabel('Time')
    plt.ylabel('VMS (Mb)')

    plt.subplot(1, 3, 3)
    plt.plot(times, mem_values, marker='o')
    plt.title('MEM')
    plt.xlabel('Time')
    plt.ylabel('MEM (%)')

    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    # process_memory_monitor()
    viewer(r"D:\project\SupQAutomation\code\project\app\protobuf\pyqcat-apps\memory_usage_13712.log")
