# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/03/27
# __author:       <PERSON>, <PERSON><PERSON><PERSON>

from copy import deepcopy
from typing import Union, List

import numpy as np

from .swap_once import (
    validate_two_qubit_exp_read_options,
    validate_qubit_pair_cz_std,
    set_measure_pulses,
)
from ..top_experiment import TopExperiment
from ...analysis import CZAssistAnalysis, CZPhaseAnalysis, AnalysisResult
from ...analysis.algorithms import phase_tomograph, change_phase
from ...errors import ExperimentOptionsError
from ...gate import Rphi_gate
from ...log import pyqlog
from ...parameters import options_wrapper, get_physical_bit, freq_list_to_amp
from ...pulse.pulse_function import (
    half_pi_pulse,
    pi_pulse,
    zero_pulse,
    stimulate_state_pulse,
)
from ...pulse.pulse_lib import Constant
from ...pulse_adjust import params_to_pulse
from ...structures import Options, MetaData
from ...tools import cz_flow_options_adapter


class CZAssist(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("cz_num", int)
        options.set_validator("add_cz", bool)
        options.set_validator("control_gate", ["I", "X"])
        options.set_validator("phase_list", list)
        options.set_validator("ramsey_bit", str)

        options.cz_num = 1
        options.add_cz = True
        options.control_gate = "I"
        options.phase_list = np.linspace(0, 2 * np.pi, 15).tolist()
        options.ramsey_bit = None
        options.readout_type = None

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.ql = None
        options.qh = None
        options.control_bit = None
        options.gate_params = {}
        options.buffer = None

        options.qc = None
        options.width = None
        options.env_bits = None
        options.control_bit = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.set_validator("quality_bounds", list)

        options.quality_bounds = [0.98, 0.95, 0.85]
        options.data_key = None
        options.figsize = (12, 12)
        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "control_gate": self.experiment_options.control_gate,
            "ramsey_bit": self.experiment_options.ramsey_bit,
            "add_cz": self.experiment_options.add_cz,
        }
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        cz_flow_options_adapter(self)
        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)

        # check ramsey bit and control bit
        ramsey_bit = self.experiment_options.ramsey_bit
        control_gate = self.experiment_options.control_gate
        readout_type = self.experiment_options.readout_type
        data_key = None
        control_bit = None

        if ramsey_bit is None:
            ramsey_bit = self.qubit_pair.ql
            control_bit = self.qubit_pair.qh
            data_key = ["P01"]
        elif ramsey_bit == self.qubit_pair.qh:
            control_bit = self.qubit_pair.ql
            data_key = ["P10"]
        elif ramsey_bit == self.qubit_pair.ql:
            control_bit = self.qubit_pair.qh
            data_key = ["P01"]

        if ramsey_bit not in [q.name for q in self.qubits] or not control_bit:
            raise ExperimentOptionsError(
                self.label,
                key="ramsey_bit",
                value=ramsey_bit,
                msg=f"ramsey bit {ramsey_bit} not in exp qubits!",
            )
        self.set_experiment_options(ramsey_bit=ramsey_bit)
        self.set_run_options(control_bit=control_bit)

        if not readout_type.startswith("union"):
            data_key = ["P1"]
        elif control_gate == "X":
            data_key = ["P11"]

        self.set_analysis_options(data_key=data_key)

        pyqlog.debug(
            f"ramsey to {ramsey_bit}, control_gate to {control_bit}, control_gate = {control_gate}"
        )

    def _set_xy_pulses(self):
        """Set experiment XY pulses."""
        cz_num = self.experiment_options.cz_num
        control_gate = self.experiment_options.control_gate
        cz_width = self.run_options.width
        phase_list = self.experiment_options.phase_list
        ramsey_bit = self.experiment_options.ramsey_bit
        control_bit = self.run_options.control_bit

        for qubit in self.qubits:
            if qubit.name.startswith("c"):
                continue

            xy_pulse_list = []
            for phase in phase_list:
                pulse_2 = Constant(cz_width, 0, name="XY")
                if qubit.name == ramsey_bit:
                    pulse_1 = half_pi_pulse(qubit)
                    pulse_3 = half_pi_pulse(qubit)
                    pulse_3.phase = phase
                elif qubit.name == control_bit:
                    pulse_1 = stimulate_state_pulse(control_gate, qubit)
                    pulse_3 = stimulate_state_pulse("I", qubit)
                else:
                    pulse_1 = stimulate_state_pulse("I", qubit)
                    pulse_3 = stimulate_state_pulse("I", qubit)

                xy_pulse = pulse_1() + pulse_2() * cz_num + pulse_3()
                xy_pulse_list.append(xy_pulse)

            self.play_pulse("XY", qubit, xy_pulse_list)

    def _set_z_pulses(self):
        cz_num = self.experiment_options.cz_num
        cz_width = self.run_options.width
        phase_list = self.experiment_options.phase_list
        add_cz = self.experiment_options.add_cz

        ql = self.run_options.ql
        qh = self.run_options.qh
        parking_qubits = self.run_options.env_bits
        gate_params = self.run_options.gate_params

        length = len(phase_list)
        qubit_list = [ql, qh]
        qubit_list.extend(parking_qubits)
        drag_time = qh.XYwave.time + qh.XYwave.offset * 2

        for qubit in qubit_list:
            if qubit.name.startswith("q"):
                pulse_1 = zero_pulse(qubit, name="Z")
                pulse_3 = zero_pulse(qubit, name="Z")
            else:
                pulse_1 = Constant(drag_time, 0)
                pulse_3 = Constant(drag_time, 0)

            s_gate_params = deepcopy(gate_params.get(qubit.name))
            s_gate_params.update({"width": cz_width})
            if add_cz is False:
                s_gate_params.update({"amp": 0})

            pulse_2 = params_to_pulse(**s_gate_params)

            z_pulse = pulse_1() + pulse_2() * cz_num + pulse_3()
            self.play_pulse("Z", qubit, [deepcopy(z_pulse) for _ in range(length)])

    def _set_measure_pulses(self):
        """Set readout pulse."""
        set_measure_pulses(self)

    def _special_run_analysis(self):
        """Experiment special analysis run logic."""
        self._run_analysis(
            x_data=self.experiment_options.phase_list, analysis_class=CZAssistAnalysis
        )

    def run(self):
        super().run()
        self._special_run_analysis()


@options_wrapper
class CPhaseTMSE(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("mode", ["TM", "SE-TM"])
        options.set_validator("ramsey_bit", str)
        options.set_validator("phase_mode", ["control", "single"])
        options.set_validator("scan_name", str)
        options.set_validator("adapter_name", str)
        options.set_validator("z_amp_list", list)
        options.set_validator("adapter_amp_list", list)
        options.set_validator("k", int)
        options.set_validator("cz_num", int)
        options.set_validator("leakage_mode", ["fit", "max"])
        options.set_validator("sweep_detune", bool)
        options.set_validator("detune_list", list)

        options.mode = "TM"
        options.phase_mode = "control"
        options.ramsey_bit = None
        options.scan_name = None
        options.adapter_name = "qc"
        options.z_amp_list = None
        options.adapter_amp_list = None
        options.k = 1
        options.cz_num = 1
        options.readout_type = None
        options.leakage_mode = "fit"
        options.scope_detune = False
        options.sweep_detune = False
        options.detune_list = None

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()

        options.data_key = ["delta_phase"]
        options.adapter_amp_list = None
        options.point = 21

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.ramsey_qubit = None
        options.drag_qubit = None
        options.data_acq = None

        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = None
        options.env_bits = []

        options.use_detune = False
        options.detune_point = False

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "mode": self.experiment_options.mode,
            "phase_mode": self.experiment_options.phase_mode,
            "k": self.experiment_options.k,
            "cz_num": self.experiment_options.cz_num,
        }
        metadata.process_meta = {
            "scan_name": self.experiment_options.scan_name,
            "x_data": self.experiment_options.detune_list
        }
        return metadata

    def _check_options(self):
        super()._check_options()
        cz_flow_options_adapter(self)
        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)
        eop = self.experiment_options
        rop = self.run_options

        if eop.phase_mode == "control":

            if self.qubit_pair.metadata.std.process.min_leakage_point.fit.detune:
                eop.leakage_mode = "fit"
            elif self.qubit_pair.metadata.std.process.min_leakage_point.max.detune:
                eop.leakage_mode = "max"

            leakage_point = self.qubit_pair.metadata.std.process.min_leakage_point.get(
                eop.leakage_mode
            )
            is_retry = False
            if eop.sweep_detune:
                if eop.detune_list:
                    self.run_options.x_data = eop.detune_list
                    amp = rop.gate_params.get(eop.adapter_name).get("amp")
                    amp_list = [amp for _ in range(len(eop.detune_list))]
                    self.set_experiment_options(z_amp_list=eop.detune_list)
                    self.run_options.scan_map.update(
                        {
                            eop.adapter_name: {
                                "amp": amp_list
                            }
                        }
                    )
            else:

                if not eop.z_amp_list and not eop.freq_list:
                    if leakage_point.detune:
                        self.run_options.x_data = leakage_point.detune
                        self.experiment_options.scan_name = None
                        freq_map = self.qubit_pair.detune_prepare(
                            self.qubits, goal_detune=leakage_point.detune
                        )
                        self.run_options.use_detune = True
                        self.run_options.detune_point = leakage_point.detune
                        for unit, freq_list in freq_map.items():
                            physical_bit, branch = get_physical_bit(self, unit)
                            amp_list = freq_list_to_amp(freq_list, physical_bit, branch)
                            self.run_options.scan_map.update(
                                {
                                    unit: {
                                        "freq": freq_list,
                                        "amp": amp_list,
                                    }
                                }
                            )
                    else:
                        al = (
                            leakage_point.qh
                            if eop.scan_name.startswith("q")
                            else leakage_point.qc
                        )
                        if al[0] > 1:
                            eop.freq_list = al
                        else:
                            eop.z_amp_list = al
                        is_retry = True

                if not eop.adapter_amp_list and not eop.adapter_freq_list:
                    bl = (
                        leakage_point.qh
                        if eop.adapter_name.startswith("q")
                        else leakage_point.qc
                    )
                    if bl[0] > 1:
                        eop.adapter_freq_list = bl
                        is_retry = True
                    else:
                        eop.adapter_amp_list = bl
                        self.run_options.scan_map.update(
                            {
                                eop.adapter_name: {
                                    "amp": bl,
                                }
                            }
                        )

            if is_retry:
                self._check_options()
                return

        eop = self.experiment_options
        rop = self.run_options

        if eop.ramsey_bit == rop.qh.name:
            ramsey_qubit = rop.qh
            drag_qubit = rop.ql
        elif eop.ramsey_bit == rop.ql.name:
            ramsey_qubit = rop.ql
            drag_qubit = rop.qh
        else:
            raise ExperimentOptionsError(
                self.label,
                key="ramsey_bit",
                value=eop.ramsey_bit,
                msg=f"No find ramsey bit {eop.ramsey_bit}",
            )

        data_acq = None
        if self.experiment_options.readout_type.startswith("union"):
            if ramsey_qubit == rop.qh:
                data_acq = [["P00", "P01"], ["P10", "P11"]]
            elif ramsey_qubit == rop.ql:
                data_acq = [["P00", "P10"], ["P01", "P11"]]

        self.set_run_options(
            ramsey_qubit=ramsey_qubit,
            drag_qubit=drag_qubit,
            data_acq=data_acq,
        )

        if eop.adapter_amp_list:
            self.analysis_options.adapter_amp_list = eop.adapter_amp_list

        self.set_analysis_options(result_name=self.qubit_pair.name)

    def _set_xy_pulses(self):
        if self.experiment_options.mode == "TM":
            self._set_tm_xy_pulse()
        else:
            self._set_se_tm_xy_pulse()

    def _set_z_pulses(self):
        if self.experiment_options.mode == "TM":
            self._set_tm_z_pulse()
        else:
            self._set_se_tm_z_pulse()

    def _set_tm_xy_pulse(self):
        eop = self.experiment_options
        rop = self.run_options

        zero_cz = Constant(rop.width, 0, name="XY")() * eop.cz_num

        for qubit in self.qubits:
            if qubit.name.startswith("c"):
                continue

            if qubit == rop.ramsey_qubit:
                x2 = half_pi_pulse(qubit)()
                y2 = Rphi_gate(phase=np.pi / 2).to_pulse(qubit)()
                xy_pulse_x2 = deepcopy(x2) + zero_cz + x2
                xy_pulse_y2 = deepcopy(x2) + zero_cz + y2
                xy_pulse_list = [
                    deepcopy(xy_pulse_x2),
                    deepcopy(xy_pulse_y2),
                    deepcopy(xy_pulse_x2),
                    deepcopy(xy_pulse_y2),
                ]
            elif qubit == rop.drag_qubit:
                x = pi_pulse(qubit)()
                zero = zero_pulse(qubit)()
                xy_pulse1 = deepcopy(zero) + zero_cz + zero
                xy_pulse2 = x + zero_cz + zero
                xy_pulse_list = [
                    deepcopy(xy_pulse1),
                    deepcopy(xy_pulse1),
                    deepcopy(xy_pulse2),
                    deepcopy(xy_pulse2),
                ]
            else:
                zero = zero_pulse(qubit)()
                xy_pulse = deepcopy(zero) + zero_cz + zero
                xy_pulse_list = [deepcopy(xy_pulse) for _ in range(4)]

            xy_pulses = []
            if self.experiment_options.phase_mode == "control":
                for _ in range(len(eop.z_amp_list)):
                    xy_pulses.extend(deepcopy(xy_pulse_list))
            else:
                if qubit == rop.drag_qubit:
                    x1 = xy_pulse_list[0]
                    xy_pulse_list = [deepcopy(x1) for _ in range(4)]
                xy_pulses = deepcopy(xy_pulse_list)

            pulse_set = []
            for _ in range(eop.k):
                pulse_set.extend(xy_pulses)

            self.play_pulse("XY", qubit, pulse_set)

    def _set_tm_z_pulse(self):
        eop = self.experiment_options
        rop = self.run_options

        zero_cz = Constant(rop.width, 0)() * eop.cz_num
        zero_x = zero_pulse(rop.ramsey_qubit, name="Z")()

        for qubit in rop.env_bis:
            if eop.phase_mode == "single":
                s_ps = rop.gate_params.get(qubit.name)
                s_ps["time"] = rop.width
                cz_pulse = params_to_pulse(**s_ps)() * eop.cz_num
                z_pulse1 = deepcopy(zero_x) + zero_cz + zero_x
                z_pulse2 = deepcopy(zero_x) + cz_pulse + zero_x
                z_pulses = [
                    deepcopy(z_pulse1),
                    deepcopy(z_pulse1),
                    deepcopy(z_pulse2),
                    deepcopy(z_pulse2),
                ]
            else:
                z_pulses = []
                if qubit.name == eop.scan_name:
                    amp_list = eop.z_amp_list
                elif qubit.name == eop.adapter_name:
                    amp = rop.gate_params.get(qubit.name).get("amp")
                    amp_list = [amp for _ in range(len(eop.z_amp_list))]
                    amp_list = eop.adapter_amp_list or amp_list
                else:
                    amp = rop.gate_params.get(qubit.name).get("amp")
                    amp_list = [amp for _ in range(len(eop.z_amp_list))]

                for amp in amp_list:
                    s_ps = rop.gate_params.get(qubit.name)
                    s_ps["amp"] = amp
                    s_ps["time"] = rop.width
                    cz_pulse = params_to_pulse(**s_ps)() * eop.cz_num
                    z_pulse = deepcopy(zero_x) + cz_pulse + zero_x
                    for _ in range(4):
                        z_pulses.append(deepcopy(z_pulse))

            pulse_set = []
            for _ in range(eop.k):
                pulse_set.extend(z_pulses)

            self.play_pulse("Z", qubit, pulse_set)

    def _set_se_tm_xy_pulse(self):
        eop = self.experiment_options
        rop = self.run_options

        zero_cz = Constant(rop.width, 0, name="XY")() * eop.cz_num

        n = (
            len(rop.x_data)
            if self.experiment_options.phase_mode == "control"
            else 1
        )

        for qubit in self.qubits:
            if qubit.name.startswith("c"):
                continue

            if qubit == rop.ramsey_qubit:
                x2 = half_pi_pulse(qubit)()
                x = pi_pulse(qubit)()
                y2 = Rphi_gate(phase=np.pi / 2).to_pulse(qubit)()
                xy_pulse1 = deepcopy(x2) + zero_cz + x + zero_cz + x2
                xy_pulse2 = deepcopy(x2) + zero_cz + x + zero_cz + y2
                xy_pulse_once = [xy_pulse1, xy_pulse2]
            elif qubit == rop.drag_qubit:
                x = pi_pulse(qubit)()
                zero = zero_pulse(qubit)()
                if eop.phase_mode == "control":
                    xy_pulse = deepcopy(zero) + zero_cz + x + zero_cz + zero
                else:
                    xy_pulse = deepcopy(zero) + zero_cz + zero + zero_cz + zero
                xy_pulse_once = [deepcopy(xy_pulse), deepcopy(xy_pulse)]
            else:
                zero = zero_pulse(qubit)()
                xy_pulse = deepcopy(zero) + zero_cz + zero + zero_cz + zero
                xy_pulse_once = [deepcopy(xy_pulse), deepcopy(xy_pulse)]

            xy_pulses = []
            for _ in range(n):
                xy_pulses.extend(deepcopy(xy_pulse_once))

            pulse_set = []
            for _ in range(eop.k):
                pulse_set.extend(deepcopy(xy_pulses))

            self.play_pulse("XY", qubit, pulse_set)

    def _set_se_tm_z_pulse(self):
        eop = self.experiment_options
        rop = self.run_options

        zero_cz = Constant(rop.width, 0)() * eop.cz_num
        k = self.experiment_options.k
        bit_list = []
        bit_list.extend(self.qubits)
        bit_list.extend(self.couplers)
        zero_x = zero_pulse(self.run_options.ramsey_qubit, name="Z")()

        for qubit in bit_list:
            gate_param = rop.gate_params.get(qubit.name)
            gate_param["time"] = rop.width
            if eop.phase_mode == "single":
                cz_pulse = (
                    params_to_pulse(**gate_param)()
                    * eop.cz_num
                )
                z_pulse = deepcopy(zero_x) + zero_cz + zero_x + cz_pulse + zero_x
                z_pulses = [deepcopy(z_pulse), deepcopy(z_pulse)]
            else:
                z_pulses = []

                # if qubit.name == eop.scan_name:
                #     amp_list = eop.z_amp_list
                # elif qubit.name == eop.adapter_name:
                #     amp_list = eop.adapter_amp_list
                # else:
                #     amp = rop.gate_params.get(qubit.name).get("amp")
                #     amp_list = [amp for _ in range(len(eop.z_amp_list))]

                for i in range(len(self.run_options.x_data)):
                    if eop.sweep_detune:
                        scan_name = eop.scan_name
                        if scan_name == qubit.name:
                            gate_param["detune2"] = self.run_options.x_data[i]
                    if qubit.name in rop.scan_map:
                        amp = rop.scan_map.get(qubit.name).get("amp")[i]
                        gate_param["amp"] = amp
                    gate_param["freq"] = 0
                    cz_pulse = params_to_pulse(**gate_param)() * eop.cz_num
                    z_pulse = deepcopy(zero_x) + cz_pulse + zero_x + cz_pulse + zero_x
                    z_pulses.extend([deepcopy(z_pulse), deepcopy(z_pulse)])

            pulse_set = []
            for _ in range(k):
                pulse_set.extend(deepcopy(z_pulses))

            self.play_pulse("Z", qubit, pulse_set)

    def _set_measure_pulses(self):
        set_measure_pulses(self)

    def _data_collection(self, x_data: Union[List, np.ndarray] = None, count: int = 0):
        super()._data_collection(x_data, count)
        k = self.experiment_options.k
        metadata = self.experiment_data.metadata
        mode = metadata.draw_meta.get("mode")
        data_acq = self.run_options.data_acq

        if data_acq:
            p0 = self.experiment_data.y_data.get(data_acq[0][0])
            p1 = self.experiment_data.y_data.get(data_acq[0][1])
            p2 = self.experiment_data.y_data.get(data_acq[1][0])
            p3 = self.experiment_data.y_data.get(data_acq[1][1])
            p0 = (np.array(p0) + np.array(p1)).tolist()
            p1 = (np.array(p2) + np.array(p3)).tolist()
        else:
            p0 = self.experiment_data.y_data.get("P0")
            p1 = self.experiment_data.y_data.get("P1")

        if mode == "TM":
            x_data = self.experiment_data.x_data
            new_x_data, phase_list = [], []
            for j in range(len(p0)):
                if j % 4 == 0:
                    *_, phase_i = phase_tomograph(p0[j], p1[j], p0[j + 1], p1[j + 1])
                    *_, phase_x = phase_tomograph(
                        p0[j + 2], p1[j + 2], p0[j + 3], p1[j + 3]
                    )
                    new_x_data.append(x_data[j])
                    phase_list.append(change_phase(phase_x - phase_i))
            if k > 1:
                x = new_x_data[: len(new_x_data) // k]
                new_x_data = x
                sl = len(x)
                mean_phase = []
                for i, amp in enumerate(x):
                    tp = []
                    for j in range(k):
                        tp.append(phase_list[i + j * sl])
                    mean_phase.append(np.mean(np.array(tp)))
                phase_list = mean_phase
            self.experiment_data._x_data = new_x_data
            self.experiment_data._y_data = {
                "delta_phase": np.unwrap(np.array(phase_list))
            }
        else:
            all_phase_data = []
            x_data = self.experiment_data.x_data
            new_x_data, phase_list = [], []
            for j in range(len(p0)):
                if j % 2 == 0:
                    *_, phase = phase_tomograph(p0[j], p1[j], p0[j + 1], p1[j + 1])
                    new_x_data.append(x_data[j])
                    phase_list.append(change_phase(phase))
            if k > 1:
                x = new_x_data[: len(new_x_data) // k]
                new_x_data = x
                sl = len(x)
                mean_phase = []
                for i, amp in enumerate(x):
                    tp = []
                    for j in range(k):
                        tp.append(phase_list[i + j * sl])
                    tp = phase_format(np.array(tp))
                    all_phase_data.append(tp.tolist())
                    mean_phase.append(np.mean(tp))
                phase_list = phase_format(mean_phase)
            self.experiment_data._x_data = new_x_data
            self.experiment_data._y_data = {"delta_phase": phase_list}
            if all_phase_data:
                self.experiment_data.metadata.process_meta["all_phase_data"] = np.array(
                    all_phase_data
                )

        y_data = self.experiment_data.y_data
        self._save_data(y_data, "Amp-Phase", False, "%.6f")

    def _special_run_analysis(self):
        """Experiment special analysis run logic."""
        z_amp_list = self.experiment_options.z_amp_list or self.run_options.x_data
        mode = self.experiment_options.mode
        phase_mode = self.experiment_options.phase_mode
        k = self.experiment_options.k

        if phase_mode == "control":
            if mode == "TM":
                x_data = []
                for amp in z_amp_list:
                    x_data.extend([amp for _ in range(4)])
            else:
                x_data = []
                for amp in z_amp_list:
                    x_data.extend([amp for _ in range(2)])
        else:
            if mode == "TM":
                x_data = [1, 1, 2, 2]
            else:
                x_data = [1, 1]

        if k > 1:
            new_x_data = []
            for _ in range(k):
                new_x_data.extend(deepcopy(x_data))
            x_data = new_x_data

        self._run_analysis(x_data=x_data, analysis_class=CZPhaseAnalysis)

    def run(self):
        super().run()
        self._special_run_analysis()
        self._set_result_path()

    def _set_result_path(self):
        """Set path to save parameter of Qubit or Coupler."""
        eop = self.experiment_options
        if eop.phase_mode == "control":
            for key in list(self.analysis.results.keys()):
                result = self.analysis.results.get(key)
                if result.value:
                    if key == "ac_scan":
                        if self.run_options.use_detune:
                            goal_detune = result.value
                            scope = {
                                "l": abs(self.run_options.detune_point[0]),
                                "r": abs(self.run_options.detune_point[-1]),
                                "p": 30,
                            }
                            freq_map = self.qubit_pair.detune_prepare(
                                self.qubits, goal_detune=goal_detune, **scope
                            )
                            for unit, params in freq_map.items():
                                self.analysis.results[unit] = AnalysisResult(
                                    name=unit,
                                    value=round(params[0], 3),
                                    extra={
                                        "name": self.qubit_pair.name,
                                        "path": f"QubitPair.metadata.std.cz.params.{unit}.freq",
                                    },
                                )
                        else:
                            des = "amp"
                            if result.value > 1:
                                result.value = round(result.value, 3)
                                des = "freq"
                            result.extra[
                                "path"
                            ] = f"QubitPair.metadata.std.cz.params.{eop.scan_name}.{des}"
                    elif key == "ac_adapter":
                        des = "amp"
                        if result.value > 1:
                            result.value = round(result.value, 3)
                            des = "freq"
                        result.extra[
                            "path"
                        ] = f"QubitPair.metadata.std.cz.params.{eop.adapter_name}.{des}"
        else:
            for key, result in self.analysis.results.items():
                if key == "phase":
                    result.extra[
                        "path"
                    ] = f"QubitPair.metadata.std.cz.params.{eop.ramsey_bit}.phase"


def phase_format(phase_list):
    index = 0
    while True:
        diff_phase = np.diff(phase_list)
        abnormal_index = np.argwhere(np.abs(diff_phase) > np.pi)

        if len(abnormal_index) == 0:
            break

        for ai in abnormal_index:
            idx = ai[0]
            if diff_phase[idx] > 0:
                phase_list[idx + 1] -= 2 * np.pi
            else:
                phase_list[idx + 1] += 2 * np.pi
        index += 1
        if index > 10000:
            break
    return phase_list


if __name__ == "__main__":
    print(phase_format(np.array([6.23092524, 0.12511])))
