# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/08/07
# __author:       <PERSON><PERSON><PERSON>

"""
实验需求: https://document.qpanda.cn/presentation/0l3NVmGayLFeWd3R
说明：验证实验请务必将 pyQCat/experiments/single/cz_assist.py 内容替换为 cz_assist_replace.py.bak，将
pyQCat/parameters.py 替换为 parameters_replace.py.bak
"""

import math
from copy import deepcopy

import matplotlib.pyplot as plt
import numpy as np

from pyQCat.analysis.curve_analysis import CurveAnalysis
from pyQCat.analysis.specification import CurveAnalysisData
from pyQCat.experiments.composite_experiment import CompositeExperiment
from pyQCat.experiments.single.cz_assist import CPhaseTMSE
from pyQCat.experiments.single.swap_once import (
    validate_two_qubit_exp_read_options,
    validate_qubit_pair_cz_std,
    set_measure_pulses,
    validate_data_key
)
from pyQCat.experiments.top_experiment import TopExperiment
from pyQCat.parameters import options_wrapper, analysis_options_wrapper
from pyQCat.pulse.pulse_function import stimulate_state_pulse, zero_pulse
from pyQCat.pulse.pulse_lib import Constant
from pyQCat.pulse_adjust import params_to_pulse
from pyQCat.structures import Options, MetaData
from pyQCat.tools import cz_flow_options_adapter, qarange


@options_wrapper
class SweepDetune(TopExperiment):

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("detune_list", list)
        options.set_validator("label", ["detune1", "detune2"])
        options.set_validator("scan_name", str)
        options.set_validator("gate_num", int)
        options.detune_list = None
        options.scan_name = "qh"
        options.label = "detune1"
        options.readout_type = None
        options.gate_num = 1
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.x_label = None
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = None
        options.env_bits = []
        options.offset_width = None
        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)
        validate_data_key(self)
        self.analysis_options.x_label = self.experiment_options.label

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "scan_name": self.experiment_options.scan_name,
        }
        metadata.process_meta = {
            "x_data": self.experiment_options.detune_list,
        }
        return metadata

    def _set_xy_pulses(self):
        """Set SwapOnce experiment XY pulses."""
        eop = self.experiment_options
        rop = self.run_options
        cz_width = rop.width

        for qubit in rop.env_bits:
            if qubit.name in [self.qubit_pair.qh, self.qubit_pair.ql]:
                state = "1"
            elif qubit.name.startswith("q"):
                state = "0"
            else:
                continue

            state_pulse = stimulate_state_pulse(state, qubit)
            offset_pulse = Constant(cz_width, 0, name="XY")
            xy_pulse = state_pulse() + offset_pulse()

            self.play_pulse("XY", qubit, [deepcopy(xy_pulse) for _ in range(len(eop.detune_list))])

    def _set_z_pulses(self):
        """Set SwapOnce experiment XY pulses."""
        eop = self.experiment_options
        rop = self.run_options
        zero_x = zero_pulse(rop.qh, "Z")
        cz_width = self.qubit_pair.width()

        for qubit in rop.env_bits:
            q_assign_pulse = deepcopy(zero_x)
            s_gate_params = deepcopy(rop.gate_params.get(qubit.name))
            s_gate_params.update({"time": cz_width})
            sd = False

            if qubit.name == eop.scan_name:
                sd = True

            z_pulse_list = []
            for detune in eop.detune_list:
                new_q_assign_pulse = deepcopy(q_assign_pulse)
                if sd:
                    s_gate_params.update({eop.label: detune})
                target_pulse = params_to_pulse(**s_gate_params)
                z_pulse = new_q_assign_pulse()
                for i in range(self.experiment_options.gate_num):
                    z_pulse += deepcopy(target_pulse())
                z_pulse_list.append(z_pulse)

            self.play_pulse("Z", qubit, z_pulse_list)

    def _set_measure_pulses(self):
        """Set readout pulse."""
        set_measure_pulses(self)

    def _special_run_analysis(self):
        """Experiment special analysis run logic."""
        self._run_analysis(
            x_data=self.experiment_options.detune_list,
            analysis_class=SweepDetuneAnalysis,
        )

    def run(self):
        """Run SwapOnce experiment."""
        cz_flow_options_adapter(self)
        super().run()
        self._special_run_analysis()


class SweepGateNum(TopExperiment):

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("gate_nums", list)
        options.gate_nums = qarange(1, 10, 1)
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.x_label = None
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = None
        options.env_bits = []
        options.offset_width = None
        return options

    def _check_options(self):
        """Check Options."""
        super()._check_options()
        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)
        validate_data_key(self)
        self.analysis_options.x_label = self.experiment_options.label

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "x_data": self.experiment_options.gate_nums,
        }
        return metadata

    def _set_xy_pulses(self):
        """Set SwapOnce experiment XY pulses."""
        eop = self.experiment_options
        rop = self.run_options
        cz_width = rop.width

        for qubit in rop.env_bits:
            if qubit.name in [self.qubit_pair.qh, self.qubit_pair.ql]:
                state = "1"
            elif qubit.name.startswith("q"):
                state = "0"
            else:
                continue

            state_pulse = stimulate_state_pulse(state, qubit)
            offset_pulse = Constant(cz_width, 0, name="XY")
            xy_pulse = state_pulse() + offset_pulse()

            self.play_pulse("XY", qubit, [deepcopy(xy_pulse) for _ in range(len(eop.gate_nums))])

    def _set_z_pulses(self):
        """Set SwapOnce experiment XY pulses."""
        eop = self.experiment_options
        rop = self.run_options
        zero_x = zero_pulse(rop.qh, "Z")
        cz_width = self.qubit_pair.width()

        for qubit in rop.env_bits:
            q_assign_pulse = deepcopy(zero_x)
            s_gate_params = deepcopy(rop.gate_params.get(qubit.name))
            s_gate_params.update({"time": cz_width})

            z_pulse_list = []
            for gate_num in eop.gate_nums:
                new_q_assign_pulse = deepcopy(q_assign_pulse)
                target_pulse = params_to_pulse(**s_gate_params)
                z_pulse = new_q_assign_pulse()
                for i in range(gate_num):
                    z_pulse += deepcopy(target_pulse())
                z_pulse_list.append(z_pulse)

            self.play_pulse("Z", qubit, z_pulse_list)

    def _set_measure_pulses(self):
        """Set readout pulse."""
        set_measure_pulses(self)

    def _special_run_analysis(self):
        """Experiment special analysis run logic."""
        self._run_analysis(
            x_data=self.experiment_options.gate_nums,
            analysis_class=SweepDetuneAnalysis,
        )

    def run(self):
        """Run SwapOnce experiment."""
        cz_flow_options_adapter(self)
        super().run()
        self._special_run_analysis()

    def _update_instrument(self):
        for qubit in self.qubits:
            self.sweep_readout_trigger_delay(
                qubit.readout_channel,
                self._pulse_time_list[:len(self.experiment_options.gate_nums)]
            )
        super()._update_instrument()


@analysis_options_wrapper()
class SweepDetuneAnalysis(CurveAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        Options:
            x_label (str): x_label mark.

        """
        options = super()._default_options()

        options.merge_y_data = True

        return options


class SweepQCAndDetune(CompositeExperiment):

    _sub_experiment_class = SweepDetune

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("qc_amp_list", list)
        options.qc_amp_list = None
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "x_data": self.experiment_options.qc_amp_list,
        }
        return metadata

    def run(self):
        super().run()

        qc = self.qubit_pair.qc
        qc_amp_list = self.experiment_options.qc_amp_list

        for index, amp in enumerate(qc_amp_list):
            exp = deepcopy(self.child_experiment)
            exp.qubit_pair.set_cz_value(qc, "amp", amp)
            exp.set_parent_file(self, f"qc-amp-{amp}v", index, len(qc_amp_list))
            exp.run()
            self._experiments.append(exp)

        self._run_analysis(x_data=qc_amp_list, analysis_class=SweepQCAndDetuneAnalysis)


@analysis_options_wrapper()
class SweepQCAndDetuneAnalysis(CurveAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        Options:
            index_policy (str): Select fit target index policy.
            interaction_location (int): Select interaction point number.
        """
        options = super()._default_options()
        options.subplots = (1, 1)
        options.y_label = "Detune Z Amp (V)"
        options.x_label = "QC Z Amp (v)"
        options.p_all_list = []
        options.pcolormesh_options = {
            "shading": "nearest",
            "cmap": plt.cm.get_cmap("viridis"),
        }

        return options

    def _check_y_label(self):
        return self.options.y_label

    def _visualization(self):
        # super()._visualization()
        self.drawer.set_options(title=self._description())

        x_arr = self.experiment_data.metadata.process_meta.get("x_data")
        if x_arr:
            self.experiment_data.metadata.process_meta.pop("x_data")
        else:
            x_arr = self.experiment_data.x_data

        if self.has_child is True:

            detune2_list = self.experiment_data.metadata.process_meta.get("y_data")

            if detune2_list:
                self.experiment_data.metadata.process_meta.pop("y_data")
            else:
                detune2_list = self.experiment_data.metadata.process_meta.get("child_scan_freq")
                # detune2_list = self.experiment_data.child_data(index=0).x_data

            phase_arr = []
            for i, _ in enumerate(x_arr):
                child_data = self.experiment_data.child_data(index=i)
                phase_arr.append(child_data.y_data["phase"])

            z_arr = np.array(phase_arr).T
            self.drawer.draw_color_map(
                x_arr, detune2_list, z_arr,
                ax_index=0,
                **self.options.pcolormesh_options
            )
        self.drawer.format_canvas()


@options_wrapper
class SweepDetuneCom(CompositeExperiment):
    _sub_experiment_class = SweepDetune

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("detune_list", list)
        options.detune_list = None
        options.set_validator("scan_name", str)
        options.scan_name = "qh"
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.x_label = None
        options.y_label = None
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "scan_name": self.experiment_options.scan_name,
        }
        metadata.process_meta = {
            "x_scan": self.experiment_options.scan_name,
            "y_scan": self.experiment_options.child_exp_options.scan_name,
            "x_data": self.experiment_options.get("detune_list"),
            "y_data": self._experiments[0].analysis.experiment_data.metadata.process_meta.get("x_data"),
            "child_scan_freq": self._experiments[0].experiment_options.get("detune_freq_list"),
            "scan_freq": self.experiment_options.detune_freq_list
        }
        return metadata

    def run(self):
        cz_flow_options_adapter(self)
        super().run()

        detune_list = self.experiment_options.detune_list
        scan_name = self.experiment_options.child_exp_options.scan_name
        label = self.experiment_options.child_exp_options.label
        scan_bit = self.qubit_pair.qh if scan_name == "qh" else self.qubit_pair.ql
        detune_name = "detune2" if label == "detune1" else "detune1"
        self.set_analysis_options(x_label=detune_name, y_label=label)

        for index, detune in enumerate(detune_list):
            exp = deepcopy(self.child_experiment)
            exp.qubit_pair.set_cz_value(scan_bit, detune_name, detune)
            exp.set_parent_file(self, f"detune-{detune}v", index, len(detune_list))
            self._check_simulator_data(exp, index)
            exp.run()
            self._experiments.append(exp)

        self._run_analysis(x_data=detune_list, analysis_class=SweepDetuneComAnalysis)


@analysis_options_wrapper()
class SweepDetuneComAnalysis(CurveAnalysis):

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        options:
            select_key (str): Select SwapOnce Pxx, mark label process width.
            p_all_list (list): SwapOnce all P array, to plot depth.

        """
        options = super()._default_options()

        options.x_label = "Detune1 (V)"
        options.y_label = "Detune2 (V)"
        options.p_all_list = []
        options.goal_width = 50
        options.var_limit = 1

        options.pcolormesh_options = {
            "shading": "nearest",
            "cmap": plt.cm.get_cmap("viridis"),
        }

        return options

    def _prepare_subplot(self):
        """Prepare one more canvas axis."""
        sub_title = []
        y_labels = []
        p_all_list = []

        if self.has_child is True:
            child_data = self.experiment_data.child_data(index=0)
            for p_label in child_data.y_data.keys():
                p_all_list.append([])
                if not isinstance(self.options.y_label, list):
                    y_labels.append(self.options.y_label)
                sub_title.append(f"Swap Depth {p_label}")

        if not y_labels:
            y_labels = self.options.y_label

        length = len(y_labels)
        row = math.ceil(length / 2)
        subplots = (row, 2)

        diff = subplots[0] * subplots[1] - length
        if diff > 0:
            y_labels.extend([""] * diff)
            sub_title.extend([""] * diff)

        self.set_options(
            y_label=y_labels,
            subplots=subplots,
            sub_title=sub_title,
            p_all_list=p_all_list,
            figsize=(12, 4 * (len(y_labels) // 2))
        )

    def _visualization(self):
        """Swap plot depth."""
        super()._visualization()

        base_ax_index = len(self.experiment_data.y_data.keys())

        if self.has_child is True:
            p_all_list = self.options.p_all_list

            x_arr = self.experiment_data.metadata.process_meta.get("x_data")

            if x_arr:
                self.experiment_data.metadata.process_meta.pop("x_data")
            else:
                x_arr = self.experiment_data.x_data

            y_arr = self.experiment_data.metadata.process_meta.get("y_data")
            if y_arr:
                self.experiment_data.metadata.process_meta.pop("y_data")

            for i, _ in enumerate(x_arr):
                child_data = self.experiment_data.child_data(index=i)
                if y_arr is None:
                    y_arr = child_data.x_data

                for j, tub in enumerate(child_data.y_data.items()):
                    p_label, p_value = tub
                    p_all_list[j].append(p_value)

            for i, new_p_arr in enumerate(p_all_list):
                ax_index = base_ax_index + i
                self.drawer.draw_color_map(
                    x_arr,
                    y_arr,
                    np.array(new_p_arr).T,
                    ax_index=ax_index,
                    **self.options.pcolormesh_options,
                )

    def run_analysis(self):
        """Run analysis on experiment data."""
        super().run_analysis()


@options_wrapper
class SweepDetuneComPhase(CompositeExperiment):
    _sub_experiment_class = CPhaseTMSE

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("detune_list", list)
        options.set_validator("scan_name", str)
        options.detune_list = None
        options.scan_name = None
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.x_label = None
        options.y_label = None
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        return options

    def _check_options(self):
        super()._check_options()
        cz_flow_options_adapter(self)

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "scan_name": self.experiment_options.scan_name,
        }
        metadata.process_meta = {
            "x_scan": self.experiment_options.scan_name,
            "y_scan": self.experiment_options.child_exp_options.scan_name,
            "x_data": self.experiment_options.get("detune_list"),
            "y_data": self._experiments[0].analysis.experiment_data.metadata.process_meta.get("x_data"),
            "child_scan_freq": self._experiments[0].experiment_options.get("detune_freq_list"),
            "scan_freq": self.experiment_options.detune_freq_list
        }
        return metadata

    def run(self):
        super().run()

        detune_list = self.experiment_options.detune_list
        phase_mode = self.child_experiment.experiment_options.phase_mode
        self.set_analysis_options(x_label="Detune1 Z Amp", y_label="Detune2 Z Amp")
        data_key = self.child_experiment.analysis_options.data_key
        scan_name = self.experiment_options.scan_name

        for idx1, detune1 in enumerate(detune_list):
            # exp = None
            phases = []
            # for idx2, detune2 in enumerate(detune2_list):
            exp = deepcopy(self.child_experiment)
            exp.qubit_pair.set_cz_value(scan_name, "detune1", detune1)
            # exp.qubit_pair.set_cz_value(scan_name, "detune2", detune2)
            exp.set_parent_file(self, f"detune-{detune1}v", idx1, len(detune_list))
            exp.set_experiment_options(mode="SE-TM", phase_mode=phase_mode)
            self._check_simulator_data(exp, idx1)
            exp.run()
            delta_phase = exp.analysis.analysis_datas.get(data_key[0]).y
            # phases.append(exp.analysis.results.phase.value)
            phases.append(delta_phase)

            exp.experiment_data._x_data = np.array(detune_list)
            exp.experiment_data._y_data = {"phase": np.array(phases)}
            exp.analysis._analysis_data_dict = CurveAnalysisData(
                x=np.array(detune_list),
                y=np.array(phases)
            )
            self._experiments.append(exp)

        self._run_analysis(x_data=detune_list, analysis_class=SweepQCAndDetuneAnalysis)
