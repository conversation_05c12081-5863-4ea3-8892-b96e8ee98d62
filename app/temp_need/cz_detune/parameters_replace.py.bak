# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2020/11/5
# __author:       <PERSON>
# __corporation:  OriginQuantum
"""
Get experimental parameters.
"""

import json
import os
import traceback
from copy import deepcopy
from typing import Dict, List, Union, Any, TYPE_CHECKING

import numpy as np
from prettytable import PrettyTable

from pyQCat.analysis.visualization.curve_drawer import CurveDrawer
from pyQCat.config import BITS_CACHE
from pyQCat.log import pyqlog
from pyQCat.parsefile import (
    get_inst,
    get_qubit,
    get_coupler,
    get_correction,
    get_iq_discriminator,
    get_freq_zamp,
    get_freq_zamp_dc,
    get_cz_pulse_param,
    get_crosstalk_param,
    get_ac_crosstalk,
    get_dc_crosstalk,
    get_qubit_pair,
    get_all_bits,
    get_all_correction,
)
from pyQCat.qubit import Qubit, BaseQubit, Coupler
from pyQCat.tools import correct_crosstalk, freq_to_amp

if TYPE_CHECKING:
    from pyQCat.experiments.base_experiment import BaseExperiment
    from pyQCat.experiments.top_experiment import TopExperiment
    from pyQCat.experiments.coupler_experiment import CouplerBaseExperiment
    from pyQCat.experiments.composite_experiment import CompositeExperiment
    from pyQCat.analysis.top_analysis import TopAnalysis
    from pyQCat.analysis.curve_analysis import CurveAnalysis

    ExperimentType = Union[
        BaseExperiment, TopExperiment, CouplerBaseExperiment, CompositeExperiment
    ]

    AnalysisType = Union[TopAnalysis, CurveAnalysis]


ABS_PATH = os.path.dirname(os.path.abspath(__file__))
CONFIG_PATH = os.path.abspath(os.path.join(ABS_PATH, "../")) + "/conf"

PARAM_DICT = {
    "instrument": get_inst,
    "qubit": get_qubit,
    "coupler": get_coupler,
    "compensate": get_correction,
    "dcm": get_iq_discriminator,
    "fq_zamp": get_freq_zamp,
    "fq_zamp_dc": get_freq_zamp_dc,
    "cz_dict": get_cz_pulse_param,
    "crosstalk_dict": get_crosstalk_param,
    "ac_crosstalk": get_ac_crosstalk,
    "dc_crosstalk": get_dc_crosstalk,
    "qubit_pair": get_qubit_pair,
    "all_bits": get_all_bits,
    "all_compensate": get_all_correction,
}


def get_parameters(
    field: str,
    name: Union[int, str, List] = None,
    fixed_args: Any = None,
    addition: Any = None,
) -> Any:
    """Get experimental parameters by name.

    Args:
        field (str): Map api field name.
        name (Union[int, str, List]): When obtaining the QAIO, it indicates the type(8 or 30),
            and when obtaining the qubit parameters, it indicates the qubit number.
        fixed_args (Any, optional): Fixed parameters, like bit_type.
        addition (optional, Any): Some api need additional parameters.
    """
    try:
        func = PARAM_DICT.get(field)
        if name is not None:
            if fixed_args is not None:
                if addition is not None:
                    return func(name, fixed_args, addition)
                else:
                    return func(name, fixed_args)
            else:
                return func(name)
        else:
            return func()
    except Exception as err:
        err_mag = f"\n{traceback.format_exc()}"
        pyqlog.error(err_mag)


def format_bits_name(bit_names: list, bit_type: str = "Qubit"):
    if not isinstance(bit_names, list):
        bit_names = [bit_names]
    type_list = ["int", "str", "float"]
    if isinstance(bit_names[0], int) and isinstance(bit_names[-1], int):
        if bit_type.lower() == "qubit":
            bit_names = [f"q{x}" for x in bit_names]
        else:
            bit_names = [f"c{x}" for x in bit_names]
    elif (
        type(bit_names[0]).__name__ not in type_list
        and type(bit_names[-1]).__name__ not in type_list
    ):
        bit_names = [x.name for x in bit_names]
    return bit_names


def get_all_bits_data(bit_names: list, bit_type: str = "Qubit", cache: bool = False):
    if not bit_names:
        return {}
    bit_names = format_bits_name(bit_names, bit_type)
    return get_parameters(BITS_CACHE.bits, bit_names, cache)


def get_all_compensate_data(
    bit_names: list, bit_type: str = "Qubit", cache: bool = False
):
    if not bit_names:
        return {}
    bit_names = format_bits_name(bit_names, bit_type)
    return get_parameters(BITS_CACHE.compensate, bit_names, cache)


def corrected_dc(
    dimension: Union[int, List[int]] = None,
    coupler_dimension: Union[int, List[int]] = None,
    sweet_qubit: Union[int, str] = None,
    static_qubits: List[BaseQubit] = None,
    use_crosstalk: bool = None,
    debug: bool = True,
) -> Dict:
    """Calibrate the DC applied on the individual qubits.

    If the ``sweet_point`` parameter is passed in, the bit corresponding to ``sweet_point``
    takes the ``dc_max`` attribute, and the other qubits take the ``dc_min`` attribute, and
    no distortion correction is required;

    If ``sweet_point`` is not passed in, then all bit voltage values take the ``dc``
    attribute of the bit and need to be corrected for distortion;

    If the ``static_qubits`` parameter is passed in, the voltage value is obtained from
    the ``qubit`` instance in ``static_qubits``, The rest of the bits are taken from the
    database.

    Args:
        dimension (Union[int, List[int]], optional): Number of bits for DC calibration.
        coupler_dimension (Union[int, List[int]], optional): Number of couplers.
        root (str, optional): Configuration file path to load qubit information.
        sweet_qubit (Union[int, str], optional): Working qubit number.
        static_qubits (List[Qubit], optional): Static custom qubit.
        use_crosstalk (bool): True means use dc crosstalk calibrate dc value, default None.
        debug (bool): True means debug model, default True.

    Raises:
        TypeError: Dimension only sopport list and int type.

    Returns:
        Dict: The voltage value corresponding to each channel,
            the voltage finally applied to the qubit.
    """
    q_list = []
    c_list = []

    if dimension is not None:
        if isinstance(dimension, int):
            q_list = list(range(dimension))
        elif isinstance(dimension, List):
            q_list = dimension
        else:
            raise TypeError(
                f"Only List and int type support! " f"{type(dimension)} is invalid!"
            )

    if coupler_dimension is not None:
        if isinstance(coupler_dimension, int):
            c_list = list(range(coupler_dimension))
        elif isinstance(coupler_dimension, List):
            c_list = coupler_dimension
        else:
            raise TypeError(
                f"Only List and int type support! "
                f"{type(coupler_dimension)} is invalid!"
            )

    msg = f"Correct DC, sweet qubit is {sweet_qubit}, infos as follows: \n"
    msg += f"working qubits: {q_list}\n"
    msg += f"working couplers: {c_list}\n"

    dc_dict = {}
    dc_logs = {}
    dc_channel = []
    dc_before_crosstalk = []
    static_basequbit_list = []
    basequbit_list = []
    basequbit_name_list = []
    working_qubits = []

    # load working qubits object
    if static_qubits:
        for base_qubit in static_qubits:
            if isinstance(base_qubit, Qubit):
                static_basequbit_list.append(f"q{base_qubit.bit}")
            elif isinstance(base_qubit, Coupler):
                static_basequbit_list.append(f"c{base_qubit.bit}")
    q_dict = get_all_bits_data(q_list)
    c_dict = get_all_bits_data(c_list, "Coupler")

    for q_bit in q_list:
        if f"q{q_bit}" in static_basequbit_list:
            base_qubit = static_qubits[static_basequbit_list.index(f"q{q_bit}")]
        else:
            base_qubit = q_dict.get(f"q{q_bit}", get_parameters("qubit", q_bit))
        basequbit_list.append(base_qubit)
        working_qubits.append(base_qubit.name)
        basequbit_name_list.append(base_qubit.name)

    for c_bit in c_list:
        bit_name = f"c{c_bit}"
        if bit_name in static_basequbit_list:
            base_qubit = static_qubits[static_basequbit_list.index(bit_name)]
        else:
            base_qubit = c_dict.get(f"c{c_bit}", get_parameters("coupler", c_bit))
        basequbit_list.append(base_qubit)
        working_qubits.append(bit_name)
        basequbit_name_list.append(base_qubit.name)

    # sweet qubit use dc max, other use dc min, coupler use dc max
    if sweet_qubit is not None:
        if use_crosstalk is None:
            use_crosstalk = False

        if isinstance(sweet_qubit, int):
            sweet_qubit = f"q{sweet_qubit}"

        for base_qubit in basequbit_list:
            if base_qubit.name == sweet_qubit:
                dc_before_crosstalk.append(base_qubit.dc_max)
                dc_logs.setdefault(
                    sweet_qubit,
                    [
                        base_qubit.anno(),
                        "DC_MAX",
                        base_qubit.z_dc_channel,
                        base_qubit.dc_max,
                    ],
                )
            elif isinstance(base_qubit, Qubit):
                dc_before_crosstalk.append(base_qubit.dc_min)
                dc_logs.setdefault(
                    base_qubit.name,
                    [
                        base_qubit.anno(),
                        "DC_MIN",
                        base_qubit.z_dc_channel,
                        base_qubit.dc_min,
                    ],
                )
            elif isinstance(base_qubit, Coupler):
                dc_before_crosstalk.append(base_qubit.dc_max)
                dc_logs.setdefault(
                    base_qubit.name,
                    [
                        base_qubit.anno(),
                        "DC_MAX",
                        base_qubit.z_dc_channel,
                        base_qubit.dc_max,
                    ],
                )
            dc_channel.append(str(base_qubit.z_dc_channel))
        dc_before_crosstalk = np.asarray(dc_before_crosstalk)
    # all qubit use dc
    else:
        if use_crosstalk is None:
            use_crosstalk = True

        for base_qubit in basequbit_list:
            if isinstance(base_qubit, Qubit):
                dc_before_crosstalk.append(base_qubit.dc)
                dc_logs.setdefault(
                    base_qubit.name,
                    [base_qubit.anno(), "DC", base_qubit.z_dc_channel, base_qubit.dc],
                )
            elif isinstance(base_qubit, Coupler):
                dc_before_crosstalk.append(base_qubit.dc_max)
                dc_logs.setdefault(
                    base_qubit.name,
                    [
                        base_qubit.anno(),
                        "DC_MAX",
                        base_qubit.z_dc_channel,
                        base_qubit.dc_max,
                    ],
                )
            dc_channel.append(str(base_qubit.z_dc_channel))
        dc_before_crosstalk = np.asarray(dc_before_crosstalk)

    # whether correct dc crosstalk
    msg += f"use_crosstalk: {use_crosstalk}\n"
    if use_crosstalk is True:
        dc_crosstalk = get_parameters("dc_crosstalk", working_qubits)
        # msg += f"dc_crosstalk = \n{dc_crosstalk}\n"
        dc_after_crosstalk = correct_crosstalk(dc_before_crosstalk, dc_crosstalk)
    else:
        dc_after_crosstalk = dc_before_crosstalk

    for i, dc_value in enumerate(dc_after_crosstalk.round(6)):
        dc_dict.setdefault(dc_channel[i], dc_value)
        dc_logs.get(basequbit_name_list[i]).append(dc_value)

    # fmt = "| {:^15} | {:^30} | {:^15} | {:^15} | {:^15} | {:^15} |\n"
    # msg += fmt.format("name", "describe", "dc type", "dc channel", "dc value", "final dc")
    # for key, value in dc_logs.items():
    #     msg += fmt.format(key, value[0], value[1], value[2], value[3], value[4])
    #
    # if debug:
    #     pyqlog.info(msg[:-1])

    return dc_dict


def update_qubit_dc(bit: int, vol: float, root: str = CONFIG_PATH):
    with open(root + "/qubit.json", "r") as f:
        data = json.load(f)
    target = "qubit " + str(bit)
    qubit_dict = data.get(target)
    qubit_dict["dc"] = vol

    with open(root + "/qubit.json", "w") as f:
        json.dump(data, f, indent=4)


def decorate_dc(
    q_list: List[Qubit],
    crosstalk_bit_names: List[str],
    username: str = None,
    env_name: str = None,
) -> Dict:
    """DC Correction Using DC Crosstalk Matrix.

    Args:
        q_list (List[Qubit]): The qubit input dc.
        crosstalk_bit_names (List[str]): Crosstalk qubit name list.
        username (str): Belong to the user data.
        env_name (str): Mark the simulator environment name.

    Returns:
        Dict: The vol of every DC channel.
    """
    dc_crosstalk_matrix = get_parameters("dc_crosstalk", crosstalk_bit_names)
    dc_dict = {}
    dc_channel = []
    dc_before_crosstalk = []

    for qubit in q_list:
        dc_before_crosstalk.append(qubit.dc)
        dc_channel.append(str(qubit.z_dc_channel))

    dc_before_crosstalk = np.asarray(dc_before_crosstalk)
    dc_after_crosstalk = correct_crosstalk(dc_before_crosstalk, dc_crosstalk_matrix)

    for i, dc_value in enumerate(dc_after_crosstalk.round(6)):
        dc_dict.setdefault(dc_channel[i], dc_value)

    return dc_dict


def transform_coupler_to_qubits(coupler: Coupler, bit_dict: Dict = None):
    """Transform coupler to drive qubit and probe qubit.

    Args:
        coupler (Coupler): _description_
        bit_dict (Dict, optional): _description_. Defaults to None.

    Raises:
        ValueError: Coupler probe qubit and drive qubit cannot be consistent

    Returns:
        Tuple(Qubit, Qubit): Coupler drive qubit and probe qubit
    """

    bit_dict = bit_dict or {}

    if coupler.drive_bit == coupler.probe_bit:
        raise ValueError(
            f"Drive qubit is same as probe qubit, " f"both Q{coupler.drive_bit}"
        )

    dq = f"q{coupler.drive_bit}"
    pq = f"q{coupler.probe_bit}"

    drive_qubit = deepcopy(bit_dict.get(dq))
    drive_qubit.drive_freq = coupler.drive_freq
    drive_qubit.drive_power = coupler.drive_power
    drive_qubit.XYwave = coupler.drive_XYwave
    drive_qubit.Zwave = coupler.Zwave

    probe_qubit = deepcopy(bit_dict.get(pq))
    probe_qubit.drive_power = coupler.probe_drive_power
    probe_qubit.drive_freq = coupler.probe_drive_freq
    probe_qubit.probe_freq = coupler.probe_freq
    probe_qubit.probe_power = coupler.probe_power
    probe_qubit.XYwave = coupler.probe_XYwave

    pyqlog.info(
        f"transform {coupler.name} to drive "
        f"{drive_qubit.name} | probe {probe_qubit.name}"
    )

    return drive_qubit, probe_qubit


def _get_child_exp(exp: "ExperimentType"):
    """Get child experiment."""
    # if not exp.is_paternal:
    #     return exp
    # else:
    #     return _get_child_exp(exp._child_experiment)

    if hasattr(exp, "_child_experiment"):
        return _get_child_exp(exp._child_experiment)
    else:
        return exp


def get_physical_bit(exp: "ExperimentType", bit_name: str = None):
    """Get the target qubit/coupler ac_spectrum parameters."""
    physical_bit, branch = None, None

    if bit_name:
        if bit_name.startswith("q"):
            field = "qubit"
        else:
            field = "coupler"
        physical_bit = get_parameters(field, bit_name)
    elif exp._experiment_options.scan_name:
        if exp._experiment_options.scan_name.startswith("q"):
            field = "qubit"
        else:
            field = "coupler"
        physical_bit = get_parameters(field, exp._experiment_options.scan_name)
    elif exp._experiment_options.cur_scan_name:
        if exp._experiment_options.cur_scan_name.startswith("q"):
            field = "qubit"
        else:
            field = "coupler"
        physical_bit = get_parameters(field, exp._experiment_options.cur_scan_name)
    else:
        chi_exp = _get_child_exp(exp)
        if chi_exp.is_coupler_exp is True:
            physical_bit = chi_exp.coupler
        else:
            physical_bit = chi_exp.qubit

    if physical_bit:
        if physical_bit.idle_point > 0:
            branch = "right"
        elif physical_bit.idle_point < 0:
            branch = "left"
        else:
            branch = exp._experiment_options.ac_branch

    return physical_bit, branch


def options_wrapper(cls: "ExperimentType"):
    """Wrappers to change BaseExperiment options behavior.

    Args:
        cls (class object): The experiment class object.

    Returns:
        The experiment class object which has more behavior.
    """
    # Get the original implementations.
    origin_options = cls._default_experiment_options
    origin_check_options = cls._check_options

    # fixed bug: when visage call cls.__default_experiment_options
    # raise TypeError: _freq2amp_default_options() missing
    # 1 required positional argument: 'self'
    def _freq2amp_default_options(self=None):
        """Add freq_list to experiment options."""
        options = origin_options()
        if "z_amp_list" in options:
            options.set_validator("freq_list", list)
            options.set_validator("ac_branch", ["left", "right", None])
            options.freq_list = []
            options.ac_branch = "right"

        if "z_amp" in options:
            options.set_validator("frequency", float)
            options.set_validator("ac_branch", ["left", "right", None])
            options.frequency = None
            options.ac_branch = "right"

        if "adapter_amp_list" in options:
            options.set_validator("adapter_freq_list", list)
            options.set_validator("adapter_ac_branch", ["left", "right", None])
            options.adapter_freq_list = []
            options.adapter_ac_branch = "right"

        if "detune_list" in options:
            options.set_validator("detune_freq_list", list)
            options.set_validator("ac_branch", ["left", "right", None])
            options.detune_freq_list = []
            options.ac_branch = "right"

        return options

    def _freq2amp_check_options(self):
        """calculate z amps value by calling ac spectrum parameters."""
        # Issue, use l_gap, r_gap, step calculate freq_list.
        if "freq_range_map" in self._experiment_options:
            use_flag = False
            if "freq_list" in self._experiment_options:
                if not self._experiment_options.freq_list:
                    use_flag = True
                if "z_amp_list" in self._experiment_options:
                    if self._experiment_options.z_amp_list:
                        use_flag = False

            if use_flag is True:
                pyqlog.log("EXP", "Use `freq_range_map` calculate `freq_list` value.")

                physical_bit, _ = get_physical_bit(self, self._experiment_options.adapter_name)

                drive_freq = physical_bit.drive_freq

                from pyQCat.tools.utilities import qarange

                freq_range_map = self._experiment_options.freq_range_map
                l_gap, r_gap, step = freq_range_map.values()
                freq_list = qarange(
                    round(drive_freq - l_gap, 3), round(drive_freq + r_gap, 3), step
                )
                self._experiment_options.freq_list = freq_list

        max_iter_count = None
        if "freq_list" in self._experiment_options:
            if not self._run_options.scan_map:
                self._run_options.scan_map = {}
            self._run_options.x_data = None

        if self._experiment_options.scope_detune is True:
            scope = self._experiment_options.scope
            label = self._experiment_options.label or self._experiment_options.child_exp_options.label
            scan_map, scope_list = self.qubit_pair.detune_prepare(self.qubits, label=label, **scope)
            max_iter_count = len(scope_list)
            for scan_bit in list(scan_map.keys()):
                physical_bit, branch = get_physical_bit(self, scan_bit)
                freq_list = scan_map[scan_bit].get("freq")
                amp_list = freq_list_to_amp(freq_list, physical_bit, branch)
                scan_map[scan_bit]["amp"] = amp_list
            self._run_options.x_data = scope_list
            self._run_options.scan_map = scan_map
            self._experiment_options.scan_name = None
        else:
            origin_check_options(self)

            # use qubit or coupler.
            if self._experiment_options.freq_list:
                pyqlog.log(
                    "EXP", "Use frequency combined with AC spectrum to convert voltage."
                )

                freq_list = self._experiment_options.freq_list
                physical_bit, branch = get_physical_bit(self)
                z_amp_list = freq_list_to_amp(freq_list, physical_bit, branch)
                self._experiment_options.z_amp_list = z_amp_list

                # add frequency list to analysis options.
                self._analysis_options.freq_list = freq_list

                # extend
                if not self._run_options.scan_map:
                    self._run_options.scan_map = {}
                self._run_options.scan_map.update({
                    physical_bit.name: {
                        "freq": freq_list,
                        "amp": z_amp_list
                    }
                })
                self._run_options.x_data = z_amp_list
                max_iter_count = len(freq_list)
            if self._experiment_options.adapter_freq_list:
                pyqlog.log(
                    "EXP", "Use frequency combined with AC spectrum to convert voltage."
                )
                physical_bit, branch = get_physical_bit(self, self._experiment_options.adapter_name)
                adapter_freq_list = self._experiment_options.adapter_freq_list
                adapter_amp_list = freq_list_to_amp(adapter_freq_list, physical_bit, branch)
                self._experiment_options.adapter_amp_list = adapter_amp_list
            if self._experiment_options.detune_freq_list:
                pyqlog.log(
                    "EXP", "Use frequency combined with AC spectrum to convert voltage."
                )
                physical_bit, branch = get_physical_bit(self, self._experiment_options.scan_name)
                detune_freq_list = self._experiment_options.detune_freq_list
                detune_list = freq_list_to_amp(detune_freq_list, physical_bit, branch)
                self._experiment_options.detune_list = detune_list
                self._analysis_options.freq_list = detune_freq_list
            if self._experiment_options.frequency:
                pyqlog.log(
                    "EXP", "Use frequency combined with AC spectrum to convert voltage."
                )
                physical_bit, branch = get_physical_bit(self)
                if physical_bit:
                    z_amp = freq_to_amp(physical_unit=physical_bit, freq=self._experiment_options.frequency, branch=branch)
                    self._experiment_options.z_amp = z_amp - physical_bit.idle_point

            if not self._run_options.scan_map and self._experiment_options.z_amp_list:
                if "scan_name" in self._experiment_options:
                    self._run_options.scan_map.update({
                        self._experiment_options.scan_name: {
                            "amp": self._experiment_options.z_amp_list
                        }
                    })
                self._run_options.x_data = self._experiment_options.z_amp_list
                max_iter_count = len(self._experiment_options.z_amp_list)

        if max_iter_count:
            self._run_options.max_iter_count = max_iter_count

        origin_check_options(self)

    # Update the class methods.
    cls._default_experiment_options = _freq2amp_default_options
    cls._check_options = _freq2amp_check_options

    return cls


def analysis_options_wrapper(**update_options):
    """Wrappers to change CurveAnalysis options behavior.

    Args:
        update_options: The extra options to update.

    Returns:
        The analysis class object which has more behavior.
    """

    def analysis_class_wrapper(cls: "AnalysisType"):
        """wrappers to change analysis behavior."""
        # Get the original implementations.
        origin_options = cls._default_options
        origin_run_analysis = cls.run_analysis

        def _freq2amp_default_options(self=None):
            """Add freq_list to experiment options."""
            options = origin_options()
            options.freq_list = None
            # options.physical_bit = None

            return options

        def _freq2amp_run_analysis(self):
            """Plot freq relationship figure."""
            # execute raw method.
            origin_run_analysis(self)

            if self.options.freq_list or (
                    self.has_child and self.experiment_data.metadata.process_meta.get("child_scan_freq")
            ):
                # update extra options.
                if update_options:
                    for key, value in update_options.items():
                        if key in self.options:
                            self.options[key] = value

                figure_list = [self.drawer.figure]
                # instead raw drawer as new drawer and execute `_visualization` again.
                if self.options.freq_list:
                    self.experiment_data._x_data = self.options.freq_list
                self.options.curve_drawer = CurveDrawer()
                self._quality = None
                origin_run_analysis(self)

                figure_list.append(self.drawer.figure)
                self.drawer._figure = figure_list

        cls._default_options = _freq2amp_default_options
        cls.run_analysis = _freq2amp_run_analysis

        return cls

    return analysis_class_wrapper


def freq_list_to_amp(freq_list: List, physical_unit: BaseQubit, branch: str):
    z_amp_list = []
    if physical_unit.tunable:
        for freq in freq_list:
            z_amp = freq_to_amp(physical_unit, freq, branch=branch)
            if np.isnan(z_amp):
                pyqlog.error(
                    f"{physical_unit.name} frequency {freq} MHz to amp result is nan!"
                )
                raise ValueError(f'Freq {freq} MHz to amp error, result is nan!')
            z_amp -= physical_unit.idle_point
            z_amp_list.append(z_amp)
    else:
        z_amp_list = [0 for _ in freq_list]
    return z_amp_list


def save_scan_map(exp):
    scan_map = exp.run_options.scan_map
    x_data = exp.run_options.x_data
    table = PrettyTable()
    new_scan_amp = {}
    for unit, ps in scan_map.items():
        for k, v in ps.items():
            new_scan_amp[f"{unit}-{k}"] = v
    fields = ["x"]
    fields.extend(list(new_scan_amp.keys()))
    values = new_scan_amp.values()
    table.field_names = fields
    for i, x in enumerate(x_data):
        cd = [x]
        cd.extend(v[i] for v in values)
        table.add_row(cd)
    pyqlog.log("EXP", f"{exp.label} scan map as follow:\n{table}")

    file_name = os.path.join(exp.file.dirs, "scan_map.txt")
    os.makedirs(exp.file.dirs, exist_ok=True)
    with open(file_name, "w") as f:
        f.write(str(table))
