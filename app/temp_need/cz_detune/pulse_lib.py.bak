# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
"""
__date:         2020/07/30
__author:       Miracle Shih
__corporation:  OriginQuantum
__usage:        convert quantum logic gate to pulse
"""

from typing import List, Dict, Any, Optional

import numpy as np
from scipy import special
from scipy.signal.windows import flattop

from ..errors import PulseError
from .base_pulse import PulseComponent
from .patch import xy_pulse_add, xy_envelope2sequence


class _SimplePulse(PulseComponent):
    """Define simple pulse template."""

    def __init__(self, time: float, amp: float, name: str = "Z"):
        self.amp = amp
        super().__init__(time, name)

    def validate_parameters(self) -> None:
        """Validate parameters."""
        if abs(self.amp) > 1.0:
            raise PulseError(f"The amplitude norm must be <= 1, found: {abs(self.amp)}")

    @property
    def parameters(self) -> Dict[str, Any]:
        """Update pulse's parameters."""
        self._parameters.update({"time": self.time, "amp": self.amp, "name": self.type})
        return self._parameters

    def _update_width(self):
        """update width."""
        self.width = self.time

    def get_pulse(self):
        """Calculate pulse array data."""
        raise NotImplementedError


class _StepMixin:
    """Realize step function."""

    @staticmethod
    def step_func(width: float, amp: float, sample_rate: float) -> np.ndarray:
        """Calculate step pulse formula."""
        points = int(width * sample_rate) + 1
        y_arr = amp * np.ones(points)
        return y_arr


class GaussianSquare(PulseComponent):
    """Gaussian Square Pulse."""

    # BUG -> when fast_m is True, the pulse length is not pick 1.6 sample rate
    def __init__(
        self,
        time: float,
        amp: float,
        sigma: float = 2.5,
        num: int = 3,
        fast_m: bool = False,
        name: str = "Z",
    ):
        self.amp = amp
        self.sigma = sigma
        self.num = num
        self.fast_m = fast_m
        self.mid_amp = amp  # adjust GaussianSquareDiff mid amp value
        super().__init__(time, name)

    @property
    def parameters(self) -> Dict[str, Any]:
        """Return a dictionary containing the pulse's parameters."""
        self._parameters.update(
            {
                "time": self.time,
                "amp": self.amp,
                "sigma": self.sigma,
                "num": self.num,
                "fast_m": self.fast_m,
                "name": self.type,
            }
        )
        return self._parameters

    def validate_parameters(self) -> None:
        """Validate parameters."""
        if abs(self.amp) > 1.0:
            raise PulseError(f"The amplitude norm must be <= 1, found: {abs(self.amp)}")
        if self.sigma <= 0:
            raise PulseError(f"Sigma must be greater than 0, found: {self.sigma}")

    def get_pulse(self):
        """Calculate pulse sequence."""
        # # old version
        # up = self._gaussian_up(3 * self.sigma, self.sigma, self.amp)
        # down = self._gaussian_down(3 * self.sigma, self.sigma, self.amp)
        # if self.fast_m:
        #     width = int(self.time * self.sample_rate) / self.sample_rate
        #     t = np.linspace(0, width, int(width * self.sample_rate) + 1)
        #     mid_size = len(t)
        # else:
        #     mid_size = int(
        #         (self.time - 2 * 3 * self.sigma) * self.sample_rate) + 1
        # mid = self.amp * np.ones(mid_size)

        # new version, 2023-03-09
        width = self.time
        ring_up_time = self.num * self.sigma

        if self.fast_m is True:
            up_down_width = self.num * self.sigma
            mid_width = width
        elif width - 2 * ring_up_time >= 0:
            up_down_width = self.num * self.sigma
            mid_width = width - 2 * up_down_width
        else:
            up_down_width = width / 2
            mid_width = 0

        up = self._gaussian_up(up_down_width, self.sigma, self.amp)
        down = self._gaussian_down(up_down_width, self.sigma, self.amp)
        mid = self.mid_amp * np.ones(int(mid_width * self.sample_rate) + 1)

        pulse_width = up_down_width * 2 + mid_width
        pulse_points = int(pulse_width * self.sample_rate) + 1
        slice_idx = pulse_points - (len(up) + len(down))

        if slice_idx >= 0:
            self._pulse = np.hstack((up, mid[:slice_idx], down))
        else:
            self._pulse = np.hstack((up, down[1:]))

        # todo calculate Gaussian Square pulse's envelope.
        self.get_raw_pulse()

    def _gaussian_up(self, time, sigma, amp):
        length = int(time * self.sample_rate) + 1
        x = np.linspace(-time, 0, length)
        y = self._gaussian_formula(x, sigma, amp)
        return y

    def _gaussian_down(self, time, sigma, amp):
        length = int(time * self.sample_rate) + 1
        x = np.linspace(0, time, length)
        y = self._gaussian_formula(x, sigma, amp)
        return y

    @staticmethod
    def _gaussian_formula(x, sigma, amp):
        return amp * np.exp(-(x**2) / (2 * sigma**2))

    def _update_width(self):
        """update width."""
        if self.fast_m is True:
            self.width = self.time + self.sigma * self.num * 2
        else:
            self.width = self.time


class GaussianUp(GaussianSquare):
    """Gaussian Up class."""

    def get_pulse(self):
        """Calculate pulse method."""
        up_arr = self._gaussian_up(self.time, self.sigma, self.amp)
        self._pulse = up_arr
        self.get_raw_pulse()

    def _update_width(self):
        """update width."""
        self.width = self.time


class GaussianDown(GaussianSquare):
    """Gaussian Down class."""

    def get_pulse(self):
        """Calculate pulse method."""
        down_arr = self._gaussian_down(self.time, self.sigma, self.amp)
        self._pulse = down_arr
        self.get_raw_pulse()

    def _update_width(self):
        """update width."""
        self.width = self.time


class Drag(PulseComponent):
    def __init__(
        self,
        time: float,
        offset: float,
        amp: float,
        detune: float,
        freq: float,
        phase: float = 0,
        alpha: float = 1.0,
        delta: float = -240.0,
        name="XY",
    ):
        self.freq = freq * 1e-3  # GHz
        self.amp = amp
        self.phase = phase
        self.alpha = alpha
        self.delta = delta * 1e-3  # GHz
        self.offset = offset
        self.detune = detune * 1e-3  # GHz
        super().__init__(time, name)
        self.width = offset * 2 + time
        self.multiple_freq.append(self.freq)

    @property
    def parameters(self) -> Dict[str, Any]:
        self._parameters.update(
            {
                "time": self.time,
                "offset": self.offset,
                "amp": self.amp,
                "detune": self.detune / 1e-3,
                "freq": self.freq / 1e-3,
                "phase": self.phase,
                "alpha": self.alpha,
                "delta": self.delta / 1e-3,
                "name": self.type,
            }
        )
        return self._parameters

    def validate_parameters(self) -> None:
        if abs(self.amp) > 1.0:
            raise PulseError(f"The amplitude norm must be <= 1, found: {abs(self.amp)}")
        start_value, end_value = self._get_param_range("intermediate_frequency")
        if (self.freq < start_value * 1e-3) or (self.freq > end_value * 1e-3):
            raise PulseError(
                f"baseband_freq must be greater than {start_value} and smaller than {end_value} MHz, "
                f"found: {self.freq * 1e3} MHz"
            )

    def get_pulse(self):
        """Get the drag pulse envelope."""
        width = int(self.time * self.sample_rate) / self.sample_rate
        t = np.linspace(0, width, int(width * self.sample_rate) + 1)
        w = int(self.offset * self.sample_rate) / self.sample_rate
        head = np.zeros(int(w * self.sample_rate))
        tail = np.zeros(int(w * self.sample_rate))
        phase = self.phase + 2 * np.pi * t * self.detune

        # envelope functions
        x0 = self.amp / 2 * (1 - np.cos(2 * np.pi * t / width))
        y0 = (
            -self.amp
            / 2
            * self.alpha
            / self.delta
            / width
            * np.sin(2 * np.pi * t / width)
        )

        x = x0 * np.cos(phase) + y0 * np.sin(phase)
        y = y0 * np.cos(phase) - x0 * np.sin(phase)

        x_envelope = np.hstack((head, x, tail))
        y_envelope = np.hstack((head, y, tail))

        self._envelope = [x_envelope, y_envelope]
        self.envelope_fragment.append(len(x_envelope))

    def envelope2sequence(self):
        """Convert Drag envelope to sequence."""
        return xy_envelope2sequence(self)

    def __add__(self, other):
        """Over write."""
        return xy_pulse_add(self, other)

    def _update_width(self):
        """update width."""
        self.multiple_freq[-1] = self.freq
        self.width = self.time + 2 * self.offset


class AcquireSine(PulseComponent):
    """Acquisition Waveform Generator."""

    def __init__(self, time: float, amp_list: List, baseband_freq_list: List, name="M"):
        """Create a new acquisition Waveform.

        Args:
            time (float): Wave width.
            amp_list (List): The list of amp, support union readout.
            baseband_freq_list (List): The list of baseband_freq, support union readout.
            name (str, optional): The name of wave, defaults to "M".
        """
        self.amp_list = amp_list
        self.baseband_freq_list = baseband_freq_list  # MHz
        super().__init__(time, name)

    @property
    def parameters(self) -> Dict[str, Any]:
        self._parameters.update(
            {
                "time": self.time,
                "amp_list": self.amp_list,
                "baseband_freq_list": self.baseband_freq_list,
                "name": self.type,
            }
        )
        return self._parameters

    def validate_parameters(self) -> None:
        for amp in self.amp_list:
            if abs(amp) > 1.0:
                raise PulseError(f"The amplitude norm must be <= 1, found: {abs(amp)}")
        start_value, end_value = self._get_param_range("intermediate_frequency")
        for freq in self.baseband_freq_list:
            if (freq < start_value) or (freq > end_value):
                raise PulseError(
                    f"baseband_freq must be greater than {start_value} and smaller than {end_value} MHz, found: {freq} MHz"
                )

    def step(self, width, amp):
        length = int(width * self.sample_rate) + 1
        return amp * np.ones(length)

    def _envelope2sequence(self, envelope, baseband_freqs=600e-3):
        length = len(envelope)
        T = (length - 1) / self.sample_rate
        t = np.linspace(0, T, int(T * self.sample_rate) + 1)
        y = envelope * np.cos(2 * np.pi * t * baseband_freqs)
        return y

    def get_pulse(self):
        w = self.time
        length = int(w * self.sample_rate) + 1
        sequence = np.zeros(length)
        for amp, baseband_freq in zip(self.amp_list, self.baseband_freq_list):
            if not amp == 0:
                y = self.step(width=w, amp=amp)
                sequence += self._envelope2sequence(y, baseband_freq * 1e-3)  # GHz
        num = len(np.array(np.nonzero(self.amp_list)).flatten())
        seq = np.array([sequence / num, np.zeros_like(sequence)])
        self._pulse = seq[0]
        self.get_raw_pulse()


class FlatTopGaussian(PulseComponent):
    def __init__(self, time, amp=0, sigma=1.25, buffer=5, is_control=True, name="Z"):
        self.amp = amp
        self.is_control = is_control
        self.sigma = sigma
        self.buffer = buffer
        super().__init__(time, name)
        self.label = "FTGauss"

    @property
    def parameters(self) -> Dict[str, Any]:
        self._parameters.update(
            {
                "time": self.time,
                "amp": self.amp,
                "sigma": self.sigma,
                "buffer": self.buffer,
                "is_control": self.is_control,
                "name": self.type,
            }
        )
        return self._parameters

    def validate_parameters(self) -> None:
        if abs(self.amp) > 1.0:
            raise PulseError(f"The amplitude norm must be <= 1, found: {abs(self.amp)}")
        if self.sigma <= 0:
            raise PulseError(f"Sigma must be greater than 0, found: {self.sigma}")

    @staticmethod
    def formula(amp, t, tb, tc, sigma):
        # return amp / 2 * (math.erf((t - tb) / (np.sqrt(2) * sigma)) - math.erf(
        #     (t - tc - tb) / (np.sqrt(2) * sigma)))

        # Optimize create pulse data time, 2023-01-30
        return (
            amp
            / 2
            * (
                special.erf((t - tb) / (np.sqrt(2) * sigma))
                - special.erf((t - tc - tb) / (np.sqrt(2) * sigma))
            )
        )

    def get_pulse(self):
        points = int(self.time * self.sample_rate)
        pulse_width = points / self.sample_rate
        t_list = np.linspace(0, pulse_width, points + 1)
        z_pulse = np.zeros_like(t_list)
        if self.is_control:
            tc = pulse_width - 2 * self.buffer
            z_pulse = self.formula(self.amp, t_list, self.buffer, tc, self.sigma)

            # for i, t in enumerate(t_list):
            #     z = self.formula(self.amp, t, self.buffer, tc, self.sigma)
            #     z_pulse[i] += z
        self._pulse = z_pulse
        self.get_raw_pulse()


class Constant(PulseComponent):
    """
    A simple constant pulse, with an amplitude value and a duration:

    .. math::

        f(x) = amp    ,  0 <= x < time
        f(x) = 0      ,  elsewhere
    """

    def __init__(self, time: float, amp: float, name: str = "Z"):
        self._amp = amp
        super().__init__(time, name)

    @property
    def amp(self) -> complex:
        """The constant value amplitude."""
        return self._amp

    @amp.setter
    def amp(self, value):
        """The constant value amplitude."""
        self._amp = value

    def get_pulse(self):
        points = int(self.time * self.sample_rate) + 1
        self._pulse = np.zeros(points)
        self._pulse += self.amp
        self.get_raw_pulse()
        # compatibility to XY pulse.
        x_envelope = self._pulse
        y_envelope = np.zeros_like(x_envelope)
        self.envelope = [x_envelope, y_envelope]
        self.envelope_fragment.append(len(x_envelope))

    def get_empty_pulse(self):
        self._pulse = np.zeros(0)
        self._pulse += self.amp
        self.get_raw_pulse()

    def validate_parameters(self) -> None:
        if abs(self.amp) > 1.0:
            raise PulseError(
                "The amplitude norm must be <= 1, " "found: {}".format(abs(self.amp))
            )

    @property
    def parameters(self) -> Dict[str, Any]:
        self._parameters.update({"time": self.time, "amp": self.amp, "name": self.type})
        return self._parameters

    def __add__(self, other):
        """Since Drag pulse need add envelope instead of sequence,
        over write this method.
        """
        if hasattr(other, "type") and other.type == "XY":
            return xy_pulse_add(self, other)
        else:
            # Constant + [Constant, SquareEnvelop, VarFreqEnvelop]
            # All Z type pulse must use super __add__ method.
            return super().__add__(other)

    def envelope2sequence(self):
        """Convert envelope to sequence."""
        if self.type == "XY":
            return xy_envelope2sequence(self)
        else:
            super().envelope2sequence()


class SquareEnvelop(PulseComponent):
    def __init__(
        self,
        time: int,
        offset: float,
        amp: float,
        detune: float,
        freq: float,
        name: str = "XY",
    ):
        self.freq = freq * 1e-3  # GHz
        self.offset = offset
        self.amp = amp
        self.detune = detune * 1e-3  # GHz
        super().__init__(time, name)
        self.width = time + offset * 2
        self.multiple_freq.append(self.freq)

    @property
    def parameters(self) -> Dict[str, Any]:
        self._parameters.update(
            {
                "time": self.time,
                "offset": self.offset,
                "amp": self.amp,
                "detune": self.detune / 1e-3,
                "freq": self.freq / 1e-3,
                "name": self.type,
            }
        )
        return self._parameters

    def validate_parameters(self) -> None:
        if abs(self.amp) > 1.0:
            raise PulseError(f"The amplitude norm must be <= 1, found: {abs(self.amp)}")
        start_value, end_value = self._get_param_range("intermediate_frequency")
        if (self.freq < start_value * 1e-3) or (self.freq > end_value * 1e-3):
            raise PulseError(
                f"baseband_freq must be greater than {start_value} and smaller "
                f"than {end_value} MHz, found: {self.freq * 1e3} MHz"
            )

    def get_pulse(self):
        width = int(self.time * self.sample_rate) / self.sample_rate
        t = np.linspace(0, width, int(width * self.sample_rate) + 1)
        phase = 2 * np.pi * t * self.detune

        offset_points = int(self.offset * self.sample_rate) / self.sample_rate
        head = np.zeros(int(offset_points * self.sample_rate))
        tail = np.zeros(int(offset_points * self.sample_rate))

        # envelope functions
        x0 = self.amp * np.ones_like(t)
        y0 = np.zeros_like(t)

        x = x0 * np.cos(phase) + y0 * np.sin(phase)
        y = y0 * np.cos(phase) - x0 * np.sin(phase)

        x_envelope = np.hstack((head, x, tail))
        y_envelope = np.hstack((head, y, tail))

        self._envelope = [x_envelope, y_envelope]
        self.envelope_fragment.append(len(x_envelope))

    def envelope2sequence(self):
        """Convert SquareEnvelop envelope to sequence."""
        return xy_envelope2sequence(self)

    def __add__(self, other):
        """Over write."""
        return xy_pulse_add(self, other)

    def _update_width(self):
        """update width."""
        self.multiple_freq[-1] = self.freq
        self.width = self.time + 2 * self.offset


class VarFreqEnvelop(PulseComponent):
    band_width = 150

    def __init__(
        self,
        time: int,
        offset: float,
        amp: float,
        detune: float,
        freq: float,
        name: str = "XY",
    ):
        self.freq = freq * 1e-3  # GHz
        self.offset = offset
        self.amp = amp
        self.detune = detune * 1e-3  # GHz
        self.freq_start = None
        self.freq_end = None
        super().__init__(time, name)
        self.width = time + offset * 2
        self.multiple_freq.append(self.freq)

    @property
    def parameters(self) -> Dict[str, Any]:
        self._parameters.update(
            {
                "time": self.time,
                "offset": self.offset,
                "amp": self.amp,
                "detune": self.detune / 1e-3,
                "freq": self.freq / 1e-3,
                # 'freq_start': self.freq_start,
                # 'freq_end': self.freq_end,
                "name": self.type,
            }
        )
        return self._parameters

    def validate_parameters(self) -> None:
        if abs(self.amp) > 1.0:
            raise PulseError(f"The amplitude norm must be <= 1, found: {abs(self.amp)}")
        start_value, end_value = self._get_param_range("intermediate_frequency")
        if (self.freq < start_value * 1e-3) or (self.freq > end_value * 1e-3):
            raise PulseError(
                f"baseband_freq must be greater than {start_value} "
                f"and smaller than {end_value} MHz, found: {self.freq * 1e3} MHz"
            )

    def get_pulse(self):
        self.freq_start = self.freq - (self.band_width / 2) * 1e-3
        self.freq_end = self.freq + (self.band_width / 2) * 1e-3

        width = int(self.time * self.sample_rate) / self.sample_rate
        t = np.linspace(0, width, int(width * self.sample_rate) + 1)
        phase = 2 * np.pi * t * self.detune
        chrip = self.band_width * 1e-3 / width * t - self.band_width * 1e-3 / 2

        offset_points = int(self.offset * self.sample_rate) / self.sample_rate
        head = np.zeros(int(offset_points * self.sample_rate))
        tail = np.zeros(int(offset_points * self.sample_rate))

        # envelope functions
        x0 = self.amp * np.cos(2 * np.pi * chrip * t)
        y0 = -self.amp * np.sin(2 * np.pi * chrip * t)

        x = x0 * np.cos(phase) + y0 * np.sin(phase)
        y = y0 * np.cos(phase) - x0 * np.sin(phase)

        x_envelope = np.hstack((head, x, tail))
        y_envelope = np.hstack((head, y, tail))

        self._envelope = [x_envelope, y_envelope]
        self.envelope_fragment.append(len(x_envelope))

    def envelope2sequence(self):
        """Convert SquareEnvelop envelope to sequence."""
        return xy_envelope2sequence(self)

    def __add__(self, other):
        """Over write."""
        return xy_pulse_add(self, other)

    def _update_width(self):
        """update width."""
        self.multiple_freq[-1] = self.freq
        self.width = self.time + 2 * self.offset


class GaussianSquareDiff(GaussianSquare):
    """Gaussian Square Diff pulse class."""

    def __init__(
        self,
        time: float,
        amp: float,
        sigma: float = 2.5,
        num: int = 3,
        fast_m: bool = False,
        name: str = "Z",
    ):
        super().__init__(time, amp, sigma, num, fast_m, name)
        self.mid_amp = 0

    @staticmethod
    def _gaussian_formula(x, sigma, amp):
        """Gaussian diff formula. Overwrite origin gaussian formula."""
        y_gs = amp * np.exp(-(x**2) / (2 * sigma**2))
        y_gs_diff = -y_gs * x / sigma**2
        return y_gs_diff

    def __add__(self, other):
        """Consider XY Line pulse, over write this method."""
        if hasattr(other, "type") and other.type == "XY":
            return xy_pulse_add(self, other)
        else:
            return super().__add__(other)

    def envelope2sequence(self):
        """Convert envelope to sequence."""
        if self.type == "XY":
            return xy_envelope2sequence(self)
        else:
            super().envelope2sequence()

    def calcu_envelope(self):
        """Calculate envelope."""
        x_envelope = self._pulse
        y_envelope = np.zeros_like(x_envelope)
        self.envelope = [x_envelope, y_envelope]
        self.envelope_fragment.append(len(x_envelope))

    def get_pulse(self):
        """Get pulse"""
        super().get_pulse()
        # compatibility to XY pulse.
        self.calcu_envelope()


class GaussianUpDiff(GaussianSquareDiff):
    """Gaussian Up Diff class."""

    def get_pulse(self):
        """Calculate pulse method."""
        up_arr = self._gaussian_up(self.time, self.sigma, self.amp)
        self._pulse = up_arr
        self.get_raw_pulse()
        self.calcu_envelope()

    def _update_width(self):
        """update width."""
        self.width = self.time


class GaussianDownDiff(GaussianSquareDiff):
    """Gaussian Down Diff class."""

    def get_pulse(self):
        """Calculate pulse method."""
        down_arr = self._gaussian_down(self.time, self.sigma, self.amp)
        self._pulse = down_arr
        self.get_raw_pulse()
        self.calcu_envelope()

    def _update_width(self):
        """update width."""
        self.width = self.time


class Cosine(_SimplePulse):
    """Cosine pulse class."""

    def get_pulse(self):
        """Calculate pulse array data."""
        width = self.time
        points = int(width * self.sample_rate) + 1

        t_arr = np.linspace(0, width, points)
        y_arr = self.amp / 2 * (1 - np.cos(2 * np.pi * t_arr / width))
        self._pulse = y_arr
        self.get_raw_pulse()


class CosineUp(_SimplePulse):
    """Cosine Up class."""

    def get_pulse(self):
        """Calculate pulse array data."""
        width = self.time
        points = int(width * self.sample_rate) + 1
        tau = width * 2

        t_arr = np.linspace(0, width, points)
        y_arr = self.amp / 2 * (1 - np.cos(2 * np.pi * t_arr / tau))
        self._pulse = y_arr
        self.get_raw_pulse()


class CosineDown(_SimplePulse):
    """Cosine Down class."""

    def get_pulse(self):
        """Calculate pulse array data."""
        width = self.time
        points = int(width * self.sample_rate) + 1
        tau = width * 2

        t_arr = np.linspace(0, width, points)
        y_arr = self.amp / 2 * (1 + np.cos(2 * np.pi * t_arr / tau))
        self._pulse = y_arr
        self.get_raw_pulse()


class CosineDiff(_SimplePulse):
    """Cosine Diff class."""

    def __add__(self, other):
        """Consider XY Line pulse, over write this method."""
        if hasattr(other, "type") and other.type == "XY":
            return xy_pulse_add(self, other)
        else:
            return super().__add__(other)

    def envelope2sequence(self):
        """Convert envelope to sequence."""
        if self.type == "XY":
            return xy_envelope2sequence(self)
        else:
            super().envelope2sequence()

    def calcu_envelope(self):
        """Calculate envelope."""
        x_envelope = self._pulse
        y_envelope = np.zeros_like(x_envelope)
        self.envelope = [x_envelope, y_envelope]
        self.envelope_fragment.append(len(x_envelope))

    def get_pulse(self):
        """Calculate pulse array data."""
        width = self.time
        points = int(width * self.sample_rate) + 1

        t_arr = np.linspace(0, width, points)
        y_arr = self.amp / 2 * 2 * np.pi / width * np.sin(2 * np.pi * t_arr / width)
        self._pulse = y_arr
        self.get_raw_pulse()
        self.calcu_envelope()


class FlatTop(_SimplePulse):
    """FlatTop pulse class."""

    def get_pulse(self):
        """Calculate pulse array data."""
        width = self.time
        points = int(width * self.sample_rate) + 1

        y_arr = self.amp * flattop(points)
        self._pulse = y_arr
        self.get_raw_pulse()


class FlatTopGaussianDiff(FlatTopGaussian):
    """FlatTopGaussian Diff pulse class."""

    def __add__(self, other):
        """Consider XY Line pulse, over write this method."""
        if hasattr(other, "type") and other.type == "XY":
            return xy_pulse_add(self, other)
        else:
            return super().__add__(other)

    def envelope2sequence(self):
        """Convert envelope to sequence."""
        if self.type == "XY":
            return xy_envelope2sequence(self)
        else:
            super().envelope2sequence()

    def calcu_envelope(self):
        """Calculate envelope."""
        x_envelope = self._pulse
        y_envelope = np.zeros_like(x_envelope)
        self.envelope = [x_envelope, y_envelope]
        self.envelope_fragment.append(len(x_envelope))

    @staticmethod
    def formula(amp, t, tb, tc, sigma):
        """flat_top_Gaussian_diff formula function."""

        def gaussian(x, sigma, amp):
            """gaussian formula."""
            return amp * np.exp(-(x**2) / (2 * sigma**2))

        y = (
            amp
            / np.sqrt(2 * np.pi)
            / sigma
            * (gaussian(t - tb, sigma, 1) - gaussian(t - tc - tb, sigma, 1))
        )
        return y

    def get_pulse(self):
        """Calculate pulse."""
        super().get_pulse()
        self.calcu_envelope()


class TwoStep(_SimplePulse, _StepMixin):
    """Two Step pulse class."""

    def __init__(
        self,
        time: float,
        amp: float,
        coeff: float = 2.0,
        pre_width: float = 100,
        name: str = "Z",
    ):
        self.coeff = coeff
        self.pre_width = pre_width
        super().__init__(time, amp, name)

    def validate_parameters(self) -> None:
        """Validate parameters."""
        max_amp = abs(self.amp * self.coeff)
        if max_amp > 1.0:
            raise PulseError(f"The amplitude norm must be <= 1, found: {max_amp}")

    @property
    def parameters(self) -> Dict[str, Any]:
        """Update pulse's parameters."""
        self._parameters.update(
            {
                "time": self.time,
                "amp": self.amp,
                "coeff": self.coeff,
                "pre_width": self.pre_width,
                "name": self.type,
            }
        )
        return self._parameters

    def _update_width(self):
        """update width."""
        self.width = self.pre_width + self.time

    def get_pulse(self):
        """Calculate pulse array data."""
        y1_arr = self.step_func(self.pre_width, self.amp * self.coeff, self.sample_rate)
        y2_arr = self.step_func(self.time, self.amp, self.sample_rate)
        self._pulse = np.hstack((y1_arr, y2_arr[1:]))
        self.get_raw_pulse()


class Clear(_SimplePulse, _StepMixin):
    """Clear pulse class."""

    def __init__(
        self,
        time: float,
        amp: float,
        coeff_list: Optional[List[float]] = None,
        t_kick: float = 50,
        tail: float = 100,
        name: str = "Z",
    ):
        self.coeff_list = coeff_list or [1.0, 0.8, -0.3, 0.3]
        self.t_kick = t_kick
        self.tail = tail  # delay parameter is used extra function.
        super().__init__(time, amp, name)

    def validate_parameters(self) -> None:
        """Validate parameters."""
        if len(self.coeff_list) != 4:
            raise PulseError(
                f"The coeff_list must be 4 elements list, "
                f"coeff_list: {self.coeff_list}"
            )
        max_amp = abs(self.amp * max(self.coeff_list))
        if max_amp > 1.0:
            raise PulseError(f"The amplitude norm must be <= 1, found: {max_amp}")

    @property
    def parameters(self) -> Dict[str, Any]:
        """Update pulse's parameters."""
        self._parameters.update(
            {
                "time": self.time,
                "amp": self.amp,
                "coeff_list": self.coeff_list,
                "t_kick": self.t_kick,
                "tail": self.tail,
                "name": self.type,
            }
        )
        return self._parameters

    def _update_width(self):
        """update width."""
        self.width = self.t_kick * 4 + self.time + self.tail

    def get_pulse(self):
        """Calculate pulse array data."""
        c_1, c_2, c_3, c_4, *_ = self.coeff_list

        y1_arr = self.step_func(self.t_kick, self.amp * c_1, self.sample_rate)
        y2_arr = self.step_func(self.t_kick, self.amp * c_2, self.sample_rate)
        y3_arr = self.step_func(self.time, self.amp, self.sample_rate)
        y4_arr = self.step_func(self.t_kick, self.amp * c_3, self.sample_rate)
        y5_arr = self.step_func(self.t_kick, self.amp * c_4, self.sample_rate)
        y6_arr = self.step_func(self.tail, 0, self.sample_rate)

        self._pulse = np.hstack(
            (y1_arr, y2_arr[1:], y3_arr[1:], y4_arr[1:], y5_arr[1:], y6_arr[1:])
        )
        self.get_raw_pulse()


class FlatTopGaussianDetune(PulseComponent):
    def __init__(
        self,
        time,
        amp=0,
        sigma=1.25,
        buffer=5,
        detune1=0,
        detune2=0,
        detune_width=0,
        is_control=True,
        name="Z",
    ):
        self.amp = amp
        self.is_control = is_control
        self.sigma = sigma
        self.buffer = buffer
        self.detune1 = detune1
        self.detune2 = detune2
        self.detune_width = detune_width
        self.mode = "std"

        super().__init__(time, name)
        self.label = "FTGauss"

    @property
    def parameters(self) -> Dict[str, Any]:
        self._parameters.update(
            {
                "time": self.time,
                "amp": self.amp,
                "sigma": self.sigma,
                "buffer": self.buffer,
                "detune1": self.detune1,
                "detune2": self.detune2,
                "detune_width": self.detune_width,
                "is_control": self.is_control,
                "name": self.type,
            }
        )
        return self._parameters

    def validate_parameters(self) -> None:
        if abs(self.amp) > 1.0:
            raise PulseError(
                f"The amplitude norm must be <= 1, found: amp({abs(self.amp)})"
            )
        if abs(self.detune1) > 1.0:
            raise PulseError(
                f"The detune1 norm must be <= 1, found: detune1({abs(self.detune1)})"
            )
        if abs(self.detune2) > 1.0:
            raise PulseError(
                f"The detune2 norm must be <= 1, found: detune2({abs(self.detune2)})"
            )
        if self.sigma <= 0:
            raise PulseError(
                f"Sigma must be greater than 0, found: sigma({self.sigma})"
            )
        if self.buffer < 3 * self.sigma:
            raise PulseError(
                f"Buffer must be greater than 3 * sigma, found: sigma({self.sigma}), buffer({self.buffer})"
            )

        tc = self.time - 2 * self.buffer
        if self.detune_width * 2 > tc:
            raise PulseError(
                f"detune width must be lower than (time - 2 * buffer), "
                f"found: dw({self.detune_width}), buffer({self.buffer}), time({self.time})"
            )

    @staticmethod
    def formula(amp, t, tb, tc, sigma):
        # return amp / 2 * (math.erf((t - tb) / (np.sqrt(2) * sigma)) - math.erf(
        #     (t - tc - tb) / (np.sqrt(2) * sigma)))

        # Optimize create pulse data time, 2023-01-30
        return (
            amp
            / 2
            * (
                special.erf((t - tb) / (np.sqrt(2) * sigma))
                - special.erf((t - tc - tb) / (np.sqrt(2) * sigma))
            )
        )

    def get_pulse(self):
        points = int(self.time * self.sample_rate)
        pulse_width = points / self.sample_rate
        t_list = np.linspace(0, pulse_width, points + 1)
        z_pulse = np.zeros_like(t_list)
        if self.is_control:
            if self.mode == "std":
                tc = pulse_width - 2 * self.buffer
                z_pulse = self.formula(
                    self.amp, t_list, self.buffer, tc, self.sigma
                ).tolist()

                len_x = len(Constant(self.detune_width, self.detune1)().pulse)
                mid_index = len(z_pulse) // 2
                for i in range(len_x):
                    if i == 0:
                        z_pulse[mid_index] = self.detune2
                    else:
                        z_pulse[mid_index + i] = self.detune2
                        z_pulse[mid_index - i] = self.detune1
            else:
                # mode2: .....
                amp1 -= self.amp
                amp2 -= self.amp
                def detune_pulse(amp1: float, amp2: float, width: float, **kwargs):
                    if amp2 == 0.0 or amp1 == 0.0 or amp2 * amp1 < 0:
                        pulse = FlatTopGaussian(time=width, amp=amp1, **kwargs)() + FlatTopGaussian(time=width, amp=amp2,
                                                                                                    **kwargs)()
                    elif 0 > amp1 >= amp2 or 0 < amp1 <= amp2:
                        if amp1 == amp2:
                            pulse2 = FlatTopGaussianSide(time=width, amp=amp2, side="right", **kwargs)
                        else:
                            pulse2 = FlatTopGaussianAsymmetric(time=width, amp1=amp2, amp2=amp2 - amp1, **kwargs)
                        pulse = FlatTopGaussianSide(time=width, amp=amp1, **kwargs)() + pulse2()
                    else:
                        pulse = FlatTopGaussianAsymmetric(
                            time=width,
                            amp1=amp1,
                            amp2=amp1 - amp2,
                            side="right",
                            **kwargs
                        )() + FlatTopGaussianSide(time=width, amp=amp2, side="right", **kwargs)()
                    return pulse.pulse

                pulse = detune_pulse(self.detune1, self.detune2, self.detune_width, sigma=1.25, buffer=5)
                start_index = int((len(z_pulse) - len(pulse) / 2))
                z_pulse[start_index: start_index + len(pulse)] = pulse + self.amp

        self._pulse = np.array(z_pulse)
        self.get_raw_pulse()


class FlatTopGaussianSide(FlatTopGaussian):
    def __init__(
        self, time, amp=0, sigma=1.25, buffer=5, is_control=True, side="left", name="Z"
    ):
        super().__init__(time, amp, sigma, buffer, is_control, name)
        self.side = side

    @property
    def parameters(self) -> Dict[str, Any]:
        self._parameters.update(
            {
                "time": self.time,
                "amp": self.amp,
                "sigma": self.sigma,
                "buffer": self.buffer,
                "is_control": self.is_control,
                "name": self.type,
                "side": self.side,
            }
        )
        return self._parameters

    def get_pulse(self):
        fake_time = self.time + self.buffer + 3 * self.sigma
        points = int(fake_time * self.sample_rate)
        actual_points = int(self.time * self.sample_rate)
        pulse_width = points / self.sample_rate
        t_list = np.linspace(0, pulse_width, points + 1)
        z_pulse = np.zeros_like(t_list)

        if self.is_control:
            tc = pulse_width - 2 * self.buffer
            z_pulse = self.formula(self.amp, t_list, self.buffer, tc, self.sigma)

        if self.side == "left":
            z_pulse = z_pulse[: actual_points + 1]
        else:
            z_pulse = z_pulse[points - actual_points:]

        self._pulse = z_pulse
        self.get_raw_pulse()


class FlatTopGaussianAsymmetric(FlatTopGaussianSide):
    def __init__(
        self,
        time,
        amp=0,
        sigma=1.25,
        is_control=True,
        side="left",
        buffer=5,
        amp2=0,
        name="Z",
    ):
        super().__init__(time, amp, sigma, buffer, is_control, side, name)
        self.amp2 = amp2

    @property
    def parameters(self) -> Dict[str, Any]:
        self._parameters.update(
            {
                "time": self.time,
                "amp": self.amp,
                "amp2": self.amp2,
                "sigma": self.sigma,
                "buffer": self.buffer,
                "is_control": self.is_control,
                "name": self.type,
                "side": self.side,
            }
        )
        return self._parameters

    def get_pulse(self):
        if self.amp < self.amp2:
            _low_amp = self.amp
            _high_amp = self.amp2
        else:
            _low_amp = self.amp2
            _high_amp = self.amp

        points = int(self.time * self.sample_rate)
        pulse_width = points / self.sample_rate
        t_list = np.linspace(0, pulse_width, points + 1)
        low_z_pulse = np.zeros_like(t_list)
        high_z_pulse = np.zeros_like(t_list)

        if self.is_control:
            tc = pulse_width - 2 * self.buffer
            low_z_pulse = self.formula(_low_amp, t_list, self.buffer, tc, self.sigma)
            high_z_pulse = self.formula(_high_amp, t_list, self.buffer, tc, self.sigma)
            if _low_amp > 0:
                low_z_pulse += _high_amp - _low_amp
            else:
                high_z_pulse -= _high_amp - _low_amp

        point_num = len(low_z_pulse)
        left = point_num // 2
        right = point_num - left

        if self.side == "left":
            z_pulse = np.hstack((low_z_pulse[:left], high_z_pulse[right - 1:]))
        else:
            z_pulse = np.hstack((high_z_pulse[:left], low_z_pulse[right - 1:]))

        self._pulse = z_pulse
        self.get_raw_pulse()


class Linear(_SimplePulse):
    """Define Linear Pulse class."""

    def __init__(self, time: float, amp: float, rate: float, name: str = "Z"):
        self.rate = rate
        super().__init__(time, amp, name)

    @property
    def parameters(self) -> Dict[str, Any]:
        """Update pulse's parameters."""
        self._parameters.update(
            {
                "time": self.time,
                "amp": self.amp,
                "rate": self.rate,
                "name": self.type,
            }
        )
        return self._parameters

    @staticmethod
    def linear_formula(x, amp, rate):
        """Liner calculate formula."""
        y = amp + rate * x
        return y

    def get_pulse(self):
        """Calculate pulse array data."""
        width = self.time
        points = int(width * self.sample_rate) + 1

        t_arr = np.linspace(0, width, points)
        y_arr = self.linear_formula(t_arr, self.amp, self.rate)
        self._pulse = y_arr
        self.get_raw_pulse()


class Slepian(PulseComponent):
    """Slepian pulse."""

    def __init__(
        self,
        time: float,
        buffer: float,
        lam_list: List[float],
        freq_high_work: float,
        freq_coupler_idle: float,
        glc_idle: float,
        coupler_ac_spectrum: Dict[str, List[float]],
        coupler_ac_branch: str = "right",
        hf_trans_flag: bool = False,
        plc: float = 0.0,
        amp: float = 0.0,  # No use, adjust QubitPair.validate().
        name: str = "Z",
    ):
        """Initial object."""
        self.buffer = buffer
        self.lam_list = lam_list
        self.freq_high_work = freq_high_work
        self.freq_coupler_idle = freq_coupler_idle
        self.glc_idle = glc_idle
        self.coupler_ac_spectrum = coupler_ac_spectrum
        self.coupler_ac_branch = coupler_ac_branch
        self.hf_trans_flag = hf_trans_flag
        self.plc = plc
        super().__init__(time, name)

    def validate_parameters(self) -> None:
        """Validate parameters."""
        start_value, end_value = 4000, 8000
        if self.freq_high_work < start_value or self.freq_high_work > end_value:
            raise PulseError(
                f"The high_bit frequency {self.freq_high_work} maybe set error!"
            )

    @property
    def parameters(self) -> Dict[str, Any]:
        """Update pulse's parameters."""
        self._parameters.update(
            {
                "time": self.time,
                "buffer": self.buffer,
                "lam_list": self.lam_list,
                "freq_high_work": self.freq_high_work,
                "freq_coupler_idle": self.freq_coupler_idle,
                "glc_idle": self.glc_idle,
                "coupler_ac_spectrum": self.coupler_ac_spectrum,
                "coupler_ac_branch": self.coupler_ac_branch,
                "hf_trans_flag": self.hf_trans_flag,
                "plc": self.plc,
                "name": self.type,
            }
        )
        return self._parameters

    def _update_width(self):
        """update width."""
        self.width = self.time

    @staticmethod
    def calculate_coupler_freq(
        t: float,
        theta_i: float,
        lam_list: List[float],
        tp: float,
        freq_high: float,
        glc: float,
    ) -> float:
        """Calculate coupler frequency."""
        theta_list = []
        for idx, lam in enumerate(lam_list):
            n = idx + 1
            theta_n = lam * (1 - np.cos(2 * np.pi * n * t / tp))
            theta_list.append(theta_n)
        theta = theta_i + sum(theta_list)
        freq_coupler = 2 * glc / np.tan(theta) + freq_high
        return freq_coupler

    @staticmethod
    def freq2amp_formula(
        x: float,
        fq_max: float,
        detune: float,
        M: float,
        offset: float,
        d: float,
        branch: str = "right",
        w: float = None,
        g: float = None,
        spectrum_type: str = "standard"
    ):
        """Calculate AC based on frequency."""
        if spectrum_type == "standard":
            x = x
        else:
            x = x - g ** 2 / (x - w)
        alpha = (x + detune) / (detune + fq_max)
        belta = (alpha ** 4 - d ** 2) / (1 - d ** 2)
        if branch == "right":
            amp = np.arccos(np.sqrt(belta)) / (M * np.pi) + offset
        elif branch == "left":
            amp = (np.arccos(-np.sqrt(belta)) - np.pi) / (M * np.pi) + offset
        else:
            amp = np.abs(np.arccos(np.sqrt(belta)) / (M * np.pi)) + offset
        return amp

    @staticmethod
    def amp2freq_formula(
        x: float,
        fq_max: float,
        detune: float,
        M: float,
        offset: float,
        d: float,
    ):
        """Calculate frequency from AC."""
        phi = np.pi * M * (x - offset)
        fq = (fq_max + detune) * np.sqrt(
            np.sqrt(1 + d ** 2 * np.tan(phi) ** 2) * np.abs(np.cos(phi))
        ) - detune
        return fq

    def freq_to_amp(self, freq: float) -> float:
        """Frequency to z_amp."""
        branch = self.coupler_ac_branch
        ac_spectrum = self.coupler_ac_spectrum

        std_spectrum = ac_spectrum.get("standard", [0.0, 0.0, 0.0, 0.0, 0.0])
        fq_max, fc, M, offset, d, *_ = std_spectrum
        if len(std_spectrum) > 0 and fq_max != 0:
            z_amp = self.freq2amp_formula(freq, fq_max, fc, M, 0, d, branch)
            return z_amp
        else:
            spectrum_type = "nonstandard"
            d_list = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
            spectrum_list = [
                ac_spectrum.get("bottom_left", d_list),
                ac_spectrum.get("top", d_list),
                ac_spectrum.get("bottom_right", d_list),
            ]
            z_amp = np.nan
            for spectrum in spectrum_list:
                if spectrum[0] != 0:
                    freq_min = spectrum[-2]
                    freq_max = spectrum[-1]
                    if freq is not None:
                        if freq_min <= freq <= freq_max:
                            res = spectrum[:-4]
                            fq_max, fc, M, offset, d, w, g = res
                            z_amp = self.freq2amp_formula(
                                freq, fq_max, fc, M, 0, d, branch, w, g, spectrum_type
                            )
                            return z_amp
            return z_amp

    def get_pulse(self):
        """Calculate pulse array data."""
        width = self.time
        buffer = self.buffer
        pulse_width = width - 2 * buffer
        if pulse_width < 0:
            pulse_width = 0
            buffer = width / 2

        head = np.zeros(int(buffer * self.sample_rate))
        tail = np.zeros(int(buffer * self.sample_rate))
        t_pulse_arr = np.linspace(0, pulse_width, int(pulse_width * self.sample_rate) + 1)
        z_pulse_arr = np.zeros_like(t_pulse_arr)
        hf_arr = np.zeros_like(t_pulse_arr)
        cf_arr = np.zeros_like(t_pulse_arr)

        if pulse_width > 0:
            theta_i = np.arctan(
                2 * self.glc_idle / (self.freq_coupler_idle - self.freq_high_work)
            )
            for i, t in enumerate(t_pulse_arr):
                # TODO, Optimize get freq_high and glc
                if self.hf_trans_flag is True:
                    freq_high = self.freq_high_work
                    glc = self.glc_idle
                else:
                    freq_high = self.freq_high_work
                    glc = self.glc_idle

                c_freq = self.calculate_coupler_freq(
                    t, theta_i, self.lam_list, pulse_width, freq_high, glc
                )
                z_amp = self.freq_to_amp(c_freq)
                if np.isnan(z_amp):
                    z_amp = z_pulse_arr[i-1] if i > 0 else z_pulse_arr[i]
                hf_arr[i] = freq_high
                cf_arr[i] = c_freq
                z_pulse_arr[i] = z_amp

        points = int(width * self.sample_rate) + 1
        slice_idx = points - (len(head) + len(tail))
        if slice_idx >= 0:
            self._pulse = np.hstack((head, z_pulse_arr[:slice_idx], tail))
        else:
            self._pulse = np.hstack((head, tail[1:]))
        self.get_raw_pulse()
