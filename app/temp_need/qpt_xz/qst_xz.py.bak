# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/24
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy

import numpy as np
import qutip as qp

from pyQCat.analysis.tomography_analysis import (
    ProcessTomographyAnalysis,
    tensor_combinations,
    init_qpt,
    qpt,
    qpt_mle,
    gen_ideal_chi_matrix,
)
from pyQCat.experiments import StateTomography, ProcessTomography
from pyQCat.log import pyqlog
from pyQCat.structures import Options, QDict
from pyQCat.tools import nm_minimize
from pyQCat.tools.utilities import amp_to_bf_freq


class StateTomographyXZ(StateTomography):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("drag_params1", dict)
        options.set_validator("drag_params2", dict)
        options.set_validator("z_params1", dict)
        options.set_validator("z_params2", dict)
        options.drag_params1 = {
            "time": 20,
            "offset": 5,
            "amp": 0.7,
            "detune": 0,
            "freq": 0,
            "phase": 0,
            "alpha": 1,
            "delta": -240,
        }
        options.drag_params2 = {
            "time": 20,
            "offset": 5,
            "amp": 0.7,
            "detune": 0,
            "freq": 0,
            "phase": 0,
            "alpha": 1,
            "delta": -240,
        }
        options.z_params1 = {"amp": 0.01, "dt": 0, "sigma": 0.01}
        options.z_params2 = {"amp": 0.01, "dt": 0, "sigma": 0.01}
        return options

    def _check_options(self):
        super()._check_options()

        self.run_options.gate_bucket.xz_gate = QDict(drag_params=[], flat_params=[])
        for i in range(2):
            drag_params = QDict(**self.experiment_options[f"drag_params{i+1}"])
            z_params = QDict(**self.experiment_options[f"z_params{i+1}"])
            if drag_params.time > 0:
                freq = amp_to_bf_freq(
                    self.qubit, z_params.amp
                )  # notes: must use amp_to_bf_freq from amp_tp_bf.txt
                drag_params.freq = freq
                z_time = drag_params.time + 2 * drag_params.offset
                z_buffer = drag_params.offset - z_params.dt

                self.run_options.gate_bucket.xz_gate.drag_params.append(
                    drag_params.to_dict()
                )
                self.run_options.gate_bucket.xz_gate.flat_params.append(
                    {
                        "time": z_time,
                        "amp": z_params.amp,
                        "sigma": z_params.sigma,
                        "buffer": z_buffer,
                    }
                )

    def _set_z_pulses(self):
        pre_gate = self.experiment_options.pre_gate
        gate_bucket = self.run_options.gate_bucket

        gates = []
        for gate in self.experiment_options.base_gate:
            pre_gates = deepcopy(pre_gate)
            pre_gates.append(gate)
            gates.append(pre_gates)

        z_pulses = []
        for gate in gates:
            z_pulses.append(gate_bucket.get_z_pulse(self.qubit, gate))

        self.play_pulse("Z", self.qubit, z_pulses)


class ProcessTomographyXZ(ProcessTomography):
    _sub_experiment_class = StateTomographyXZ

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("goal_gate", ["XZ"])
        options.goal_gate = "XZ"
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("opt_ab", bool)
        options.set_validator("nm_params", dict)
        options.opt_ab = False
        options.nm_params = Options(
            ftarget=-1e8,
            maxiter=None,
            maxfev=None,
            disp=True,
            return_all=True,
            initial_simplex=None,
            xatol=1e-4,
            fatol=1e-4,
            adaptive=False,
            nonzdelt=[0.01, 0.01],
            step=[0.001, 0.001],
            bound=[[-2 * np.pi, 2 * np.pi], [-2 * np.pi, 2 * np.pi]]
        )
        return options

    def _run_analysis(
        self, x_data, analysis_class
    ):
        return super()._run_analysis(x_data, ProcessTomographyXZAnalysis)


class ProcessTomographyXZAnalysis(ProcessTomographyAnalysis):
    """Analysis for process tomography experiments."""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        **Analysis Options**:

            - **titles (List)** - Subplot title, default is
              ``[ideal-real, ideal-image, exp-real, exp-image]``.

            - **labels (List)** - Axis labels, default is ``[I, X, Y, Z]``.

            - **result_parameters (List)** - Expect to extract the ``exp_chi_matrix``,
              ``ideal_chi_matrix`` , ``fidelity`` and ``process_fidelity``.

        Returns:
            Process tomography experiment analysis options.
        """
        options = super()._default_options()
        options.opt_ab = False
        options.nm_params = Options(
            ftarget=-1e8,
            maxiter=None,
            maxfev=None,
            disp=True,
            return_all=True,
            initial_simplex=None,
            xatol=1e-4,
            fatol=1e-4,
            adaptive=False,
            nonzdelt=[0.01, 0.01],
            step=[0.001, 0.001],
            bound=[[-2 * np.pi, 2 * np.pi], [-2 * np.pi, 2 * np.pi]]
        )
        return options

    def _tomography(self):
        """Tomography process.

        We do as follows:

            - calculate ideal chi matrix
            - calculate exp chi matrix
            - calculate fidelity
        """
        sigma_basis = self.options.sigma_basis
        qubit_nums = self.options.qubit_nums
        sigma_basis = tensor_combinations(sigma_basis, repeat=qubit_nums)
        base_ops = self.analysis_datas.base_ops
        qst_base_ops = self.analysis_datas.qst_base_ops
        qst_base_ops = tensor_combinations(qst_base_ops, repeat=qubit_nums)

        options = QDict(
            use_mle=self.options.use_mle,
            sigma_basis=sigma_basis,
            base_ops=base_ops,
            density_matrix_list=self.analysis_datas.density_matrix,
            qubit_nums=qubit_nums,
            qst_base_ops=qst_base_ops,
            goal_gate_matrix=self.analysis_datas.goal_gate_matrix,
            measure_results=self.analysis_datas.measure_results,
        )
        self.options.nm_base_options = options
        self.options.count = 0
        if not self.options.opt_ab:
            f, fp, em, im = cal_qpt_fidelity(input_data=None, options=options)
        else:
            res, sim = nm_minimize(
                self.nm_opt_func,
                x0=[0, 0],
                ftarget=self.options.nm_params.ftarget,
                nonzdelt=self.options.nonzdelt,
                maxiter=self.options.nm_params.maxiter,
                maxfev=self.options.nm_params.maxfev,
                disp=self.options.nm_params.disp,
                return_all=self.options.nm_params.return_all,
                xatol=self.options.nm_params.xatol,
                fatol=self.options.nm_params.fatol,
                adaptive=self.options.nm_params.adaptive,
                step=self.options.step,
                bound=self.options.bound,
            )
            print(res)
            f, fp, em, im = self.options.nm_res

        self.analysis_datas["ideal_chi_matrix"] = im
        self.analysis_datas["exp_chi_matrix"] = em
        self.analysis_datas.fidelity = f
        self.analysis_datas.process_fidelity = fp

    def nm_opt_func(self, parameters: list, *args):
        f, fp, em, im = cal_qpt_fidelity(
            parameters, deepcopy(self.options.nm_base_options)
        )
        self.options.nm_res = [f, fp, em, im]
        pyqlog.log("EXP", f"NM FUNC - {self.options.count}: {parameters} | {-fp}")
        self.options.count += 1
        return -fp


def cal_qpt_fidelity(input_data, options) -> tuple:
    if input_data is not None:
        a, b = input_data
        goal_gate_matrix = np.array(
            [[0, np.exp(1j * a)], [np.exp(1j * b), 0]], dtype=complex
        )
    else:
        goal_gate_matrix = options.goal_gate_matrix

    sigma_ops = init_qpt(options.sigma_basis)
    ideal_chi_matrix, *_ = gen_ideal_chi_matrix(
        goal_gate_matrix, sigma_ops, options.base_ops
    )

    mid = int(len(options.density_matrix_list) / 2)
    l_rhos = options.density_matrix_list[:mid]
    r_rhos = options.density_matrix_list[mid:]

    if options.use_mle:
        exp_chi_matrix = qpt_mle(
            list(
                options.measure_results[mid * (3**options.qubit_nums) :].reshape(
                    4**options.qubit_nums,
                    3**options.qubit_nums,
                    2**options.qubit_nums,
                )
            ),
            l_rhos,
            options.qst_base_ops,
            options.sigma_basis,
            sigma_ops,
        )
    else:
        exp_chi_matrix = qpt(l_rhos, r_rhos, sigma_ops)

    fidelity = qp.fidelity(qp.Qobj(exp_chi_matrix), qp.Qobj(ideal_chi_matrix))
    process_fidelity = qp.process_fidelity(
        qp.Qobj(exp_chi_matrix), qp.Qobj(ideal_chi_matrix)
    )

    print(process_fidelity.real)
    return fidelity, process_fidelity.real, exp_chi_matrix, ideal_chi_matrix
