# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2020 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/10/11
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

import dataclasses
from abc import ABC, abstractmethod
from copy import deepcopy, copy
from typing import Optional, Tuple, Union, List, Dict

import numpy as np

from ..errors import PulseError
from ..log import pyqlog
from ..pulse.pulse_function import zero_pulse, pi_pulse, half_pi_pulse
from ..pulse.pulse_lib import PulseComponent, Constant, Drag, FlatTopGaussian
from ..pulse_adjust import params_to_pulse
from ..qubit import Qubit


class SingleQgate(ABC):
    @abstractmethod
    def __init__(
        self,
        matrix: np.matrix,
        theta: float = None,
        axis_direction: Optional[Tuple] = None,
    ):
        self._matrix = matrix
        self._name = ""
        self._theta = theta
        self._axis_direction = axis_direction

    def __repr__(self) -> str:
        return f"{self._name}, matrix={self._matrix})"

    @abstractmethod
    def to_pulse(self, qubit: Qubit) -> PulseComponent:
        """Convert quantum logic gate to pulse Object."""
        raise NotImplementedError

    @property
    def matrix(self):
        return self._matrix

    @property
    def name(self):
        return self._name

    @property
    def theta(self):
        return self._theta


class Xgate(SingleQgate):
    def __init__(self):
        matrix = np.mat([[0, 1], [1, 0]])
        super().__init__(matrix, np.pi, (1, 0, 0))
        self._name = "X"

    def to_pulse(self, qubit: Qubit) -> PulseComponent:
        pulse = pi_pulse(qubit)
        return pulse()


class Ygate(SingleQgate):
    def __init__(self):
        matrix = np.mat([[0, -1j], [1j, 0]])
        super().__init__(matrix, np.pi, (0, 1, 0))
        self._name = "Y"

    def to_pulse(self, qubit: Qubit) -> PulseComponent:
        pulse = pi_pulse(qubit)
        pulse.phase = np.pi / 2
        return pulse()


class RXgate(SingleQgate):
    def __init__(self, theta: float):
        self._theta = theta
        matrix = np.mat(
            [
                [np.cos(self._theta / 2), -1j * np.sin(self._theta / 2)],
                [-1j * np.sin(self._theta / 2), np.cos(self._theta / 2)],
            ]
        )
        super().__init__(matrix)
        self._name = "RX"

    def to_pulse(self, qubit: Qubit) -> PulseComponent:
        """todo Optimized"""


class RYgate(SingleQgate):
    def __init__(self, theta: float):
        self._theta = theta
        matrix = np.mat(
            [
                [np.cos(self._theta / 2), -1j * np.sin(self._theta / 2)],
                [-1j * np.sin(self._theta / 2), np.cos(self._theta / 2)],
            ]
        )
        super().__init__(matrix)
        self._name = "RY"

    def to_pulse(self, qubit: Qubit) -> PulseComponent:
        """todo Optimized"""


class Igate(SingleQgate):
    def __init__(self):
        matrix = np.mat([[1, 0], [0, 1]])
        super().__init__(matrix)
        self._name = "I"

    def __repr__(self):
        return self._name

    def to_pulse(self, qubit: Qubit) -> PulseComponent:
        pulse = zero_pulse(qubit)
        return pulse()


# -----------------------------------------------------------------------------
# Rphi Gate (Qcloud link)
# -----------------------------------------------------------------------------
class Rphi_gate(SingleQgate):
    def __init__(self, phase: float, theta=np.pi / 2):
        matrix = np.mat(
            [
                [np.cos(theta / 2), -1j * np.sin(theta / 2) * np.exp(-1j * phase)],
                [-1j * np.sin(theta / 2) * np.exp(1j * phase), np.cos(theta / 2)],
            ]
        )
        self.phase = phase
        self._theta = theta
        super().__init__(matrix, theta)
        self._name = f"R_φ{np.rad2deg(phase)}°_θ{np.rad2deg(theta)}°"

    def to_pulse(self, qubit: Qubit) -> PulseComponent:
        if self._theta == np.pi / 2:
            pulse = half_pi_pulse(qubit)
        elif self._theta == np.pi:
            pulse = pi_pulse(qubit)
        else:
            raise PulseError(
                f"θ only support π({np.pi}) and π/2({np.pi / 2}) now!, {self._theta} is invalid."
            )

        pulse.phase = self.phase

        return pulse


# -----------------------------------------------------------------------------
# Multi Qubit Gate (Qcloud link)
# -----------------------------------------------------------------------------
class CPhaseGate:
    def __init__(self, accumulation_phase: float = np.pi):
        self._qh = None
        self._ql = None
        self._parking_qubits = None
        self._gate_infos = None
        self._matrix = np.diag([1, 1, 1, np.exp(1j * accumulation_phase)])

        self._width = None

        self.pulse_map = {}
        self.phase_map = {}
        self.bind_flag = False
        self.compensate_z_zero_pulse = None
        self.compensate_xy_zero_pulse = None

        self.accumulation_phase = accumulation_phase

    @property
    def matrix(self):
        return self._matrix

    @property
    def width(self):
        return self._width

    @property
    def parking_qubits(self):
        return self._parking_qubits

    def bind_gate(self, pair, qubits=None, qh=None, ql=None):
        pair.validate(qubits=qubits, qh=qh, ql=ql)
        self._ql = pair.ql
        self._qh = pair.qh
        self._width = pair.width()
        self._parking_qubits = list(pair.parking_bits)
        self._gate_infos = pair.gate_params()
        self.pulse_map = {}
        self.phase_map = {}

        for key, value in self._gate_infos.items():
            value.update({"time": self.width})
            self.pulse_map[key] = params_to_pulse(**value)()
            self.phase_map[key] = value.get("phase")

        self.compensate_z_zero_pulse = Constant(self.width, 0)()
        self.compensate_xy_zero_pulse = Constant(self.width, 0, name="XY")()

        self.bind_flag = True

        return self

    def to_pulse(self, qubit: Qubit):
        if qubit.name not in self.pulse_map:
            raise PulseError(
                f"{qubit} not in this conditional phase gate, please check!"
            )
        return self.pulse_map[qubit.name]

    def get_phase(self, qubit):
        if qubit.name not in self.phase_map:
            raise PulseError(
                f"{qubit} not in this conditional phase gate, please check!"
            )
        return self.phase_map.get(qubit.name)


@dataclasses.dataclass(frozen=True)
class GateCollection:
    single_gate_map = {
        "I": Igate(),
        "X": Rphi_gate(phase=0, theta=np.pi),
        "Y": Rphi_gate(phase=np.pi / 2, theta=np.pi),
        "X/2": Rphi_gate(phase=0),
        "-X/2": Rphi_gate(phase=np.pi),
        "Y/2": Rphi_gate(phase=np.pi / 2),
        "-Y/2": Rphi_gate(phase=-np.pi / 2),
        "-X": Rphi_gate(phase=np.pi, theta=np.pi),
        "-Y": Rphi_gate(phase=-np.pi / 2, theta=np.pi),
    }

    single_x2_gate_map = {
        "X/2": Rphi_gate(phase=0),
        "Xp/2": Rphi_gate(phase=np.pi / 4),
        "Y/2": Rphi_gate(phase=np.pi / 2),
        "Yp/2": Rphi_gate(phase=3 * np.pi / 4),
        "-X/2": Rphi_gate(phase=np.pi),
        "-Xp/2": Rphi_gate(phase=5 * np.pi / 4),
        "-Y/2": Rphi_gate(phase=-np.pi / 2),
        "-Yp/2": Rphi_gate(phase=7 * np.pi / 4),
    }

    double_gate_map = {"CZ": CPhaseGate()}

    @classmethod
    def gate_infos(cls):
        return list(cls.single_gate_map.keys()) + list(cls.double_gate_map.keys())

    @classmethod
    def single_gate_infos(cls):
        return list(cls.single_gate_map.keys())

    @classmethod
    def double_gate_infos(cls):
        return list(cls.double_gate_map.keys())

    @classmethod
    def single_x2_gate_infos(cls):
        return list(cls.single_x2_gate_map.keys())


class GateBucket:
    pauli_matrix = {
        "I": np.eye(2, dtype=complex),
        "X": np.array([[0, 1], [1, 0]], dtype=complex),
        "Y": np.array([[0, -1j], [1j, 0]], dtype=complex),
        "Z": np.array([[1, 0], [0, -1]], dtype=complex),
    }

    def __init__(self):
        self._xy_pulse = {}
        self._z_pulse = {}
        self.register_qubits = []

        self.gate_collector = GateCollection()

        self.cz_gate = None
        self.xz_gate = None

    def get_matrix(self, gate_name: str):
        if gate_name in self.gate_collector.single_gate_map:
            return self.gate_collector.single_gate_map.get(gate_name).matrix
        elif gate_name in self.gate_collector.single_x2_gate_map:
            return self.gate_collector.single_x2_gate_map.get(gate_name).matrix
        elif gate_name in self.gate_collector.double_gate_map:
            return self.gate_collector.double_gate_map.get(gate_name).matrix
        elif gate_name == 'XZ':
            return self.gate_collector.single_gate_map.get("X").matrix
        else:
            raise PulseError(f"{gate_name} not in gate collector!")

    def bind_single_gates(self, qubit: Qubit):
        self._bind_single_gate(qubit, self.gate_collector.single_gate_map)

    def bind_single_x2_gates(self, qubit: Qubit):
        self._bind_single_gate(qubit, self.gate_collector.single_x2_gate_map)

    def _bind_single_gate(self, qubit, gate_map: Dict):
        if isinstance(qubit, Qubit):
            if qubit.name not in self.register_qubits:
                for name, gate in gate_map.items():
                    gate_describe = f"{qubit.name}-{name}"
                    xy_pulse = gate.to_pulse(qubit)
                    z_pulse = Constant(xy_pulse.width, 0)
                    self._xy_pulse[gate_describe] = xy_pulse()
                    self._z_pulse[gate_describe] = z_pulse()

                    if "zero_pulse" not in self._z_pulse:
                        self._z_pulse["zero_pulse"] = deepcopy(z_pulse)

                    if "zero_pulse" not in self._xy_pulse:
                        self._xy_pulse["zero_pulse"] = Constant(
                            xy_pulse.width, 0, name="XY"
                        )()

                self.register_qubits.append(qubit.name)
        else:
            pyqlog.warning(f"{qubit} is not a Qubit")

    def bind_cz_gates(self, pair, qubits=None):
        name = pair.name
        if name not in self.register_qubits:
            cz_gate = deepcopy(self.gate_collector.double_gate_map.get("CZ"))
            cz_gate.bind_gate(pair, qubits=qubits)
            self.cz_gate = cz_gate

            for bit_name, pulse in cz_gate.pulse_map.items():
                gate_describe = f"{bit_name}-CZ"
                self._z_pulse[gate_describe] = pulse
                offset_pulse = deepcopy(cz_gate.compensate_xy_zero_pulse)
                self._xy_pulse[gate_describe] = offset_pulse
            self.register_qubits.append(name)

    def get_xy_pulse(self, qubit, gate_name: Union[List, str]):
        if isinstance(gate_name, str):
            return self._get_xy_pulse(qubit, gate_name)

        pulse = Constant(0, 0, name="XY")()
        if "CZ" not in gate_name:
            for gate in gate_name:
                pulse += self._get_xy_pulse(qubit, gate)
            return pulse
        else:
            # Eliminate the phase accumulated by the virtual Z-gate
            sum_phase = 0

            for gate in gate_name:
                cur_pulse = self._get_xy_pulse(qubit, gate)
                if gate not in ["I", "CZ"]:
                    cur_pulse = copy(cur_pulse)
                    cur_pulse.phase -= sum_phase
                    pulse += cur_pulse()
                else:
                    pulse += cur_pulse

                if gate == "CZ":
                    sum_phase += self.cz_gate.get_phase(qubit)

            return pulse

    def get_z_pulse(self, qubit, gate_name: Union[List, str]):
        if isinstance(gate_name, str):
            return self._get_z_pulse(qubit, gate_name)
        else:
            pulse = Constant(0, 0)()
            for gate in gate_name:
                pulse += self._get_z_pulse(qubit, gate)
            return pulse

    def _get_xy_pulse(self, qubit, gate_name: str):
        if gate_name == 'XZ':
            pulse = Constant(0, 0, name="XY")
            for pm in self.xz_gate.drag_params:
                pulse += Drag(**pm)()
            return pulse
        gate_key = f"{qubit.name}-{gate_name}"
        if gate_key in self._xy_pulse:
            return self._xy_pulse.get(gate_key)
        return self._xy_pulse.get("zero_pulse")

    def _get_z_pulse(self, qubit, gate_name: str):
        if gate_name == 'XZ':
            pulse = Constant(0, 0)
            for pm in self.xz_gate.flat_params:
                pulse += FlatTopGaussian(**pm)()
            return pulse
        gate_key = f"{qubit.name}-{gate_name}"
        if gate_key in self._z_pulse:
            return self._z_pulse.get(gate_key)
        return self._z_pulse.get("zero_pulse")


CLIFFORD_GATE_SET = [
    ["I"],
    ["X"],
    ["Y"],
    ["Y", "X"],
    ["X/2"],
    ["-X/2"],
    ["Y/2"],
    ["-Y/2"],
    ["-X/2", "Y/2", "X/2"],
    ["-X/2", "-Y/2", "X/2"],
    ["X", "-Y/2"],
    ["X", "Y/2"],
    ["Y", "X/2"],
    ["Y", "-X/2"],
    ["X/2", "Y/2", "X/2"],
    ["-X/2", "Y/2", "-X/2"],
    ["Y/2", "X/2"],
    ["Y/2", "-X/2"],
    ["-Y/2", "X/2"],
    ["-Y/2", "-X/2"],
    ["-X/2", "-Y/2"],
    ["X/2", "-Y/2"],
    ["-X/2", "Y/2"],
    ["X/2", "Y/2"],
]
INTERLEAVED_GATE_1Q = {
    "I": CLIFFORD_GATE_SET.index(["I"]),
    "X": CLIFFORD_GATE_SET.index(["X"]),
    "Y": CLIFFORD_GATE_SET.index(["Y"]),
    "X/2": CLIFFORD_GATE_SET.index(["X/2"]),
    "Y/2": CLIFFORD_GATE_SET.index(["Y/2"]),
    "-X/2": CLIFFORD_GATE_SET.index(["-X/2"]),
    "-Y/2": CLIFFORD_GATE_SET.index(["-Y/2"]),
}
GATE_2Q = [
    [["I"], ["I"]],
    [["X"], ["I"]],
    [["I"], ["X"]],
    [["Y"], ["I"]],
    [["I"], ["Y"]],
    [["X/2"], ["I"]],
    [["-X/2"], ["I"]],
    [["I"], ["X/2"]],
    [["I"], ["-X/2"]],
    [["Y/2"], ["I"]],
    [["-Y/2"], ["I"]],
    [["I"], ["Y/2"]],
    [["I"], ["-Y/2"]],
    [["CZ"], ["CZ"]],
]

S1_SET = [["I"], ["Y/2", "X/2"], ["-X/2", "-Y/2"]]

S1_X2_SET = [["X/2"], ["X/2", "Y/2", "X/2"], ["-Y/2"]]

S1_Y2_SET = [["Y/2"], ["Y", "X/2"], ["-X/2", "-Y/2", "X/2"]]
