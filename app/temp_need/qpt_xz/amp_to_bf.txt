def amp_to_bf_freq(physical_unit, z_amp: float = None, **kwargs):

    z_amp += physical_unit.idle_point

    if not physical_unit.tunable:
        freq = physical_unit.drive_freq
        pyqlog.warning(f"qubit={physical_unit} is not tunable, return freq={freq}")
        return freq

    ac_spectrum = copy.deepcopy(physical_unit.ac_spectrum)
    std_spectrum = ac_spectrum.standard
    fq_max, fc, M, offset, d, *_ = std_spectrum

    if len(std_spectrum) > 0 and fq_max != 0:
        freq = amp2freq_formula(z_amp , fq_max, fc, M, 0, d)
        baseband_freq = physical_unit.XYwave.baseband_freq
        bf = np.round(freq - physical_unit.drive_freq + baseband_freq, 3)
        if bf < 800 or bf > 1300:
            raise ExperimentFieldError("", f"Sweep baseband freq muse limit in [800, 1300]!")
        pyqlog.debug(f"Sweep baseband freq: {bf}")

        return bf
    else:
        spectrum_list = [
            ac_spectrum.bottom_left,
            ac_spectrum.top,
            ac_spectrum.bottom_right,
        ]
        spectrum_type_list = ["bottom_left", "top", "bottom_right"]
        for i, spectrum in enumerate(spectrum_list):
            if spectrum[0] != 0:
                amp_min = spectrum[-4]
                amp_max = spectrum[-3]
                if z_amp is not None:
                    if amp_min <= z_amp <= amp_max:
                        res = spectrum[:-4]
                        res[3] = 0
                        freq = calculate_freq(spectrum_type_list[i], z_amp, res)
                        baseband_freq = physical_unit.XYwave.baseband_freq
                        bf = np.round(freq - physical_unit.drive_freq + baseband_freq, 3)
                        if bf < 800 or bf > 1300:
                            raise ExperimentFieldError("self.label", f"Sweep baseband freq muse limit in [800, 1300]!")
                        pyqlog.debug(f"Sweep baseband freq: {bf}")
                        return bf
            else:
                continue
        else:
            raise ExperimentFieldError(
                "validate_ac_spectrum",
                f"qubit={physical_unit}, spectrum_list={spectrum_list}，"
                f"z_amp={z_amp} is out of scope!",
            )