# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/02/01
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy

import numpy as np

from pyQCat.analysis.library.xeb_analysis import XEBAnalysis, calculate_xeb_fidelity
from pyQCat.experiments import XEBSingle
from pyQCat.experiments.composite.nm_base import NMBase
from pyQCat.log import pyqlog
from pyQCat.structures import QDict, Options
from pyQCat.tools import nm_minimize
from pyQCat.tools.utilities import ea_optimize, amp_to_bf_freq


class XEBSingleXZ(XEBSingle):

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("drag_params1", dict)
        options.set_validator("drag_params2", dict)
        options.set_validator("z_params1", dict)
        options.set_validator("z_params2", dict)
        options.set_validator("goal_gate", ["XZ"])
        options.goal_gate = "XZ"
        options.drag_params1 = {
            "time": 20,
            "offset": 5,
            "amp": 0.7,
            "detune": 0,
            "freq": 0,
            "phase": 0,
            "alpha": 1,
            "delta": -240,
        }
        options.drag_params2 = {
            "time": 20,
            "offset": 5,
            "amp": 0.7,
            "detune": 0,
            "freq": 0,
            "phase": 0,
            "alpha": 1,
            "delta": -240,
        }
        options.z_params1 = {"amp": 0.01, "dt": 0, "sigma": 0.01}
        options.z_params2 = {"amp": 0.01, "dt": 0, "sigma": 0.01}
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("opt_ab", bool)
        options.set_validator("nm_params", dict)
        options.set_validator("opt_mode", ["nm", "de"])
        options.opt_ab = False
        options.opt_mode = "nm"
        options.nm_params = Options(
            ftarget=-1e8,
            maxiter=None,
            maxfev=None,
            disp=True,
            return_all=True,
            initial_simplex=None,
            xatol=1e-4,
            fatol=1e-4,
            adaptive=False,
            nonzdelt=0.01,
            nm_step=0.01,
        )
        return options

    def _check_options(self):
        self.run_options.gate_bucket.xz_gate = QDict(drag_params=[], flat_params=[])
        for i in range(2):
            drag_params = QDict(**self.experiment_options[f"drag_params{i+1}"])
            z_params = QDict(**self.experiment_options[f"z_params{i+1}"])
            if drag_params.time > 0:
                freq = amp_to_bf_freq(self.qubit, z_params.amp)
                drag_params.freq = freq
                z_time = drag_params.time + 2 * drag_params.offset
                z_buffer = drag_params.offset - z_params.dt

                self.run_options.gate_bucket.xz_gate.drag_params.append(
                    drag_params.to_dict()
                )
                self.run_options.gate_bucket.xz_gate.flat_params.append(
                    {
                        "time": z_time,
                        "amp": z_params.amp,
                        "sigma": z_params.sigma,
                        "buffer": z_buffer,
                    }
                )
        super()._check_options()

    def _transform_pulse(self):
        temp_struct = self.run_options.temp_struct

        xy_pulse_list = []
        z_pulse_list = []
        for struct in temp_struct:
            xy_pulse = self.pulse_from_struct(struct, self.qubit)
            z_pulse = self.pulse_from_struct(struct, self.qubit, mode="Z")
            xy_pulse_list.append(xy_pulse)
            z_pulse_list.append(z_pulse)

        self.run_options.xy_pulse_map[self.qubit] = xy_pulse_list
        self.run_options.z_pulse_map[self.qubit] = z_pulse_list

    def _metadata(self):
        """Set XEB experiment metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "gate_bucket": self.run_options.gate_bucket,
            "temp_struct": self.run_options.temp_struct,
            "dirs": self.file.dirs
        }
        return metadata

    def _special_run_analysis(self):
        self._run_analysis(x_data=self.run_options.x_data, analysis_class=XEBAnalysis)


class XEBNMAnalysis(XEBAnalysis):
    """Analysis for process tomography experiments."""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        **Analysis Options**:

            - **titles (List)** - Subplot title, default is
              ``[ideal-real, ideal-image, exp-real, exp-image]``.

            - **labels (List)** - Axis labels, default is ``[I, X, Y, Z]``.

            - **result_parameters (List)** - Expect to extract the ``exp_chi_matrix``,
              ``ideal_chi_matrix`` , ``fidelity`` and ``process_fidelity``.

        Returns:
            Process tomography experiment analysis options.
        """
        options = super()._default_options()
        options.opt_ab = False
        options.opt_mode = "nm"
        options.nm_params = Options(
            ftarget=-1e8,
            maxiter=None,
            maxfev=None,
            disp=True,
            return_all=True,
            initial_simplex=None,
            xatol=1e-4,
            fatol=1e-4,
            adaptive=False,
            nonzdelt=0.01,
            nm_step=0.01,
        )
        options.de_options = {
            "NIND": 30,  # The number of individuals in the population, MATLAB gave 15 at the time.
            # Take a look at the npj single-bit gate and give it a little more,
            # because the single-bit DAG may not run through and there will be losses.
            "MAXGEN": 20,  # Maximum number of genetic generations
            "mutF": 0.7,  # Parameter F in differential evolution
            "XOVR": 0.7,  # Cross-recombination probability
            "init_population_path": None,
        }  # mutF=0.5/0.7, XOVR=0.7

        return options

    def nm_opt_func(self, parameters: list, *args):
        xeb_fidelity = self._calculate_xeb_fidelity(parameters)
        fp = -float(np.mean(xeb_fidelity))
        pyqlog.log("EXP", f"NM FUNC - {self.options.count}: {parameters} | {fp}")
        self.options.count += 1
        return fp

    def _calculate_xeb_fidelity(self, parameters):
        a, b = parameters
        goal_gate_matrix = np.array(
            [[0, np.exp(1j * a)], [np.exp(1j * b), 0]], dtype=complex
        )
        gate_bucket = self.experiment_data.metadata.process_meta.get("gate_bucket")
        temp_struct = self.experiment_data.metadata.process_meta.get("temp_struct")
        matrix_list = []
        for struct in temp_struct:
            matrix = np.eye(2)
            for gate in struct.gates:
                if gate == "XZ":
                    matrix = np.dot(goal_gate_matrix, matrix)
                elif gate == "XR":
                    continue
                else:
                    matrix = np.dot(gate_bucket.get_matrix(gate), matrix)
            matrix = np.dot(struct.su2_matrix, matrix)
            matrix_list.append(matrix)

        return calculate_xeb_fidelity(self.options.p_meas, matrix_list)

    def _pre_operation(self):
        super()._pre_operation()

        if self.options.opt_ab is True:

            if self.options.opt_mode == "nm":
                res, sim = nm_minimize(
                    self.nm_opt_func,
                    x0=[0.05, 0.05],
                    ftarget=self.options.nm_params.ftarget,
                    nonzdelt=self.options.nm_params.nonzdelt,
                    maxiter=self.options.nm_params.maxiter,
                    maxfev=self.options.nm_params.maxfev,
                    disp=self.options.nm_params.disp,
                    return_all=self.options.nm_params.return_all,
                    xatol=self.options.nm_params.xatol,
                    fatol=self.options.nm_params.fatol,
                    adaptive=self.options.nm_params.adaptive,
                    step=[self.options.nm_params.nm_step, self.options.nm_params.nm_step],
                    bound=[[-np.pi, np.pi], [-np.pi, np.pi]],
                )
                fidelity_list = self._calculate_xeb_fidelity(list(res.x))
            else:
                res = ea_optimize(
                    opt_keys=["phase_a", "phase_b"],
                    opt_iniv_v=[0.05, 0.05],
                    opt_params=self.options.de_options,
                    args_bounds=[[-np.pi, np.pi], [-np.pi, np.pi]],
                    file_path=self.experiment_data.metadata.process_meta.get("dirs"),
                    cost_func=self.nm_opt_func
                )
                result = res.get("Vars")[0]
                fidelity_list = self._calculate_xeb_fidelity(result)

            self.experiment_data.y_data["F_xeb"] = np.array(fidelity_list)


class NMXEBSingleXZ(NMBase):
    _sub_experiment_class = XEBSingleXZ

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.input_data = {
            "amp1": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.001,
                "bound": [-1, 1],
                "nonzdelt": 0.05,
            },
            "amp2": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.001,
                "bound": [-1, 1],
                "nonzdelt": 0.05,
            },
            "phase": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.001,
                "bound": [-1, 1],
                "nonzdelt": 0.05,
            },
        }

        return options

    def _check_options(self):
        super()._check_options()
        init_v = []
        for k, v in self.experiment_options.input_data.items():
            if v.get("is_opt") is True:
                iv = v.get("init_v")
                init_v.append(iv)
        self.set_run_options(init_v=init_v)

    def _execute_exp(self, parameters):
        exp = deepcopy(self.child_experiment)

        self.experiment_options.child_exp_options["drag_params1"][
            "amp"
        ] = parameters[0]
        self.experiment_options.child_exp_options["drag_params2"][
            "amp"
        ] = parameters[1]
        self.experiment_options.child_exp_options["drag_params2"][
            "phase"
        ] = parameters[2]

        self._run_exp(exp)

        return -float(np.mean(exp.analysis.results.f_xeb.value))
