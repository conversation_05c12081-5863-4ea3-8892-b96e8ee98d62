# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/02/01
# __author:       <PERSON><PERSON><PERSON>

from copy import deepcopy

import numpy as np
import math

from pyQCat.analysis import ParameterRepr
from pyQCat.analysis.library.xeb_analysis import XEBAnalysis, calculate_xeb_fidelity
from pyQCat.experiments import XEBSingle
from pyQCat.experiments.composite.nm_base import NMBase
from pyQCat.gate import Rphi_gate
from pyQCat.log import pyqlog
from pyQCat.pulse import Constant
from pyQCat.qubit import Qubit
from pyQCat.structures import QDict, Options
from pyQCat.tools import nm_minimize
from pyQCat.tools.utilities import ea_optimize


class XEBSingleXZ(XEBSingle):

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()
        options.set_validator("drag_params1", dict)
        options.set_validator("drag_params2", dict)
        options.set_validator("z_params1", dict)
        options.set_validator("z_params2", dict)
        options.set_validator("goal_gate", ["XZ", None])
        options.goal_gate = "XZ"

        options.drag_params1 = {
            "time": 20,
            "offset": 2,
            "amp": 0.38,
            "detune": 0,
            "freq": 4000,
            "phase": 0,
            "alpha": 1,
            "delta": -240,
        }
        options.drag_params2 = {
            "time": 0,
            "offset": 0,
            "amp": 0,
            "detune": 0,
            "freq": 0,
            "phase": 0,
            "alpha": 1,
            "delta": -240,
        }
        options.z_params1 = {"amp": 0.02, "time": 12, "buffer": 1, "sigma": 0.01}
        options.z_params2 = {"amp": -0.02, "time": 12, "buffer": 1, "sigma": 0.01}
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.set_validator("opt_ab", bool)
        options.set_validator("nm_params", dict)
        options.set_validator("opt_mode", ["nm", "de"])
        options.opt_ab = False
        options.opt_mode = "nm"
        options.nm_params = Options(
            x0=[0.05, 0],
            ftarget=-1e8,
            maxiter=None,
            maxfev=None,
            disp=True,
            return_all=True,
            initial_simplex=None,
            xatol=1e-4,
            fatol=1e-4,
            adaptive=False,
            nonzdelt=0.1,
            nm_step=0.0001,
        )

        # NEW3 ↓ ------------------------------------
        options.set_validator("gate_mode", ["normal", "pure"])
        options.gate_mode = "normal"
        # NEW3 ↑ ------------------------------------

        return options

    def _check_options(self):
        self.run_options.gate_bucket.xz_gate = QDict(drag_params=[], flat_params=[])
        # NEW ↓ ------------------------------------
        if self.analysis_options.gate_mode == "normal":
            for i in range(2):
                drag_params = QDict(**self.experiment_options[f"drag_params{i + 1}"])
                z_params = QDict(**self.experiment_options[f"z_params{i + 1}"])
                if drag_params.time > 0:
                    baseband_freq = self.qubit.XYwave.baseband_freq
                    freq = np.round(drag_params.freq - self.qubit.drive_freq + baseband_freq, 3)
                    # freq = amp_to_bf_freq(self.qubit, z_params.amp)
                    drag_params.freq = freq
                    z_time = z_params.time
                    z_buffer = z_params.buffer

                    self.run_options.gate_bucket.xz_gate.drag_params.append(
                        drag_params.to_dict()
                    )
                    self.run_options.gate_bucket.xz_gate.flat_params.append(
                        {
                            "time": z_time,
                            "amp": z_params.amp,
                            "sigma": z_params.sigma,
                            "buffer": z_buffer,
                        }
                    )
        elif self.analysis_options.gate_mode == "pure":
            drag_params = QDict(**self.experiment_options[f"drag_params1"])
            baseband_freq = self.qubit.XYwave.baseband_freq
            freq = np.round(drag_params.freq - self.qubit.drive_freq + baseband_freq, 3)
            drag_params.freq = freq
            for i in range(4):
                if i == 1 or i == 2:
                    z_params = QDict(**self.experiment_options[f"z_params{i}"])
                    # baseband_freq = self.qubit.XYwave.baseband_freq
                    # freq = np.round(drag_params.freq - self.qubit.drive_freq + baseband_freq, 3)
                    # freq = amp_to_bf_freq(self.qubit, z_params.amp)
                    z_time = z_params.time

                    self.run_options.gate_bucket.xz_gate.flat_params.append(
                        {
                            "time": z_time,
                            "amp": z_params.amp,
                            "sigma": z_params.sigma,
                            "buffer": 0.05,
                        }
                    )
                else:
                    z_params = QDict(**self.experiment_options[f"z_params1"])
                    z_time = z_params.buffer
                    self.run_options.gate_bucket.xz_gate.flat_params.append(
                        {
                            "time": z_time,
                            "amp": 0,
                            "sigma": 0.01,
                            "buffer": 0.05,
                        }
                    )

            self.run_options.gate_bucket.xz_gate.drag_params.append(drag_params.to_dict())
        super()._check_options()
        self.set_run_options(analysis_class=XEBNMAnalysis)
        # print("wch for ")
        # NEW ↑ ------------------------------------

    def _transform_pulse(self):
        temp_struct = self.run_options.temp_struct

        xy_pulse_list = []
        z_pulse_list = []
        for struct in temp_struct:
            xy_pulse = self.pulse_from_struct(struct, self.qubit)
            z_pulse = self.pulse_from_struct(struct, self.qubit, mode="Z")
            xy_pulse_list.append(xy_pulse)
            z_pulse_list.append(z_pulse)

        self.run_options.xy_pulse_map[self.qubit] = xy_pulse_list
        self.run_options.z_pulse_map[self.qubit] = z_pulse_list

    def _metadata(self):
        """Set XEB experiment metadata."""
        metadata = super()._metadata()
        metadata.process_meta = {
            "gate_bucket": self.run_options.gate_bucket,
            "temp_struct": self.run_options.temp_struct,
            "dirs": self.file.dirs,
            "gate_mode": self.analysis_options.gate_mode
        }
        return metadata

    # mark
    def _alone_save_result(self):

        super()._alone_save_result()

        depth_1 = self.experiment_options.get("depth1")
        depth_2 = self.experiment_options.get("depth2")
        depth_3 = self.experiment_options.get("depth3")
        depth_4 = self.experiment_options.get("depth4")
        depth_all = depth_1 + depth_2 + depth_3 + depth_4

        self.file.save_data(
            depth_all,
            self.experiment_data.y_data["F_xeb"],
            self.analysis.analysis_datas["F_xeb"].fit_data.y_fit,
            name="X_Y_Fit"
        )


class XEBNMAnalysis(XEBAnalysis):
    """Analysis for process tomography experiments."""

    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields.

        **Analysis Options**:

            - **titles (List)** - Subplot title, default is
              ``[ideal-real, ideal-image, exp-real, exp-image]``.

            - **labels (List)** - Axis labels, default is ``[I, X, Y, Z]``.

            - **result_parameters (List)** - Expect to extract the ``exp_chi_matrix``,
              ``ideal_chi_matrix`` , ``fidelity`` and ``process_fidelity``.

        Returns:
            Process tomography experiment analysis options.
        """
        options = super()._default_options()
        options.opt_ab = True
        options.opt_mode = "nm"
        options.nm_params = Options(
            x0=[0.05, 0],
            ftarget=-1e8,
            maxiter=None,
            maxfev=None,
            disp=True,
            return_all=True,
            initial_simplex=None,
            xatol=1e-4,
            fatol=1e-4,
            adaptive=False,
            nonzdelt=0.1,
            nm_step=0.0001,
        )
        options.de_options = {
            "NIND": 30,  # The number of individuals in the population, MATLAB gave 15 at the time.
            # Take a look at the npj single-bit gate and give it a little more,
            # because the single-bit DAG may not run through and there will be losses.
            "MAXGEN": 20,  # Maximum number of genetic generations
            "mutF": 0.7,  # Parameter F in differential evolution
            "XOVR": 0.7,  # Cross-recombination probability
            "init_population_path": None,
        }  # mutF=0.5/0.7, XOVR=0.7

        options.result_parameters = [ParameterRepr(name="amp")]

        return options

    def nm_opt_func(self, parameters: list, *args):
        xeb_fidelity = self._calculate_xeb_fidelity(parameters)

        filter_xeb_fidelity = []
        for v in list(xeb_fidelity):
            if math.isnan(v) or v in [float("inf"), -float("inf"), 0]:
                filter_xeb_fidelity.append(1)
            else:
                filter_xeb_fidelity.append(v)

        fp = -float(np.mean(filter_xeb_fidelity))
        pyqlog.log("EXP", f"NM FUNC - {self.options.count}: {parameters} | {fp}")
        self.options.count += 1
        return fp

    def _calculate_xeb_fidelity(self, parameters):
        a, b = parameters
        # c = parameters
        # NEW ↓ ------------------------------------
        if self.options.gate_mode == "normal":
            # goal_gate_matrix = np.array(
            #     [[0, np.exp(1j * a)], [np.exp(1j * b), 0]], dtype=complex
            # )
            goal_gate_matrix = np.array(
                [[0, 1], [1, 0]], dtype=complex
            )
        elif self.options.gate_mode == "pure":
            # goal_gate_matrix = np.array(
            #     [
            #         [
            #             1 / np.sqrt(2),
            #             -1j * np.exp(1j * (a + b)) / np.sqrt(2)
            #         ],
            #         [
            #             -1j / np.sqrt(2),
            #             np.exp(1j * (a + b)) / np.sqrt(2)
            #         ]
            #     ],
            #     dtype=complex
            # )
            goal_gate_matrix = np.array(
                [
                    [
                        np.cos(-np.pi),
                        np.sin(-np.pi)
                    ],
                    [
                        np.sin(-np.pi),
                        np.cos(-np.pi)*np.exp(1j * a)
                    ]
                ],
                dtype=complex
            )
            # Z_a = np.array(
            #     [[1, 0], [0, np.exp(1j * a)]], dtype=complex
            # )
            # Z_neg_a = np.array(
            #     [[1, 0], [0, np.exp(-1j * a)]], dtype=complex
            # )

        # NEW ↑ ------------------------------------

        gate_bucket = self.experiment_data.metadata.process_meta.get("gate_bucket")
        temp_struct = self.experiment_data.metadata.process_meta.get("temp_struct")
        matrix_list = []
        for struct in temp_struct:
            matrix = np.eye(2)
            for gate in struct.gates:
                if gate == "XZ":
                    matrix = np.dot(goal_gate_matrix, matrix)
                elif gate == "XR":
                    continue
                else:
                    matrix_pulsz = np.dot(goal_gate_matrix, gate_bucket.get_matrix(gate))
                    matrix = np.dot(matrix_pulsz, matrix)
            # NEW2 ↓ ------------------------------------
            # matrix = np.dot(Z_neg_a, matrix)
            # matrix = np.dot(matrix, Z_a)
            # NEW2 ↑ ------------------------------------
            # matrix = np.dot(struct.su2_matrix, matrix)    # su2
            matrix_list.append(matrix)

        return calculate_xeb_fidelity(self.options.p_meas, matrix_list)

    def _pre_operation(self):
        super()._pre_operation()

        if self.options.opt_ab is True:

            if self.options.opt_mode == "nm":
                res, sim = nm_minimize(
                    self.nm_opt_func,
                    x0=self.options.nm_params.x0,
                    ftarget=self.options.nm_params.ftarget,
                    nonzdelt=self.options.nm_params.nonzdelt,
                    maxiter=self.options.nm_params.maxiter,
                    maxfev=self.options.nm_params.maxfev,
                    disp=self.options.nm_params.disp,
                    return_all=self.options.nm_params.return_all,
                    xatol=self.options.nm_params.xatol,
                    fatol=self.options.nm_params.fatol,
                    adaptive=self.options.nm_params.adaptive,
                    step=[self.options.nm_params.nm_step, self.options.nm_params.nm_step],
                    bound=[[-np.pi, np.pi], [0, 0]],
                )
                fidelity_list = self._calculate_xeb_fidelity(list(res.x))
            else:
                res = ea_optimize(
                    opt_keys=["phase_a", "phase_b"],
                    opt_init_v=[0.05, 0],
                    opt_params=self.options.de_options,
                    args_bounds=[[-np.pi, np.pi], [0, 0]],
                    file_path=self.experiment_data.metadata.process_meta.get("dirs"),
                    cost_func=self.nm_opt_func
                )
                result = res.get("Vars")[0]
                fidelity_list = self._calculate_xeb_fidelity(result)

            self.experiment_data.y_data["F_xeb"] = np.array(fidelity_list)


class NMXEBSingleXZ(NMBase):
    _sub_experiment_class = XEBSingleXZ

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.input_data = {
            "amp1": {
                "is_opt": True,
                "init_v": None,
                "iter_step": 0.001,
                "bound": [0.34, 0.43],
                "nonzdelt": 0.05,
            },
            "amp2": {
                "is_opt": False,
                "init_v": None,
                "iter_step": 0.001,
                "bound": [-1, 1],
                "nonzdelt": 0.05,
            },
            "phase": {
                "is_opt": False,
                "init_v": None,
                "iter_step": 0.001,
                "bound": [-1, 1],
                "nonzdelt": 0.05,
            },
        }

        # NEW ↓ ------------------------------------
        # options.set_validator("gate_mode", ["normal", "pure"])
        # options.gate_mode = "normal"
        # NEW ↑ ------------------------------------

        return options

    def _check_options(self):
        super()._check_options()
        init_v = []
        for k, v in self.experiment_options.input_data.items():
            if v.get("is_opt") is True:
                iv = v.get("init_v")
                init_v.append(iv)
        self.set_run_options(init_v=init_v)

    async def _execute_exp(self, parameters):
        exp = deepcopy(self.child_experiment)

        # NEW ↓ ------------------------------------
        # exp.analysis_options.gate_mode = self.analysis_options.gate_mode
        gate_mode = exp.analysis_options.gate_mode

        # if self.analysis_options.gate_mode == "normal":
        if gate_mode == "normal":
            self.experiment_options.child_exp_options["drag_params1"][
                "amp"
            ] = parameters[0]
            self.experiment_options.child_exp_options["drag_params2"][
                "amp"
            ] = parameters[1]
            self.experiment_options.child_exp_options["drag_params2"][
                "phase"
            ] = parameters[2]
        # elif self.analysis_options.gate_mode == "pure":
        elif gate_mode == "pure":
            self.experiment_options.child_exp_options["drag_params1"][
                "amp"
            ] = parameters[0]
            # self.experiment_options.child_exp_options["drag_params1"][
            #     "phase"
            # ] = parameters[1]
        # NEW ↑ ------------------------------------
        await self._run_exp(exp)

        # return -float(np.mean(exp.analysis.analysis_datas.F_xeb.y))
        return -float(np.mean(exp.analysis.results.f_xeb.value))
