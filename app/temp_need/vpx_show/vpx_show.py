# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/10
# __author:       <PERSON><PERSON><PERSON>

from collections import defaultdict

from loguru import logger

from app.config import init_backend


def vpx_information(
    chip_data,
    vpx_x_channel: int = 24,
    vpx_z_channel: int = 64,
    vpx_m_channel: int = 24,
    csv_file: str = "vpx_info.csv",
):
    chip_data.sorted_qubits()
    channel_map = []
    for name, qubit in chip_data.cache_qubit.items():
        channel_map.append(
            dict(
                name=name,
                xy_channel=qubit.xy_channel,
                xy_vpx=(qubit.xy_channel - 1) // vpx_x_channel + 1,
                z_flux_channel=qubit.z_flux_channel,
                z_vpx=(qubit.z_flux_channel - 1) // vpx_z_channel + 1,
                readout_channel=qubit.readout_channel,
                m_vpx=(qubit.readout_channel - 1) // vpx_m_channel + 1,
            )
        )
    for name, coupler in chip_data.cache_coupler.items():
        channel_map.append(
            dict(
                name=name,
                xy_channel="-",
                xy_vpx="-",
                z_flux_channel=coupler.z_flux_channel,
                z_vpx=(coupler.z_flux_channel - 1) // vpx_z_channel + 1,
                readout_channel="-",
                m_vpx="-",
            )
        )

    z_vpx_map = defaultdict(list)
    for data in channel_map:
        z_vpx_map[data["z_vpx"]].append(data["name"])

    try:
        from pyQCat.tools.utilities import display_dict_as_table

        table = display_dict_as_table(channel_map)
        logger.info(f"VPX information: \n{table}")
    except Exception:
        pass

    try:
        import csv

        fieldnames = [
            "name",
            "xy_channel",
            "xy_vpx",
            "z_flux_channel",
            "z_vpx",
            "readout_channel",
            "m_vpx",
        ]
        with open(csv_file, mode="w", newline="", encoding="utf-8") as file:
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            writer.writeheader()
            for data in channel_map:
                writer.writerow(data)
        logger.info(f"save path {csv_file} suc")
    except Exception:
        logger.error(f"save path {csv_file} error")

    for vpx, bits in z_vpx_map.items():
        logger.info(f"Z-VPX-{vpx} | {','.join(bits)}")


if __name__ == "__main__":
    backend = init_backend()
    vpx_information(backend.chip_data, csv_file="channel_map.csv")
