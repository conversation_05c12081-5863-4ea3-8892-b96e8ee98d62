# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/03/08
# __author:       <PERSON><PERSON><PERSON>


import numpy as np

from pyQCat.analysis.algorithms.tomography import TomographyCalculator


if __name__ == '__main__':
    calculator = TomographyCalculator()
    data = np.loadtxt("SingleProcessTomography(probability).dat")[:, 1:].T
    calculator.analysis_options.mode = "qpt"
    calculator.analysis_options.goal_gate = "CZ"
    calculator.analysis_options.phase_opt = False
    calculator.calculate(data)
    print(calculator.result)
