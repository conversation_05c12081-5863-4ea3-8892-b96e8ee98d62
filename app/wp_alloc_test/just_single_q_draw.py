#
import pickle
from pathlib import Path

from pyQCat.tools.freq_allocator.work_point_allocator import SingleQAllocator

cur_path = Path.cwd()

print(f"cur_path: {cur_path}")

chip_graph_file = cur_path / "result_data" / "finally single_q_chip_process.pickle"

save_path = r"D:\result-visage"

with open(str(chip_graph_file), mode="rb") as fp:
    chip_graph = pickle.load(fp)

single_q_allocator = SingleQAllocator()
single_q_allocator.chip_graph = chip_graph
single_q_allocator.set_options(save_path=save_path)

self = single_q_allocator

self._set_save_path()
self._draw_chip_node_error(self.chip_graph, "bak")
self._draw_chip_frequency(self.chip_graph, "bak single_q_frequency.png")
self._save_json("single_q_alloc_frequency.json", self.run_options.qubit_freq_dict)
self._save_json("single_q_alloc_error.json", self.run_options.qubit_error_dict)

print(f"save_path: {self.run_options.save_path}")


