#
import pickle
from pathlib import Path

from pyQCat.tools.freq_allocator.work_point_allocator import TwoQAllocator

cur_path = Path.cwd()

print(f"cur_path: {cur_path}")

xy_cross_sim_json = cur_path / "alloc_data" / "xy_crosstalk_sim.json"
qubit_data_json = cur_path / "alloc_data" / "qubit_data.json"
qubit_freq_json = cur_path / "result_data" / "single_q_alloc_frequency.json"
chip_graph_file = cur_path / "result_data" / "finally single_q_chip_process.pickle"

save_path = r"D:\result-visage"

options_data = TwoQAllocator.view_options()
print(options_data)

with open(str(chip_graph_file), mode="rb") as fp:
    chip_graph = pickle.load(fp)

axeb_params = {
    "twoq_T1": 2e-4,
    "twoq_T2": 1e-7,
    "distort": 1e-5,
    "inner_leakage": [1e-5, 1e-2],
    "twoq_xtalk": [1, 10, 1, 10],
}

two_q_allocator = TwoQAllocator()
two_q_allocator.set_options(
    xy_cross_sim_json=xy_cross_sim_json,
    qubit_data_json=qubit_data_json,
    qubit_freq_json=qubit_freq_json,
    chip_graph_file=chip_graph_file,
    save_path=save_path,
    save_process_chip=False,
    delete_unused=False,
    iteration=20,
    axeb_params=axeb_params
)

# two_q_allocator.chip_graph = chip_graph

two_q_allocator.alloc()
