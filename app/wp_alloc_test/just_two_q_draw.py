#
import pickle
from pathlib import Path

from pyQCat.tools.freq_allocator.work_point_allocator import TwoQAllocator

cur_path = Path.cwd()

print(f"cur_path: {cur_path}")

chip_graph_file = cur_path / "result_data" / "finally two_q_chip_process.pickle"

save_path = r"D:\result-visage"

with open(str(chip_graph_file), mode="rb") as fp:
    chip_graph = pickle.load(fp)

two_q_allocator = TwoQAllocator()
two_q_allocator.chip_graph = chip_graph
two_q_allocator.set_options(save_path=save_path)

self = two_q_allocator
self._set_save_path()
self._draw_chip_edge_error(self.chip_graph, "bak")
self._draw_chip_frequency(self.chip_graph, "bak two_q_frequency.png", draw_edge=True)
self._save_json("two_q_alloc_frequency.json", self.run_options.pair_freq_dict)
self._save_json("two_q_alloc_error.json", self.run_options.pair_error_dict)

print(f"save_path: {self.run_options.save_path}")
