# -*- coding: utf-8 -*-

import os
import re
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Union

import numpy as np

from scipy.interpolate import interp1d


def amp2freq_formula(
        x: Union[float, np.ndarray],
        fq_max: float,
        detune: float,
        M: float,
        d: float,
        w: float = None,
        g: float = None,
        tans2phi: bool = False,
) -> Union[float, np.ndarray]:
    """Calculate frequency from AC.

    Args:
        tans2phi (bool): Judge x is phase or not.

    """
    if tans2phi is True:
        phi = x
    else:
        phi = np.pi * M * x

    fq = (fq_max + detune) * np.sqrt(
        np.sqrt(1 + d ** 2 * np.tan(phi) ** 2) * np.abs(np.cos(phi))
    ) - detune

    if w and g:
        fg = np.sqrt((w - fq) ** 2 + 4 * g ** 2)
        fq = (w + fq + fg) / 2
    return fq


def select_rb_data(rb_records_dict: Dict) -> Dict:
    """Select rb data."""
    rb_fidelity_threshold = 0.995
    rb_std_threshold = 0.06
    freq_step = 6

    q_data_dict = {}
    for q_name, q_result_dict in rb_records_dict.items():
        allow_freq = []
        isolated_error = []
        tunable = None
        anharm = None
        ac_spectrum = None

        freq_real_list = []
        rb_error_list = []
        for freq_str, s_freq_dict in q_result_dict.items():
            is_pass = s_freq_dict.get("is_pass")
            if is_pass is True:
                rb_fidelity = s_freq_dict.get("rb_fidelity")
                rb_std = s_freq_dict.get("rb_std")
                params = s_freq_dict.get("params")
                if rb_fidelity > rb_fidelity_threshold and rb_std < rb_std_threshold:
                    if anharm is None:
                        note_anharm = params.get("anharmonicity")
                        if isinstance(note_anharm, float):
                            anharm = round(note_anharm, 3)

                    if ac_spectrum is None:
                        ac_spectrum_dict = params.get("ac_spectrum")
                        if ac_spectrum_dict["standard"] and ac_spectrum_dict["standard"][0] != 0.0:
                            ac_spectrum = ac_spectrum_dict["standard"]
                        elif ac_spectrum_dict["bottom_left"] and ac_spectrum_dict["bottom_left"][0] != 0.0:
                            ac_spectrum = ac_spectrum_dict["bottom_left"]
                        elif ac_spectrum_dict["top"] and ac_spectrum_dict["top"][0] != 0.0:
                            ac_spectrum = ac_spectrum_dict["top"]
                        elif ac_spectrum_dict["bottom_right"] and ac_spectrum_dict["bottom_right"][0] != 0.0:
                            ac_spectrum = ac_spectrum_dict["bottom_right"]

                    if tunable is None:
                        tunable = params.get("tunable")

                    freq_real = params.get("drive_freq")
                    rb_error = 1 - rb_fidelity
                    freq_real_list.append(freq_real)
                    rb_error_list.append(rb_error)

                    if tunable is True:
                        freq_range = np.arange(
                            freq_real - freq_step,
                            freq_real + freq_step,
                            1,
                            dtype=int,
                        )
                        allow_freq.extend(freq_range.tolist())
                    else:
                        allow_freq.append(int(round(freq_real)))

        allow_freq = sorted(list(map(float, set(allow_freq))), reverse=True)
        if len(freq_real_list) > 2:
            if len(ac_spectrum) == 5:
                note_ac_spectrum = ac_spectrum[:]
                del note_ac_spectrum[3]
                freq_max = ac_spectrum[0]
                freq_min = amp2freq_formula(np.pi / 2, *note_ac_spectrum, tans2phi=True)
            else:
                freq_max = ac_spectrum[-1]
                freq_min = ac_spectrum[-2]
            allow_freq = [
                freq
                for freq in allow_freq
                if freq_min <= freq <= freq_max
            ]
            func = interp1d(freq_real_list, rb_error_list, kind="linear")
            min_freq = min(freq_real_list)
            max_freq = max(freq_real_list)
            for freq in allow_freq:
                if freq <= min_freq:
                    error = rb_error_list[freq_real_list.index(min_freq)]
                elif min_freq < freq < max_freq:
                    error = float(func(freq))
                else:
                    error = rb_error_list[freq_real_list.index(max_freq)]
                isolated_error.append(error)
        elif freq_real_list:
            error = rb_error_list[0]
            isolated_error = [error] * len(allow_freq)

        s_result = {
            "allow_freq": allow_freq,
            "isolated_error": isolated_error,
            "ac_spectrum": ac_spectrum,
            "anharm": anharm,
        }
        q_data_dict.update({q_name: s_result})
    return q_data_dict


def select_t1_spectrum_data(t1_spectrum_path: str, mode: str = "batch") -> Dict:
    """Select t1_spectrum data."""
    q_pattern = re.compile(r"^q\d+$")
    target_mark = "t1_spectrum_run_options.dat"

    q_t1_spectrum_dict = {}
    dir_list = os.listdir(t1_spectrum_path)
    for sub_dir in dir_list:
        if q_pattern.match(sub_dir):
            q_name = sub_dir
            if mode == "batch":
                sub_path = os.path.join(t1_spectrum_path, sub_dir, "T1Spectrum")
            else:
                sub_path = os.path.join(t1_spectrum_path, sub_dir)

            date_dir_list = os.listdir(sub_path)
            date_dir_list.sort(
                key=lambda x: datetime.strptime(x, "%Y-%m-%d"),
                reverse=True,
            )

            break_flag = False
            for date_dir in date_dir_list:
                date_path = os.path.join(sub_path, date_dir)
                time_dir_list = os.listdir(date_path)
                time_dir_list.sort(
                    key=lambda x: datetime.strptime(x, "%H.%M.%S"),
                    reverse=True,
                )
                for time_dir in time_dir_list:
                    time_path = os.path.join(date_path, time_dir)
                    file_list = os.listdir(time_path)
                    for file in file_list:
                        if target_mark in file:
                            f_path = os.path.join(time_path, file)
                            f_data = np.loadtxt(f_path)
                            freq_arr, t1_arr = f_data[:, 1], f_data[:, 2]
                            s_result = {
                                "freq": freq_arr.tolist(),
                                "t1": t1_arr.tolist()
                            }
                            q_t1_spectrum_dict.update({q_name: s_result})
                            break_flag = True
                            break
                    if break_flag is True:
                        break

                if break_flag is True:
                    break

    return q_t1_spectrum_dict


def select_xy_coefficient_data(xy_rw_path: str) -> Dict:
    """Select xy_cross coefficient data."""
    t_pattern = re.compile(r"^q\d+_cross_coefficient.json$")

    xy_cr_coef_dict = {}
    res = os.walk(xy_rw_path)
    for r_path, dir_list, file_list in res:
        for file in file_list:
            if t_pattern.match(file):
                f_path = os.path.join(r_path, file)
                with open(f_path, mode="r", encoding="utf-8") as fp:
                    s_xy_dict = json.load(fp)
                for q_name, coef_dict in s_xy_dict.items():
                    s_dict = xy_cr_coef_dict.get(q_name) or {}
                    s_dict.update(coef_dict)
                    xy_cr_coef_dict.update({q_name: s_dict})

    return xy_cr_coef_dict


if __name__ == '__main__':
    cur_path = Path.cwd()
    print(f"cur_path: {cur_path}")

    qubit_data_json = cur_path / "alloc_data" / "qubit_data.json"
    rb_records_json = r"F:\MayNewCodes\Monster\1225-monster\pyqcat-monster\zck\q_data_test\exp_result_json\rb_records.json"
    t1_spectrum_path = r"D:\result-visage\221205-72bit-300pin-V8.2P1-base-4#-72bitSE3\T1Spectrum"
    xy_cr_plus_path = r"D:\result-visage\221205-72bit-300pin-V8.2P1-base-4#-72bitSE3\XYCrossPlusRabiWidth"

    load_t1_spec_mode = "un-batch"  # "un-batch" or "batch"

    with open(str(rb_records_json), mode="r", encoding="utf-8") as fp:
        rb_records_dict = json.load(fp)

    try:
        with open(str(qubit_data_json), mode="r", encoding="utf-8") as fp:
            qubit_data_dict = json.load(fp)
    except:
        qubit_data_dict = {}

    rb_data_dict = select_rb_data(rb_records_dict)
    t1_spectrum_dict = select_t1_spectrum_data(t1_spectrum_path, mode=load_t1_spec_mode)
    xy_cross_coef_dict = select_xy_coefficient_data(xy_cr_plus_path)

    q_names = [f"q{bit}" for bit in range(1, 73)]
    for q_name in q_names:
        s_dict = qubit_data_dict.get(q_name) or {}
        if q_name in rb_data_dict:
            s_dict.update(**rb_data_dict.get(q_name))
        if q_name in t1_spectrum_dict:
            s_dict.update({"t1_spectrum": t1_spectrum_dict.get(q_name)})
        if q_name in xy_cross_coef_dict:
            s_dict.update({"xy_crosstalk_coef": xy_cross_coef_dict.get(q_name)})
        if s_dict:
            qubit_data_dict.update({q_name: s_dict})

    with open(str(qubit_data_json), mode="w", encoding="utf-8") as fp:
        json.dump(qubit_data_dict, fp, ensure_ascii=False, indent=4)

    print(f"qubit_data_json: {str(qubit_data_json)}")
