# WorkPoint Frequency Allocate

## 1. 简介

工作点频率分配测试脚本，用于测试单比特，两比特工作点频率分配；

wp_alloc_test  在 项目 pyqcat-apps  中目录 pyqcat-apps/app/wp_alloc_test

```
wp_alloc_test 目录结构

├─wp_alloc_test
   ├─alloc_data
   |  ├─qubit_data.json
   |  └─xy_crosstalk_sim.json
   ├─__init__.py
   ├─demo_qubit_data.py
   ├─demo_single_q_allocator.py
   ├─demo_two_q_allocator.py
   ├─just_single_q_draw.py
   └─just_two_q_draw.py
```



- alloc_data 存放基本数据
  - qubit_data.json 根据 rb 谱，T1 谱，XYCrossPlusRabiWidth 测试结果生成的数据；
  - xy_crosstalk_sim.json 实验室提供 xy 串扰仿真数据，**无需改动**；
- demo_qubit_data.py  用于生成 qubit_data.json 脚本
- demo_single_q_allocator.py  单比特工作点频率分配脚本
- demo_two_q_allocator.py  两比特工作点频率分配脚本
- just_single_q_draw.py  指定某次单比特分配结果进行绘图，用于调试
- just_two_q_draw.py 指定某次两比特分配结果进行绘图，用于调试



## 2. 使用说明



### 生成 qubit_data



```python
# demo_qubit_data.py.py

...


if __name__ == '__main__':
    cur_path = Path.cwd()
    print(f"cur_path: {cur_path}")

    qubit_data_json = cur_path / "alloc_data" / "qubit_data.json"
    rb_records_json = r"xx/rb_records.json"
    t1_spectrum_path = r"xx/T1Spectrum"
    xy_cr_plus_path = r"xx/XYCrossPlusRabiWidth"

    load_t1_spec_mode = "un-batch"  # "un-batch" or "batch"

    ...
```



需要设置参数：

- qubit_data_json: 保存 qubit_data.json 路径；
- rb_records_json: RB谱测试得到 rb_records.json 路径；
- t1_spectrum_path: T1Spectrum 测试结果路径；
- xy_cr_plus_path: XYCrossPlusRabiWidth 测试结果路径；
- load_t1_spec_mode: 加载 T1 谱数据模式，由于 batch 测试与正常测试实验结果路径不一样，所以分为两种模式 `un-batch` , `batch`



### 单比特工作点频率分配

```Python
# demo_single_q_allocator.py 


from pathlib import Path

from pyQCat.tools.freq_allocator.work_point_allocator import SingleQAllocator

cur_path = Path.cwd()
print(f"cur_path: {cur_path}")

xy_cross_sim_json = cur_path / "alloc_data" / "xy_crosstalk_sim.json"
qubit_data_json = cur_path / "alloc_data" / "qubit_data.json"
save_path = r"D:\result-visage"

arb_params = {
    "singq_T1": 2e-4,
    "singq_T2": 1e-7,
    "singq_xtalk": 1.0,
    "singq_residual_nn": [0.3, 10],
    "singq_residual_nnn": [0.5, 10],
}

options_data = SingleQAllocator.view_options()
print(options_data)

single_q_allocator = SingleQAllocator()

single_q_allocator.set_options(
    xy_cross_sim_json=xy_cross_sim_json,
    qubit_data_json=qubit_data_json,
    save_path=save_path,
    save_process_chip=False,
    delete_unused=False,
    iteration=100,
    inner_iteration=1,
    algorithm_name="DE",  # "DE" or "PSO"
    use_rb_spectrum=True,
    arb_params=arb_params,
    surround=2,
    center_conflict_node=None,
    mu_threshold=0.005,
    conflict_xy_threshold=4e-3,
    conflict_nnn_threshold=2.5e-3,
)

single_q_allocator.alloc()

```

参数：

- xy_cross_sim_json:  xy 串扰仿真数据路径；
- qubit_data_json： qubit_data 路径
- save_path：设置结果保存路径
- save_process_chip： 是否保存迭代过程中的 图结构
- delete_unused ：芯片拓扑图中是否删除不可用的比特节点
- iteration： 外层最多迭代次数
- inner_iteration： 内存优化迭代次数
- algorithm_name： 优化算法名称，支持 DE, PSO  (注意： 选择 PSO 需要提前安装 sko 第三方库)
- use_rb_spectrum： 是否实验 rb 谱中 error 值
- arb_params： 单比特各个错误模型的系数，方便用户调节
- surround： 迭代过程中选择比特的辐射距离
- center_conflict_node： 设置中心点，一般不设置，若设置为则为 两元素 列表 或 元组，如：(6, 3)
- mu_threshold： xy 串扰系数最大阈值
- conflict_xy_threshold： xy 串扰误差模型阈值
- conflict_nnn_threshold： nnn 误差模型阈值



### 两比特工作点频率分配

两比特工作点频率分配，依赖单比特工作点分配结果

```Python
# demo_two_q_allocator.py

import pickle
from pathlib import Path

from pyQCat.tools.freq_allocator.work_point_allocator import TwoQAllocator

cur_path = Path.cwd()

print(f"cur_path: {cur_path}")

xy_cross_sim_json = cur_path / "alloc_data" / "xy_crosstalk_sim.json"
qubit_data_json = cur_path / "alloc_data" / "qubit_data.json"
qubit_freq_json = cur_path / "result_data" / "single_q_alloc_frequency.json"
chip_graph_file = cur_path / "result_data" / "finally single_q_chip_process.pickle"

save_path = r"D:\result-visage"

options_data = TwoQAllocator.view_options()
print(options_data)

with open(str(chip_graph_file), mode="rb") as fp:
    chip_graph = pickle.load(fp)

axeb_params = {
    "twoq_T1": 2e-4,
    "twoq_T2": 1e-7,
    "distort": 1e-5,
    "inner_leakage": [1e-5, 1e-2],
    "twoq_xtalk": [1, 10, 1, 10],
}

two_q_allocator = TwoQAllocator()
two_q_allocator.set_options(
    xy_cross_sim_json=xy_cross_sim_json,
    qubit_data_json=qubit_data_json,
    qubit_freq_json=qubit_freq_json,
    chip_graph_file=chip_graph_file,
    save_path=save_path,
    save_process_chip=False,
    delete_unused=False,
    iteration=20,
    axeb_params=axeb_params
)

# two_q_allocator.chip_graph = chip_graph

two_q_allocator.alloc()

```

参数：

- xy_cross_sim_json:  xy 串扰仿真数据路径；
- qubit_data_json： qubit_data 路径
- qubit_freq_json： 单比特工作点分配得到的频率 json 文件
- chip_graph_file： 单比特工作点分配得到的 pickle 文件
- save_path：设置结果保存路径
- save_process_chip： 是否保存迭代过程中的 图结构
- delete_unused ：芯片拓扑图中是否删除不可用的比特节点
- iteration： 外层最多迭代次数
- axeb_params: 两比特各种误差模型对应的系数



