#

from pathlib import Path

from pyQCat.tools.freq_allocator.work_point_allocator import SingleQAllocator

cur_path = Path.cwd()
print(f"cur_path: {cur_path}")

xy_cross_sim_json = cur_path / "alloc_data" / "xy_crosstalk_sim.json"
qubit_data_json = cur_path / "alloc_data" / "qubit_data.json"

save_path = r"D:\result-visage"

arb_params = {
    "singq_T1": 2e-4,
    "singq_T2": 1e-7,
    "singq_xtalk": 1.0,
    "singq_residual_nn": [0.3, 10],
    "singq_residual_nnn": [0.5, 10],
}

options_data = SingleQAllocator.view_options()
print(options_data)

single_q_allocator = SingleQAllocator()

single_q_allocator.set_options(
    xy_cross_sim_json=xy_cross_sim_json,
    qubit_data_json=qubit_data_json,
    save_path=save_path,
    save_process_chip=False,
    delete_unused=False,
    iteration=100,
    inner_iteration=1,
    algorithm_name="DE",  # "DE" or "PSO"
    use_rb_spectrum=True,
    arb_params=arb_params,
    surround=2,
    center_conflict_node=None,
    mu_threshold=0.005,
    conflict_xy_threshold=4e-3,
    conflict_nnn_threshold=2.5e-3,
)

single_q_allocator.alloc()
