#

from pathlib import Path

# from pyQCat.tools.utilities import calculate_freq
from pyQCat.tools.freq_allocator.work_point_allocator_v1 import AllocatorStandard

# cur_path = Path.cwd()
cur_path = Path(r"D:\data\20240823\wangpeng\工作点分配")
print(f"cur_path: {cur_path}")

xy_cross_sim_json = cur_path / "alloc_data" / "xy_crosstalk_sim_new.json"
qubit_data_json = cur_path / "alloc_data" / "qubit_data.json"
qubit_freq_json = Path(r'D:\data\20240823\wangpeng\工作点分配\alloc_data') / "qubit_freq_dict.json"
pair_freq_json = Path(r'D:\data\20240823\wangpeng\工作点分配\alloc_data') / "pair_freq_dict.json"

save_path = r"D:\data\20240823\wangpeng\工作点分配"

arb_params = {
    "T1": 2e-4,
    "T2": 1e-4,
    "xtalk": 1.0,
    "residual_nn": [0.5, 10, 1., 20],
    "residual_nnn": [0.5, 10, 0.5, 10],
    "distortion": 1e-5,
    "spectator_1q": [1, 10, 1, 10],
    "spectator_2q": [1, 10, 1, 10],
    "tls": 2e-4,
    "freq_abs": 5e-6,
}

fix_freq_dict = {
    'q32': 4240.0,
    'q33': 4130.0,
    'q34': 4690.0,
    'q35': 4011.0,
    'q38': 4066.0,
    'q39': 4551.0,
    'q40': 4197.0,
    'q41': 4357.0,
    # 'q33-q39': 4139,
    # 'q34-q40': 4427,
    # 'q39-q40': 4238,
    # 'q40-q41': 4102,
}
bad_edges = ['q32-q38', 'q56-q62', 'q45-q46', 'q68-q69']
lo_dict = [
    [
        'q1',
        'q7',
        'q10',
        'q14',
        'q15',
        'q22',
        'q24',
        'q25',
        'q26',
        'q31',
        'q32',
        'q36',
        'q39',
        'q48',
        'q49',
        'q53',
        'q55',
        'q56',
        'q58',
        'q68',
        'q72',
    ],
    [
        'q2',
        'q4',
        'q6',
        'q9',
        'q11',
        'q17',
        'q18',
        'q20',
        'q21',
        'q23',
        'q27',
        'q28',
        'q37',
        'q40',
        'q41',
        'q46',
        'q47',
        'q52',
        'q57',
        'q60',
        'q61',
        'q64',
        'q65',
        'q67',
    ],
    [
        'q3',
        'q5',
        'q8',
        'q12',
        'q13',
        'q16',
        'q29',
        'q30',
        'q33',
        'q34',
        'q35',
        'q38',
        'q42',
        'q44',
        'q45',
        'q51',
        'q54',
        'q59',
        'q62',
        'q63',
        'q66',
        'q69',
        'q70',
        'q71',
    ],
]

if __name__ == '__main__':
    options_data = AllocatorStandard.view_options()
    print(options_data)
    for q in ['q24']:
        allocator = AllocatorStandard()

        allocator.set_options(
            xy_cross_sim_json=xy_cross_sim_json,
            qubit_data_json=qubit_data_json,
            qubit_freq_json=qubit_freq_json,
            pair_freq_json=pair_freq_json,
            save_path=save_path,
            save_process_chip=True,
            delete_unused=True,
            optimize_nodes=None,
            fix_freq_dict=None,
            iteration=100,
            inner_iteration=4,
            algorithm_name="DE",  # "DE" or "PSO"
            use_rb_spectrum=True,
            arb_params=arb_params,
            verify_frequencys=None,
            surround=1,
            center_conflict_node=q,
            bad_edges=bad_edges,
            mu_threshold=0.01,
            seed=None,
            xy_lo_list=None,
        )

        allocator.alloc()
