from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
import os


def create_ppt_from_dict(
    data_dict,
    picture_data,
    ppt_title: str = "字典数据展示",
    output_filename="dict_table.pptx",
    max_rows_per_page=15,
    N=4,
):
    """
    创建带美化表格的PPT，支持自动分页

    参数:
    data_dict -- 深度为1的字典 (单层键值对)
    output_filename -- 输出的PPT文件名
    max_rows_per_page -- 每页最多显示的行数（包括表头）
    """
    # 创建新演示文稿
    prs = Presentation()

    # 添加标题幻灯片
    title_slide = prs.slides.add_slide(prs.slide_layouts[0])
    title = title_slide.shapes.title
    title.text = ppt_title
    subtitle = title_slide.placeholders[1]
    subtitle.text = f"共 {len(data_dict)} 项数据"

    # 计算总页数
    total_items = len(data_dict)
    items_per_page = max_rows_per_page - 1  # 减去表头行
    total_pages = (total_items + items_per_page - 1) // items_per_page

    # 将字典转换为列表以便分页
    items = list(data_dict.items())

    # 创建数据页
    for page in range(total_pages):
        # 计算当前页的数据范围
        start_idx = page * items_per_page
        end_idx = min((page + 1) * items_per_page, total_items)
        page_data = items[start_idx:end_idx]

        # 添加内容幻灯片 - 使用空白布局
        content_slide = prs.slides.add_slide(prs.slide_layouts[6])

        # 添加页码标题
        title_box = content_slide.shapes.add_textbox(
            left=Inches(0.5),
            top=Inches(0.2),
            width=prs.slide_width - Inches(1),
            height=Inches(0.5),
        )
        title_frame = title_box.text_frame
        title_para = title_frame.add_paragraph()
        title_para.text = f"数据展示 - 第 {page + 1} 页（共 {total_pages} 页）"
        title_para.font.bold = True
        title_para.font.size = Pt(18)
        title_para.font.name = "微软雅黑"
        title_para.alignment = PP_ALIGN.CENTER

        # 计算表格尺寸和位置
        slide_width = prs.slide_width
        slide_height = prs.slide_height

        # 表格尺寸 (根据数据量自适应)
        num_rows = len(page_data) + 1  # 表头 + 数据行
        table_width = min(
            Inches(8), slide_width - Inches(2)
        )  # 最大宽度为幻灯片宽度减2英寸边距
        row_height = Inches(0.4)  # 固定行高

        # 表格位置 (居中)
        top = Inches(1.0)  # 标题下方
        table_height = row_height * num_rows

        # 确保表格不会超出页面底部
        max_table_height = slide_height - top - Inches(1)
        if table_height > max_table_height:
            # 如果表格太高，减小行高
            row_height = max_table_height / num_rows

        left = (slide_width - table_width) / 2

        # 创建表格
        table_shape = content_slide.shapes.add_table(
            rows=num_rows,
            cols=2,
            left=left,
            top=top,
            width=table_width,
            height=row_height * num_rows,
        )
        table = table_shape.table

        # ===== 表头样式设置 =====
        # 设置表头文本
        table.cell(0, 0).text = "键 (Key)"
        table.cell(0, 1).text = "值 (Value)"

        # 设置表头样式
        for col in range(2):
            header_cell = table.cell(0, col)

            # 文本格式
            tf = header_cell.text_frame
            tf.vertical_anchor = MSO_ANCHOR.MIDDLE  # 垂直居中

            p = tf.paragraphs[0]
            p.alignment = PP_ALIGN.CENTER
            p.font.bold = True
            p.font.size = Pt(14)
            p.font.color.rgb = RGBColor(255, 255, 255)  # 白色文字
            p.font.name = "微软雅黑"

            # 表头背景填充
            header_cell.fill.solid()
            header_cell.fill.fore_color.rgb = RGBColor(59, 89, 152)  # 深蓝色背景

        # ===== 填充数据 =====
        for row_idx, (key, value) in enumerate(page_data, start=1):
            # 键列
            key_cell = table.cell(row_idx, 0)
            key_cell.text = str(key)

            # 值列
            value_cell = table.cell(row_idx, 1)
            value_cell.text = str(value)

        # ===== 美化所有单元格 =====
        for row in range(num_rows):
            for col in range(2):
                cell = table.cell(row, col)

                # 设置文本格式
                tf = cell.text_frame
                tf.vertical_anchor = MSO_ANCHOR.MIDDLE  # 垂直居中

                # 确保每个单元格至少有一个段落
                if not tf.paragraphs:
                    p = tf.add_paragraph()
                else:
                    p = tf.paragraphs[0]

                p.alignment = PP_ALIGN.CENTER
                p.font.size = Pt(11)
                p.font.name = "微软雅黑"

                # 设置背景色（斑马纹）
                if row == 0:  # 表头已设置
                    continue
                elif row % 2 == 1:
                    cell.fill.solid()
                    cell.fill.fore_color.rgb = RGBColor(240, 245, 255)  # 浅蓝色
                else:
                    cell.fill.solid()
                    cell.fill.fore_color.rgb = RGBColor(255, 255, 255)  # 白色

        # ===== 设置边框 =====
        # 使用表格形状的边框设置
        table_shape.table.style = "Light Style 1"  # 使用内置表格样式

        # ===== 添加底部页码 =====
        footer_box = content_slide.shapes.add_textbox(
            left=Inches(0.5),
            top=slide_height - Inches(0.6),
            width=prs.slide_width - Inches(1),
            height=Inches(0.4),
        )
        footer_frame = footer_box.text_frame
        footer_para = footer_frame.add_paragraph()
        footer_para.text = f"第 {page + 1}/{total_pages} 页 | 数据项 {start_idx + 1}-{end_idx} (共 {total_items} 项)"
        footer_para.font.size = Pt(10)
        footer_para.font.color.rgb = RGBColor(100, 100, 100)
        footer_para.alignment = PP_ALIGN.CENTER

        # 如果提供了picture_data，则处理图片添加
    if picture_data is not None and len(picture_data) > 0:
        total_pages = (len(picture_data) + N - 1) // N
        for page in range(total_pages):
            add_pictures_to_slide(prs, picture_data, page, N)

    # 保存PPT
    output_path = os.path.abspath(output_filename)
    prs.save(output_path)
    print(f"PPT创建成功: {output_path}")
    print(f"总页数: {total_pages} 页")
    print(f"每页最多显示: {items_per_page} 项数据")
    print(
        f"表格尺寸: 宽 {table_width.inches:.1f} 英寸, 行高 {row_height.inches:.1f} 英寸"
    )


def add_pictures_to_slide(prs, picture_data, page, N=4):
    """
    根据给定的图片数据向幻灯片添加图片，并根据N值自动调整布局。

    参数:
    prs -- Presentation对象
    picture_data -- 包含图片信息的列表
    page -- 当前页面索引
    N -- 每页最多显示的图片数量
    """
    slide = prs.slides.add_slide(prs.slide_layouts[6])  # 使用空白布局

    # 设置页脚标题
    footer_box = slide.shapes.add_textbox(
        Inches(0.5),
        prs.slide_height - Inches(0.9),
        prs.slide_width - Inches(1),
        Inches(0.6),
    )
    footer_frame = footer_box.text_frame
    footer_para = footer_frame.add_paragraph()
    footer_para.text = f"图片展示 - 第 {page + 1} 页"
    footer_para.font.size = Pt(10)
    footer_para.font.color.rgb = RGBColor(100, 100, 100)
    footer_para.alignment = PP_ALIGN.CENTER

    # 图片和标题区域配置
    margin = Inches(0.5)
    available_width = prs.slide_width - 2 * margin
    available_height = prs.slide_height - Inches(1.5)  # 减去顶部和底部边距以及页脚高度

    title_height = Inches(0.5)  # 标题的高度
    gap = Inches(0.1)  # 标题与图片之间的间隙
    pic_gap = Inches(0.1)  # 图片之间的垂直间隙

    if N == 1:
        pic_width = available_width
        pic_height = available_height - title_height - gap
        left = margin
        top = (prs.slide_height - pic_height - title_height - gap) / 2
    elif N == 2:
        pic_width = (available_width - margin) / 2
        pic_height = available_height - title_height - gap
        left = [margin, margin + pic_width + margin / 2]
        top = (prs.slide_height - pic_height - title_height - gap) / 2
    elif N in [3, 4]:
        pic_width = (available_width - margin) / 2
        pic_height = (available_height - 2 * title_height - 2 * gap - pic_gap) / 2
        left = [margin + i % 2 * (pic_width + margin / 2) for i in range(4)]
        top = [
            (prs.slide_height - 2 * pic_height - 2 * title_height - 2 * gap - pic_gap)
            / 2
            + i // 2 * (pic_height + title_height + gap + pic_gap)
            for i in range(4)
        ]
    elif N in [5, 6]:
        pic_width = (available_width - margin) / 3
        pic_height = (available_height - 2 * title_height - 2 * gap - pic_gap) / 2
        left = [margin + i % 3 * (pic_width + margin / 2) for i in range(6)]
        top = [
            (prs.slide_height - 2 * pic_height - 2 * title_height - 2 * gap - pic_gap)
            / 2
            + i // 3 * (pic_height + title_height + gap + pic_gap)
            for i in range(6)
        ]

    length = len(picture_data)
    start_idx = page * N
    end_idx = min((page + 1) * N, length)

    for idx in range(start_idx, end_idx):
        print(f"渲染进度 {idx + 1}/{length}")
        pic_info = picture_data[idx]
        try:
            current_top = top if isinstance(top, float) else top[idx - start_idx]
            current_left = left if isinstance(left, float) else left[idx - start_idx]

            pic_path = pic_info["png"]
            if os.path.exists(pic_path):
                # 添加图片
                pic = slide.shapes.add_picture(
                    pic_path,
                    current_left,
                    current_top + title_height + gap,
                    width=pic_width,
                    height=pic_height,
                )

                # 添加描述文字（图片上方）
                txBox = slide.shapes.add_textbox(
                    current_left, current_top, pic_width, title_height
                )
                tf = txBox.text_frame
                p = tf.add_paragraph()
                p.text = f"{pic_info['unit']}_{pic_info['exp']}"
                p.font.bold = True
                p.font.size = Pt(12)
                p.font.name = "微软雅黑"
                p.alignment = PP_ALIGN.CENTER
            else:
                print(f"无法找到图片 {pic_path}")
        except Exception as e:
            import traceback
            print(f"无法加载图片 {pic_info['png']}: {e}\n{traceback.format_exc()}")
