# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/12/09
# __author:       <PERSON><PERSON><PERSON>

from openpyxl import load_workbook

from app.config import Backend, init_backend
from pyQCat.processor.chip_data import ChipConfigField
from pyQCat.processor.hardware_manager import HardwareOffsetManager


def v1_to_v2(backend: Backend, reset: bool = True):
    """线路延时管理器 Character 方式自动转化为 HardwareOffsetManager

    Args:
        backend (Backend): _description_
        reset (bool, optional): 转化前是否清空 HardwareOffsetManager 中的记录, 如果不清空，可能存在线路冲突.
    """
    chip_data = backend.chip_data
    character_data = chip_data.cache_config.get(ChipConfigField.character.value)
    hardware_offset_data = chip_data.cache_config.get(ChipConfigField.hardware_offset.value)
    offset_manager = HardwareOffsetManager.from_data(hardware_offset_data)
    offset_manager.to_origin_data(output_path="pre_offset_data.json")
    if reset is True:
        offset_manager.reset()
        for bit, data in character_data.items():
            if bit.startswith("q"):
                x_delay, z_delay = data["hardware_offset"][:-1]
                if x_delay or z_delay:
                    qubit = chip_data.cache_qubit.get(bit)
                    offset_manager.insert_xyz_timing(qubit.xy_channel, qubit.z_flux_channel, x_delay - z_delay)
    offset_manager.save_db()
    offset_manager.log_state()
    offset_manager.log_bit_info(chip_data)


def v2_to_v1(backend: Backend):
    """线路延时管理器 HardwareOffsetManager 方式自动转化为 Character

    Args:
        backend (Backend): _description_
    """
    chip_data = backend.chip_data
    character_data = chip_data.cache_config.get(ChipConfigField.character.value)
    hardware_offset_data = chip_data.cache_config.get(ChipConfigField.hardware_offset.value)
    offset_manager = HardwareOffsetManager.from_data(hardware_offset_data)
    for bit, data in character_data.items():
        if bit.startswith("q"):
            qubit = chip_data.cache_qubit.get(bit)
            x_delay = offset_manager.xy_delay(qubit.xy_channel)
            z_delay = offset_manager.xy_delay(qubit.z_flux_channel)
            data["hardware_offset"][0] = x_delay
            data["hardware_offset"][1] = z_delay
    backend.save_chip_data_to_db(ChipConfigField.character.value)


def build_v2_from_all_z(backend, z_line_path: str, is_save: bool = False, is_sync_character: bool = False):
    """从全 Z 通道测试数据构建延时管理器

    Args:
        backend (Backend): 测试终端, 由 config 配置的测试环境
        z_line_path (str): 全 Z 通道延时测试结果文件地址
        is_save (bool, optional): 是否将构建结果存储至数据库. Defaults to False.
        is_sync_character (bool, optional): 是否从 character.json 中同步 XYZTiming 延时. Defaults to False.
    """

    def infinite_iterator(start=1):
        value = start
        while True:
            yield value
            value += 1

    channel_iterator = infinite_iterator()

    manager = HardwareOffsetManager()

    workbook = load_workbook(filename=z_line_path)
    sheet = workbook["Sheet1"]
    for row in sheet.iter_rows(values_only=True):
        for offset in row[:8]:
            z_channel = next(channel_iterator)
            if offset is not None:
                manager.insert_releative_offset("z0", f"z{z_channel}", offset)

    if is_sync_character is True:
        character_data = backend.chip_data.cache_config.get(ChipConfigField.character)
        for bit, cd in character_data.items():
            xd, zd, _ = cd["hardware_offset"]
            if bit.startswith("q"):
                qubit = backend.chip_data.cache_qubit.get(bit)
                manager.insert_xyz_timing(qubit.xy_channel, qubit.z_flux_channel, xd - zd)

    manager.log_state()

    if is_save:
        manager.save_db()


if __name__ == "__main__":
    backend = init_backend()

    # 线路延时管理器 Character 方式自动转化为 HardwareOffsetManager
    # v1_to_v2(backend)

    # 线路延时管理器 HardwareOffsetManager 方式自动转化为 Character
    # v2_to_v1(backend)

    # 从全 Z 通道测试数据构建延时管理器
    build_v2_from_all_z(
        backend, z_line_path=r"app\data_extraction\Z通道延时.xlsx", is_save=False, is_sync_character=False
    )
