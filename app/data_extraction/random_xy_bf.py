# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/11/26
# __author:       <PERSON><PERSON><PERSON>


def random_set_qubit_xy_baseband_freq(left: int = -249, right: int = 249, step: int = 5):
    from app.config import init_backend
    import random
    
    backend = init_backend()
    
    start_freq = left
    for qubit_name, qubit in backend.chip_data.cache_qubit.items():
        cur_freq = round(start_freq + random.uniform(-1, 1), 3)
        qubit.XYwave.baseband_freq = cur_freq
        print(f"Set {qubit_name} baseband freq to {cur_freq} MHz")
        start_freq += step
        if start_freq > right:
            start_freq = left
            
    backend.save_chip_data_to_db(list(backend.chip_data.cache_qubit.keys()))


if __name__ == "__main__":
    random_set_qubit_xy_baseband_freq()