# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/02/28
# __author:       <PERSON><PERSON><PERSON>
# __need:         https://document.qpanda.cn/docs/m4kMLg0RJOh44dqD

import json
from dataclasses import dataclass

from loguru import logger

from app.config import init_backend
from pyQCat.executor.backend import Backend


def read_json_file(file_path):
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            data = json.load(file)
            return data
    except FileNotFoundError:
        logger.error(f"file {file_path} no find!")
    except json.JSONDecodeError:
        logger.error(f"file {file_path} is not a valid JSON format!")


@dataclass
class ConfigParams:
    width: int = 40
    buffer: float = 5
    sigma_qubit: float = 0.2
    sigma_coupler: float = 0.2
    pulse_type: str = "FlatTopGaussian"
    ca: float = 0.2
    qubit_point_file: str = ""
    pair_point_file: str = ""
    rb_spectrum_file: str = ""


class PointImportTool:
    def __init__(self, backend: Backend, config: ConfigParams, save_db: bool = False):
        self.backend: Backend = backend
        self.config: ConfigParams = config
        self.save_db = save_db

    def run(self):
        qubit_point_data = read_json_file(self.config.qubit_point_file)
        pair_point_data = read_json_file(self.config.pair_point_file)
        rb_spectrum_data = read_json_file(self.config.rb_spectrum_file)

        for qubit_name, freq in qubit_point_data.items():
            qubit_point_data = rb_spectrum_data[qubit_name]
            closest_value = min(
                [float(k) for k in qubit_point_data.keys()], key=lambda x: abs(x - freq)
            )
            qubit_data = qubit_point_data[str(closest_value)]
            qubit_obj = self.backend.chip_data.cache_qubit[qubit_name]
            qubit_obj.update(qubit_obj, qubit_data["params"])
            
            if self.save_db:
                qubit_obj.save_data()

        for pair_name, point_data in pair_point_data.items():
            pair_obj = self.backend.chip_data.cache_qubit_pair.get(pair_name)
            ql_name, qh_name, qc_name = point_data["ql"], point_data["qh"], pair_obj.qc
            pair_obj.metadata.std.ql = ql_name
            pair_obj.metadata.std.qh = qh_name
            coupler = self.backend.chip_data.cache_coupler[qc_name]

            ql_params = dict(
                buffer=self.config.buffer,
                sigma=self.config.sigma_qubit,
                pulse_type=self.config.pulse_type,
                freq=point_data["ql_freq"],
            )
            qh_params = dict(
                buffer=self.config.buffer,
                sigma=self.config.sigma_qubit,
                pulse_type=self.config.pulse_type,
                freq=point_data["qh_freq"],
            )
            qc_params = dict(
                buffer=self.config.buffer,
                sigma=self.config.sigma_qubit,
                pulse_type=self.config.pulse_type,
                amp=self.config.ca if coupler.idle_point > 0 else -self.config.ca,
            )
            pair_obj.metadata.std.cz.width = self.config.width
            pair_obj.metadata.std.cz.params[ql_name].update(ql_params)
            pair_obj.metadata.std.cz.params[qh_name].update(qh_params)
            pair_obj.metadata.std.cz.params[qc_name].update(qc_params)
            
            if self.save_db:
                pair_obj.save_data()
    
                
if __name__ == "__main__":
    backend = init_backend()
    config = ConfigParams(
        qubit_point_file="/home/<USER>/code/pyqcat-apps/app/data_extraction/point_import/qubit_freq_dict.json",
        pair_point_file="/home/<USER>/code/pyqcat-apps/app/data_extraction/point_import/pair_freq_dict.json",
        rb_spectrum_file="/home/<USER>/code/pyqcat-apps/app/data_extraction/point_import/20250225_rb_records_checked.json"
    )
    tool = PointImportTool(backend, config, save_db=True)
    tool.run()

