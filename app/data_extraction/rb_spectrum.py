# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/10/12
# __author:       <PERSON><PERSON><PERSON>

import os
import traceback
from collections import defaultdict
from datetime import datetime

import pandas as pd
from app.tool.utils import logger, read_json_file

from pyQCat.structures import QDict


def extract_data_from_dict(field: str, record: dict):
    if field in record:
        return record[field]

    result = None

    if "." in field:
        field_list = field.split(".")
        for child_field in field_list:
            if child_field in record:
                record = record[child_field]
            else:
                return None
        result = record

    return result


def extract_rb_spectrum(
    rb_record_path: str,
    result_save_path: str = "",
    extract_params=["drive_freq", "T1", "T2", "TS2", "XYwave.Xpi"],
):
    """https://document.qpanda.cn/presentation/Wr3DVzxp9aUERZkJ

    Args:
        rb_record_path (str): the path of `rb_records.json`
        result_save_path (str, optional): Target storage path. Defaults to "".
        extract_params (list, optional): target extract params. Defaults to ["drive_freq", "T1", "T2", "TS2"].
    """
    rb_record = read_json_file(rb_record_path)

    if rb_record:
        data_frames = []
        rb_record = QDict(**rb_record)
        for qubit_name, rb_data in rb_record.items():
            param_list_map = defaultdict(list)

            for freq, conf in rb_data.items():
                if not conf.params:
                    continue

                param_list_map["Qubit"].append(qubit_name)
                param_list_map["F01"].append(freq)
                param_list_map["p0"].append(conf.p0 or "")
                param_list_map["p1"].append(conf.p1 or "")
                param_list_map["rb_fidelity"].append(conf.rb_fidelity or "")

                for p in extract_params:
                    param_list_map[p].append(extract_data_from_dict(p, conf.params))

            df = pd.DataFrame(dict(param_list_map))
            data_frames.append(df)

        result_df = pd.concat(data_frames, ignore_index=True)

        try:
            file_name = f"result-{datetime.now().strftime('%Y-%m-%d %H-%M-%S')}.xlsx"
            result_save_path = result_save_path or os.path.dirname(__file__)
            os.makedirs(result_save_path, exist_ok=True)
            output_path = os.path.join(result_save_path, file_name)
            result_df.to_excel(output_path, index=False)
            logger.success(f"Data has been saved to {output_path}")
        except Exception as e:
            logger.error(f"saved to {output_path} error! cause by {e} | detail\n{traceback.format_exc()}")


if __name__ == "__main__":
    extract_rb_spectrum(rb_record_path=r"D:\project\SupQAutomation\code\project\app\protobuf\pyqcat-apps\app\data_extraction\rb_records_2.json")
