import re
import matplotlib.pyplot as plt
import pandas as pd
import os
import matplotlib.dates as mdates
import matplotlib.ticker as ticker


log_files = [r"Z:\kwc\log\monster-2025-02-25.2025-02-25_20-18-37_089806.log", r"Z:\kwc\log\monster-2025-02-25.log", ]
search_string = "|  RESULT  | pyQCat.experiments.parallel_experiment:_display_experiment_result:150"


def match_log(log_content):
    pattern = r"\| (\w+-\w+_\d+) \|  SUC  \| K=(\d+) F0=([\d.]+) F1=([\d.]+) OL=([\d.]+) \|"
    timestamp_pattern = r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})"

    matches = re.findall(pattern, log_content)
    timestamp_match = re.search(timestamp_pattern, log_content)
    timestamp = timestamp_match.group(1) if timestamp_match else "Unknown Timestamp"

    # 转换为字典
    result = {}
    for match in matches:
        task, k, f0, f1, ol = match
        result[task.split("-")[0]] = {"K": int(k), "F0": float(f0), "F1": float(f1), "OL": float(ol)}

    data = dict(
        timestamp=timestamp,
        data=result
    )
    return data


def plot_record(result):
    # 将数据转换为 Pandas DataFrame
    data_list = []
    for entry in result:
        timestamp = entry["timestamp"]
        for q, values in entry["data"].items():
            data_list.append({
                "timestamp": timestamp,
                "q": q,
                "K": values["K"],
                "F0": values["F0"],
                "F1": values["F1"]
            })

    df = pd.DataFrame(data_list)
    df["timestamp"] = pd.to_datetime(df["timestamp"])

    # 创建保存图表的目录
    output_dir = "output_plots"
    os.makedirs(output_dir, exist_ok=True)

    # 提取所有 q 的值
    q_values = df["q"].unique()

    # 为每个 q 绘制单独的图表
    for q in q_values:
        print(q)
        subset = df[df["q"] == q]

        # 创建图表
        fig, axes = plt.subplots(3, 1, figsize=(10, 12), sharex=True)

        # 绘制 K 的波动图
        axes[0].plot(subset["timestamp"], subset["K"], label="K", color="blue")
        axes[0].set_title(f"{q} - K over Time")

        axes[0].set_ylabel("K")

        # 绘制 F0 的波动图
        axes[1].plot(subset["timestamp"], subset["F0"], label="F0", color="green")
        axes[1].set_title(f"{q} - F0 over Time")
        axes[1].set_ylabel("F0")
        axes[1].set_ylim(0, 1)

        # 绘制 F1 的波动图
        axes[2].plot(subset["timestamp"], subset["F1"], label="F1", color="red")
        axes[2].set_title(f"{q} - F1 over Time")
        axes[2].set_xlabel("Time")
        axes[2].set_ylabel("F1")
        axes[2].set_ylim(0, 1)

        # 添加图例
        for ax in axes:
            ax.legend()

        # 自动调整 X 轴时间标签的稀疏程度
        axes[2].xaxis.set_major_locator(ticker.MaxNLocator(nbins=6))  # 自动调整刻度数量[^11^]
        axes[2].xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d %H:%M"))  # 设置时间格式
        plt.xticks(rotation=45, ha='right')  # 倾斜角度为 45 度，水平对齐方式为右对齐

        # 调整布局
        plt.tight_layout()

        # 保存图表到本地
        output_path = os.path.join(output_dir, f"{q}_plot.png")
        plt.savefig(output_path)
        plt.close(fig)  # 关闭当前图表以释放资源

    print(f"所有图表已保存到 {output_dir} 目录中。")


def read_log():
    stability_data = []

    # 打开日志文件并逐行检查
    for log_file in log_files:
        with open(log_file, "r", encoding="utf-8") as infile:
            cur_text = ""
            start_find = False
            index = 0
            for line in infile:

                if index > 30:
                    index = 0
                    start_find = False
                    result = match_log(cur_text)
                    stability_data.append(result)
                    print(result)
                    cur_text = ""

                if start_find is True:
                    cur_text += line
                    index += 1
                    continue

                if search_string in line:
                    start_find = True
                    cur_text += line
                    # outfile.write(line)

    return stability_data


if __name__ == '__main__':
    plot_record(read_log())
