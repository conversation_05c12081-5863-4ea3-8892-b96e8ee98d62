# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/01/03
# __author:       <PERSON><PERSON><PERSON>

import atexit
import csv
import json
import os
import pickle
import re
import textwrap
import traceback
from collections import defaultdict
from concurrent.futures import ProcessPoolExecutor, as_completed
from datetime import datetime
from pathlib import Path
from typing import Callable, Dict, List, Optional, Union

import matplotlib.dates as mdates
import matplotlib.pyplot as plt
from bson import ObjectId
from loguru import logger
from prettytable import MARKDOWN
from pymongo import MongoClient

from pyQCat.processor import HeatMap
from pyQCat.processor.chip_data import ChipConfigField, ChipData, ChipPhysicalUnit
from pyQCat.qubit import NAME_PATTERN
from pyQCat.structures import QDict
from pyQCat.tools.utilities import display_dict_as_table

FIND_USER = []
FIND_POINT = []
FIND_SAMPLE = []
FIND_ENV = []
FILTER_SAMPLE = ["Fake-1000Bit", "Fake-500Bit", "Fake-300Bit", "Fake-200Bit"]
FILTER_ENV = []
HOST = "***********"
PORT = 27017
FIELD = ["dc_max", "dc_min"]

USE_PROCESS_POOL = True
RECORD_MD = False
SAVE_PATH = r"F:\Test\heatmap"
PROCESS_NUM = 15
STD_FIELD = QDict(
    qubit=[
        "drive_freq",
        "drive_power",
        "probe_freq",
        "probe_power",
        "anharmonicity",
        "dc_max",
        "dc_min",
        "T1",
        "T2",
        "idle_point",
        "fidelity",
    ],
    coupler=[
        "drive_freq",
        "drive_power",
        "probe_freq",
        "probe_power",
        "anharmonicity",
        "dc_max",
        "dc_min",
        "T1",
        "T2",
        "idle_point",
    ],
    pair=[
        "metadata.std.fidelity.xeb_fidelity",
        "metadata.std.cz.params.ql.phase",
        "metadata.std.cz.params.qh.phase",
    ],
)

MD_PATH = str(Path(SAVE_PATH, "chip_blueprint.md"))
if USE_PROCESS_POOL is True:
    RECORD_MD = False

CHIP_DATA = ChipData()

store = QDict(config_store=None, base_qubit=None, base_qubit_his=None, client=None, executor=None)


class JSONEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, ObjectId):
            return str(o)
        elif isinstance(o, datetime):
            return str(o)
        elif isinstance(o, bytes):
            return "fake binary data"
        return super().default(o)


def _close_signal():
    if store.client:
        store.client.close()
        logger.info("关闭 mongo 链接")


def _init_mongo_client():
    client = MongoClient(f"mongodb://bylz:fjsaoJOIjiojj28hjj@{HOST}:{PORT}")
    db = client["UserData"]
    store.config_store = db["ConfigStore"]
    store.base_qubit = db["BaseQubit"]
    store.base_qubit_his = db["BaseQubitHistory"]
    store.client = client
    atexit.register(_close_signal)
    logger.info(f"os {os.getpid()} start ...")


def _write_to_file(msg=None):
    if RECORD_MD:
        if msg is None:
            with open(MD_PATH, "w", encoding="utf-8") as md_file:
                md_file.write("# Qubit Blue Print \n\n")
        else:
            with open(MD_PATH, "a+", encoding="utf-8") as md_file:
                md_file.write(msg)


def _query_all_qubits_from_chip(chip):
    if store.executor:
        return store.executor.submit(_query_and_plot_heatmap, chip)
    return _query_and_plot_heatmap(chip)


def _query_and_plot_heatmap(chip):
    """
    Retrieves qubit data for a given chip, filters it based on user and point label,
    and generates heatmap for the specified fields.

    Args:
        chip (dict): A dictionary containing chip information, including 'sample', 'env_name', and 'json'.

    Returns:
        bool: True if the process is successful, False otherwise.

    Raises:
        Exception: Any exception that occurs during database operations or plotting.
    """
    try:
        # Extract qubit parameters from the chip data.
        parameters = chip["json"]["QubitParams"]

        # Initialize a dictionary to store user-point data.
        user_point_map = defaultdict(list)

        # Query the database for qubit data and filter it based on user and point label.
        for bit in parameters:
            qubit_results = store.base_qubit.find(
                dict(sample=chip["sample"], env_name=chip["env_name"], name=bit), sort=[("create_time", -1)]
            )
            for qubit in qubit_results:
                username = qubit["username"]
                point_label = qubit["point_label"]

                # Determine whether to include the qubit data based on filtering criteria.
                add_flag = (
                    (not FIND_POINT and not FIND_USER)
                    or (FIND_POINT and FIND_USER and username in FIND_USER and point_label in FIND_POINT)
                    or (username in FIND_USER and not FIND_POINT)
                    or (point_label in FIND_POINT and not FIND_USER)
                )

                if add_flag:
                    user_point_map[(username, point_label)].append(qubit)

        # Remove entries where data is incomplete.
        for info in list(user_point_map.keys()):
            if len(user_point_map[info]) != len(parameters):
                logger.warning(f"{info} 数据不全")
                user_point_map.pop(info)

        # Generate heatmap and save them to files.
        qubit_records = []
        for (username, point_label), records in user_point_map.items():
            if FIELD:
                cur_chip_data = {"chip_line_connect.json": chip}
                for cq in records:
                    cur_chip_data[cq["name"]] = cq
                CHIP_DATA.cache_qubit.clear()
                CHIP_DATA.cache_config.clear()
                CHIP_DATA.refresh(cur_chip_data)

                for field in FIELD:
                    try:
                        save_path = Path(
                            SAVE_PATH,
                            str(chip["env_name"]),
                            str(chip["sample"]),
                            username,
                            point_label,
                            f"{field}.png",
                        )
                        parent_dir = save_path.parent
                        if not parent_dir.exists():
                            parent_dir.mkdir(parents=True, exist_ok=True)

                        # save heatmap png
                        _, value_map = CHIP_DATA.physical_unit_heatmap(field)
                        heatmap = HeatMap(
                            value_map,
                            layout_style=CHIP_DATA.chip_layout_style,
                            show_value=True,
                            edit_units={},
                            annotation_format=".5g",
                        )
                        title = f"{chip['sample']}_|_{chip['env_name']} {username} {point_label} Qubit.{field}"
                        title = "\n".join(textwrap.wrap(title, width=30))
                        _, fig, _ = heatmap.plot(title=title)
                        fig.savefig(save_path)

                        # save csv data
                        CHIP_DATA.to_xlsx(
                            name_of_data="Qubit",
                            name_of_attribute=field,
                            color_scheme="info",
                            path_of_file=str(Path(save_path.parent, f"{field}.csv")),
                            exist_ok=True,
                        )
                    except Exception as e:
                        logger.error(f"{chip['sample']} {chip['env_name']} {username} {point_label} error: \n{e}")

            qubit_records.append(
                dict(username=username, point_label=point_label, last_update=records[0]["create_time"])
            )

        # Display qubit records as a table and write it to a file.
        if qubit_records:
            qubit_table = display_dict_as_table(qubit_records)
            qubit_table.set_style(MARKDOWN)
            _write_to_file(f"## {chip['sample']}_|_{chip['env_name']}\n\n{qubit_table}\n\n")
        return True
    except Exception:
        # Log the exception traceback
        logger.error(f"{chip['sample']} {chip['env_name']} error: \{traceback.format_exc()}")
        return False


def query_all_chip_line():
    """
    Retrieves all chip line connections from the database, filters them based on sample and environment,
    and queries all qubits from each chip if they meet the specified criteria.

    If USE_PROCESS_POOL is True, it utilizes a ProcessPoolExecutor to parallelize the qubits query.

    Args:
        None

    Returns:
        None

    Raises:
        Exception: Any exception that occurs during database operations or multiprocessing.
    """
    _init_mongo_client()
    _write_to_file()

    if USE_PROCESS_POOL:
        # Initialize a process pool executor if multiprocessing is enabled.
        store.executor = ProcessPoolExecutor(max_workers=PROCESS_NUM, initializer=_init_mongo_client)

    # Query the database for all chip line connections, sorted by update time in descending order.
    chip_line_connect_results = store.config_store.find(
        {"filename": "chip_line_connect.json"}, sort=[("update_time", -1)]
    )

    sample_records = []
    chip_data = []
    for chip in chip_line_connect_results:
        # Extract relevant information from each chip line connection and store it in sample_records.
        sample_records.append(
            dict(
                sample=chip["sample"],
                env_name=chip["env_name"],
                shape=chip["json"].get("shape"),
                layout_style=chip["json"].get("layout_style"),
                last_update=chip["update_time"],
            )
        )
        # Store the raw chip data for further processing.
        chip_data.append(chip)

    # Display the sample records as a table and write it to a file in Markdown format.
    sample_table = display_dict_as_table(sample_records)
    sample_table.set_style(MARKDOWN)
    _write_to_file(f"## Database all chip information\n\n{sample_table}\n\n")

    futures = []
    for chip in chip_data:
        # Skip the current chip if it matches the filter criteria.
        if chip["sample"] in FILTER_SAMPLE or chip["env_name"] in FILTER_ENV:
            continue

        # Determine whether to query all qubits from the current chip based on sample and environment filters.
        if not FIND_SAMPLE and not FIND_ENV:
            task = _query_all_qubits_from_chip(chip)
        elif FIND_SAMPLE and FIND_ENV:
            if chip["sample"] in FIND_SAMPLE and chip["env_name"] in FIND_ENV:
                task = _query_all_qubits_from_chip(chip)
        elif FIND_SAMPLE and chip["sample"] in FIND_SAMPLE:
            task = _query_all_qubits_from_chip(chip)
        elif chip["env_name"] in FIND_ENV:
            task = _query_all_qubits_from_chip(chip)

        # If a task is created, add it to the list of futures for tracking.
        if task:
            futures.append(task)

    # If multiprocessing is enabled, wait for all tasks to complete and shutdown the executor.
    if USE_PROCESS_POOL:
        logger.info(f"{len(futures)} tasks submitted, waiting for completion...")
        for task in as_completed(futures):
            pass  # This loop does nothing, it just waits for all tasks to finish.
        store.executor.shutdown(wait=True)


def query_user_data(
    username: str,
    sample: str,
    env_name: str,
    point_label: str,
    data_names: Optional[Union[List[str], str]] = None,
    record_fields: Optional[Dict[ChipPhysicalUnit, List[str]]] = None,
    record_objects: Optional[Dict[ChipPhysicalUnit, List[str]]] = None,
    callback: Optional[Dict[ChipPhysicalUnit, Dict[str, Callable]]] = None,
    find_history: bool = False,
    host: str = "***********",
    port: int = 27017,
):
    """
    Queries user data from MongoDB based on provided parameters.

    Args:
        username (str): The username for which to query the data.
        sample (str): The sample identifier for the data.
        env_name (str): The environment name for the data.
        point_label (str): The label for the data point.
        data_names (Optional[Union[List[str], str]]): A single data name or a list of data names to query.
        record_fields (Optional[Dict[ChipPhysicalUnit, List[str]]]): Expected bit attributes to be extracted
        record_objects (Optional[Dict[ChipPhysicalUnit, List[str]]]): Record local bits
        callback (Optional[Dict[ChipPhysicalUnit, Dict[str, Callable]]]): Destruction function for custom attributes
        find_history (bool): A flag to indicate whether to find historical data.
        host (str): The MongoDB host address. Defaults to "***********".
        port (int): The MongoDB port number. Defaults to 27017.

    Returns:
        dict: A dictionary mapping data names to their corresponding data documents.

    Raises:
        Exception: Any exception that occurs during the execution of this function.
    """
    # Establish a connection to the MongoDB server
    client = MongoClient(f"mongodb://bylz:fjsaoJOIjiojj28hjj@{host}:{port}")
    db = client["UserData"]
    config_store_collections = db["ConfigStore"]
    base_qubit_collections = db["BaseQubit"]
    base_qubit_history_collections = db["BaseQubitHistory"]

    chip_data_map = {}

    try:
        # If no specific data names are provided, query for all relevant data
        if not data_names:
            chip_line_connect = config_store_collections.find_one(
                dict(sample=sample, env_name=env_name, username="common")
            )
            config_data_results = config_store_collections.find(
                dict(sample=sample, env_name=env_name, username=username)
            )
            chip_data_map["chip_line_connect.json"] = chip_line_connect
            for data in config_data_results:
                chip_data_map[data["filename"]] = data

            # Query for qubit data
            for qubit in chip_line_connect["json"]["QubitParams"]:
                qubit_data = base_qubit_collections.find_one(
                    dict(
                        name=qubit,
                        sample=sample,
                        env_name=env_name,
                        point_label=point_label,
                        username=username,
                    )
                )
                chip_data_map[qubit] = qubit_data

            # Query for coupler data and related pair data
            for coupler in chip_line_connect["json"]["CouplerParams"]:
                coupler_data = base_qubit_collections.find_one(
                    dict(
                        name=coupler,
                        sample=sample,
                        env_name=env_name,
                        point_label=point_label,
                        username=username,
                    )
                )
                chip_data_map[coupler] = coupler_data
                pair_name = "q" + "q".join(coupler[1:].split("-"))
                pair_data = base_qubit_collections.find_one(
                    dict(
                        name=pair_name,
                        sample=sample,
                        env_name=env_name,
                        point_label=point_label,
                        username=username,
                    )
                )
                if pair_data:
                    chip_data_map[pair_name] = pair_data
        else:
            # If specific data names are provided, query for those
            if isinstance(data_names, str):
                data_names = [data_names]

            for name in data_names:
                if name in ChipConfigField._value2member_map_:
                    un = "common" if name == ChipConfigField.chip_line_connect else username
                    chip_data_map[name] = config_store_collections.find_one(
                        dict(sample=sample, env_name=env_name, username=un, filename=name)
                    )
                elif find_history:
                    qubit_results = base_qubit_history_collections.find(
                        dict(
                            name=name,
                            sample=sample,
                            env_name=env_name,
                            point_label=point_label,
                        ),
                        sort=[("create_time", -1)],
                    )
                    chip_data_map[name] = list(qubit_results)
                else:
                    chip_data_map[name] = base_qubit_collections.find_one(
                        dict(
                            name=name,
                            sample=sample,
                            env_name=env_name,
                            point_label=point_label,
                        )
                    )

        if record_fields:
            CHIP_DATA.refresh(chip_data_map)
            if not record_objects:
                record_objects = {
                    ChipPhysicalUnit.qubit: list(CHIP_DATA._cache_qubit.keys()),
                    ChipPhysicalUnit.coupler: list(CHIP_DATA._cache_coupler.keys()),
                    ChipPhysicalUnit.pair: list(CHIP_DATA._cache_qubitpair.keys()),
                }

            for bit_type, fields in record_fields.items():
                if bit_type == ChipPhysicalUnit.qubit:
                    if record_objects.get(ChipPhysicalUnit.qubit):
                        records = {bit: {"name": bit} for bit in record_objects.get(ChipPhysicalUnit.qubit)}
                    else:
                        records = {bit: {"name": bit} for bit in CHIP_DATA._cache_qubit}
                elif bit_type == ChipPhysicalUnit.coupler:
                    if record_objects.get(ChipPhysicalUnit.coupler):
                        records = {bit: {"name": bit} for bit in record_objects.get(ChipPhysicalUnit.coupler, [])}
                    else:
                        records = {bit: {"name": bit} for bit in CHIP_DATA._cache_coupler}
                elif bit_type == ChipPhysicalUnit.pair:
                    if record_objects.get(ChipPhysicalUnit.pair):
                        records = {bit: {"name": bit} for bit in record_objects.get(ChipPhysicalUnit.pair)}
                    else:
                        records = {bit: {"name": bit} for bit in CHIP_DATA._cache_qubitpair}
                else:
                    continue
                cur_path = Path(
                    SAVE_PATH, env_name, sample, username, point_label, f"{ChipPhysicalUnit.qubit.value}.csv"
                )
                for field in fields:
                    value_map, _ = CHIP_DATA.physical_unit_heatmap(field, bit_type=bit_type)
                    if not value_map:
                        if callback and bit_type in callback and field in callback[bit_type]:
                            value_map = callback[bit_type][field](list(records.keys()))
                        else:
                            value_map = dict.fromkeys(records)
                    for k in records.keys():
                        records[k][field] = value_map.get(k)
                table = display_dict_as_table(list(records.values()))
                csv_string = table.get_csv_string()
                with open(cur_path, "w", newline="") as f:
                    f.write(csv_string)
                logger.info(f"{bit_type} result:\n{table}\n{cur_path}")

        for name in list(chip_data_map.keys()):
            if name.endswith(".bin"):
                try:
                    bin_bytes = chip_data_map[name].get("bin")
                    chip_data_map[name] = pickle.loads(bin_bytes).to_dict()
                except Exception as e:
                    chip_data_map[name]["bin"] = str(e)

        filename = Path(SAVE_PATH, env_name, sample, username, point_label, "chip_data.json")
        filename.parent.mkdir(parents=True, exist_ok=True)
        with open(str(filename), mode="w", encoding="utf-8") as fp:
            json.dump(chip_data_map, fp, ensure_ascii=False, indent=4, cls=JSONEncoder)
        logger.info(f"save in | {filename}")

    except Exception:
        # Log the exception traceback
        logger.error(traceback.format_exc())
    finally:
        # Ensure the MongoDB client is closed
        client.close()
        return chip_data_map


def is_work_method(bits):
    result = {}
    for bit in bits:
        qubit = CHIP_DATA.get_physical_unit(bit)
        result[bit] = qubit.goodness and quit.tunable
    return result


def _get_nested_value(data, key, extra_data: Optional[Dict] = None):
    """
    Retrieves a value from a nested dictionary using a dot-separated key string.

    This function splits the key string into individual keys and traverses the nested dictionary
    to retrieve the value. If any key in the path does not exist, the function returns None.

    Args:
        data (dict): The nested dictionary to search.
        key (str): The dot-separated key string (e.g., "a.b.c").

    Returns:
        any: The value associated with the key path, or None if the path does not exist.

    Example:
        >>> data = {"a": {"b": {"c": "value"}}}
        >>> result = get_nested_value(data, "a.b.c")
        >>> print(result)
        value

    Note:
        This function assumes that the input dictionary is well-formed and does not contain
        any cycles. If the key path is invalid, the function returns None.

    See Also:
        set_nested_value: A function to set a value in a nested dictionary.
    """
    extra_data = extra_data or {}
    keys = key.split(".")
    current_data = data

    for k in keys:
        k = extra_data.get(k, k)
        if isinstance(current_data, dict) and k in current_data:
            current_data = current_data[k]
        else:
            return None  # Return None if any key in the path does not exist
    return current_data


def query_history(
    user: str,
    sample: str,
    env: str,
    point_labels: Optional[List[str]] = None,
    bit_names: Optional[List[str]] = None,
    fields: Optional[Union[str, List[str]]] = None,
    date_start: Optional[str] = None,
    date_end: Optional[str] = None,
    mode: Optional[str] = None,
):
    input_data = dict(
        user=user,
        sample=sample,
        env=env,
        point_labels=point_labels,
        bit_names=bit_names,
        fields=fields,
        date_start=date_start,
        date_end=date_end,
    )
    head_path = os.path.join(SAVE_PATH, "history", f"{sample} {env}", user)
    formatted_now = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    log_path = os.path.join(head_path, "log", f"history-{formatted_now}.log")
    if os.path.exists(log_path):
        with open(log_path, "w") as f:
            f.write("")
    logger.add(
        sink=log_path,
        format="<yellow>{time:YYYY-MM-DD HH:mm:ss.SSS}</yellow> | <level>{message}</level>",
    )
    logger.info(f"Query History:\n{display_dict_as_table(input_data)}")

    records = {}

    _init_mongo_client()

    query_params = dict(
        sample=sample,
        env_name=env,
        username=user,
    )
    if point_labels:
        query_params["point_label"] = {"$in": point_labels}
    if bit_names:
        query_params["name"] = {"$in": bit_names}

    qubit_results = store.base_qubit_his.find(query_params, sort=[("create_time", -1)])
    for result in qubit_results:
        point_label = result["point_label"]
        name = result["name"]
        ct = result["create_time"]

        if (date_start and ct < date_start) or (date_end and ct > date_end):
            continue

        parameters = result["parameters"]
        cur_data = dict(create_time=ct)
        print(type(ct), ct)

        if point_label not in records:
            records[point_label] = {}
        if name not in records[point_label]:
            records[point_label][name] = []

        if re.match(NAME_PATTERN.qubit, name) and (not mode or mode == ChipPhysicalUnit.qubit):
            cur_fields = fields or STD_FIELD.qubit
            for field in cur_fields:
                cur_data[field] = _get_nested_value(parameters, field)
            records[point_label][name].append(cur_data)
        elif re.match(NAME_PATTERN.coupler, name) and (not mode or mode == ChipPhysicalUnit.coupler):
            cur_fields = fields or STD_FIELD.coupler
            for field in cur_fields:
                cur_data[field] = _get_nested_value(parameters, field)
            records[point_label][name].append(cur_data)
        elif re.match(NAME_PATTERN.qubit_pair, name) and (not mode or mode == ChipPhysicalUnit.pair):
            qn_map = dict(
                ql=parameters["metadata"]["std"]["ql"],
                qh=parameters["metadata"]["std"]["qh"],
                qc=parameters["metadata"]["std"]["qc"],
            )
            cur_fields = fields or STD_FIELD.pair
            for field in cur_fields:
                cur_data[field] = _get_nested_value(parameters, field, qn_map)
            records[point_label][name].append(cur_data)

    body_message = []
    for point, all_data in records.items():
        for bit, rs in all_data.items():
            if rs:
                body_message.append(
                    dict(
                        point_label=point,
                        name=bit,
                        update_count=len(rs),
                        first_time=rs[-1]["create_time"],
                        final_time=rs[0]["create_time"],
                    )
                )
    if body_message:
        logger.info(f"{sample} | {env} | {user} history details:\n{display_dict_as_table(body_message)}")

    for point, all_data in records.items():
        for bit, rs in all_data.items():
            if rs:
                csv_path = os.path.join(head_path, point, bit, f"{bit}-{formatted_now}.csv")
                png_path = os.path.join(head_path, point, bit, f"{bit}-{formatted_now}.png")
                os.makedirs(os.path.dirname(csv_path), exist_ok=True)
                plot_history(rs, png_path, bit)
                format_table = display_dict_as_table(rs)
                logger.info(f"{sample} | {env} | {user} | {point} | {bit} details:\nPath: {csv_path}\n{format_table}")
                with open(csv_path, "w", newline="") as f:
                    csv_writer = csv.writer(f)
                    csv_writer.writerow(format_table.field_names)
                    for row in format_table._rows:
                        csv_writer.writerow(row)

    logger.warning(f"History data generate success!\nLog path: {log_path}")


def plot_history(data: List[Dict], save_path: str, bit: str):
    # extract create_time
    create_times = [entry["create_time"] for entry in data]

    # extract other attribute（remove create_time）
    attributes = [key for key in data[0].keys() if key != "create_time"]

    # Dynamically create subgraphs
    num_attributes = len(attributes)
    fig, axes = plt.subplots(num_attributes, 1, figsize=(10, 5 * num_attributes))

    # If there is only one attribute, axes is not a list and needs to be converted to a list
    if num_attributes == 1:
        axes = [axes]

    # Traverse each attribute and draw a chart
    for i, attr in enumerate(attributes):
        ax = axes[i]
        values = [entry[attr] for entry in data]
        ax.plot(create_times, values, marker="o", label=attr)
        ax.set_ylabel(attr)
        ax.set_title(f"{bit} - {attr} vs. Time")
        ax.legend()
        ax.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d %H:%M:%S"))
        ax.xaxis.set_major_locator(mdates.AutoDateLocator())
        plt.setp(ax.get_xticklabels(), rotation=45)

    # Adjust layout
    plt.tight_layout()

    # Display chart
    fig.savefig(save_path)

    plt.close()


if __name__ == "__main__":
    # query_all_chip_line()
    query_history(
        user="BY170003",
        sample="241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）",
        env="Y3",
        point_labels=["2q_gate"],
        bit_names=["q1q7"],
        mode=ChipPhysicalUnit.pair,
        date_start=datetime(2025, 1, 24, 0, 0, 0),
        date_end=datetime.now(),
        fields=[
            "metadata.std.fidelity.xeb_fidelity",
            "metadata.std.cz.params.ql.phase",
            "metadata.std.cz.params.qh.phase",
            "metadata.std.cz.params.qh.amp",
        ]
    )
