# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/13
# __author:       <PERSON><PERSON><PERSON>


"""
功能：根据 Z Exp 实验测出的全 Z 通道延时和室温畸变数据，完成数据库一键导入

提出者：张盛
"""

import os
from glob import glob

import numpy as np
from loguru import logger

from app.config import init_backend
from app.tool.utils import read_json_file
from pyQCat.processor.chip_data import ChipConfigField
from pyQCat.processor.hardware_manager import HardwareOffsetManager


def find_files_with_suffix(parent_path, suffix):
    """Searches for files with a specific suffix in a parent directory and its subdirectories.

    Args:
        parent_path (str): The parent directory to search in.
        suffix (str): The suffix of the files to search for (e.g., 'LeakageNumV2.epd').

    Returns:
        List[str]: A list of file paths that match the suffix.
    """
    # Use glob to recursively search for files with the given suffix
    search_pattern = os.path.join(parent_path, "**", f"*{suffix}")
    matching_files = glob(search_pattern, recursive=True)

    return matching_files


def main(sos_path: str, offset_path: str, limit: float = 200.0, is_save: bool = False):
    backend = init_backend()
    chip_data = backend.chip_data
    chip_line_connect_data = chip_data.cache_config.get(
        ChipConfigField.chip_line.value
    )
    channel_map = {}
    for bit_name, data in chip_line_connect_data["QubitParams"].items():
        channel_map[str(data["z_flux_channel"])] = bit_name
    for bit_name, data in chip_line_connect_data["CouplerParams"].items():
        channel_map[str(data["z_flux_channel"])] = bit_name
    
    hardware_offset_data = chip_data.cache_config.get(
        ChipConfigField.hardware_offset.value
    )
    offset_manager = HardwareOffsetManager.from_data(hardware_offset_data)
    offset_manager.reset()
    offset_data = read_json_file(offset_path)
    base_channel = f"z{offset_data['ref']}"
    for channel, offset in offset_data["delay"].items():
        offset = float(offset)
        if offset > 200:
            logger.warning(f"channel {channel} offset is {offset} ns, limit {limit} ns")
            continue
        offset_manager.insert_z2_timing(base_channel, channel, float(offset))

    character_data = chip_data.cache_config.get(ChipConfigField.character.value)
    sos_files = find_files_with_suffix(sos_path, "sos_digital_filter_RT.dat")
    for sf in sos_files:
        file_name = os.path.basename(sf)
        bit = file_name.split("_")[0]
        if bit.startswith("Z"):
            bit = channel_map[bit[1:]]
        character_data[bit]["distortion_sos"]["Room_temperature_sos_filter"] = (
            np.loadtxt(sf).tolist()
        )
        logger.info(f"insert {bit} room sos success!")

    if is_save is True:
        backend.save_chip_data_to_db(
            names=[
                ChipConfigField.hardware_offset.value,
                ChipConfigField.character.value,
            ]
        )


def change_awg_bias_offset(offset: float = 200., is_save: bool = True):
    backend = init_backend()
    chip_data = backend.chip_data

    character_data = chip_data.cache_config.get(
        ChipConfigField.character.value
    )
    for qubit, data in character_data.items():
        data["hardware_offset"] = [0, offset, 0]
        
    if is_save is True:
        backend.save_chip_data_to_db(
            names=[
                ChipConfigField.character.value,
            ]
        )


if __name__ == "__main__":
    # main(
    #     sos_path=r"app/data_extraction/offset_and_distortion_import/data",
    #     offset_path=r"/home/<USER>/code/pyqcat-apps/app/data_extraction/offset_and_distortion_import/data/Y4_channel_delay.json",
    #     is_save=False,
    # )
    
    change_awg_bias_offset()
