# 室温畸变使用说明

## 配置文件

1. 按照指定的格式存储各机箱通道和bit的通道关系映射表

- ![图片](config/awg_channel_map.png)
- ![图片](config/xy_channel_map.png)

2. \__init__.py通用参数说明

   \__init__.py中存储的是各个脚本之间的通用参数，实验开始前前将此文件中的参数一一填写正确，每个参数的说明如下

- oscilloscope_file_type：示波器存储的畸变文件格式，两个参数选择：qubit/channel
  - qubit，表示以bit和coupler的方式来命名畸变数据/z51-52、C2-7-C2-8；
    - ![image-20250529111543028](config/qubit.png)
  - channel， 表示以awg通道的方式来命名畸变数据/AWG14-15、AWG39-40;
    - ![image](config/channel.png)
- channel_file_path：机箱通道和bit的关系映射表的文件路径
- oscilloscope_path：示波器畸变文件的存储路径
- base_file：数据处理保存的根目录，后续所有的数据处理文件存储在此文件内
- time_data_path：时序分析存储的路径
- plot_path：绘图数据存储的路径
- offset_path：延时偏移文件存储路径
- sos_path：畸变sos文件存储路径
- reference_channel：延时参考通道
- parallel_num：并行计算的线程数，具体参数结合自身的测控电脑性能而定，性能较好的电脑可以适当大一些，但是不要超过cpu的核数
- channel_units：一体机单通道的单元数，目前有6和24两个选项可选，请根据具体的一体机硬件配置设置
- use_channel_unit：芯片单bus实际使用的单元数，也就是一个bus包含了几个bit
- bus_num：芯片中bus的总数
- total_qubit_num：芯片中总的bit数量
- period：畸变实验中小循环周期
- square_width：畸变实验中的方波时序长度

## 运行脚本说明

1. automatic_setchannel.py

- 将机箱通道和bit的通道关系映射表中的通道数据到数据库中bit和couper中，包括awg/dac/rd模块

2. offset_dis_import_tool.py

- 将数据处理得到的offset数据更新到数据库

3. Unit_step_response_fit_oscilloscope_integration_for_dis

- 畸变sos数据处理

4. Unit_step_response_fit_oscilloscope_integration_for_time

- 时序分析的数据处理

5. z_distortion_oscillope_sampling-v4

- 畸变测试的实验

  需要调整的参数如下

  - vpx_num：输出的机箱编号，示例：vpx_num=[1,2]
  - z_pulse_params中的amp：awg方波的输出幅值
  - loop：实验大循环的次数
  - repeat：小循环的重复次数

6. z_flux_plot

- 畸变数据及一体机板卡时序绘图

