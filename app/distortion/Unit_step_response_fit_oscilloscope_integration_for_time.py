# -*- coding: UTF-8 -*-
from fileinput import filename

import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import time
import json
from scipy import signal
from scipy import interpolate
import os
import re

from multiprocessing import Pool

from sympy import false

# from app.distortion.z_flux_plot import qname
from pyQCat.experiments.batch.batch_coupler_distortion import distortion_collection
from app.distortion import period, square_width, channel_file_path, reference_channel, oscilloscope_path, time_data_path, \
    offset_path, oscilloscope_file_type, parallel_num
from app.distortion.automatic_setchannel import read_map_channel


# from Zresponse_fitTool import *


def gaussian_fir_low_pass_filter(order=5, Ts=0.625):
    """
    order: # the order of the Filter
    """
    bw = 1 / Ts / order  # 3 dB cut-off frequency
    print(f'order={order},bandwidth is {bw * 1000}MHz')
    t = np.linspace(-1 / 2, 1 / 2, order + 1)
    alpha = np.sqrt(np.log(2) / 2)
    h = (np.sqrt(np.pi) / alpha) * np.exp(-(t * np.pi / alpha) ** 2)
    # Normalize coefficients
    h = h / sum(h)
    return h


def integration(osci_data, channelpath, first_channel_num, i, real_poles_num_list=[2, 3, 4],
                complex_poles_num_list=[0, 0, 0], best_poles_num_index=2, period=400e3, square_width=69e3,
                start_time=20,
                FIR_length=30, step_response_normalization=True, Ts_AWG=1 / 1.2, add_Gaussian_digital_filter=False,
                response_type='rise', fall_bound=[10e3, 10.0002e3], temperature="room_temperature"):
    if temperature == "room_temperature":
        tlist = osci_data[:, 0] * 1e9
        Ts_osci = tlist[1] - tlist[0]
        print(f'sampling rate is {Ts_osci} ns')

        title = f'Channel {first_channel_num + i - 1}'
        print(title)
        amplist = osci_data[:, i]

        # 新建一个子文件夹
        # try:
        #     os.mkdir(channelpath + title)
        # except:
        #     pass

        # filepath = os.path.join(channelpath + title + '\\')

        # filepath = os.path.join(channelpath + '\\')
        # print(filepath)

        # data length for average to get offset and convergence amp
        avarage_length = int(np.round(500 / Ts_osci)) + 1  # 500 ns
        bottom_offset = np.mean(amplist[0:avarage_length])

        # data length for average to get offset and convergence amp
        avarage_length = int(np.round(100 / Ts_osci)) + 1  # 100 ns
        index_square_width = int((square_width - 100 - tlist[0]) / Ts_osci)  # 单位 ns
        top_convergence = np.mean(amplist[-avarage_length - 1 + index_square_width:-1 + index_square_width])

        # amplist_normalization = (amplist - bottom_offset) / (top_convergence - bottom_offset)
        amplist_normalization = (amplist - bottom_offset) / (top_convergence - bottom_offset)
        amplist_normalization = np.abs(amplist_normalization)
        amplist_normalization = amplist_normalization / max(amplist_normalization)

        # only preserve a period data
        length_period = int(np.round(period / Ts_osci)) + 1
        tlist = tlist[0:length_period]
        amplist_normalization = amplist_normalization[0:length_period]

        if add_Gaussian_digital_filter:
            h = gaussian_fir_low_pass_filter(order=25, Ts=Ts_osci)
            amplist_normalization = signal.lfilter(h, 1, amplist_normalization)

        # ## determine t=0
        impulse_pulse = np.diff(np.hstack(([0], amplist_normalization)))

        index_zeros_point1 = np.argmax(impulse_pulse)
        index_zeros_point = index_zeros_point1 + (0)  # adjust manually
        relative_shift = tlist[index_zeros_point]
        print(f'relative_shift:{relative_shift}')
        tlist_0 = tlist[index_zeros_point:index_zeros_point + int(np.round((square_width + 10e3) / Ts_osci)) + 1] - \
                  tlist[index_zeros_point]  # start from t=0
        amplist_normalization_0 = amplist_normalization[index_zeros_point:index_zeros_point + int(
            np.round((square_width + 10e3) / Ts_osci)) + 1]
        return tlist_0, amplist_normalization_0, relative_shift


def distortion_processing(file_path, file_name, distortion_path, temperature, z_channel_dict=None):
    filename = Path(file_name).stem.lower()  # 文件名不带后缀
    if filename.startswith("c"):
        qname_split = filename.split('-')
        qname_list = ['-'.join(qname_split[:2]), '-'.join(qname_split[-2:])]

    elif filename.startswith("z"):
        qname_split = filename.split('-')
        # qname_list = [name.replace("Z", "q") for name in qname_split]
        qname_list = qname_split
    elif filename.startswith("awg"):
        qname_split = re.findall(r'\d+', filename)
        qname_list = [int(qname) for qname in qname_split]
    first_channel_num = 1
    if temperature == "room_temperature":
        osci_data = np.loadtxt(os.path.join(file_path, file_name), skiprows=1, delimiter=',')
        relative_shift_dict = dict()
        read_column = [[2, 3][i] for i in range(len(qname_list))]
        for i in read_column:
            tlist_0, amplist_normalization_0, relative_shift = integration(osci_data, file_path,
                                                                           first_channel_num, i,
                                                                           real_poles_num_list=[3, 4, 5],
                                                                           complex_poles_num_list=[0, 0,
                                                                                                   0],
                                                                           best_poles_num_index=1,
                                                                           period=period * 1e3,
                                                                           square_width=square_width,
                                                                           start_time=20, FIR_length=0,
                                                                           step_response_normalization=True,
                                                                           Ts_AWG=1 / 1.2,
                                                                           add_Gaussian_digital_filter=False,
                                                                           response_type='rise',
                                                                           fall_bound=[10e3, 10.0002e3],
                                                                           temperature=temperature)
            bit_name = qname_list[i - 2]
            distortion_file_name = os.path.join(distortion_path,
                                                f'{bit_name}_relative_shift={relative_shift}ns.dat')
            if oscilloscope_file_type == "qubit":
                if z_channel_dict.get(bit_name):
                    relative_shift_dict[z_channel_dict[bit_name]] = relative_shift
            elif oscilloscope_file_type == "channel":
                relative_shift_dict[bit_name] = relative_shift
            amplist_peak_data = np.mean(amplist_normalization_0[1000:10000])

            print(f"The average value of {bit_name} is {amplist_peak_data}")
            amplist_normalization_0 /= amplist_peak_data
            np.savetxt(distortion_file_name, np.column_stack((tlist_0, amplist_normalization_0)))
        return relative_shift_dict


def parallel_integration(temperature, oscilloscope_path, time_data_path, offset_path):
    if not os.path.exists(time_data_path):
        os.mkdir(time_data_path)
    if not os.path.exists(offset_path):
        os.mkdir(offset_path)
    z_channel_dict = None
    if oscilloscope_file_type == "qubit":
        z_channel_dict = read_map_channel(channel_file_path, sheet_name="AWG端口")
    pool = Pool(parallel_num)
    results = []
    for file_name in os.listdir(oscilloscope_path):
        results.append(pool.apply_async(func=distortion_processing, args=(
            oscilloscope_path, file_name, time_data_path, temperature, z_channel_dict)))
    pool.close()
    pool.join()
    delay_dict = dict()
    for res in results:
        delay_dict.update(res.get())
    delay_dict[reference_channel] = 0
    delay_dict_temp = dict(sorted(delay_dict.items(), key=lambda i: i[0]))
    print(delay_dict_temp)
    final_dict_dict = dict()
    final_dict_dict['ref'] = reference_channel
    final_dict_dict['delay'] = delay_dict_temp
    with open(f'{offset_path}/channel_delay_origin.json', 'w') as f:
        json.dump(final_dict_dict, f, indent=4)


if __name__ == "__main__":
    from scipy.signal import find_peaks
    from scipy.signal import butter, filtfilt
    temperature = "room_temperature"  # "low_temperature" or "room_temperature"

    """automatic moudle"""
    parallel_integration(temperature, oscilloscope_path, time_data_path, offset_path)



