import re

import numpy as np
import matplotlib.pyplot as plt

import os
from automatic_setchannel import read_map_channel
from multiprocessing import Pool
from app.distortion import channel_file_path, parallel_num, plot_data_path, plot_path, oscilloscope_file_type
import traceback


def _plot(card_num, files, plt_type, z_channel_dict=None):
    try:
        for file in files:
            file_name = os.path.join(plot_data_path, file)
            distortion_data = np.loadtxt(file_name)
            tlist = distortion_data[:, 0]  # ns
            Ts_osci = tlist[1] - tlist[0]  # ns
            print(f'sampling rate is {np.round(Ts_osci, 3)} ns')
            qname = file.split("_relative")[0]
            if oscilloscope_file_type == "qubit":
                z_channel = z_channel_dict[qname]
            elif oscilloscope_file_type == "channel":
                z_channel = int(qname)
            title = f'Channel {z_channel}'
            # 依次获取每一个通道的数据
            amplist = distortion_data[:, 1]
            plt.plot(tlist, amplist, label=title)
        plt.xlabel('Time(ns)', size=14)
        plt.ylabel('Amplitude', size=14)
        plt.legend()
        plt.title('original data')
        if "normal" in plt_type:
            plt.savefig(f"{plot_path}//CARD_{card_num}_original_data.png")
        if "rise" in plt_type:
            plt.xlim(-1, 10)
            plt.savefig(f"{plot_path}//CARD_{card_num}_rise_data.png")
        if "short" in plt_type:
            plt.xlim(0, 30)
            plt.ylim(0.9, 1.01)
            plt.savefig(f"{plot_path}//CARD_{card_num}_short_data.png")
        if "long" in plt_type:
            plt.xlim(10, 80000)
            plt.ylim(0.98, 1.01)
            plt.xscale("log")
            plt.savefig(f"{plot_path}//CARD_{card_num}_long_data.png")
        plt.cla()
    except Exception as err:
        print(traceback.format_exc())


def plot_origin(card_file_dict, z_channel_dict=None, plt_type=None):
    if not plt_type:
        return
    pool = Pool(parallel_num)
    for card_num, files in card_file_dict.items():
        pool.apply_async(func=_plot, args=(card_num, files, plt_type, z_channel_dict))
    pool.close()
    pool.join()


def plot_delay(delay_file_dict:dict):
    error_config = {'ecolor': 'red', 'elinewidth': 2}  # 设置误差线颜色
    ind = delay_file_dict.keys()
    means = list()
    stds = list()
    for card_num, delays in delay_file_dict.items():
        means.append(np.mean(delays))
        stds.append(np.std(delays))
    # 绘制误差棒图
    width = 0.35
    plt.figure(figsize=(16, 9))
    plt.bar(ind, means, width, yerr=stds, error_kw=error_config)
    # 添加一些文本信息
    plt.ylabel('delay (ns)')
    plt.title('ZZTiming_RT')
    plt.xticks(range(0, max(ind)+1))
    # ax.set_xticklabels(('Group 1', 'Group 2', 'Group 3'))
    plt.savefig(f"{plot_path}//delay-stds-means.png")
    plt.cla()
    # 显示图形
    # plt.show()


def osci_plot(plot_origin_flag, plot_delay_flag):
    """映射表读取"""
    # 读取Excel文件
    if oscilloscope_file_type == "qubit":
        z_channel_dict = read_map_channel(channel_file_path, sheet_name="AWG端口", key_reversal=False)
    else:
        z_channel_dict = None
    # """对应板卡分类"""
    sub_distortion_paths = os.listdir(plot_data_path)
    if not os.path.exists(plot_path):
        os.mkdir(plot_path)

    z_card_channel_count = 8
    card_file_dict = dict()
    delay_file_dict = dict()
    for sub_distortion_path in sub_distortion_paths:
        qname = sub_distortion_path.split("_relative")[0]
        delay = float(sub_distortion_path.split("=")[1].split("ns")[0])
        if oscilloscope_file_type == "qubit":
            if qname not in z_channel_dict:
                print(f"\033[31m{qname} 不在映射表中\033[0m")
                continue
            z_channel = z_channel_dict[qname]
        elif oscilloscope_file_type == "channel":
            z_channel = int(qname)
        card_num = (z_channel - 1) // z_card_channel_count + 1
        card_file_dict.setdefault(card_num, list())
        delay_file_dict.setdefault(card_num, list())
        card_file_dict[card_num].append(sub_distortion_path)
        delay_file_dict[card_num].append(delay)

    # 绘图
    # normal绘制全部图像
    # long 绘制x轴[10, 80000]， y轴[0.98, 1.005]
    # rise 绘制x轴[-1, 10]
    # short 绘制x轴[0, 30]， y轴[0.9, 1.01]
    if plot_origin_flag:
        plt_type = ["normal", "long", "rise", "short"]
        plot_origin(card_file_dict, z_channel_dict, plt_type)

    # 绘制card的平均值和标准差
    if plot_delay_flag:
        delay_file_dict = dict(sorted(delay_file_dict.items(), key=lambda i: i[0]))
        plot_delay(delay_file_dict)


if __name__ == "__main__":
    plot_origin_flag = True
    plot_delay_flag = True
    osci_plot(plot_origin_flag, plot_delay_flag)




