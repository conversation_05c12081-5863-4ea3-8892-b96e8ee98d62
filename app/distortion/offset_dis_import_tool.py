# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/13
# __author:       <PERSON><PERSON><PERSON>


"""
功能：根据 Z Exp 实验测出的全 Z 通道延时和室温畸变数据，完成数据库一键导入

提出者：张盛
"""

import os
from glob import glob

import numpy as np
from loguru import logger
from app.distortion import sos_path, offset_path
from app.config import init_backend
from app.tool.utils import read_json_file
from pyQCat.processor.chip_data import ChipConfigField
from pyQCat.processor.hardware_manager import HardwareOffsetManager


def find_files_with_suffix(parent_path, suffix):
    """Searches for files with a specific suffix in a parent directory and its subdirectories.

    Args:
        parent_path (str): The parent directory to search in.
        suffix (str): The suffix of the files to search for (e.g., 'LeakageNumV2.epd').

    Returns:
        List[str]: A list of file paths that match the suffix.
    """
    # Use glob to recursively search for files with the given suffix
    search_pattern = os.path.join(parent_path, "**", f"*{suffix}")
    matching_files = glob(search_pattern, recursive=True)

    return matching_files


def main(sos_path: str, offset_path: str, limit: float = 200.0, is_save: bool = False):
    backend = init_backend()
    chip_data = backend.chip_data

    hardware_offset_data = chip_data.cache_config.get(
        ChipConfigField.hardware_offset.value
    )
    offset_manager = HardwareOffsetManager.from_data(hardware_offset_data)
    offset_manager.reset()
    offset_data = read_json_file(offset_path)
    base_channel = f"z{offset_data['ref']}"
    for channel, offset in offset_data["delay"].items():
        offset = float(offset)
        if offset > 200:
            logger.warning(f"channel {channel} offset is {offset} ns, limit {limit} ns")
            continue
        offset_manager.insert_z2_timing(base_channel, channel, float(offset))

    character_data = chip_data.cache_config.get(ChipConfigField.character.value)
    sos_files = find_files_with_suffix(sos_path, "sos_digital_filter_RT.dat")
    for sf in sos_files:
        file_name = os.path.basename(sf)
        bit = file_name.split("_")[0]
        character_data[bit]["distortion_sos"]["Room_temperature_sos_filter"] = (
            np.loadtxt(sf).tolist()
        )

        """低温滤波器初始化"""
        character_data[bit]["distortion_sos"]["Low_temperature_IIR_sos_filter"] = []
        character_data[bit]["distortion_sos"]["Low_temperature_FIR_tf_filter"] = []
        logger.info(f"insert {bit} room sos success!")

    if is_save is True:
        backend.save_chip_data_to_db(
            names=[
                ChipConfigField.hardware_offset.value,
                ChipConfigField.character.value,
            ]
        )


if __name__ == "__main__":
    main(
        sos_path=sos_path,
        offset_path=offset_path,
        is_save=True,
    )

