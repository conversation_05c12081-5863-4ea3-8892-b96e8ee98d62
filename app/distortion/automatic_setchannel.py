"""
channel.csv 排序显示 by ZS
根据映射关系自动 update通道

"""
import numpy as np
import pandas as pd
import re

from app.config import init_backend
from app.distortion import total_qubit_num, channel_file_path, channel_units, use_channel_unit, bus_num
# from app.distortion import channel_file_path

def custom_sort(key):
    # 判断首字符并分配优先级：'Z'开头的优先级最高（返回值最小），其次是'C'
    if key.startswith('Z'):
        priority = 0
    elif key.startswith('C'):
        priority = 1
    else:
        priority = 2  # 其他字符放在最后

    # 提取第一个出现的数字
    match = re.search(r'\d+', key)
    number = int(match.group()) if match else float('inf')  # 如果没有找到数字，则认为它很大，以便于排序

    return priority, number


def get_attr_columns(df_columns, attr):
    """
    获取excel中指定值位于文档中的第几列
    """
    columns_list = list()
    for col in df_columns:
        if attr in col:
            # print(f"参数 '{attr}' 在第 {df_columns.get_loc(col)} 列")
            columns_list.append(df_columns.get_loc(col))
    return columns_list


def get_m_channel(channel_units=24, use_channel_units=6, bus_num=30):
    """
    生成测量通道
    """
    m_channels = dict(
        [(f'M{i * use_channel_units + j + 1}', i * channel_units + j + 1) for i in range(bus_num) for j in
         range(use_channel_units)])
    return m_channels


def read_map_channel(file_name: str, sheet_name: str = None, qubit_vpx_map=False, key_reversal=False) -> dict:
    """
    从配置文件获取读取通道映射关系值
    file_name: 读取文件名称
    sheet_name: 文件sheet名称
    qubit_vpx_map: 读取通道映射到bit,存储按照vpx区分
    key_reversal: False时，key值为对应的z/x/c，value为通道；True时，key和value翻转
    """
    df = pd.read_excel(file_name, sheet_name=sheet_name)
    machine_ports_cols = get_attr_columns(df.columns, "一体机端口")
    chip_ports_cols = get_attr_columns(df.columns, "芯片端口")
    vpx_num_cols = get_attr_columns(df.columns, "机柜编号")
    mc_cell = dict()
    for machine_col_idx, chip_col_idx in zip(machine_ports_cols, chip_ports_cols):
        vpx_num = None
        if machine_col_idx + 1 != chip_col_idx:
            raise ValueError(f'一体机端口:{machine_col_idx}和芯片端口:{chip_col_idx}在所在列不在相邻位置')
        for i, (machine_port, chip_port) in enumerate(zip(df.iloc[:, machine_col_idx], df.iloc[:, chip_col_idx])):
            if pd.notna(chip_port) and chip_port != '板卡异常':
                # print(f'一体机端口:{machine_port}, 芯片端口:{chip_port}')
                if isinstance(chip_port, str) and 'CZ' in chip_port:  # 统一键值对应 CZ
                    chip_port = chip_port.replace('CZ', 'C')
                if qubit_vpx_map:
                    bit_name = None
                    if chip_port.startswith("C"):
                        bit_name = chip_port.lower()
                    elif chip_port.startswith("Z"):
                        bit_name = chip_port.replace("Z", "q")
                    elif chip_port.startswith("X"):
                        bit_name = chip_port.replace("X", "q")
                    read_vpx_num = df.iloc[i, vpx_num_cols[0]]
                    vpx_num = vpx_num if np.isnan(read_vpx_num) else read_vpx_num
                    mc_cell.setdefault(int(vpx_num), dict())
                    if key_reversal:
                        mc_cell[vpx_num][int(machine_port)] = bit_name
                    else:
                        mc_cell[vpx_num][bit_name] = int(machine_port)
                else:
                    if key_reversal:
                        mc_cell[int(machine_port)] = chip_port
                    else:
                        mc_cell[chip_port] = int(machine_port)

    if not qubit_vpx_map and not key_reversal:
        mc_cell = dict(sorted(mc_cell.items(), key=lambda item: custom_sort(item[0])))
    return mc_cell


def save_qubit(z_channels: dict, x_channels: dict, m_channels: dict, max_qubit_num=102):
    backend = init_backend()  # 获取账户数据库
    qubit_sets = backend.chip_data.cache_qubit  # 对应到相应qubit位置
    coupler_sets = backend.chip_data.cache_coupler  # 对应到相应qubit位置

    for qk, qubit in qubit_sets.items():
        _, num = qk.split('q')
        if int(num) > max_qubit_num:
            print(f"比特数目:q{num}超出了最大比特数：{max_qubit_num}")
            continue
        if z_channels:
            z_name = 'Z' + num
            qchannel = z_channels[z_name]
            qubit.z_flux_channel = qchannel
            qubit.save_data()  # 保存到数据库
        if x_channels:
            x_name = "X" + num
            qchannel = x_channels[x_name]
            qubit.xy_channel = qchannel
            qubit.save_data()  # 保存到数据库
        if m_channels:
            m_name = "M" + num
            qrchannel = m_channels[m_name]
            qubit.readout_channel = qrchannel
            qubit.save_data()  # 保存到数据库
    if z_channels:
        for qck, coupler in coupler_sets.items():
            z_name = 'C' + qck.split('c')[1]
            try:
                """文件中有奇怪的命名 例如CZ99-1，无法自动化unless修正为规范拓扑表"""
                qchannel = z_channels[z_name]
                coupler.z_flux_channel = int(qchannel)
                coupler.save_data()
            except:
                continue


def main():
    # 读取Excel文件
    # file_path = r'F:\mycode\gitlab\apps\pyqcat-apps\app\distortion\一体机端口与芯片端口映射表20250309@Y4_ref.xlsx'
    z_channel_dict = read_map_channel(channel_file_path, sheet_name="AWG端口")
    x_channel_dict = read_map_channel(channel_file_path, sheet_name="X端口")
    # channel_units = 24  # 一体机单通道的单元数
    # use_channel_unit = 6  # 单bus实际使用的单元数，也就是一个bus几个bit
    # bus_num = 18  # 芯片中bus总数量
    # total_qubit_num = 108  # 总的bit数量
    m_channel_dict = get_m_channel(channel_units, use_channel_unit, bus_num)
    #
    updata_flag = 1
    # print_flag = 1

    # if print_flag == 1:
    #     for key, value in list(z_channel_dict.items()):
    #         print(f"{key}:{value}")
    # # return
    if updata_flag == 1:
        save_qubit(z_channel_dict, x_channel_dict, m_channel_dict, max_qubit_num=total_qubit_num)


if __name__ == "__main__":
    main()
