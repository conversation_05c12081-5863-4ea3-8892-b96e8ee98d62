# -*- coding: UTF-8 -*-

from pathlib import Path
from multiprocessing import Pool
from scipy import interpolate
import os
import re
from app.distortion.fit.Zresponse_fitTool import *
from app.distortion import period, square_width, oscilloscope_path, sos_path, parallel_num


def gaussian_fir_low_pass_filter(order=5, Ts=0.625):
    """
    order: # the order of the Filter
    """
    bw = 1 / Ts / order  # 3 dB cut-off frequency
    print(f'order={order},bandwidth is {bw * 1000}MHz')
    t = np.linspace(-1 / 2, 1 / 2, order + 1)
    alpha = np.sqrt(np.log(2) / 2)
    h = (np.sqrt(np.pi) / alpha) * np.exp(-(t * np.pi / alpha) ** 2)
    # Normalize coefficients
    h = h / sum(h)
    return h


def get_ab_full(b, a, y_u_FIR_n):
    # The sum of a IIR system and a FIR system
    # Clist_FIR=[deltaC00,C01,C02,C03,C04,C05,C06,……C032]
    # The last point of y_u_FIR_n must be zero
    Clist_FIR = np.diff(y_u_FIR_n)
    residues, poles, klist = signal.residuez(b, a, tol=1e-10)

    Clist = []
    if len(klist) > len(Clist_FIR):
        Clist = copy.deepcopy(klist)
        Clist[:len(Clist_FIR)] = Clist[:len(Clist_FIR)] + Clist_FIR
    else:
        Clist = copy.deepcopy(Clist_FIR)
        Clist[:len(klist)] = Clist[:len(klist)] + klist
    b, a = signal.invresz(residues, poles, Clist, tol=1e-10)
    a_full = np.real(a)
    b_full = np.real(b)
    print(f'a_full:{a_full},\nb_full:{b_full}')
    return b_full, a_full


def fit_complex_plot(real_poles_num_list, complex_poles_num_list, step_response_normalization,
                     poles_model_params_list, time_data,
                     zresponse, title, png_name, Ts, square_width, response_type):
    fig, ax = plt.subplots()
    ax.scatter(time_data, zresponse, label='Experiment data', s=6, c='b')
    c = ['C1', 'C2', 'C3']
    for index, (poles_model_params, real_poles_num, complex_poles_num) in enumerate(
            list(zip(poles_model_params_list,
                     real_poles_num_list, complex_poles_num_list))):
        poles_model_params_dic = arrange_poles_model_params(poles_model_params[0], real_poles_num, complex_poles_num,
                                                            step_response_normalization, Ts, response_type)
        #         print(poles_model_params_dic)

        y_equation = get_from_equation(time_data, poles_model_params_dic, Ts, square_width, response_type)
        if index <= len(c) - 1:
            ax.plot(time_data, y_equation, c=c[index], lw=2,
                    label=f'{real_poles_num} real poles,{complex_poles_num} complex poles, r2={poles_model_params[1]}')
        else:
            ax.plot(time_data, y_equation, lw=2,
                    label=f'{real_poles_num} real poles,{complex_poles_num} complex poles')
    # save time_data, y_equation
    ax.legend(loc='lower right')
    ax.set_title(title)
    ax.set_xlabel('Time(ns)')
    ax.set_ylabel('Zresponse')
    ax.set_xlim(10, 80000)
    #     ax.set_xlim(square_width-1000,square_width+420000)
    ax.set_ylim(0.98, 1.003)
    #     ax.set_ylim(0.9999,1.003)
    #     ax.set_ylim(-0.002,0.0005)
    ax.set_xscale('log')
    #     plt.tick_params(labelsize=14)
    ax.grid()
    return fig, ax


def integration(osci_data, channelpath, qname=1, i=1, real_poles_num_list=[2, 3, 4],
                complex_poles_num_list=[0, 0, 0], period=400e3, square_width=69e3,
                start_time=20,
                FIR_length=30, step_response_normalization=True, Ts_AWG=1 / 1.2, add_Gaussian_digital_filter=False,
                response_type='rise', fall_bound=[10e3, 10.0002e3], temperature="room_temperature"):
    """

    Args:
        i: temperature = "room_temperature"时表示该板卡上第i个通道，如果只测了一个通道的数据i给1即可
        osci_data: 单位阶跃响应数据，其中第一列为时间，其余列为幅值响应，i表示取i+1列的幅值响应
        channelpath: 保存响应数据的文件夹
        qname: temperature = "room_temperature"时表示该板卡的第一个通道序号
        real_poles_num_list, complex_poles_num_list: 尝试多组实数极点和复数极点组合对数据进行拟合
        best_poles_num_index：上面哪一组实数极点和复数极点的组合拟合效果最佳
        period: 采集AWG通道响应时的周期参数
        square_width: 采集AWG通道响应时的方波宽度
        start_time: 从多少 ns开始使用极点模型拟合响应数据
        FIR_length: FIR响应处理的长度，FIR_length=0时表示对 start_time之前未拟合上的数据不做额外的残差FIR处理。
        step_response_normalization: 一般都是True
        Ts_AWG: AWG板卡采样率
        add_Gaussian_digital_filter: 如果原始信号存在明显的过冲，需要对其 Gaussian 数字滤波，否则最终生成的预失真数字滤波器可能不稳定。
        response_type: 'rise' or 'rise_and_fall' 会影响两个地方，一个是我们的待拟合数据的选取，另一个相对应的是拟合公式的选择。
        fall_bound: fall_bound = [lb,ub],单位 ns,表示用于拟合的下降沿数据处于下降沿过后时间范围在lb到ub区间，
        只有response_type='rise_and_fall' 时才起作用。
        temperature: "low_temperature" or "room_temperature"，会影响到从哪份配置文件中生成拟合的初始值和上下界。
        "room_temperature" 时需要对示波器采集到的数据进行归一化在内的一系列预处理，但"low_temperature"时的数据通常可以直接拿来拟合


    Returns:

    """
    if temperature == "room_temperature":
        tlist = osci_data[:, 0] * 1e9  # ns
        Ts_osci = tlist[1] - tlist[0]  # ns
        print(f'sampling rate is {Ts_osci} ns')

        # title = f'Channel {qname + i - 1}'
        title = qname
        # print(title)
        amplist = osci_data[:, i]
        # 新建一个子文件夹
        # try:
        #     os.mkdir(channelpath + title)
        # except:
        #     pass
        #
        # channelpath=""
        # title = ""
        filepath = os.path.join(channelpath, title)
        if not os.path.exists(filepath):
            os.mkdir(filepath)
        filepath = os.path.join(filepath, title + "_")

        # data length for average to get offset and convergence amp
        avarage_length = int(np.round(500 / Ts_osci)) + 1  # 500 ns
        bottom_offset = np.mean(amplist[0:avarage_length])

        # data length for average to get offset and convergence amp
        avarage_length = int(np.round(100 / Ts_osci)) + 1  # 100 ns
        index_square_width = int((square_width - 100 - tlist[0]) / Ts_osci)  # 单位 ns
        top_convergence = np.mean(amplist[-avarage_length - 1 + index_square_width:-1 + index_square_width])

        # amplist_normalization = (amplist - bottom_offset) / (top_convergence - bottom_offset)
        amplist_normalization = (amplist - bottom_offset) / (top_convergence - bottom_offset)
        amplist_normalization = np.abs(amplist_normalization)
        amplist_normalization = amplist_normalization / max(amplist_normalization)
        # amplist = amplist_normalization
        fig, ax = plt.subplots()
        ax.plot(tlist, amplist_normalization, '-', label=f'Oscilloscope({np.round(Ts_osci, 3)} ns)')

        plt.legend()
        ax.set_xlabel('Time(ns)')
        ax.set_ylabel('Amplitude')
        # ax.set_xlim([-10,100])
        # ax.set_ylim([0.96,1.01])
        ax.set_xlim([0, 60])
        ax.set_ylim([0.3, 1.002])
        # ax.set_title(title + f',sos error max {np.round(error_max, 10)}')
        fig.savefig(filepath + '_short_time_check.png')
        plt.cla()
        plt.close(fig)
        # only preserve a period data
        length_period = int(np.round(period / Ts_osci)) + 1
        tlist = tlist[0:length_period]
        amplist_normalization = amplist_normalization[0:length_period]

        if add_Gaussian_digital_filter:
            h = gaussian_fir_low_pass_filter(order=25, Ts=Ts_osci)
            amplist_normalization = signal.lfilter(h, 1, amplist_normalization)

        # ## determine t=0
        impulse_pulse = np.diff(np.hstack(([0], amplist_normalization)))

        index_zeros_point1 = np.argmax(impulse_pulse)
        index_zeros_point = index_zeros_point1 + (0)  # adjust manually
        tlist_0 = tlist[index_zeros_point:] - tlist[index_zeros_point]  # start from t=0
        amplist_normalization_0 = amplist_normalization[index_zeros_point:]

        ## sampling from oscilloscope data
        # rise
        index_0ns = 0
        index_100ns = int(np.round(100 / Ts_osci))
        index_1us = int(np.round(1000 / Ts_osci))
        index_10us = int(np.round(10000 / Ts_osci))
        index_square_width = int(np.round((square_width) / Ts_osci))
        # step in every segment
        # step_0_100ns = 1
        # step_100_1us = int(np.round(1/Ts_osci)) # 1 ns
        # step_1_10us = int(np.round(10/Ts_osci)) # 10 ns
        # step_after_10us = int(np.round(100/Ts_osci)) # 100 ns
        step_0_100ns = 1
        step_100_1us = 1
        step_1_10us = 11
        step_after_10us = 17

        segmented_index_list_rise = np.hstack(
            (np.arange(index_0ns, index_100ns, step_0_100ns), np.arange(index_100ns, index_1us, step_100_1us),
             np.arange(index_1us, index_10us, step_1_10us),
             np.arange(index_10us, index_square_width - 200, step_after_10us)))

        if response_type == 'rise_and_fall':
            # fall: 10us-80us data
            segmented_index_list_fall = np.arange(int(np.round(fall_bound[0] / Ts_osci)),
                                                  int(np.round(fall_bound[1] / Ts_osci)),
                                                  step_after_10us * 2) + index_square_width
            segmented_index_list = np.hstack((segmented_index_list_rise, segmented_index_list_fall))
        elif response_type == 'rise':
            segmented_index_list = segmented_index_list_rise
        unit_step_for_fit_data = np.column_stack((tlist_0[segmented_index_list], amplist_normalization_0[segmented_index_list]))
        np.savetxt(filepath + '_unit_step_response_for_fit.dat', unit_step_for_fit_data)

    else:
        title = ''
        tlist_0 = osci_data[:, 0]  # ns
        Ts_osci = tlist_0[1] - tlist_0[0]  # ns
        amplist_normalization_0 = osci_data[:, 1]
        square_width = tlist_0[-1]

        filepath = channelpath
        unit_step_for_fit_data = osci_data
        np.savetxt(filepath + '_unit_step_response_for_fit.dat', unit_step_for_fit_data)

    # ## fit with the sampling rate of AWG
    # ### complex poles number = 0

    # debug 时在这里打断点

    poles_model_params_list = []
    for (real_poles_num, complex_poles_num) in list(zip(real_poles_num_list, complex_poles_num_list)):
        poles_model_params = data_fit_using_poles_model(real_poles_num, complex_poles_num,
                                                        step_response_normalization, unit_step_for_fit_data,
                                                        filepath=filepath, start_time=start_time,
                                                        Ts=Ts_AWG,
                                                        square_width=square_width, response_type=response_type,
                                                        temperature=temperature)
        poles_model_params_list.append(poles_model_params)


    fig, ax = fit_complex_plot(real_poles_num_list, complex_poles_num_list, step_response_normalization,
                               poles_model_params_list, tlist_0, amplist_normalization_0, title=None, png_name=None,
                               Ts=Ts_AWG,
                               square_width=square_width, response_type=response_type)
    ax.set_title(title)
    fig.savefig(filepath + '_poles_model_fit.png')
    plt.cla()
    plt.close(fig)


    # sort r2
    r2_sort = sorted(enumerate(poles_model_params_list), key= lambda x:x[1][1], reverse=True)
    index_r2 = [i for i, _ in r2_sort]
    # ## Compute ab of the full system
    # ### Sampling with AWG samplerate
    # choose suitable number of poles according to the fit result above

    def calculate_poles(best_poles_num_index):
        real_poles_num = real_poles_num_list[best_poles_num_index]
        complex_poles_num = complex_poles_num_list[best_poles_num_index]
        poles_model_params = poles_model_params_list[best_poles_num_index][0]
        print(f"real pole={real_poles_num}")
        poles_model_params_dic = arrange_poles_model_params(poles_model_params, real_poles_num, complex_poles_num,
                                                            step_response_normalization, Ts_AWG, response_type)
        time_data_sampling = np.arange(0, square_width, Ts_AWG)
        y_equation_sampling = get_from_equation(time_data_sampling, poles_model_params_dic, Ts_AWG, square_width,
                                                response_type) / poles_model_params_dic[
                                  "normalization_coefficient"]  # re-normalization

        amplist_normalization_temp = amplist_normalization_0 / poles_model_params_dic[
            "normalization_coefficient"]  # re-normalization

        # substitute several samples at the beginning
        end_index = int(np.round(100 / Ts_osci))  # 100 ns
        func = interpolate.interp1d(tlist_0[:end_index], amplist_normalization_temp[:end_index], kind='cubic')

        # # FIR_length = start_time
        # FIR_length = 30

        x = np.arange(0, FIR_length, Ts_AWG)
        y_equation_sampling[0:len(x)] = func(x)
        np.savetxt(filepath + '_unit_step_response_after_sampling_for_fit.dat',
                   np.column_stack((time_data_sampling, y_equation_sampling)))

        # ### Compute ab
        a_IIR, b_IIR = generate_ab_from_poles_model_params(poles_model_params,
                                                           real_poles_num, complex_poles_num,
                                                           step_response_normalization=step_response_normalization,
                                                           check_ab=True, Ts=Ts_AWG)

        x = np.arange(0, FIR_length + Ts_AWG * 0, Ts_AWG)
        difference = y_equation_sampling[0:len(x)] - signal.lfilter(b_IIR, a_IIR, np.ones_like(x))
        y_u_FIR_n = np.hstack((0, difference, 0))

        b_full, a_full = get_ab_full(b_IIR, a_IIR, y_u_FIR_n)

        np.savetxt(filepath + 'afull.dat', a_full)
        np.savetxt(filepath + 'bfull.dat', b_full)

        # print zeors, poles
        # print(np.abs(signal.tf2zpk(b_full, a_full)))

        sos_digital_filter, sos_system, error_max = sosfilter_design_using_ab(filepath)

        np.savetxt(filepath + '_sos_digital_filter_RT.dat', sos_digital_filter)
        np.savetxt(filepath + '_sos_system_RT.dat', sos_system)

        x = np.ones_like(time_data_sampling)

        fig, ax = plt.subplots()
        ax.plot(tlist_0, amplist_normalization_temp, '-', label=f'Oscilloscope({np.round(Ts_osci, 3)} ns)')

        ax.plot(time_data_sampling, signal.sosfilt(sos_system, x),
                '-', color='black', label=f'Optimized sos({Ts_AWG} ns)')
        plt.legend()
        ax.set_xlabel('Time(ns)')
        ax.set_ylabel('Amplitude')
        # ax.set_xlim([-10,100])
        # ax.set_ylim([0.96,1.01])
        ax.set_xlim([-1, 50])
        ax.set_ylim([0.9, 1.002])
        ax.set_title(title + f',sos error max {np.round(error_max, 10)}')
        fig.savefig(filepath + '_short_time_check.png')
        plt.cla()
        plt.close(fig)
        x = np.ones_like(time_data_sampling)

        fig, ax = plt.subplots()
        ax.plot(tlist_0, amplist_normalization_temp, '-', label=f'Oscilloscope({np.round(Ts_osci, 3)} ns)')

        ax.plot(time_data_sampling, signal.sosfilt(sos_system, x),
                '-', color='black', label=f'Optimized sos({Ts_AWG} ns)')
        plt.legend()
        ax.set_xlabel('Time(ns)')
        ax.set_ylabel('Amplitude')

        ax.set_xlim(10, 80000)
        ax.set_ylim(0.98, 1.003)
        ax.set_xscale('log')
        ax.grid()
        ax.set_title(title + f',sos error max {np.round(error_max, 10)}')
        fig.savefig(filepath + '_long_time_check.png')
        plt.cla()
        plt.close(fig)
        # ## pre-distortion check
        y = np.hstack((np.zeros(80), np.ones(170), np.zeros(170)))
        y_t = np.arange(0, len(y), 1) * Ts_AWG
        # pre_distorted_wave_before_Gaussian = signal.lfilter(a_full, b_full, y)
        pre_distorted_wave_before_Gaussian = signal.sosfilt(sos_digital_filter, y)
        order = 7
        h = gaussian_fir_low_pass_filter(order=order, Ts=Ts_AWG)
        pre_distorted_wave_after_Gaussian = signal.lfilter(h, 1, pre_distorted_wave_before_Gaussian)

        fig, ax = plt.subplots()
        ax.plot(y_t, pre_distorted_wave_before_Gaussian, '-', label='pre-distorted wave before Gaussian')
        ax.plot(y_t, pre_distorted_wave_after_Gaussian, '-', label='pre-distorted wave after Gaussian')
        # ax.plot(y_t,signal.sosfilt(sos_system,pre_distorted_wave_after_Gaussian))
        # ax.plot(y_t,signal.lfilter(h,1,y),'--',label=f'ideal output,order={order}')
        plt.legend()
        ax.set_xlabel('Time(ns)')
        ax.set_ylabel('Amplitude')
        # ax.set_xlim([-1,30])
        ax.set_title(title + f',pre-distortion wave check')
        fig.savefig(filepath + '_pre-distortion_wave_check.png')
        plt.cla()
        plt.close(fig)

    for best_poles_num_index in index_r2:
        try:
            calculate_poles(best_poles_num_index)
            print(f"qname={qname}: The best-performing pole value is {real_poles_num_list[best_poles_num_index]} ")
            break
        except Exception as err:
            print(f"qname={qname}: An error occurs in data processing when pole is set to {real_poles_num_list[best_poles_num_index]}"
                  f" error:{err} ")


def distortion_processing(file_path, file_name, sos_path, temperature):
    filename = Path(file_name).stem.lower()  # 文件名不带后缀
    if filename.startswith("c"):
        qname_split = filename.split('-')
        qname_list = ['-'.join(qname_split[:2]), '-'.join(qname_split[-2:])]
        # qname_list = [name.replace("C", 'c') for name in qname_list]

    elif filename.startswith("z"):
        qname_split = filename.split('-')
        qname_list = [name.replace("z", "q") for name in qname_split]

    elif filename.startswith("awg"):
        qname_split = re.findall(r'\d+', filename)
        qname_list = [qname for qname in qname_split]
    read_column = [[2, 3][i] for i in range(len(qname_list))]
    if temperature == "room_temperature":
        osci_data = np.loadtxt(os.path.join(file_path, file_name), skiprows=1, delimiter=',')
        for i in read_column:
            try:
                integration(osci_data, sos_path, qname_list[i - 2], i, real_poles_num_list=[3, 4, 5],
                            complex_poles_num_list=[0, 0, 0], period=period*1e3,
                            square_width=square_width,
                            start_time=20, FIR_length=30,
                            step_response_normalization=True, Ts_AWG=1 / 1.2, add_Gaussian_digital_filter=True,
                            response_type='rise', fall_bound=[10e3, 10.0002e3], temperature=temperature)
            except Exception as e:
                import traceback
                print(qname_list[i - 2], traceback.format_exc())


def parallel_integration(temperature, oscilloscope_path, sos_path):
    if not os.path.exists(sos_path):
        os.mkdir(sos_path)
    pool = Pool(parallel_num)
    for file_name in os.listdir(oscilloscope_path):
        pool.apply_async(func=distortion_processing, args=(
            oscilloscope_path, file_name, sos_path, temperature,))
    pool.close()
    pool.join()


if __name__ == "__main__":
    temperature = "room_temperature"  # "low_temperature" or "room_temperature"

    """automatic moudle"""
    parallel_integration(temperature, oscilloscope_path, sos_path)

