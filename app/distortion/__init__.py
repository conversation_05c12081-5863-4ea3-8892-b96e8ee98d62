"""

__init__

"""
import os

# 数据文件格式
# qubit，表示以bit和coupler的方式来命名畸变数据/z51-52、C2-7-C2-8
# channel， 表示以awg通道的方式来命名畸变数据/AWG14-15、AWG39-40
oscilloscope_file_type = "channel"

# 通道配置文件信息
channel_file_path = r'F:\mycode\gitlab\pretest\一体机端口与芯片端口映射表20250309@Y4_ref.xlsx'
# 示波器畸变数据存储文件夹
# oscilloscope_path = '\mycode\gitlab\pretest\osci_data'
oscilloscope_path = r"F:\mycode\gitlab\pretest\20240425\Y4 20250425"

# 根目录
base_file = r"F:\mycode\gitlab\pretest\20240425\\"
_file = lambda x: os.path.join(base_file, x)
# 波形时序分析存储文件夹
time_data_path = _file('time_data')
# 绘图数据存储位置
plot_path = _file('plot')
# 延时文件保存地址
offset_path = _file('delay_data')

# 畸变sos数据处理文件保存路径
sos_path = _file('sos_data')

# 延时参考通道，延时
reference_channel = 1

# 并行计算的进程数量，具体参数结合自身的测控电脑性能而定

parallel_num = 6

# 芯片&一体机信息
channel_units = 24  # 一体机单通道的单元数，目前有6和24两个选项可选，请根据具体的一体机硬件配置设置
use_channel_unit = 6  # 单bus实际使用的单元数，也就是一个bus几个bit
bus_num = 18  # 芯片中bus总数量
total_qubit_num = 108  # 总的bit数量

# 畸变预测试实验信息
period = 400  # us
square_width = 50000
