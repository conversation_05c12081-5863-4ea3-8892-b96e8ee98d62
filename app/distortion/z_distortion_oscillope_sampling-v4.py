#

from typing import Optional, Union
from pyQCat.types import StandardContext
from pyQCat.experiments.single.z_distortion import ZExp
from app.distortion.automatic_setchannel import read_map_channel
from app.distortion import period, square_width, channel_file_path, reference_channel


# 使用实验模式输出Z波形，用于Z畸变和时序采集。注意一次打开的通道数不要超过128个，
# 否则可能超出一体机服务器的内存，能打开的最多通道数视一体机服务器的配置。


def channel_map_bit(channel, bit_dict):
    for _, bit_map in bit_dict.items():
        reversed_dict = {v: k for k, v in bit_map.items()}
        key = reversed_dict.get(channel)  # 直接查找
        if key:
            return key


def read_vpx_qubit(file_path:str, vpx_num:Union[list, int, None]=None, reference_channel:Optional[int]=None):
    """
    根据表中AWG端口设置的通道信息，自动匹配bit，包括couple
    """
    z_channel_dict = read_map_channel(file_path, "AWG端口", qubit_vpx_map=True)
    if vpx_num is None:
        vpx_num = list(z_channel_dict.keys())
    if isinstance(vpx_num, int):
        vpx_num = [vpx_num]
    bit_name = list()
    for vpx in vpx_num:
        bit_name.extend(z_channel_dict[vpx].keys())
    trigger_qubit = channel_map_bit(reference_channel, z_channel_dict)
    if trigger_qubit and trigger_qubit not in bit_name:
        bit_name.append(trigger_qubit)
    return bit_name



if __name__ == "__main__":

    import asyncio
    from app.config import init_backend


    # 根据通道映射表中的信息自动匹配对应机箱的bit
    vpx_num = [2]
    physical_unit = read_vpx_qubit(channel_file_path, vpx_num=vpx_num, reference_channel=reference_channel)
    context_params = {
        "name": StandardContext.URM.value,
        "physical_unit": physical_unit,
        "readout_type": "",
    }

    backend = init_backend()
    context = backend.context_manager.generate_context(**context_params)
    exp = ZExp.from_experiment_context(context)

    z_pulse_params = {
        "time": square_width,
        "amp": 0.8,
    }

    xy_pulse_params = {
        "time": square_width,
        "amp": 0,
    }

    exp.set_experiment_options(
        z_pulse_params=z_pulse_params,
        xy_pulse_params=xy_pulse_params,
        loop=500,
        repeat=10000,
        period=period,
    )

    asyncio.run(exp.run_experiment())
