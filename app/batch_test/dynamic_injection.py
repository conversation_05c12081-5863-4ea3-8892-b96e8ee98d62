# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/07/22
# __author:       <PERSON><PERSON><PERSON>



def injection_experiment_libs(*exp_class):
    import pyQCat.experiments as monster_exp_library

    for cls in exp_class:
        cls_name = cls.__name__
        if cls not in dir(monster_exp_library):
            print(f"set {cls_name} success!")
            setattr(monster_exp_library, cls_name, cls)
