{"T1SpectrumWithBiasCoupler": {"meta": {"username": "gkk", "visage_version": "0.23.19", "monster_version": "*********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "idle_point"}, "exp_class_name": "T1SpectrumWithBiasCoupler", "export_datetime": "2025-07-08 09:01:34", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "01", "physical_unit": "q7,c1-7,c2-7,c7-13,c7-14"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(100) | qarange | (200, 29900, 300)", "bias_coupler_name_list": "Points(0) | normal | None", "bias_coupler_z_amp_list": "Points(0) | normal | None"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "freq_range_map": {"l": 200, "r": 20, "s": 10}, "z_amp_list": "Points(11) | qarange | (-1, 1, 0.2)", "freq_list": "Points(0) | normal | None", "ac_branch": "right"}, "analysis_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "child_ana_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "quality_bounds": [0.9, 0.85, 0.77]}, "r_square_threshold": 0.7, "rate_threshold": 0.38, "ymax": 40}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}