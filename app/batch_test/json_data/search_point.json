{"QubitSpectrum": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "QubitSpectrum", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"freq_list": "Points(0) | normal | None", "drive_power": -40, "z_amp": null, "use_square": true, "band_width": 50.0, "fine_flag": false, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70.0, "fine_window_length": 11, "fine_freq_distance": 80.0}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 1.0, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0.0, "sigma": 5.0, "fast_m": false}, "scope": {"l": 100, "r": 100, "s": 2}, "f02_scope": {"l": 150, "r": -50, "s": 2}, "mode": "IF", "f_type": "f01", "is_dynamic": 0}, "analysis_options": {"quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CheckFreqRabiWidth": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "CheckFreqRabiWidth", "export_datetime": "2023-11-29 19:58:37", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "is_dynamic": 0}, "simulator_data_path": "D:\\project\\SupQAutomation\\code\\develop\\pyqcat-monster\\scripts\\simulator\\data\\CheckFreqRabiWidth", "freq_list": "Points(2) | normal | [4500, 4502, 4503, 4504]"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}}}, "QubitFreqCalibration": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null, "is_dynamic": 0}, "fringes": "Points(2) | qarange | (10, -10, -20)", "delays": "Points(71) | qarange | (100, 800, 10)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}, "subplots": [2, 2], "freq_gap_threshold": 0.1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "XpiDetection", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null, "is_dynamic": 0}, "expect_value": 0.7, "scope": 0.1, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanWidth": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "RabiScanWidth", "export_datetime": "2023-11-29 19:58:37", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1,q2,q3,q4,q5,q6"}, "options_for_regular_exec": {"experiment_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {}}, "CavityFreqSpectrum": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CavityFreqSpectrum", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q5"}, "options_for_regular_exec": {"experiment_options": {"fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": true, "scope": 3, "z_amp": null, "mode": "IF"}, "analysis_options": {"quality_bounds": [0.997, 0.992, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShot": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"quality_bounds": [2, 0.85, 0.8, 0.02], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ReadoutAmpCalibration": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "ReadoutAmpCalibration", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"level_str": "01"}, "sweep_list": "Points(17) | qarange | (0.03, 0.18, 0.01)", "left_rate": 0.1, "right_rate": 0.1, "point": 30}, "analysis_options": {"child_ana_options": {"quality_bounds": [2, 0.7, 0.6, 0.02], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ReadoutFreqCalibrate": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "ReadoutFreqCalibrate", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 3, "z_amp": null, "mode": "IF"}, "fc_list": "Points(0) | normal | None", "readout_power": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.998, 0.992, 0.85]}, "save_mode": "max_distance_point", "diff_threshold": 0.4}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SampleWidthOptimize": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "SampleWidthOptimize", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"level_str": "01"}, "sweep_list": "Points(17) | qarange | (600, 3000, 100)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [2, 0.85, 0.7, 0.02], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XYZTimingComposite": {"meta": {"username": "dpY2", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "batch_test"}, "exp_class_name": "XYZTimingComposite", "export_datetime": "2024-01-15 16:45:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"is_dynamic": 0, "time_perform": true, "delays": "Points(161) | qarange | (0.0, 100, 1.25)", "const_delay": 80, "z_pulse_params": {"time": 15, "amp": 0.1, "sigma": 0.1, "buffer": 5}}, "max_count": 2}, "analysis_options": {"child_ana_options": {"extract_mode": "fit_params", "quality_bounds": [0.95, 0.9, 0.75], "fit_model_name": "gauss_lo<PERSON>zian"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}