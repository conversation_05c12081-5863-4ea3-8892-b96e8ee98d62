{"ACSpectrumByCoupler_0": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion"}, "exp_class_name": "ACSpectrumByCoupler", "export_datetime": "2023-12-26 11:23:46", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c1-2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null, "is_dynamic": 0}, "z_amps": "Points(15) | qarange | (-0.17, -0.23, -0.003)", "delays": "Points(81) | qarange | (50, 100, 0.625)", "freq_bound": 100, "osc_freq_limit": 200, "init_fringe": 200, "spectrum_type": "standard"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}, "quality_bounds": [0.995, 0.99, 0.985], "fit_model_name": "flat_top_amp2freq_formula"}}, "options_for_parallel_exec": {"experiment_options": {" z_amps": {"c7-8": "Points(15) | qarange | (-0.15, -0.22, -0.004)", "c33-34": "Points(15) | qarange | (-0.15, -0.22, -0.004)", "c45-46": "Points(15) | qarange | (-0.18, -0.25, -0.004)"}}, "analysis_options": {}}}, "ACSpectrumByCoupler_1": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion"}, "exp_class_name": "ACSpectrumByCoupler", "export_datetime": "2023-12-29 11:06:14", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c7-8,c33-34,c45-46"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null}, "z_amps": "Points(18) | qarange | (-0.15, -0.22, -0.004)", "delays": "Points(81) | qarange | (50, 100, 0.625)", "freq_bound": 100, "osc_freq_limit": 200, "init_fringe": 200, "spectrum_type": "standard"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}, "quality_bounds": [0.995, 0.992, 0.99], "fit_model_name": "flat_top_amp2freq_formula"}}, "options_for_parallel_exec": {"experiment_options": {"child_exp_options": {"delays": {"c7-8": "Points(41) | qarange | (0, 200, 5)", "c33-34": "Points(41) | qarange | (0, 200, 5)", "c45-46": "Points(41) | qarange | (0, 200, 5)"}, "fringe": {"c7-8": 25, "c33-34": 25, "c45-46": 25}, "z_amp": {"c7-8": null, "c33-34": null, "c45-46": null}, "frequency": {"c7-8": null, "c33-34": null, "c45-46": null}, "ac_branch": {"c7-8": null, "c33-34": null, "c45-46": null}}, "z_amps": {"c7-8": "Points(18) | qarange | (-0.15, -0.22, -0.004)", "c33-34": "Points(18) | qarange | (-0.15, -0.22, -0.004)", "c45-46": "Points(18) | qarange | (-0.18, -0.25, -0.004)"}, "delays": {"c7-8": "Points(81) | qarange | (50, 100, 0.625)", "c33-34": "Points(81) | qarange | (50, 100, 0.625)", "c45-46": "Points(81) | qarange | (50, 100, 0.625)"}, "freq_bound": {"c7-8": 100, "c33-34": 100, "c45-46": 100}, "osc_freq_limit": {"c7-8": 200, "c33-34": 200, "c45-46": 200}, "init_fringe": {"c7-8": 200, "c33-34": 200, "c45-46": 200}, "spectrum_type": {"c7-8": "standard", "c33-34": "standard", "c45-46": "standard"}}, "analysis_options": {"child_ana_options": {"quality_bounds": {"c7-8": [0.98, 0.93, 0.81], "c33-34": [0.98, 0.93, 0.81], "c45-46": [0.98, 0.93, 0.81]}, "factor": {"c7-8": 3.5, "c33-34": 3.5, "c45-46": 3.5}}, "quality_bounds": {"c7-8": [0.995, 0.992, 0.99], "c33-34": [0.995, 0.992, 0.99], "c45-46": [0.995, 0.992, 0.99]}, "fit_model_name": {"c7-8": "flat_top_amp2freq_formula", "c33-34": "flat_top_amp2freq_formula", "c45-46": "flat_top_amp2freq_formula"}}}}, "ACSpectrumByCoupler_2": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion"}, "exp_class_name": "ACSpectrumByCoupler", "export_datetime": "2024-01-03 11:26:57", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c11-12,c15-16,c19-20,c45-46,c49-50,c57-58"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null}, "z_amps": "Points(18) | qarange | (-0.15, -0.22, -0.004)", "delays": "Points(81) | qarange | (50, 100, 0.625)", "freq_bound": 100, "osc_freq_limit": 200, "init_fringe": 200, "spectrum_type": "standard"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}, "quality_bounds": [0.995, 0.992, 0.99], "fit_model_name": "flat_top_amp2freq_formula"}}, "options_for_parallel_exec": {"experiment_options": {"child_exp_options": {"delays": {"c11-12": "Points(41) | qarange | (0, 200, 5)", "c15-16": "Points(41) | qarange | (0, 200, 5)", "c19-20": "Points(41) | qarange | (0, 200, 5)", "c45-46": "Points(41) | qarange | (0, 200, 5)", "c49-50": "Points(41) | qarange | (0, 200, 5)", "c57-58": "Points(41) | qarange | (0, 200, 5)"}, "fringe": {"c11-12": 25, "c15-16": 25, "c19-20": 25, "c45-46": 25, "c49-50": 25, "c57-58": 25}, "z_amp": {"c11-12": null, "c15-16": null, "c19-20": null, "c45-46": null, "c49-50": null, "c57-58": null}, "frequency": {"c11-12": null, "c15-16": null, "c19-20": null, "c45-46": null, "c49-50": null, "c57-58": null}, "ac_branch": {"c11-12": null, "c15-16": null, "c19-20": null, "c45-46": null, "c49-50": null, "c57-58": null}}, "z_amps": {"c11-12": "Points(21) | qarange | (-0.14, -0.18, -0.002)", "c15-16": "Points(21) | qarange | (-0.16, -0.2, -0.002)", "c19-20": "Points(21) | qarange | (-0.2, -0.24, -0.002)", "c45-46": "Points(21) | qarange | (-0.2, -0.24, -0.002)", "c49-50": "Points(21) | qarange | (-0.26, -0.3, -0.002)", "c57-58": "Points(21) | qarange | (-0.22, -0.26, -0.002)"}, "delays": {"c11-12": "Points(81) | qarange | (50, 100, 0.625)", "c15-16": "Points(81) | qarange | (50, 100, 0.625)", "c19-20": "Points(81) | qarange | (50, 100, 0.625)", "c45-46": "Points(81) | qarange | (50, 100, 0.625)", "c49-50": "Points(81) | qarange | (50, 100, 0.625)", "c57-58": "Points(81) | qarange | (50, 100, 0.625)"}, "freq_bound": {"c11-12": 100, "c15-16": 100, "c19-20": 100, "c45-46": 100, "c49-50": 100, "c57-58": 100}, "osc_freq_limit": {"c11-12": 200, "c15-16": 200, "c19-20": 200, "c45-46": 200, "c49-50": 200, "c57-58": 200}, "init_fringe": {"c11-12": 200, "c15-16": 200, "c19-20": 200, "c45-46": 200, "c49-50": 200, "c57-58": 200}, "spectrum_type": {"c11-12": "standard", "c15-16": "standard", "c19-20": "standard", "c45-46": "standard", "c49-50": "standard", "c57-58": "standard"}}, "analysis_options": {"child_ana_options": {"quality_bounds": {"c11-12": [0.98, 0.93, 0.81], "c15-16": [0.98, 0.93, 0.81], "c19-20": [0.98, 0.93, 0.81], "c45-46": [0.98, 0.93, 0.81], "c49-50": [0.98, 0.93, 0.81], "c57-58": [0.98, 0.93, 0.81]}, "factor": {"c11-12": 3.5, "c15-16": 3.5, "c19-20": 3.5, "c45-46": 3.5, "c49-50": 3.5, "c57-58": 3.5}}, "quality_bounds": {"c11-12": [0.995, 0.992, 0.99], "c15-16": [0.995, 0.992, 0.99], "c19-20": [0.995, 0.992, 0.99], "c45-46": [0.995, 0.992, 0.99], "c49-50": [0.995, 0.992, 0.99], "c57-58": [0.995, 0.992, 0.99]}, "fit_model_name": {"c11-12": "flat_top_amp2freq_formula", "c15-16": "flat_top_amp2freq_formula", "c19-20": "flat_top_amp2freq_formula", "c45-46": "flat_top_amp2freq_formula", "c49-50": "flat_top_amp2freq_formula", "c57-58": "flat_top_amp2freq_formula"}}}}, "ACSpectrumByCoupler_3": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion_group1"}, "exp_class_name": "ACSpectrumByCoupler", "export_datetime": "2024-01-08 15:12:22", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c2-3,c4-5,c8-9,c10-11,c14-15,c16-17,c22-23,c26-27,c28-29,c32-33,c34-35,c38-39,c40-41,c50-51,c56-57,c62-63,c68-69"}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "child_exp_options": {"time_perform": false, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "z_amps": "Points(22) | qarange | (-0.02, 0.4, 0.02)", "delays": "Points(61) | qarange | (200, 800, 10)", "freq_bound": 800, "osc_freq_limit": 2.5, "init_fringe": 10, "spectrum_type": "standard"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}, "quality_bounds": [0.995, 0.992, 0.99], "fit_model_name": "amp2freq_formula"}}, "options_for_parallel_exec": {"experiment_options": {"time_perform": {"c2-3": false, "c4-5": false, "c8-9": false, "c10-11": false, "c14-15": false, "c16-17": false, "c22-23": false, "c26-27": false, "c28-29": false, "c32-33": false, "c34-35": false, "c38-39": false, "c40-41": false, "c50-51": false, "c56-57": false, "c62-63": false, "c68-69": false}, "child_exp_options": {"time_perform": {"c2-3": false, "c4-5": false, "c8-9": false, "c10-11": false, "c14-15": false, "c16-17": false, "c22-23": false, "c26-27": false, "c28-29": false, "c32-33": false, "c34-35": false, "c38-39": false, "c40-41": false, "c50-51": false, "c56-57": false, "c62-63": false, "c68-69": false}, "delays": {"c2-3": "Points(41) | qarange | (0, 200, 5)", "c4-5": "Points(41) | qarange | (0, 200, 5)", "c8-9": "Points(41) | qarange | (0, 200, 5)", "c10-11": "Points(41) | qarange | (0, 200, 5)", "c14-15": "Points(41) | qarange | (0, 200, 5)", "c16-17": "Points(41) | qarange | (0, 200, 5)", "c22-23": "Points(41) | qarange | (0, 200, 5)", "c26-27": "Points(41) | qarange | (0, 200, 5)", "c28-29": "Points(41) | qarange | (0, 200, 5)", "c32-33": "Points(41) | qarange | (0, 200, 5)", "c34-35": "Points(41) | qarange | (0, 200, 5)", "c38-39": "Points(41) | qarange | (0, 200, 5)", "c40-41": "Points(41) | qarange | (0, 200, 5)", "c50-51": "Points(41) | qarange | (0, 200, 5)", "c56-57": "Points(41) | qarange | (0, 200, 5)", "c62-63": "Points(41) | qarange | (0, 200, 5)", "c68-69": "Points(41) | qarange | (0, 200, 5)"}, "fringe": {"c2-3": 25, "c4-5": 25, "c8-9": 25, "c10-11": 25, "c14-15": 25, "c16-17": 25, "c22-23": 25, "c26-27": 25, "c28-29": 25, "c32-33": 25, "c34-35": 25, "c38-39": 25, "c40-41": 25, "c50-51": 25, "c56-57": 25, "c62-63": 25, "c68-69": 25}, "z_amp": {"c2-3": null, "c4-5": null, "c8-9": null, "c10-11": null, "c14-15": null, "c16-17": null, "c22-23": null, "c26-27": null, "c28-29": null, "c32-33": null, "c34-35": null, "c38-39": null, "c40-41": null, "c50-51": null, "c56-57": null, "c62-63": null, "c68-69": null}, "frequency": {"c2-3": null, "c4-5": null, "c8-9": null, "c10-11": null, "c14-15": null, "c16-17": null, "c22-23": null, "c26-27": null, "c28-29": null, "c32-33": null, "c34-35": null, "c38-39": null, "c40-41": null, "c50-51": null, "c56-57": null, "c62-63": null, "c68-69": null}, "ac_branch": {"c2-3": "right", "c4-5": "right", "c8-9": "right", "c10-11": "right", "c14-15": "right", "c16-17": "right", "c22-23": "right", "c26-27": "right", "c28-29": "right", "c32-33": "right", "c34-35": "right", "c38-39": "right", "c40-41": "right", "c50-51": "right", "c56-57": "right", "c62-63": "right", "c68-69": "right"}}, "z_amps": {"c2-3": "Points(31) | qarange | (-0.15, -0.21, -0.002)", "c4-5": "Points(31) | qarange | (-0.16, -0.22, -0.002)", "c8-9": "Points(31) | qarange | (-0.16, -0.22, -0.002)", "c10-11": "Points(31) | qarange | (-0.12, -0.15, -0.001)", "c14-15": "Points(31) | qarange | (-0.18, -0.24, -0.002)", "c16-17": "Points(31) | qarange | (-0.15, -0.21, -0.002)", "c22-23": "Points(31) | qarange | (-0.15, -0.21, -0.002)", "c26-27": "Points(31) | qarange | (-0.16, -0.22, -0.002)", "c28-29": "Points(31) | qarange | (-0.16, -0.22, -0.002)", "c32-33": "Points(31) | qarange | (-0.16, -0.22, -0.002)", "c34-35": "Points(31) | qarange | (-0.16, -0.22, -0.002)", "c38-39": "Points(31) | qarange | (-0.16, -0.22, -0.002)", "c40-41": "Points(31) | qarange | (-0.18, -0.24, -0.002)", "c50-51": "Points(31) | qarange | (-0.16, -0.22, -0.002)", "c56-57": "Points(31) | qarange | (-0.17, -0.23, -0.002)", "c62-63": "Points(31) | qarange | (-0.17, -0.23, -0.002)", "c68-69": "Points(31) | qarange | (0.17, 0.23, 0.002)"}, "delays": {"c2-3": "Points(81) | qarange | (50, 100, 0.625)", "c4-5": "Points(81) | qarange | (50, 100, 0.625)", "c8-9": "Points(81) | qarange | (50, 100, 0.625)", "c10-11": "Points(81) | qarange | (50, 100, 0.625)", "c14-15": "Points(81) | qarange | (50, 100, 0.625)", "c16-17": "Points(81) | qarange | (50, 100, 0.625)", "c22-23": "Points(81) | qarange | (50, 100, 0.625)", "c26-27": "Points(81) | qarange | (50, 100, 0.625)", "c28-29": "Points(81) | qarange | (50, 100, 0.625)", "c32-33": "Points(81) | qarange | (50, 100, 0.625)", "c34-35": "Points(81) | qarange | (50, 100, 0.625)", "c38-39": "Points(81) | qarange | (50, 100, 0.625)", "c40-41": "Points(81) | qarange | (50, 100, 0.625)", "c50-51": "Points(81) | qarange | (50, 100, 0.625)", "c56-57": "Points(81) | qarange | (50, 100, 0.625)", "c62-63": "Points(81) | qarange | (50, 100, 0.625)", "c68-69": "Points(81) | qarange | (50, 100, 0.625)"}, "freq_bound": {"c2-3": 100, "c4-5": 100, "c8-9": 100, "c10-11": 100, "c14-15": 100, "c16-17": 100, "c22-23": 100, "c26-27": 100, "c28-29": 100, "c32-33": 100, "c34-35": 100, "c38-39": 100, "c40-41": 100, "c50-51": 100, "c56-57": 100, "c62-63": 100, "c68-69": 100}, "osc_freq_limit": {"c2-3": 200, "c4-5": 200, "c8-9": 200, "c10-11": 200, "c14-15": 200, "c16-17": 200, "c22-23": 200, "c26-27": 200, "c28-29": 200, "c32-33": 200, "c34-35": 200, "c38-39": 200, "c40-41": 200, "c50-51": 200, "c56-57": 200, "c62-63": 200, "c68-69": 200}, "init_fringe": {"c2-3": 200, "c4-5": 200, "c8-9": 200, "c10-11": 200, "c14-15": 200, "c16-17": 200, "c22-23": 200, "c26-27": 200, "c28-29": 200, "c32-33": 200, "c34-35": 200, "c38-39": 200, "c40-41": 200, "c50-51": 200, "c56-57": 200, "c62-63": 200, "c68-69": 200}, "spectrum_type": {"c2-3": "standard", "c4-5": "standard", "c8-9": "standard", "c10-11": "standard", "c14-15": "standard", "c16-17": "standard", "c22-23": "standard", "c26-27": "standard", "c28-29": "standard", "c32-33": "standard", "c34-35": "standard", "c38-39": "standard", "c40-41": "standard", "c50-51": "standard", "c56-57": "standard", "c62-63": "standard", "c68-69": "standard"}}, "analysis_options": {"child_ana_options": {"quality_bounds": {"c2-3": [0.98, 0.93, 0.81], "c4-5": [0.98, 0.93, 0.81], "c8-9": [0.98, 0.93, 0.81], "c10-11": [0.98, 0.93, 0.81], "c14-15": [0.98, 0.93, 0.81], "c16-17": [0.98, 0.93, 0.81], "c22-23": [0.98, 0.93, 0.81], "c26-27": [0.98, 0.93, 0.81], "c28-29": [0.98, 0.93, 0.81], "c32-33": [0.98, 0.93, 0.81], "c34-35": [0.98, 0.93, 0.81], "c38-39": [0.98, 0.93, 0.81], "c40-41": [0.98, 0.93, 0.81], "c50-51": [0.98, 0.93, 0.81], "c56-57": [0.98, 0.93, 0.81], "c62-63": [0.98, 0.93, 0.81], "c68-69": [0.98, 0.93, 0.81]}, "factor": {"c2-3": 3.5, "c4-5": 3.5, "c8-9": 3.5, "c10-11": 3.5, "c14-15": 3.5, "c16-17": 3.5, "c22-23": 3.5, "c26-27": 3.5, "c28-29": 3.5, "c32-33": 3.5, "c34-35": 3.5, "c38-39": 3.5, "c40-41": 3.5, "c50-51": 3.5, "c56-57": 3.5, "c62-63": 3.5, "c68-69": 3.5}}, "quality_bounds": {"c2-3": [0.995, 0.99, 0.98], "c4-5": [0.995, 0.99, 0.98], "c8-9": [0.995, 0.99, 0.98], "c10-11": [0.995, 0.99, 0.98], "c14-15": [0.995, 0.99, 0.98], "c16-17": [0.995, 0.99, 0.98], "c22-23": [0.995, 0.99, 0.98], "c26-27": [0.995, 0.99, 0.98], "c28-29": [0.995, 0.99, 0.98], "c32-33": [0.995, 0.99, 0.98], "c34-35": [0.995, 0.99, 0.98], "c38-39": [0.995, 0.99, 0.98], "c40-41": [0.995, 0.99, 0.98], "c50-51": [0.995, 0.99, 0.98], "c56-57": [0.995, 0.99, 0.98], "c62-63": [0.995, 0.99, 0.98], "c68-69": [0.995, 0.99, 0.98]}, "fit_model_name": {"c2-3": "flat_top_amp2freq_formula", "c4-5": "flat_top_amp2freq_formula", "c8-9": "flat_top_amp2freq_formula", "c10-11": "flat_top_amp2freq_formula", "c14-15": "flat_top_amp2freq_formula", "c16-17": "flat_top_amp2freq_formula", "c22-23": "flat_top_amp2freq_formula", "c26-27": "flat_top_amp2freq_formula", "c28-29": "flat_top_amp2freq_formula", "c32-33": "flat_top_amp2freq_formula", "c34-35": "flat_top_amp2freq_formula", "c38-39": "flat_top_amp2freq_formula", "c40-41": "flat_top_amp2freq_formula", "c50-51": "flat_top_amp2freq_formula", "c56-57": "flat_top_amp2freq_formula", "c62-63": "flat_top_amp2freq_formula", "c68-69": "flat_top_amp2freq_formula"}}}}, "CouplerTunableByQS_0": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "0.5.6", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "test"}, "exp_class_name": "CouplerTunableByQS", "export_datetime": "2023-12-21 22:57:54", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c1-2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"freq_list": "Points(0) | normal | None", "drive_power": -40, "z_amp": null, "use_square": true, "band_width": 50.0, "fine_flag": false, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70.0, "fine_window_length": 11, "fine_freq_distance": 80.0}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 1.0, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0.0, "sigma": 5.0, "fast_m": false}, "scope": {"l": 200, "r": 200, "s": 3}, "f02_scope": {"l": 150, "r": -50, "s": 2}, "mode": "IF", "f_type": "f01", "is_dynamic": 0}, "flux_list": "Points(61) | qarange | (-0.4, -0.1, 0.005)", "scan_name": "amp"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerDistortionZZ_0": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "0.5.6", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "test"}, "exp_class_name": "CouplerDistortionZZ", "export_datetime": "2023-12-21 16:56:11", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c2-3,c28-29"}, "options_for_regular_exec": {"experiment_options": {"z_offset_list": "Points(81) | qarange | (-0.02, 0.02, 0.001)", "gauss_sigma": 5.0, "gauss_width": 15.0, "const_width": 100, "ta": 10000, "add_tb_width": 100, "z_amp": 0.3, "xy_delay": 1000, "run_mode": "normal", "frequency": null, "ac_branch": null, "simulator_data_path": "D:\\data\\20231127 72bitV9.2.3 @ Y4\\Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#\\BatchCouplerDistortionT1New\\2024-01-09_22.48.13\\c3-9\\CouplerDistortionZZCompositeNew\\2024-01-09\\22.48.14\\q3_iter0\\child\\CouplerDistortionZZ\\22-48-51 iter0_xy_delay=21.25_err_count=0 - CouplerDistortionZZ(p0_p1).dat"}, "analysis_options": {"quality_bounds": [0.95, 0.85, 0.75], "fit_model_name": "skewed_gauss_lorentz", "cali_offset_method": "direct", "cut_index": true, "adjust_noise": true}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerDistortionZZ_1": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "0.5.6", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "test"}, "exp_class_name": "CouplerDistortionZZ", "export_datetime": "2023-12-21 16:56:11", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c2-3,c28-29"}, "options_for_regular_exec": {"experiment_options": {"z_offset_list": "Points(81) | qarange | (-0.04, 0.04, 0.001)", "gauss_sigma": 5.0, "gauss_width": 15.0, "const_width": 100, "ta": 10000, "add_tb_width": 100, "z_amp": -0.4, "xy_delay": 100, "run_mode": "normal", "frequency": null, "ac_branch": null, "simulator_data_path": "D:\\data\\20231127 72bitV9.2.3 @ Y4\\Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#\\BatchCouplerDistortion\\2023-12-15_21.46.47\\c28-29\\idle=-0.16\\CouplerDistortionZZ\\2023-12-15\\21.49.56\\CouplerDistortionZZ(p0_p1).dat"}, "analysis_options": {"quality_bounds": [0.95, 0.85, 0.75], "fit_model_name": "lorentzian", "cali_offset_method": "direct", "cut_index": true, "adjust_noise": true}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerXYZTimingByZZShift_0": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion"}, "exp_class_name": "CouplerXYZTimingByZZShift", "export_datetime": "2024-01-03 20:32:06", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"delays": "Points(161) | qarange | (0.0, 100, 0.625)", "const_delay": 80, "z_pulse_params": {"time": 30, "amp": 0.2, "sigma": 1, "buffer": 5}, "is_dynamic": 0}, "analysis_options": {"extract_mode": "fit_params", "quality_bounds": [0.95, 0.9, 0.75], "fit_model_name": "gauss_lo<PERSON>zian"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerDistortionZZCompositeNew_0": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion"}, "exp_class_name": "CouplerDistortionZZCompositeNew", "export_datetime": "2024-01-02 14:43:30", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c1-2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"z_offset_list": "Points(151) | qarange | (-0.15, 0.15, 0.002)", "gauss_sigma": 5.0, "gauss_width": 15.0, "const_width": 100, "ta": 10000, "add_tb_width": 100, "z_amp": -0.5, "xy_delay": 0, "run_mode": "normal", "frequency": null, "ac_branch": null}, "iteration_times": 1, "xy_delay_start": 20, "xy_delay_max": 20000, "xy_step_map": {"lt_10": 0.625, "lt_50": 1.25, "lt_100": 5.0, "lt_200": 10.0, "lt_500": 20.0, "lt_1000": 50.0, "lt_5000": 100.0, "lt_10000": 200.0, "gt_10000": 300.0, "gt_20000": 400.0}, "exp_max_num": 250, "width_num": 1, "width_min": 0.003, "width_max": 0.009, "min_scan_points": 51, "max_scan_points": 51, "bit_type": "<PERSON><PERSON><PERSON>", "base_history": false, "update_xy_delay_slu": false, "update_z_offset_range": true, "z_amp": 0.3, "z_offset_list": "Points(81) | qarange | (-0.03, 0.03, 0.0003)", "expect_z_step": 0.0003, "z_offset_min": -0.03, "z_offset_max": 0.03, "frequency": null, "ac_branch": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.95, 0.85, 0.75], "fit_model_name": "skewed_gauss_lorentz", "cali_offset_method": "direct", "cut_index": true, "adjust_noise": true}, "quality_bounds": [0.9999, 0.999, 0.9], "lfilter_flag": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitFreqCalibration_0": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2023-12-26 11:38:55", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(57) | qarange | (20, 160, 2.5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null}, "fringes": "Points(2) | qarange | (25, -25, -50)", "delays": "Points(49) | qarange | (20, 140, 2.5)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}, "subplots": [2, 2], "freq_gap_threshold": 0.99}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitSpectrum_0": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "0.5.6", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "test"}, "exp_class_name": "QubitSpectrum", "export_datetime": "2023-12-21 16:56:11", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q3,q29"}, "options_for_regular_exec": {"experiment_options": {"freq_list": "Points(0) | normal | None", "drive_power": -40, "z_amp": null, "use_square": true, "band_width": 50.0, "fine_flag": true, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70.0, "fine_window_length": 11, "fine_freq_distance": 80.0}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 1.0, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0.0, "sigma": 5.0, "fast_m": false}, "scope": {"l": 80, "r": 20, "s": 1}, "f02_scope": {"l": 150, "r": -50, "s": 2}, "mode": "IF", "f_type": "f01"}, "analysis_options": {"quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanAmp_0": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "0.5.6", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "test"}, "exp_class_name": "RabiScanAmp", "export_datetime": "2023-12-21 17:03:44", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q3,q29"}, "options_for_regular_exec": {"experiment_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": -30}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "Ramsey_0": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion"}, "exp_class_name": "<PERSON>", "export_datetime": "2024-01-03 20:34:11", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"delays": "Points(49) | qarange | (20, 140, 2.5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null}, "analysis_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection_0": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "0.5.6", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "test"}, "exp_class_name": "XpiDetection", "export_datetime": "2023-12-21 16:56:11", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q3,q29"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "expect_value": 0.7, "scope": 0.1, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": -20}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection_1": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "0.5.6", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "test"}, "exp_class_name": "XpiDetection", "export_datetime": "2023-12-21 16:56:11", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q3,q29"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "expect_value": 0.4, "scope": 0.1, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": -30}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ZZShiftFixedPointCalibration_0": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle_point"}, "exp_class_name": "ZZShiftFixedPointCalibration", "export_datetime": "2024-01-16 10:26:44", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c4-10"}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "child_exp_options": {"time_perform": false, "delays": "Points(81) | qarange | (50, 100, 0.625)", "fringe": 200, "z_amp": null, "frequency": null, "ac_branch": "right"}, "iteration": 8, "threshold": 1, "guess_step": 0.4, "mode": "wave", "z_amp": -0.18, "delta_freq": 30}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}