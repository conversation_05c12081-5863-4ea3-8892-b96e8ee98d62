{"QubitFreqCalibration_preliminary": {"context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "experiment_options": {"fringes": [50, -50], "delays*qarange": [20, 140, 2.5], "child_exp_options": {"is_dynamic": 0}}, "analysis_options": {"freq_gap_threshold": 8, "child_ana_options": {"quality_bounds": [0.9, 0.4, 0.3]}}}, "XpiDetection": {"context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "experiment_options": {"expect_value": 0.4, "max_loops": 5, "name": "Xpi", "amps*qarange": [0, 0.9, 0.02], "child_exp_options": {"is_dynamic": 0}}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.95, 0.8, 0.7]}}}, "QubitFreqCalibration_detail": {"context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "experiment_options": {"fringes": [50, -50], "delays*qarange": [20, 140, 2.5], "child_exp_options": {"is_dynamic": 0}}, "analysis_options": {"freq_gap_threshold": 0.5, "child_ana_options": {"quality_bounds": [0.9, 0.6, 0.5]}}}, "SingleShot_0": {"context_options": {"name": "qubit_calibration", "physical_unit": ["q1", "q2"], "readout_type": null}, "experiment_options": {"level_str": "01", "repeat": 5000}, "analysis_options": {"quality_bounds": [2, 0.7, 0.5, 0.05]}}, "SingleShot_1": {"context_options": {"name": "qubit_calibration", "physical_unit": ["q1", "q2"], "readout_type": null}, "experiment_options": {"level_str": "01", "repeat": 5000}, "analysis_options": {"quality_bounds": [2, 0.7, 0.58, 0.05]}}, "RabiScanWidth": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "RabiScanWidth", "export_datetime": "2023-11-29 19:58:37", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1,q2,q3,q4,q5,q6"}, "options_for_regular_exec": {"experiment_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {}}}