{"FindBusCavityFreq": {"meta": {"username": "BY170003", "visage_version": "0.4.9", "monster_version": "*******", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "FindBusCavityFreq", "export_datetime": "2024-02-04 17:24:53", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "01", "physical_unit": "q1,q2,q3,q4,q5,q6"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "device_conf_path": null, "chip_type": "V9_72", "bus": 1, "segm_scan": false, "freq_list": "Points(601) | qarange | (7080, 7380, 0.5)", "scope": {"l": 2, "r": 2, "p": 201}, "network_analyzer": "E5071C", "net_power": -45, "ATT": 0, "net_IFBW": 100}, "analysis_options": {"cavity_count": 6, "fit_q": true, "chi_square": 0.01, "distance": 20}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ImpaCavityFluxScan": {"meta": {"username": "BY170003", "visage_version": "0.4.9", "monster_version": "*******", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "ImpaCavityFluxScan", "export_datetime": "2024-02-04 17:26:05", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "", "physical_unit": "q1,q2,q3,q4,q5,q6"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "device_conf_path": null, "network_analyzer": "E5071C", "net_IFBW": 10000, "net_power": -40, "dc_source": "qaio", "scan_name": "dc", "dc_list": "Points(201) | qarange | (-0.5, 0.5, 0.01)", "freq_list": "Points(501) | qarange | (8000, 8500, 1)", "span": null, "bus": 1}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ImpaOptiParams": {"meta": {"username": "BY170003", "visage_version": "0.4.9", "monster_version": "*******", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "ImpaOptiParams", "export_datetime": "2024-02-04 17:26:53", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "", "physical_unit": "q1,q2,q3,q4,q5,q6"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "device_conf_path": null, "network_analyzer": "E5071C", "net_IFBW": 10000, "net_power": -40, "ATT": -100, "freq_list": "Points(601) | qarange | (6900, 7500, 1)", "input_data": {"flux": {"is_opt": true, "init_v": -0.01, "bound": [-0.2, 0.2]}, "fp": {"is_opt": true, "init_v": 14.9, "bound": [-0.1, 0.1]}, "pp": {"is_opt": true, "init_v": -0.1, "bound": [-25, 15]}}, "opt_method": "DE", "de_opts": {"NIND": 6, "MAXGEN": 30, "mutF": 0.6, "XOVR": 0.7}, "nm_custom_opts": {"ftarget": -100, "nonzdelt": [0.02, 0.0002, 0.1], "step": [5e-05, 0.1, 0.02], "maxiter": 150, "maxfev": 150, "xatol": 0.0002, "return_all": true, "disp": true}, "nm_opts": {"maxiter": 250, "maxfev": 250, "xatol": 0.0002, "return_all": true, "disp": true}, "flux_pump_off": 0, "mic_source": "stfsh9004", "dc_source": "qaio", "segm_scan": true, "interval": 0.2, "mean_gain_ratio": 0.6, "std_gain_ratio": 0.4, "flag_opt_init": true, "bus": 1, "gain_bound": "Points(2) | qarange | (5, 20, 15)"}, "analysis_options": {"std_gain": 5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ImpaGain": {"meta": {"username": "BY170003", "visage_version": "0.4.9", "monster_version": "*******", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "ImpaGain", "export_datetime": "2024-02-04 17:27:37", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "", "physical_unit": "q1,q2,q3,q4,q5,q6"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "device_conf_path": null, "network_analyzer": "E5071C", "freq_list": "Points(1201) | qarange | (6900, 7500, 0.5)", "net_IFBW": 20000, "net_power": -40, "ATT": -100, "ffp_list": "Points(3) | normal | [-0.07861, 14.9816, 10.4]", "mic_source": "stfsh9004", "dc_source": "qaio", "interval": 0.1, "total_time": 2, "interval_time": 20, "stab_flag": true, "bus": 1}, "analysis_options": {"gain_threshold": 25}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ImpaSetParams": {"meta": {"username": "BY170003", "visage_version": "0.4.9", "monster_version": "*******", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "ImpaSetParams", "export_datetime": "2024-02-04 17:28:23", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "", "physical_unit": "q1,q2,q3,q4,q5,q6"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "device_conf_path": null, "bus": 1, "ffp_list": "Points(3) | normal | [-0.07861, 14.9816, 10.4]", "mic_source": "stfsh9004", "dc_source": "qaio", "interval": 1}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}