{"QubitFreqCalibration_0": {"context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "experiment_options": {"fringes": [100, -100], "delays*qarange": [20, 140, 2.5], "child_exp_options": {"is_dynamic": 0}}, "analysis_options": {"freq_gap_threshold": 8, "child_ana_options": {"quality_bounds": [0.9, 0.4, 0.3]}}}, "XpiDetection_0": {"context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "experiment_options": {"expect_value": 0.4, "max_loops": 5, "name": "Xpi", "amps*qarange": [0, 0.9, 0.02], "child_exp_options": {"is_dynamic": 0}}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.95, 0.8, 0.7]}}}, "QubitFreqCalibration_1": {"context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "experiment_options": {"fringes": [50, -50], "delays*qarange": [20, 140, 2.5], "child_exp_options": {"is_dynamic": 0}}, "analysis_options": {"freq_gap_threshold": 0.5, "child_ana_options": {"quality_bounds": [0.9, 0.6, 0.5]}}}, "RabiScanAmp_0": {"context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "experiment_options": {"amps*qarange": [0, 0.9, 0.02], "name": "Xpi", "is_dynamic": 0}, "analysis_options": {"quality_bounds": [0.95, 0.8, 0.7]}}, "SingleShot_0": {"context_options": {"name": "qubit_calibration", "physical_unit": ["q1", "q2"], "readout_type": null}, "experiment_options": {"level_str": "01", "repeat": 5000}, "analysis_options": {"quality_bounds": [2, 0.7, 0.5, 0.05]}}, "DetuneCalibration_0": {"context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "experiment_options": {"detune_list*qarange": [-28, 28, 1], "rough_n_list": [6, 7, 8], "fine_n_list": [12, 13], "theta_type": "Xpi", "fine_precision": 0.15, "child_exp_options": {"child_exp_options": {"is_dynamic": 0}}}, "analysis_options": {"child_ana_options": {"diff_threshold": 0.3}}}, "RabiScanAmp_1": {"context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "experiment_options": {"amps*qarange": [0, 0.9, 0.02], "name": "Xpi", "is_dynamic": 0}, "analysis_options": {"quality_bounds": [0.95, 0.8, 0.7]}}, "AmpComposite_0": {"context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "experiment_options": {"n_list": [8, 16], "theta_type": "Xpi", "child_exp_options": {"points": 81, "threshold_left": 0.9, "threshold_right": 1.1, "is_dynamic": 0}}, "analysis_options": {"diff_threshold": 0.05}}, "AmpComposite_1": {"context_options": {"name": "qubit_calibration", "physical_unit": "q1", "readout_type": "01"}, "experiment_options": {"n_list": [16, 20], "theta_type": "Xpi/2", "child_exp_options": {"points": 81, "threshold_left": 0.9, "threshold_right": 1.1, "is_dynamic": 0}}, "analysis_options": {"diff_threshold": 0.05}}, "SingleShot_1": {"context_options": {"name": "qubit_calibration", "physical_unit": ["q1", "q2"], "readout_type": null}, "experiment_options": {"level_str": "01", "repeat": 5000}, "analysis_options": {"quality_bounds": [2, 0.7, 0.58, 0.05]}}, "RBSingle_0": {"context_options": {"name": "qubit_calibration", "physical_unit": ["q1", "q2"], "readout_type": "01"}, "experiment_options": {"times": 30, "depth1*qarange": [2, 10, 2], "depth2*qarange": [15, 50, 5], "depth3*qarange": [60, 100, 10], "depth4*qarange": [120, 380, 20], "gate_split": true, "mode": "cache", "is_dynamic": 0, "use_simulator": false}, "analysis_options": {"quality_bounds": [0.99, 0.9, 0.8], "std_bound": 0.06}}, "QubitFreqCalibration": {"meta": {"username": "why_y4_debug", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2024-08-20 09:53:26", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "auto_set_fringe": false, "frequency": null, "ac_branch": "right"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "fringes": "Points(2) | qarange | (50, -50, -100)", "delays": "Points(41) | qarange | (20, 120, 2.5)"}, "analysis_options": {"pure_exp_mode": false, "child_ana_options": {"pure_exp_mode": false, "quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}, "subplots": [2, 2], "freq_gap_threshold": 0.1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanWidth": {"meta": {"username": "why_y4_debug", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "RabiScanWidth", "export_datetime": "2024-08-21 18:17:01", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false}, "analysis_options": {"pure_exp_mode": false, "quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XYCrossRabiWidthOnce": {"meta": {"username": "why_y4_debug", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "XYCrossRabiWidthOnce", "export_datetime": "2024-08-20 17:50:11", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"widths": "Points(40) | qarange | (5, 10000, 250)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "xy_name": "", "rd_name": "", "direct_execute": true}, "analysis_options": {"pure_exp_mode": false, "quality_bounds": [0.9, 0.8, 0.7], "loga_fit": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}