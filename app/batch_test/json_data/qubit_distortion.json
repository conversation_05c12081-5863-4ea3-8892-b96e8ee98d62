{"VoltageDriftGradientCalibration": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "VoltageDriftGradientCalibration", "export_datetime": "2023-12-22 17:41:32", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (10, 70, 0.625)", "fringe": 100, "z_amp": null, "frequency": null, "ac_branch": null, "is_dynamic": 0}, "threshold": 0.05, "guess_step": 0.2, "iteration": 10, "cali_point": "sweet_point"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "DistortionT1CompositeNew": {"meta": {"username": "why_y4_debug", "visage_version": "0.4.9", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "DistortionT1CompositeNew", "export_datetime": "2024-02-04 14:29:21", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q67"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "z_offset_list": "Points(81) | qarange | (-0.02, 0.02, 0.0005)", "gauss_sigma": 5.0, "gauss_width": 15.0, "const_width": 100, "ta": 2000, "add_tb_width": 100, "z_amp": -0.5, "xy_delay": 0, "run_mode": "new_case", "frequency": null, "ac_branch": "right", "is_dynamic": 0}, "iteration_times": 1, "xy_delay_start": 0, "xy_delay_max": 30000, "xy_step_map": {"lt_10": 0.625, "lt_50": 1.25, "lt_100": 5.0, "lt_200": 10.0, "lt_500": 20.0, "lt_1000": 50.0, "lt_5000": 100.0, "lt_10000": 200.0, "gt_10000": 300.0, "gt_20000": 500.0}, "exp_max_num": 200, "width_num": 1, "width_min": 0.02, "width_max": 0.05, "min_scan_points": 51, "max_scan_points": 51, "bit_type": "<PERSON><PERSON><PERSON>", "base_history": false, "update_xy_delay_slu": false, "update_z_offset_range": true, "z_amp": null, "z_offset_list": "Points(81) | qarange | (-0.025, 0.025, 0.0005)", "expect_z_step": null, "z_offset_min": -0.5, "z_offset_max": 0.5, "frequency": null, "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.95, 0.85, 0.75], "fit_model_name": "skewed_gauss_lorentz", "cali_offset_method": "direct", "cut_index": true, "adjust_noise": true}, "quality_bounds": [0.9999, 0.999, 0.9], "lfilter_flag": true}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShot": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"quality_bounds": [2, 0.85, 0.7, 0.011], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}