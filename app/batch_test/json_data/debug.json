{"RamseyExtend": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "RamseyExtend", "export_datetime": "2024-01-03 10:27:35", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q1q2"}, "options_for_regular_exec": {"experiment_options": {"delays": "Points(41) | qarange | (10, 70, 0.625)", "fringe": 100, "z_amp": 0.1, "frequency": null, "ac_branch": null, "tq_name": "qh", "bq_name_list": "Points(1) | normal | ['qc']", "bq_z_amp_list": "Points(0) | normal | None", "bq_freq_list": "Points(0) | normal | None", "auto_set_coupler_zamp": true, "auto_set_label": "cz", "simulator_data_path": "D:\\data\\SimulatorData\\RamseyExtend\\origin_data"}, "analysis_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "FixedPointCalibration": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "FixedPointCalibration", "export_datetime": "2024-01-03 11:38:29", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q1q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null, "tq_name": "qh", "bq_name_list": "Points(1) | normal | ['qc']", "bq_z_amp_list": "Points(0) | normal | None", "bq_freq_list": "Points(0) | normal | None", "auto_set_coupler_zamp": false, "auto_set_label": "cz"}, "z_amp": null, "frequency": null, "diff_frequency": null, "label": "cz", "gradient_type": "quadratic", "threshold": 0.1, "guess_step": 0.2, "iteration": 5, "trans_amp_to_freq": false}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SweepDetuneCom": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "SweepDetuneCom", "export_datetime": "2024-01-03 11:38:29", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": "q1q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"scan_name": "ql", "is_dynamic": 0, "detune_freq_list": "Points(21) | qarange | (4180, 4200, 1)"}, "scan_name": "ql", "detune_list": "Points(21) | normal | None", "detune_freq_list": "Points(21) | qarange | (4180, 4200, 1)", "simulator_data_path": "D:\\project\\SupQAutomation\\resource\\实验需求\\SweepDetune\\SweepDetuneCom\\child\\SweepDetune"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SweepDetuneComPhase": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "SweepDetuneComPhase", "export_datetime": "2024-01-03 11:38:29", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": "q1q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"is_dynamic": 0, "mode": "SE-TM", "ramsey_bit": "qh", "scan_name": "ql", "sweep_detune": true, "detune_list": "Points(21) | normal | None", "detune_freq_list": "Points(21) | qarange | (4180, 4200, 1)"}, "scan_name": "ql", "detune_list": "Points(21) | normal | None", "detune_freq_list": "Points(21) | qarange | (4180, 4200, 1)", "simulator_data_path": "D:\\project\\SupQAutomation\\resource\\实验需求\\SweepDetune\\SweepDetuneComPhase\\child\\CPhaseTMSE"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitSpectrum": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "QubitSpectrum", "export_datetime": "2024-01-12 09:10:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "freq_list": "Points(0) | normal | None", "drive_power": null, "z_amp": null, "use_square": true, "band_width": 50.0, "fine_flag": false, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70.0, "fine_window_length": 11, "fine_freq_distance": 80.0}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 1.0, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0.0, "sigma": 5.0, "fast_m": false}, "scope": {"l": 100, "r": 100, "s": 2}, "f02_scope": {"l": 150, "r": -50, "s": 2}, "mode": "IF", "f_type": "f01", "simulator_data_path": "D:\\project\\SupQAutomation\\resource\\实验需求\\QubitSpectrum\\q0\\2023-11-10\\09.29.35"}, "analysis_options": {"quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanWidth": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "RabiScanWidth", "export_datetime": "2023-11-29 19:58:37", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {"widths": {"q1": "Points(40) | qarange | (5, 200, 5)", "q2": "Points(40) | qarange | (5, 200, 5)", "q3": "Points(40) | qarange | (5, 200, 5)", "q4": "Points(40) | qarange | (5, 200, 5)", "q5": "Points(40) | qarange | (5, 200, 5)", "q6": "Points(40) | qarange | (5, 200, 5)"}, "drive_power": {"q1": -40, "q2": -40, "q3": -40, "q4": -40, "q5": -40, "q6": -40}, "drive_freq": {"q1": null, "q2": null, "q3": null, "q4": null, "q5": null, "q6": null}, "adjust_power_flag": {"q1": false, "q2": false, "q3": false, "q4": false, "q5": false, "q6": false}}, "analysis_options": {"quality_bounds": {"q1": [0.98, 0.95, 0.91], "q2": [0.98, 0.95, 0.91], "q3": [0.98, 0.95, 0.91], "q4": [0.98, 0.95, 0.91], "q5": [0.98, 0.95, 0.91], "q6": [0.98, 0.95, 0.91]}}}}, "CheckFreqRabiWidth": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "CheckFreqRabiWidth", "export_datetime": "2023-11-29 19:58:37", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "is_dynamic": 0}, "simulator_data_path": "D:\\project\\SupQAutomation\\code\\develop\\pyqcat-monster\\scripts\\simulator\\data\\CheckFreqRabiWidth", "freq_list": "Points(2) | normal | [4500, 4502, 4503, 4504]"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {"freq_list": {"q0": "Points(2) | normal | [4500, 4502, 4503, 4504]"}}, "analysis_options": {}}}}