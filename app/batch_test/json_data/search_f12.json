{"QubitFreqCalibration": {"meta": {"username": "zyc", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2024-08-15 15:23:39", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "auto_set_fringe": false, "frequency": null, "ac_branch": "right"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "fringes": "Points(2) | qarange | (10, -10, -20)", "delays": "Points(71) | qarange | (100, 800, 10)"}, "analysis_options": {"pure_exp_mode": false, "child_ana_options": {"pure_exp_mode": false, "quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}, "subplots": [2, 2], "freq_gap_threshold": 0.1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection": {"meta": {"username": "zyc", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "XpiDetection", "export_datetime": "2024-08-15 15:25:31", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "run_mode": "sync", "async_time_out": 10, "quality_filter": false, "expect_value": 0.7, "scope": 0.1, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "analysis_options": {"pure_exp_mode": false, "child_ana_options": {"pure_exp_mode": false, "quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShot": {"meta": {"username": "zyc", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "SingleShot", "export_datetime": "2024-08-15 15:25:24", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"pure_exp_mode": false, "quality_bounds": [2, 0.85, 0.7, 0.011], "method": "GMM", "n_multiple": 3, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitSpectrumF12": {"meta": {"username": "zyc", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "QubitSpectrumF12", "export_datetime": "2024-08-15 15:25:45", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"f12_list": "Points(0) | normal | None", "f12_xpi": null, "f12_width": 500, "scope": {"l": 250, "r": -150, "s": 2}, "z_amp": 0, "delay": 0}, "analysis_options": {"pure_exp_mode": false, "quality_bounds": [0.8, 0.6, 0.5], "window_length": 11, "freq_distance": 80}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}