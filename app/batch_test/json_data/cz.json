{"FixedPointCalibration_cz_qh_coupler_biased": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "FixedPointCalibration", "export_datetime": "2023-12-22 17:41:32", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "qh-01", "physical_unit": "q1q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (40, 140, 2.5)", "fringe": 50, "z_amp": null, "frequency": null, "ac_branch": null, "tq_name": "qh", "bq_name_list": ["qc"], "bq_z_amp_list": [0], "bq_freq_list": [0], "auto_set_coupler_zamp": true}, "z_amp": null, "frequency": null, "diff_frequency": null, "label": "cz", "gradient_type": "quadratic", "threshold": 1, "guess_step": 0.5, "iteration": 10, "trans_amp_to_freq": true}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "FixedPointCalibration_cz_ql_coupler_biased": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "FixedPointCalibration", "export_datetime": "2023-12-22 17:41:32", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": "q1q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (40, 140, 2.5)", "fringe": 50, "z_amp": null, "frequency": null, "ac_branch": null, "tq_name": "ql", "bq_name_list": ["qc"], "bq_z_amp_list": [0], "bq_freq_list": [0], "auto_set_coupler_zamp": true}, "z_amp": null, "frequency": null, "diff_frequency": null, "label": "cz", "gradient_type": "quadratic", "threshold": 1, "guess_step": 0.5, "iteration": 10, "trans_amp_to_freq": true}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "FixedPointCalibration_cz_qh": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "FixedPointCalibration", "export_datetime": "2023-12-22 17:41:32", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "qh-01", "physical_unit": "q1q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (140, 260, 2.5)", "fringe": 50, "z_amp": null, "frequency": null, "ac_branch": null, "tq_name": "qh", "bq_name_list": [], "bq_z_amp_list": [], "bq_freq_list": [], "auto_set_coupler_zamp": true}, "z_amp": null, "frequency": null, "diff_frequency": null, "label": "cz", "gradient_type": "quadratic", "threshold": 2, "guess_step": 0.5, "iteration": 10, "trans_amp_to_freq": true}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "FixedPointCalibration_cz_ql": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "FixedPointCalibration", "export_datetime": "2023-12-22 17:41:32", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": "q1q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (140, 260, 2.5)", "fringe": 50, "z_amp": null, "frequency": null, "ac_branch": null, "tq_name": "ql", "bq_name_list": [], "bq_z_amp_list": [], "bq_freq_list": [], "auto_set_coupler_zamp": true}, "z_amp": null, "frequency": null, "diff_frequency": null, "label": "cz", "gradient_type": "quadratic", "threshold": 2, "guess_step": 0.5, "iteration": 10, "trans_amp_to_freq": true}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "FixedSwapFreqCaliCoupler_cz": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "FixedSwapFreqCaliCoupler", "export_datetime": "2023-12-21 09:15:43", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"swap_state": "11", "scan_buffer": false, "time_list": "Points(37) | qarange | (20.0, 200.0, 5.0)", "label": "zz"}, "z_amp": null, "frequency": null, "diff_frequency": null, "label": "cz", "gradient_type": "quadratic", "threshold": 1, "guess_step": 10, "iteration": 10, "cali_name": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.75, 0.7], "interaction_location": 1, "data_key": "Points(0) | normal | None", "fit_model_name": "swap_once_cosine"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "FixedSwapFreqCaliCoupler2_cz": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "FixedSwapFreqCaliCoupler2", "export_datetime": "2023-12-21 09:15:43", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"swap_state": "11", "scan_buffer": false, "time_list": "Points(37) | qarange | (100.0, 300.0, 5.0)", "label": "cz"}, "z_amp": null, "frequency": 25, "diff_frequency": null, "label": "cz", "gradient_type": "quadratic", "threshold": 5, "guess_step": 2, "iteration": 10, "cali_name": null, "scope": {"l": 0.1, "r": 0.1, "p": 11}}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.85, 0.75], "interaction_location": 1, "data_key": "Points(0) | normal | None", "fit_model_name": "swap_once_cosine"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SwapOnce_cz": {"meta": {"username": "zyc", "visage_version": "0.4.6", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "point_test"}, "exp_class_name": "SwapOnce", "export_datetime": "2023-12-20 09:55:02", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"swap_state": "11", "scan_buffer": false, "time_list": "Points(33) | qarange | (20.0, 100.0, 2.5)", "label": "cz", "is_dynamic": 0}, "analysis_options": {"quality_bounds": [0.98, 0.8, 0.7], "interaction_location": 1, "data_key": "Points(0) | normal | None", "fit_model_name": "swap_once_cosine"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "Swap_qc": {"meta": {"username": "zhf_Y4", "visage_version": "0.4.7", "monster_version": "0.5.6", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle_point"}, "exp_class_name": "<PERSON><PERSON><PERSON>", "export_datetime": "2023-12-19 20:11:43", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q17q23"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"swap_state": "11", "scan_buffer": false, "time_list": "Points(25) | qarange | (20.0, 200, 5)", "label": "cz"}, "scan_name": "qc", "z_amp_list": "Points(0) | normal | None", "scope": {"l": 0.1, "r": 0.1, "p": 31}, "auto_check": false, "freq_list": "Points(0) | normal | None", "ac_branch": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.85, 0.8], "interaction_location": 1, "data_key": "Points(0) | normal | None", "fit_model_name": "cosine"}, "quality_bounds": [0.98, 0.95, 0.85], "goal_width": null, "var_limit": 1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RBMultiple": {"meta": {"username": "zyc", "visage_version": "0.4.6", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "RBMultiple", "export_datetime": "2023-12-19 13:42:10", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q45q46"}, "options_for_regular_exec": {"experiment_options": {"depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(10) | qarange | (15, 50, 5)", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 30, "interleaved_gate": null, "gate_split": true, "is_amend": true, "fidelity_correct_type": "ibu", "open_seed": false, "seed": null, "mode": "cpp", "check_matrix": false}, "analysis_options": {"quality_bounds": [0.9, 0.85, 0.77]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "Swap": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "<PERSON><PERSON><PERSON>", "export_datetime": "2023-12-21 18:02:26", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q10"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"swap_state": "11", "scan_buffer": false, "time_list": "Points(25) | qarange | (22.5, 300.0, 2.5)", "label": "cz", "is_dynamic": 0, "fake_pulse": true}, "scan_name": "qh", "z_amp_list": "Points(0) | normal | None", "scope": {"l": 40, "r": 29, "p": 24}, "auto_check": false, "freq_list": "Points(0) | normal | None", "ac_branch": null, "scope_detune": false}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.9, 0.8, 0.75], "interaction_location": 1, "data_key": "Points(0) | normal | None", "fit_model_name": "swap_once_cosine"}, "quality_bounds": [0.9, 0.85, 0.8], "goal_width": null, "var_limit": 10}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "LeakageOnce": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "LeakageOnce", "export_datetime": "2023-12-21 18:02:26", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q1q7"}, "options_for_regular_exec": {"experiment_options": {"swap_state": "11", "scope": {"l": 0.12, "r": 0.12, "p": 61}, "scope_detune": false, "scan_name": "qc"}, "analysis_options": {"fit_model_name": "skewed_lorentzian", "quality_bounds": [0.98, 0.9, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "LeakagePre": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "LeakagePre", "export_datetime": "2023-12-21 18:02:26", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q10"}, "options_for_regular_exec": {"experiment_options": {"iteration": 3, "child_exp_options": {"swap_state": "11", "scope": {"l": 0.08, "r": 0.12, "p": 51}, "scope_detune": false, "scan_name": "qc"}}, "analysis_options": {"child_ana_options": {"fit_model_name": "skewed_lorentzian"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShot_1": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q4,q10"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"quality_bounds": [2, 0.8, 0.7, 0.03], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {}}, "LeakageAmp": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "LeakageAmp", "export_datetime": "2023-12-21 18:02:26", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q33q39"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"swap_state": "11", "scope": {"l": 0.05, "r": 0.03, "p": 61}, "scope_detune": false, "scan_name": "qc", "is_amend": true, "fidelity_correct_type": "ibu"}, "scope_detune": false, "scope": {"l": 35, "r": 35, "p": 51}}, "analysis_options": {"child_ana_options": {"fit_model_name": "skewed_lorentzian"}, "use_qc": false, "leak_threshold": 0.3, "remove_jump": true, "jump_threshold": 0.01}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "LeakageAmp_use_qc": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "LeakageAmp", "export_datetime": "2023-12-21 18:02:26", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q33q39"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"swap_state": "11", "scope": {"l": 0.06, "r": 0.03, "p": 51}, "scope_detune": false, "scan_name": "qc", "is_amend": true, "fidelity_correct_type": "ibu"}, "scope_detune": false, "scan_name": "qh", "scope": {"l": 20, "r": 20, "p": 41}}, "analysis_options": {"child_ana_options": {"fit_model_name": "skewed_lorentzian", "quality_bounds": [0.03, 0.02, 0.01]}, "use_qc": true, "point": 41, "leak_threshold": 0.3}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CPhaseTMSE": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CPhaseTMSE", "export_datetime": "2023-12-21 18:02:26", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q10"}, "options_for_regular_exec": {"experiment_options": {"mode": "SE-TM", "ramsey_bit": "ql", "phase_mode": "control", "leakage_mode": "fit", "k": 5, "is_amend": true, "fidelity_correct_type": "ibu", "scan_name": "qh"}, "analysis_options": {"quality_bounds": [0.95, 0.7, 0.6]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ConditionalPhaseFixed": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "ConditionalPhaseFixed", "export_datetime": "2023-12-21 18:02:26", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q10"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"ramsey_bit": "ql", "phase_list": "Points(22) | qarange | (0.0, 6.3, 0.3)"}, "leakage_mode": "fit"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SQPhaseTMSE": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "SQPhaseTMSE", "export_datetime": "2023-12-21 18:02:26", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q10"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"mode": "SE-TM", "ramsey_bit": "ql", "phase_mode": "single", "k": 5, "is_amend": true, "fidelity_correct_type": "ibu"}, "gate_nums": "Points(5) | qarange | (1, 20, 1)"}, "analysis_options": {"std_limit": 2}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XEBMultiple_1": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "XEBMultiple", "export_datetime": "2023-12-27 15:50:05", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q10"}, "options_for_regular_exec": {"experiment_options": {"depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(10) | qarange | (15, 60, 5)", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 30, "goal_gate": "CZ", "open_seed": false, "seed": null, "is_amend": true, "fidelity_correct_type": "ibu"}, "analysis_options": {"fidelity_threshold": 0.9, "quality_bounds": [0.95, 0.9, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XEBMultiple_2": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "XEBMultiple", "export_datetime": "2023-12-27 15:50:05", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q10"}, "options_for_regular_exec": {"experiment_options": {"depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(10) | qarange | (15, 60, 5)", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 30, "goal_gate": "CZ", "open_seed": false, "seed": null, "is_amend": true, "fidelity_correct_type": "ibu"}, "analysis_options": {"fidelity_threshold": 0.94, "quality_bounds": [0.97, 0.92, 0.9]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RBInterleavedMultiple": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "RBInterleavedMultiple", "export_datetime": "2023-12-27 15:50:05", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q10"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(10) | qarange | (15, 60, 5)", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 30, "interleaved_gate": null, "gate_split": false, "open_seed": false, "seed": null, "mode": "cpp", "check_matrix": false, "is_amend": true, "fidelity_correct_type": "ibu"}, "interleaved_gate": "CZ"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.9, 0.85, 0.77]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "NMRBMulitple": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "NMRBMultiple", "export_datetime": "2023-12-28 19:07:29", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q26q32"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"is_amend": true, "fidelity_correct_type": "ibu", "depth1": "Points(1) | qarange | (12, 12, 2)", "depth2": "Points(0) | normal | None", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 30, "interleaved_gate": "CZ", "gate_split": true, "open_seed": false, "seed": null, "mode": "dynamic"}, "input_data": {"detune": {"is_opt": true, "init_v": 0.2, "iter_step": 0.01, "nonzdelt": -1, "bound": null}, "ql_amp": {"is_opt": false, "init_v": null, "iter_step": 1e-05, "nonzdelt": 0.01, "bound": [-0.5, 0.5]}, "qh_amp": {"is_opt": false, "init_v": null, "iter_step": 1e-05, "nonzdelt": 0.01, "bound": [-0.5, 0.5]}, "ql_freq": {"is_opt": false, "init_v": null, "iter_step": 0.01, "nonzdelt": 0.001, "bound": [4000, 8000]}, "qh_freq": {"is_opt": false, "init_v": null, "iter_step": 0.01, "nonzdelt": 0.001, "bound": [4000, 8000]}, "qc_amp": {"is_opt": true, "init_v": null, "iter_step": 1e-05, "nonzdelt": 0.002, "bound": null}, "qh_phase": {"is_opt": true, "init_v": null, "iter_step": 0.05, "nonzdelt": 0.08, "bound": null}, "ql_phase": {"is_opt": true, "init_v": null, "iter_step": 0.05, "nonzdelt": 0.08, "bound": null}}, "nm_params": {"ftarget": -0.95, "maxiter": 40, "maxfev": 60, "disp": true, "return_all": true, "initial_simplex": null, "xatol": 1e-05, "fatol": 0.0001, "adaptive": false}}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.9, 0.85, 0.77]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "NMXEBMultiple": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "NMXEBMultiple", "export_datetime": "2023-12-28 19:07:29", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q26q32"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"is_amend": true, "fidelity_correct_type": "ibu", "depth1": "Points(1) | qarange | (10, 10, 4)", "depth2": "Points(0) | normal | None", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 30, "goal_gate": "CZ", "open_seed": false, "seed": null}, "input_data": {"detune": {"is_opt": true, "init_v": 0.2, "iter_step": 0.01, "nonzdelt": -1, "bound": null}, "ql_amp": {"is_opt": false, "init_v": null, "iter_step": 1e-05, "nonzdelt": 0.01, "bound": [-0.5, 0.5]}, "qh_amp": {"is_opt": false, "init_v": null, "iter_step": 1e-05, "nonzdelt": 0.01, "bound": [-0.5, 0.5]}, "ql_freq": {"is_opt": false, "init_v": null, "iter_step": 0.01, "nonzdelt": 0.001, "bound": [4000, 8000]}, "qh_freq": {"is_opt": false, "init_v": null, "iter_step": 0.01, "nonzdelt": 0.001, "bound": [4000, 8000]}, "qc_amp": {"is_opt": true, "init_v": null, "iter_step": 1e-05, "nonzdelt": 0.002, "bound": null}, "qh_phase": {"is_opt": true, "init_v": null, "iter_step": 0.05, "nonzdelt": 0.08, "bound": null}, "ql_phase": {"is_opt": true, "init_v": null, "iter_step": 0.05, "nonzdelt": 0.08, "bound": null}}, "nm_params": {"ftarget": -0.95, "maxiter": 100, "maxfev": 70, "disp": true, "return_all": true, "initial_simplex": null, "xatol": 1e-05, "fatol": 0.0001, "adaptive": false}, "mode": "NM"}, "analysis_options": {"child_ana_options": {}}}, "options_for_parallel_exec": {}}, "NMXEBPhaseOpt": {"meta": {"username": "dpY4", "visage_version": "0.4.11", "monster_version": "0.5.11", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "NMXEBPhaseOpt", "export_datetime": "2024-03-07 07:23:41", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q26q32"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "depth1": "Points(1) | qarange | (12, 12, 2)", "depth2": "Points(0) | normal | None", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 30, "goal_gate": "CZ", "open_seed": false, "seed": null}, "input_data": {"qc_amp": {"is_opt": true, "init_v": null, "iter_step": 1e-05, "bound": [-0.5, 0.5], "nonzdelt": 0.002}, "qh_phase": {"is_opt": false, "init_v": null, "iter_step": 1e-05, "bound": [-6.1415, 6.1416], "nonzdelt": 2e-05}, "ql_phase": {"is_opt": false, "init_v": null, "iter_step": 1e-05, "bound": [-6.1415, 6.1416], "nonzdelt": 2e-05}, "detune": {"is_opt": true, "init_v": 0.2, "iter_step": 0.01, "bound": [-3, 3], "nonzdelt": -1}}, "nm_params": {"ftarget": -0.9, "maxiter": 50, "maxfev": 70, "disp": true, "return_all": true, "initial_simplex": null, "xatol": 1e-05, "fatol": 0.001, "adaptive": false}}, "analysis_options": {"child_ana_options": {"fidelity_threshold": 0.95, "input_data": {"phase_qh": {"is_opt": true, "init_v": null, "iter_step": 0.01, "bound": [0, 6.3], "nonzdelt": 0.05}, "phase_ql": {"is_opt": true, "init_v": null, "iter_step": 0.01, "bound": [0, 6.3], "nonzdelt": 0.05}}, "nm_params": {"ftarget": -0.9, "maxiter": 50, "maxfev": 100, "disp": true, "return_all": true, "initial_simplex": null, "xatol": 1e-07, "fatol": 1e-07, "adaptive": false}, "de_opts": {"NIND": 8, "MAXGEN": 15, "mutF": 0.001, "XOVR": 0.7}, "opt_phase": true, "opt_method": "NM"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleQubitPhase": {"meta": {"username": "BY170003", "visage_version": "0.22.2", "monster_version": "0.22.2", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "2q_gate"}, "exp_class_name": "SingleQubitPhase", "export_datetime": "2025-01-25 17:58:59", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q10"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"period": 200, "acq_index": "Points(0) | normal | None", "cz_num": 1, "add_cz": true, "control_gate": "I", "phase_list": "Points(22) | qarange | (0.0, 6.3, 0.3)", "ramsey_bit": null}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "gate_nums": "Points(8) | qarange | (1, 8, 1)"}, "analysis_options": {"pure_exp_mode": false, "child_ana_options": {"pure_exp_mode": false, "quality_bounds": [0.98, 0.95, 0.85]}, "std_limit": 0.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}