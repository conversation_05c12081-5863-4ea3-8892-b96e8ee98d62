{"XYZTimingComposite": {"meta": {"username": "dpY2", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "batch_test"}, "exp_class_name": "XYZTimingComposite", "export_datetime": "2024-01-15 16:45:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"is_dynamic": 0, "time_perform": true, "delays": "Points(161) | qarange | (0.0, 100, 1.25)", "const_delay": 80, "z_pulse_params": {"time": 15, "amp": 0.1, "sigma": 0.1, "buffer": 5}}, "max_count": 2}, "analysis_options": {"child_ana_options": {"extract_mode": "fit_params", "quality_bounds": [0.95, 0.9, 0.75], "fit_model_name": "gauss_lo<PERSON>zian"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShot": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q4,q10"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01", "is_dynamic": 0}, "analysis_options": {"quality_bounds": [2, 0.6, 0.4, 0.05], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {}}, "SingleShot_1": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q4,q10"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01", "is_dynamic": 0}, "analysis_options": {"quality_bounds": [2, 0.6, 0.4, 0.05], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {}}, "QubitFreqCalibration": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (20, 120, 2.5)", "fringe": 250, "z_amp": null, "frequency": null, "ac_branch": null, "is_dynamic": 0}, "fringes": "Points(2) | qarange | (50, -50, -100)", "delays": "Points(71) | qarange | (20, 120, 2.5)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.75, 0.7], "factor": 3.5}, "subplots": [2, 2], "freq_gap_threshold": 0.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "XpiDetection", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null, "is_dynamic": 0}, "expect_value": 0.5, "scope": 0.2, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityFreqSpectrum": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CavityFreqSpectrum", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q5"}, "options_for_regular_exec": {"experiment_options": {"fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 2, "z_amp": null, "mode": "IF", "is_dynamic": 0}, "analysis_options": {"quality_bounds": [0.99, 0.95, 0.9]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}