{"AmpComposite_f12": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion_group3"}, "exp_class_name": "AmpComposite", "export_datetime": "2024-01-10 16:19:14", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "child_exp_options": {"time_perform": true, "theta_type": "Xpi", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 7, "threshold_left": 0.8, "threshold_right": 1.1, "f12_opt": false, "is_dynamic": 0}, "n_list": "Points(3) | normal | [7, 9, 13]", "theta_type": "Xpi", "f12_opt": true}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "diff_threshold": 0.02}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "DetuneCalibration_f12": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion_group3"}, "exp_class_name": "DetuneCalibration", "export_datetime": "2024-01-10 16:17:07", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "child_exp_options": {"time_perform": false, "child_exp_options": {"time_perform": false, "sweep_name": "detune", "theta_type": "Xpi", "sweep_list": "Points(36) | qarange | (-25, 10, 1)", "phi_num": 1, "N": 9, "is_dynamic": 0}, "detune_list": "Points(36) | qarange | (-25, 10, 1)", "n_list": "Points(3) | normal | [7, 9, 13]", "theta_type": "Xpi", "scan_type": "rough"}, "detune_list": "Points(81) | qarange | (-20, 20, 0.5)", "rough_n_list": "Points(3) | qarange | (7, 13, 3)", "fine_n_list": "Points(3) | qarange | (9, 13, 2)", "theta_type": "Xpi", "fine_precision": 0.1, "f12_opt": true}, "analysis_options": {"child_ana_options": {"child_ana_options": {"quality_bounds": [0.95, 0.9, 0.8], "filter": {"window_length": 5, "polyorder": 3}, "fine": false, "prominence_divisor": 4.0}, "diff_threshold": 0.2}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "F12Calibration_0": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion_group3"}, "exp_class_name": "F12Calibration", "export_datetime": "2024-01-10 16:13:56", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "child_exp_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right", "is_dynamic": 0}, "fringes": "Points(2) | normal | [25, -25]", "delays": "Points(49) | qarange | (20, 140, 2.5)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}, "subplots": [2, 2], "freq_gap_threshold": 0.6}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitSpectrumF12_0": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion_group3"}, "exp_class_name": "QubitSpectrumF12", "export_datetime": "2024-01-10 16:09:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "f12_list": "Points(0) | normal | None", "f12_xpi": 0.08, "f12_width": 500, "scope": {"l": 270, "r": -180, "s": 1}, "is_dynamic": 0}, "analysis_options": {"quality_bounds": [0.8, 0.6, 0.5], "window_length": 11, "freq_distance": 80}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanAmpF12_0": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion_group3"}, "exp_class_name": "RabiScanAmpF12", "export_datetime": "2024-01-10 16:12:00", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 0.8, 0.01)", "drive_power": null, "f12_time": 20, "is_dynamic": 0}, "analysis_options": {"quality_bounds": [0.98, 0.7, 0.6]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ReadoutAmpCalibration_02": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion_group3"}, "exp_class_name": "ReadoutAmpCalibration", "export_datetime": "2024-01-10 16:21:45", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q7,q8,q9,q10,q11,q12,q13,q14,q15,q16,q17,q18,q25,q26,q27,q28,q29,q30,q31,q32,q33,q34,q35,q36,q43,q45,q47,q49,q51,q53,q61,q62,q63,q66,q67,q68,q69,q72"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "level_str": "02"}, "sweep_list": "Points(11) | qarange | (0.05, 0.15, 0.01)", "left_rate": null, "right_rate": null, "point": 30}, "analysis_options": {"child_ana_options": {"quality_bounds": [2, 0.85, 0.7, 0.011], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ReadoutFreqSSCalibrate_02": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle_point"}, "exp_class_name": "ReadoutFreqSSCalibrate", "export_datetime": "2024-01-18 14:15:24", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q62"}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "child_exp_options": {"time_perform": false, "level_str": "02"}, "sweep_list": "Points(0) | normal | None", "scope": 1.5, "point": 31}, "analysis_options": {"child_ana_options": {"quality_bounds": [2, 0.85, 0.7, 0.011], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SampleWidthOptimize_02": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion_group3"}, "exp_class_name": "SampleWidthOptimize", "export_datetime": "2024-01-10 16:24:07", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q7,q8,q9,q10,q11,q12,q13,q14,q15,q16,q17,q18,q25,q26,q27,q28,q29,q30,q31,q32,q33,q34,q35,q36,q43,q45,q47,q49,q51,q53,q61,q62,q63,q66,q67,q68,q69,q72"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "level_str": "02"}, "sweep_list": "Points(10) | qarange | (600, 1500, 100)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [2, 0.85, 0.7, 0.011], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShot_01": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion_group3"}, "exp_class_name": "SingleShot", "export_datetime": "2024-01-10 16:05:57", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "level_str": "01"}, "analysis_options": {"quality_bounds": [2, 0.85, 0.7, 0.011], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShot_02": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion_group3"}, "exp_class_name": "SingleShot", "export_datetime": "2024-01-10 16:05:57", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "level_str": "02"}, "analysis_options": {"quality_bounds": [2, 0.7, 0.6, 0.05], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ReadoutFreqCalibrate_02": {"meta": {"username": "zyc", "visage_version": "0.4.9", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "ReadoutFreqCalibrate", "export_datetime": "2024-01-31 16:36:31", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "child_exp_options": {"time_perform": false, "fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "extend_f12": false, "scope": 3, "z_amp": null, "mode": "IF"}, "fc_list": "Points(0) | normal | None", "readout_power": null, "readout_type": "02"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "save_mode": "max_distance_point", "diff_threshold": 0.1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}