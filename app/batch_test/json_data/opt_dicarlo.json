{"SingleShot_0": {"meta": {"username": "zzaY2", "visage_version": "0.4.10", "monster_version": "********", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "IIR200"}, "exp_class_name": "SingleShot", "export_datetime": "2024-02-22 16:35:17", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q18"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "level_str": "01"}, "analysis_options": {"quality_bounds": [3, 0.75, 0.6, 0.05], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection": {"meta": {"username": "zzaY2", "visage_version": "0.4.10", "monster_version": "********", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "IIR200"}, "exp_class_name": "XpiDetection", "export_datetime": "2024-02-22 16:51:53", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q18"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "expect_value": 0.7, "scope": 0.1, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.97, 0.92, 0.88]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitFreqCalibration": {"meta": {"username": "zzaY2", "visage_version": "0.4.10", "monster_version": "********", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "IIR200"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2024-02-22 16:16:09", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q18"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "child_exp_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "schedule_flag": true, "schedule_save": true, "schedule_measure": true, "schedule_type": "envelop", "schedule_show_measure": 150, "schedule_show_real": false, "schedule_index": "Points(1) | normal | [-1]", "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "schedule_show_bits": "Points(0) | normal | None", "schedule_show_params": "Points(1) | normal | ['width']", "bind_baseband_freq": true, "schedule_hide_ac": true, "fidelity_correct_type": "least-sq", "post_select_type": null, "is_amend": false, "plot_iq": false, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "fringes": "Points(2) | qarange | (25, -25, -50)", "delays": "Points(81) | qarange | (50, 250, 2.5)"}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "child_ana_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}, "subplots": [2, 2], "freq_gap_threshold": 0.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "VoltageDriftGradientCalibration": {"meta": {"username": "zzaY2", "visage_version": "0.4.10", "monster_version": "********", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "IIR200"}, "exp_class_name": "VoltageDriftGradientCalibration", "export_datetime": "2024-02-23 15:14:04", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q64"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "iteration": 7, "threshold": 0.05, "guess_step": 0.02, "cali_point": "idle_point"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}}}, "options_for_parallel_exec": {"experiment_options": {"time_perform": {"q53": true, "q59": true}, "child_exp_options": {"time_perform": {"q53": true, "q59": true}, "delays": {"q53": "Points(41) | qarange | (0, 200, 5)", "q59": "Points(41) | qarange | (0, 200, 5)"}, "fringe": {"q53": 25, "q59": 25}, "z_amp": {"q53": null, "q59": null}, "frequency": {"q53": null, "q59": null}, "ac_branch": {"q53": "right", "q59": "right"}}, "iteration": {"q53": 5, "q59": 5}, "threshold": {"q53": 0.01, "q59": 0.01}, "guess_step": {"q53": 0.02, "q59": 0.02}, "cali_point": {"q53": "idle_point", "q59": "idle_point"}}, "analysis_options": {"child_ana_options": {"quality_bounds": {"q53": [0.98, 0.93, 0.81], "q59": [0.98, 0.93, 0.81]}, "factor": {"q53": 3.5, "q59": 3.5}, "fit_type": {"q53": "osc", "q59": "osc"}}}}}, "AmpOptimize_0": {"meta": {"username": "zzaY2", "visage_version": "0.4.10", "monster_version": "********", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "IIR200"}, "exp_class_name": "AmpOptimize", "export_datetime": "2024-02-22 16:33:44", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q18"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "theta_type": "Xpi", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 7, "threshold_left": 0.9, "threshold_right": 1.1, "f12_opt": false}, "analysis_options": {"quality_bounds": [0.98, 0.92, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "DetuneCalibration": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "DetuneCalibration", "export_datetime": "2023-12-30 12:54:48", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"detune_list": "Points(36) | qarange | (-35, 25, 1)", "rough_n_list": "Points(3) | qarange | (6, 8, 1)", "fine_n_list": "Points(2) | qarange | (11, 13, 2)", "theta_type": "Xpi", "fine_precision": 0.15, "f12_opt": false}, "analysis_options": {"child_ana_options": {"child_ana_options": {"quality_bounds": [0.95, 0.9, 0.8], "filter": {"window_length": 5, "polyorder": 3}, "fine": false, "prominence_divisor": 4.0}, "diff_threshold": 0.25}}}, "options_for_parallel_exec": {}}, "AmpOptimize_1": {"meta": {"username": "zzaY2", "visage_version": "0.4.10", "monster_version": "********", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "IIR200"}, "exp_class_name": "AmpOptimize", "export_datetime": "2024-02-22 16:33:44", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q18"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "theta_type": "Xpi/2", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 8, "threshold_left": 0.9, "threshold_right": 1.1, "f12_opt": false}, "analysis_options": {"quality_bounds": [0.98, 0.92, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "AmpComposite_1": {"meta": {"username": "zzaY2", "visage_version": "0.4.10", "monster_version": "********", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "IIR200"}, "exp_class_name": "AmpComposite", "export_datetime": "2024-02-22 16:36:13", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q18"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "theta_type": "Xpi", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 7, "threshold_left": 0.9, "threshold_right": 1.1, "f12_opt": false}, "n_list": "Points(3) | normal | [7, 9, 13]", "theta_type": "Xpi", "f12_opt": false}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.92, 0.85]}, "diff_threshold": 0.2}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "AmpComposite_2": {"meta": {"username": "zzaY2", "visage_version": "0.4.10", "monster_version": "********", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "IIR200"}, "exp_class_name": "AmpComposite", "export_datetime": "2024-02-22 16:36:13", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q18"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "theta_type": "Xpi/2", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 7, "threshold_left": 0.9, "threshold_right": 1.1, "f12_opt": false}, "n_list": "Points(3) | normal | [8, 10, 12]", "theta_type": "Xpi/2", "f12_opt": false}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.92, 0.85]}, "diff_threshold": 0.2}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShot_1": {"meta": {"username": "zzaY2", "visage_version": "0.4.10", "monster_version": "********", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "IIR200"}, "exp_class_name": "SingleShot", "export_datetime": "2024-02-22 16:35:17", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q18"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "level_str": "01"}, "analysis_options": {"quality_bounds": [3, 0.75, 0.6, 0.05], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ACSpectrum": {"meta": {"username": "zzay4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweet_point"}, "exp_class_name": "ACSpectrum", "export_datetime": "2024-01-03 16:36:11", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q18,q55,q69"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_remote_path": null, "record_text": true, "save_context": false, "child_exp_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "schedule_flag": true, "schedule_save": true, "schedule_measure": true, "schedule_type": "envelop", "schedule_show_measure": 150, "schedule_show_real": false, "schedule_index": "Points(1) | normal | [-1]", "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "schedule_show_bits": "Points(0) | normal | None", "schedule_show_params": "Points(1) | normal | ['width']", "bind_baseband_freq": true, "schedule_hide_ac": true, "fidelity_correct_type": "least-sq", "post_select_type": null, "is_amend": false, "plot_iq": false, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null}, "z_amps": "Points(22) | qarange | (-0.02, 0.4, 0.02)", "delays": "Points(61) | qarange | (70, 120, 0.625)", "freq_bound": 800, "osc_freq_limit": 400, "init_fringe": 10, "spectrum_type": "standard", "use_init_fringe": true}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "child_ana_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}, "quality_bounds": [0.92, 0.9, 0.88], "fit_model_name": "amp2freq_formula"}}, "options_for_parallel_exec": {"experiment_options": {"z_amps": {}, "init_fringe": {}}, "analysis_options": {}}}, "OptimizeFirDicarlo": {"meta": {"username": "zzaY2", "visage_version": "0.4.10", "monster_version": "********", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "IIR200"}, "exp_class_name": "OptimizeFirDicarlo", "export_datetime": "2024-02-21 16:12:30", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q18,q34,q66"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "separation": 200, "width_list": "Points(0) | normal | None", "z_amp": 0.13, "phi": 0, "fringe": 25, "sample_step": 2.5, "xy_width": null, "separa_num": 2, "fake_pulse": false}, "fringe": 50, "iter": 5, "separation": 200, "width_list": "Points(0) | normal | None", "z_amp": 0.13, "repeat_times": 1, "separa_num": 2, "ignore_history": true, "fir_length": 30, "branch": "right", "fname": null, "ac_data": null}, "analysis_options": {"child_ana_options": {}, "ac_spectrum_params": "Points(0) | normal | None", "z_amp": null, "drive_freq": null, "cali_offset": true, "freq_switch": false, "savgol_win_length": 21, "smooth_win_length": 11}}, "options_for_parallel_exec": {"experiment_options": {"same_options": false, "z_amp": {}, "branch": {}, "fname": {}, "ac_data": {}, "fir_length": {}, "separation": {}}, "analysis_options": {}}}, "OptimizeFirDicarlo1": {"meta": {"username": "zzaY2", "visage_version": "0.4.10", "monster_version": "********", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "IIR200"}, "exp_class_name": "OptimizeFirDicarlo", "export_datetime": "2024-02-21 16:12:30", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q18,q34,q66"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "separation": 240, "width_list": "Points(0) | normal | None", "z_amp": 0.13, "phi": 0, "fringe": 25, "sample_step": 2.5, "xy_width": null, "separa_num": 2, "fake_pulse": false}, "fringe": 50, "iter": 1, "separation": 240, "width_list": "Points(0) | normal | None", "z_amp": 0.13, "repeat_times": 1, "separa_num": 2, "ignore_history": true, "fir_length": 30, "branch": "right", "fname": null, "ac_data": null}, "analysis_options": {"child_ana_options": {}, "ac_spectrum_params": "Points(0) | normal | None", "z_amp": null, "drive_freq": null, "cali_offset": true, "freq_switch": false, "savgol_win_length": 21, "smooth_win_length": 11}}, "options_for_parallel_exec": {"experiment_options": {"z_amp": {}, "branch": {}, "fname": {}, "ac_data": {}}, "analysis_options": {}}}}