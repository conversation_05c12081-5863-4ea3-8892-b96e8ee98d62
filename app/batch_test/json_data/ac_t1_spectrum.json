{"ACSpectrum": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "ACSpectrum", "export_datetime": "2024-01-16 09:44:24", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "child_exp_options": {"time_perform": false, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "z_amps": "Points(22) | qarange | (-0.02, 0.4, 0.02)", "delays": "Points(61) | qarange | (200, 800, 10)", "freq_bound": 800, "osc_freq_limit": 2.5, "init_fringe": 10, "spectrum_type": "standard"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}, "quality_bounds": [0.995, 0.992, 0.99], "fit_model_name": "amp2freq_formula"}}, "options_for_parallel_exec": {}}, "SweetPointCalibration": {"meta": {"username": "zyc", "visage_version": "********", "monster_version": "********", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "SweetPointCalibration", "export_datetime": "2024-02-26 17:47:12", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "iteration": 5, "threshold": 0.01, "guess_step": 0.02, "cali_point": "idle_point", "z_amp": null, "frequency": null, "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SweetPointCalibrationVMin": {"meta": {"username": "zyc", "visage_version": "********", "monster_version": "********", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "SweetPointCalibrationVMin", "export_datetime": "2024-02-26 17:47:12", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "iteration": 5, "threshold": 0.01, "guess_step": 0.02, "cali_point": "sweet_point", "z_amp": null, "frequency": null, "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}