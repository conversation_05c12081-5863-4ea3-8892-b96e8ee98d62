{"ZZShiftSweetPointCalibration": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "ZZShiftSweetPointCalibration", "export_datetime": "2023-12-22 17:41:32", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "probeQ", "physical_unit": "c1-2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (20, 120, 2.5)", "fringe": 50, "z_amp": null, "frequency": null, "ac_branch": null, "is_dynamic": 0}, "z_amp": 0.15, "frequency": null, "threshold": 0.1, "guess_step": 0.2, "iteration": 10}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SweetPointCalibration": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "SweetPointCalibration", "export_datetime": "2023-12-22 17:41:32", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (20, 120, 2.5)", "fringe": 50, "z_amp": null, "frequency": null, "ac_branch": null, "is_dynamic": 0}, "z_amp": 0.05, "frequency": null, "threshold": 0.1, "guess_step": 0.2, "iteration": 10}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "VoltageDriftGradientCalibration": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "VoltageDriftGradientCalibration", "export_datetime": "2023-12-22 17:41:32", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (20, 120, 2.5)", "fringe": 50, "z_amp": null, "frequency": null, "ac_branch": null, "is_dynamic": 0}, "threshold": 0.05, "guess_step": 0.2, "iteration": 10}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShot_1": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q4,q10"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"quality_bounds": [2, 0.8, 0.7, 0.03], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {}}, "ZZShiftSweetPointCalibrationNew": {"meta": {"username": "dpY4", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "ZZShiftSweetPointCalibrationNew", "export_datetime": "2024-01-10 20:47:12", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c1-2"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(41) | qarange | (20, 120, 2.5)", "fringe": 50, "z_amp": null, "frequency": null, "ac_branch": "right", "is_dynamic": 0}, "iteration": 10, "threshold": 0.1, "guess_step": 0.5, "mode": "awg_bias"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.9, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "VoltageDriftGradientCalibration_sw": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "VoltageDriftGradientCalibration", "export_datetime": "2023-12-22 17:41:32", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (20, 120, 2.5)", "fringe": 50, "z_amp": null, "frequency": null, "ac_branch": null, "is_dynamic": 0}, "threshold": 0.05, "guess_step": 0.2, "iteration": 10, "cali_point": "sweet_point"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}