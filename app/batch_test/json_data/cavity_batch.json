{"CavityFreqSpectrum": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CavityFreqSpectrum", "export_datetime": "2024-01-24 11:13:04", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 3, "z_amp": null, "mode": "IF"}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityPowerScan": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "CavityPowerScan", "export_datetime": "2023-11-29 19:58:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"fc_list": "Points(0) | normal | None", "points": 121, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 6, "z_amp": null, "mode": "IF"}, "power_list": "Points(31) | qarange | (-40, -10, 1)", "fc_list": "Points(0) | normal | None"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "quality_bounds": [0.98, 0.95, 0.85], "window_size": 5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityTunable_for_qubit": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CavityTunable", "export_datetime": "2024-01-24 11:13:04", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 3, "z_amp": null, "mode": "IF"}, "fc_list": "Points(0) | normal | None", "readout_power": null, "flux_list": "Points(0) | qarange | (-0.48, 0.48, 0.06)", "scan_name": "ac_bias"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "diff_threshold": 0.1, "tackle_type": "<PERSON><PERSON><PERSON>", "quality_bounds": [0.98, 0.95, 0.9]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityTunable_for_coupler": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CavityTunable", "export_datetime": "2024-01-24 11:13:04", "description": null}, "context_options": {"name": "coupler_calibration", "physical_unit": "c1-2", "readout_type": "probeQ"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 3, "z_amp": null, "mode": "IF"}, "fc_list": "Points(0) | normal | None", "readout_power": null, "flux_list": "Points(0) | qarange | (-0.48, 0.48, 0.06)", "scan_name": "ac_bias"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "diff_threshold": 0.1, "tackle_type": "<PERSON><PERSON><PERSON>", "quality_bounds": [0.98, 0.95, 0.9]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}