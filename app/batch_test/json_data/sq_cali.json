{"SingleShot_0": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q4,q10"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"quality_bounds": [2, 0.8, 0.7, 0.03], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {}}, "AmpOptimize_0": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "AmpOptimize", "export_datetime": "2023-12-30 12:54:39", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"theta_type": "Xpi", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 3, "threshold_left": 0.5, "threshold_right": 1.2, "f12_opt": false}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.85]}}, "options_for_parallel_exec": {}}, "DetuneCalibration": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "DetuneCalibration", "export_datetime": "2023-12-30 12:54:48", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"detune_list": "Points(36) | qarange | (-35, 25, 1)", "rough_n_list": "Points(3) | qarange | (6, 8, 1)", "fine_n_list": "Points(2) | qarange | (11, 13, 2)", "theta_type": "Xpi", "fine_precision": 0.15, "f12_opt": false}, "analysis_options": {"child_ana_options": {"child_ana_options": {"quality_bounds": [0.95, 0.9, 0.8], "filter": {"window_length": 5, "polyorder": 3}, "fine": false, "prominence_divisor": 4.0}, "diff_threshold": 0.25}}}, "options_for_parallel_exec": {}}, "AmpOptimize_1": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "AmpOptimize", "export_datetime": "2023-12-30 12:54:39", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"theta_type": "Xpi", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 3, "threshold_left": 0.7, "threshold_right": 1.1, "f12_opt": false}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.85]}}, "options_for_parallel_exec": {}}, "AmpComposite_1": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "AmpComposite", "export_datetime": "2023-12-30 12:55:01", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"theta_type": "Xpi", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 7, "threshold_left": 0.95, "threshold_right": 1.05, "f12_opt": false}, "n_list": "Points(3) | normal | [10, 19, 30]", "theta_type": "Xpi", "f12_opt": false}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "diff_threshold": 0.05}}, "options_for_parallel_exec": {}}, "AmpComposite_2": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "AmpComposite", "export_datetime": "2023-12-30 12:55:01", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"theta_type": "Xpi/2", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 7, "threshold_left": 0.95, "threshold_right": 1.05, "f12_opt": false}, "n_list": "Points(3) | normal | [20, 30, 40]", "theta_type": "Xpi/2", "f12_opt": false}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "diff_threshold": 0.05}}, "options_for_parallel_exec": {}}, "SingleShot_1": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q4,q10"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"quality_bounds": [2, 0.8, 0.7, 0.03], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {}}, "RBSingle": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "RBSingle", "export_datetime": "2023-12-30 12:56:08", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"times": 30, "depth1": "Points(3) | qarange | (2, 10, 4)", "depth2": "Points(9) | qarange | (15, 100, 10)", "depth3": "Points(6) | qarange | (150, 400, 50)", "depth4": "Points(0) | normal | None", "interleaved_gate": null, "gate_split": true, "mode": "cpp", "open_seed": false, "seed": null}, "analysis_options": {"quality_bounds": [0.9, 0.85, 0.77], "std_bound": 0.05}}, "options_for_parallel_exec": {}}}