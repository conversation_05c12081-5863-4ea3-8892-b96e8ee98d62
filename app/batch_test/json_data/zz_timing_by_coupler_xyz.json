{"CavityFreqSpectrum": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CavityFreqSpectrum", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q5"}, "options_for_regular_exec": {"experiment_options": {"fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 2, "z_amp": null, "mode": "IF", "is_dynamic": 0}, "analysis_options": {"quality_bounds": [0.99, 0.95, 0.9]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ReadoutFreqCalibrate": {"meta": {"username": "ZS_MYCH", "visage_version": "0.11.5", "monster_version": "********", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#", "env_name": "D108", "point_label": "zz_label"}, "exp_class_name": "ReadoutFreqCalibrate", "export_datetime": "2024-11-25 10:33:39", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q13"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"period": 100, "fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "extend_f12": false, "scope": 3, "z_amp": null, "mode": "IF"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "fc_list": "Points(0) | normal | None", "readout_power": null, "readout_type": "01"}, "analysis_options": {"pure_exp_mode": false, "child_ana_options": {"pure_exp_mode": false, "quality_bounds": [0.98, 0.95, 0.85]}, "save_mode": "max_distance_point", "diff_threshold": 0.1}}, "options_for_parallel_exec": {"experiment_options": {"child_exp_options": {"period": {"q13": 100, "q14": 100}, "fc_list": {"q13": "Points(0) | normal | None", "q14": "Points(0) | normal | None"}, "points": {"q13": 61, "q14": 61}, "readout_power": {"q13": null, "q14": null}, "pi_amp": {"q13": null, "q14": null}, "add_pi_pulse": {"q13": false, "q14": false}, "extend_f12": {"q13": false, "q14": false}, "scope": {"q13": 3, "q14": 3}, "z_amp": {"q13": null, "q14": null}, "mode": {"q13": "IF", "q14": "IF"}}, "run_mode": {"q13": "async", "q14": "async"}, "async_time_out": {"q13": 10, "q14": 10}, "quality_filter": {"q13": false, "q14": false}, "is_sub_merge": {"q13": true, "q14": true}, "minimize_mode": {"q13": false, "q14": false}, "fc_list": {"q13": "Points(0) | normal | None", "q14": "Points(0) | normal | None"}, "readout_power": {"q13": null, "q14": null}, "readout_type": {"q13": "01", "q14": "01"}}, "analysis_options": {"pure_exp_mode": {"q13": false, "q14": false}, "child_ana_options": {"pure_exp_mode": {"q13": false, "q14": false}, "quality_bounds": {"q13": [0.98, 0.95, 0.85], "q14": [0.98, 0.95, 0.85]}}, "save_mode": {"q13": "max_distance_point", "q14": "max_distance_point"}, "diff_threshold": {"q13": 0.1, "q14": 0.1}}}}, "RabiScanWidth": {"meta": {"username": "why_y4_debug", "visage_version": "0.11.5", "monster_version": "0.11.5", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "20bit1D"}, "exp_class_name": "RabiScanWidth", "export_datetime": "2024-11-20 15:24:24", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"period": 100, "widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false}, "analysis_options": {"pure_exp_mode": false, "quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitFreqCalibration": {"meta": {"username": "ZS_MYCH", "visage_version": "0.11.5", "monster_version": "********", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#", "env_name": "D108", "point_label": "zz_label"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2024-11-25 10:54:25", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q13,q14"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"period": 100, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "fringes": "Points(2) | qarange | (51, -51, -102)", "delays": "Points(41) | qarange | (100, 200, 2.5)"}, "analysis_options": {"pure_exp_mode": false, "child_ana_options": {"pure_exp_mode": false, "quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}, "subplots": [2, 2], "freq_gap_threshold": 1.0}}, "options_for_parallel_exec": {"experiment_options": {"child_exp_options": {"period": {"q13": 100, "q14": 100}, "delays": {"q13": "Points(41) | qarange | (0, 200, 5)", "q14": "Points(41) | qarange | (0, 200, 5)"}, "fringe": {"q13": 25, "q14": 25}, "z_amp": {"q13": null, "q14": null}, "frequency": {"q13": null, "q14": null}, "ac_branch": {"q13": "right", "q14": "right"}}, "run_mode": {"q13": "async", "q14": "async"}, "async_time_out": {"q13": 10, "q14": 10}, "quality_filter": {"q13": false, "q14": false}, "is_sub_merge": {"q13": true, "q14": true}, "minimize_mode": {"q13": false, "q14": false}, "fringes": {"q13": "Points(2) | qarange | (51, -51, -102)", "q14": "Points(2) | qarange | (51, -51, -102)"}, "delays": {"q13": "Points(41) | qarange | (100, 200, 2.5)", "q14": "Points(41) | qarange | (100, 200, 2.5)"}}, "analysis_options": {"pure_exp_mode": {"q13": false, "q14": false}, "child_ana_options": {"pure_exp_mode": {"q13": false, "q14": false}, "quality_bounds": {"q13": [0.98, 0.93, 0.81], "q14": [0.98, 0.93, 0.81]}, "factor": {"q13": 3.5, "q14": 3.5}, "fit_type": {"q13": "osc", "q14": "osc"}}, "subplots": {"q13": [2, 2], "q14": [2, 2]}, "freq_gap_threshold": {"q13": 1.0, "q14": 1.0}}}}, "XpiDetection": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "0.5.6", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "test"}, "exp_class_name": "XpiDetection", "export_datetime": "2023-12-21 16:56:11", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "expect_value": 0.45, "scope": 0.1, "max_loops": 7, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.85, 0.7]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "DetuneCalibration": {"meta": {"username": "ZS_MYCH", "visage_version": "0.11.5", "monster_version": "********", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#", "env_name": "D108", "point_label": "zz_label"}, "exp_class_name": "DetuneCalibration", "export_datetime": "2024-11-25 10:55:39", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q13,q14"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"child_exp_options": {"period": 100, "sweep_name": "detune", "theta_type": "Xpi", "sweep_list": "Points(36) | qarange | (-25, 10, 1)", "phi_num": 1, "N": 9}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "detune_list": "Points(36) | qarange | (-25, 10, 1)", "n_list": "Points(3) | normal | [7, 9, 13]", "theta_type": "Xpi", "scan_type": "rough"}, "run_mode": "sync", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "detune_list": "Points(36) | qarange | (-25, 10, 1)", "rough_n_list": "Points(3) | qarange | (6, 8, 1)", "fine_n_list": "Points(2) | qarange | (7, 9, 2)", "theta_type": "Xpi", "fine_precision": 0.1, "f12_opt": false}, "analysis_options": {"pure_exp_mode": false, "child_ana_options": {"pure_exp_mode": false, "child_ana_options": {"pure_exp_mode": false, "quality_bounds": [0.95, 0.9, 0.8], "filter": {"window_length": 5, "polyorder": 3}, "fine": false, "prominence_divisor": 4.0}, "diff_threshold": 0.2}}}, "options_for_parallel_exec": {"experiment_options": {"child_exp_options": {"child_exp_options": {"period": {"q13": 100, "q14": 100}, "sweep_name": {"q13": "detune", "q14": "detune"}, "theta_type": {"q13": "Xpi", "q14": "Xpi"}, "sweep_list": {"q13": "Points(36) | qarange | (-25, 10, 1)", "q14": "Points(36) | qarange | (-25, 10, 1)"}, "phi_num": {"q13": 1, "q14": 1}, "N": {"q13": 9, "q14": 9}}, "run_mode": {"q13": "async", "q14": "async"}, "async_time_out": {"q13": 10, "q14": 10}, "quality_filter": {"q13": false, "q14": false}, "is_sub_merge": {"q13": true, "q14": true}, "minimize_mode": {"q13": false, "q14": false}, "detune_list": {"q13": "Points(36) | qarange | (-25, 10, 1)", "q14": "Points(36) | qarange | (-25, 10, 1)"}, "n_list": {"q13": "Points(3) | normal | [7, 9, 13]", "q14": "Points(3) | normal | [7, 9, 13]"}, "theta_type": {"q13": "Xpi", "q14": "Xpi"}, "scan_type": {"q13": "rough", "q14": "rough"}}, "run_mode": {"q13": "sync", "q14": "sync"}, "async_time_out": {"q13": 10, "q14": 10}, "quality_filter": {"q13": false, "q14": false}, "is_sub_merge": {"q13": true, "q14": true}, "minimize_mode": {"q13": false, "q14": false}, "detune_list": {"q13": "Points(36) | qarange | (-25, 10, 1)", "q14": "Points(36) | qarange | (-25, 10, 1)"}, "rough_n_list": {"q13": "Points(3) | qarange | (6, 8, 1)", "q14": "Points(3) | qarange | (6, 8, 1)"}, "fine_n_list": {"q13": "Points(2) | qarange | (7, 9, 2)", "q14": "Points(2) | qarange | (7, 9, 2)"}, "theta_type": {"q13": "Xpi", "q14": "Xpi"}, "fine_precision": {"q13": 0.1, "q14": 0.1}, "f12_opt": {"q13": false, "q14": false}}, "analysis_options": {"pure_exp_mode": {"q13": false, "q14": false}, "child_ana_options": {"pure_exp_mode": {"q13": false, "q14": false}, "child_ana_options": {"pure_exp_mode": {"q13": false, "q14": false}, "quality_bounds": {"q13": [0.95, 0.9, 0.8], "q14": [0.95, 0.9, 0.8]}, "filter": {"q13": {"window_length": 5, "polyorder": 3}, "q14": {"window_length": 5, "polyorder": 3}}, "fine": {"q13": false, "q14": false}, "prominence_divisor": {"q13": 4.0, "q14": 4.0}}, "diff_threshold": {"q13": 0.2, "q14": 0.2}}}}}, "SingleShot": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"level_str": "01", "simulator_data_path": "F:\\MayNewCodes\\Apps\\protobuf-apps\\pyqcat-apps\\zak\\iq_q8.dat"}, "analysis_options": {"quality_bounds": [2, 0.8, 0.5, 0.03], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {}}, "FreqShiftByCoupler": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle_point"}, "exp_class_name": "FreqShiftByCoupler", "export_datetime": "2024-01-16 10:26:44", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c4-10"}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "child_exp_options": {"time_perform": false, "delays": "Points(81) | qarange | (50, 100, 0.833)", "fringe": 200, "z_amp": null, "frequency": null, "ac_branch": "left"}, "iteration": 8, "threshold": 1, "guess_step": 1, "mode": "wave", "z_amp": -0.22, "delta_freq": 40}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {"child_exp_options": {"ac_branch": {"c1-2": "left"}}, "z_amp": {"c1-2": 0.2}}, "analysis_options": {}}}, "CouplerXYZTimingByZZShift": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion"}, "exp_class_name": "CouplerXYZTimingByZZShift", "export_datetime": "2024-01-03 20:32:06", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c1-2"}, "options_for_regular_exec": {"experiment_options": {"delays": "Points(161) | qarange | (0.0, 100, 0.625)", "const_delay": 80, "z_pulse_params": {"time": 15, "amp": 0.25, "sigma": 0.01, "buffer": 0.03}, "is_dynamic": 0}, "analysis_options": {"extract_mode": "fit_params", "quality_bounds": [0.9, 0.7, 0.6], "fit_model_name": "gauss_lo<PERSON>zian"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}