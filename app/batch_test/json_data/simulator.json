{"CavityFreqSpectrum": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "CavityFreqSpectrum", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 3, "z_amp": null, "mode": "IF"}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ZFluxCheck": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "ZFluxCheck", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ZFluxCheckComposite": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "ZFluxCheckComposite", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityPowerScan": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "CavityPowerScan", "export_datetime": "2023-11-29 19:58:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"points": 121, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 6, "z_amp": null, "mode": "IF"}, "power_list": "Points(31) | qarange | (-40, -10, 1)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "quality_bounds": [0.98, 0.95, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityTunable_for_coupler": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "CavityTunable", "export_datetime": "2023-11-29 19:58:05", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "driveQ", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"points": 101, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 5, "z_amp": null, "mode": "IF"}, "readout_power": null, "flux_list": "Points(54) | qarange | (-0.4, 0.4, 0.015)", "scan_name": "ac_bias", "run_mode": "async"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "diff_threshold": 0.1, "tackle_type": "<PERSON><PERSON><PERSON>", "quality_bounds": [0.98, 0.95, 0.9]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityTunable_for_qubit": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "CavityTunable", "export_datetime": "2023-11-29 19:58:05", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"points": 101, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 5, "z_amp": null, "mode": "IF", "fake_pulse": true}, "readout_power": null, "flux_list": "Points(54) | qarange | (-0.4, 0.4, 0.015)", "scan_name": "ac_bias", "run_mode": "async"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "diff_threshold": 0.1, "tackle_type": "<PERSON><PERSON><PERSON>", "quality_bounds": [0.98, 0.95, 0.9]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "DistortionDetunePhaseTomo": {"meta": {"username": "Giant", "visage_version": "*******", "monster_version": "0.5.5", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "DistortionDetunePhaseTomo", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"detune_list": [0, 0.625, 1.25, 1.875, 2.5, 3.125, 3.75, 4.375, 5, 5.625, 6.25, 6.875, 7.5, 8.125, 8.75, 9.375, 10, 10.625, 11.875, 13.125, 14.375, 15.625, 16.875, 18.125, 19.375, 20.625, 21.875, 23.125, 24.375, 25.625, 26.875, 28.125, 29.375, 30.625, 31.875, 33.125, 34.375, 35.625, 36.875, 38.125, 39.375, 40.625, 41.875, 43.125, 44.375, 45.625, 46.875, 48.125, 49.375, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 220, 240, 260, 280, 300, 320, 340, 360, 380, 400, 420, 440, 460, 480, 500, 550, 600, 650, 700, 750, 800, 850, 900, 950, 1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000], "drag_gap": 0, "t1": 1000, "t2": 1000, "delay": 100, "base_num": 2, "exp_mode": "half_square", "diy_readout_point": false, "fake_pulse": true, "is_dynamic": false}, "analysis_options": {}}}, "DistortionDetuneSweepPhase": {"meta": {"username": "Giant", "visage_version": "*******", "monster_version": "0.5.5", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "DistortionPhaseDetune2D", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"detune_list": "Points(31) | qarange | (-0.02, 0.02, 0.0005)", "unit": "V", "child_exp_options": {"t1": 1000, "t2": 1000, "delay": 5000, "z_amp": 0, "detune": null, "diy_readout_point": false, "phases": "Points(31) | qarange | (0, 8, 0.261799)", "is_dynamic": false, "fake_pulse": true}, "use_simulator": true}, "analysis_options": {}}}, "DistortionPhaseDetuneScan": {"meta": {"username": "Giant", "visage_version": "*******", "monster_version": "0.5.5", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "DistortionPhaseDetuneScan", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"t1": 1000, "t2": 1000, "delay": 5000, "z_amp": 0, "detune": -0.02, "diy_readout_point": false, "phases": "Points(31) | qarange | (0, 8, 0.261799)", "is_dynamic": false, "fake_pulse": true, "use_simulator": true}, "analysis_options": {}}}, "DistortionSweepPhase": {"meta": {"username": "Giant", "visage_version": "*******", "monster_version": "0.5.5", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "DistortionPhase2D", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"delays": [0, 0.625, 1.25, 1.875, 2.5, 3.125, 3.75, 4.375, 5, 5.625, 6.25, 6.875, 7.5, 8.125, 8.75, 9.375, 10, 10.625, 11.875, 13.125, 14.375, 15.625, 16.875, 18.125, 19.375, 20.625, 21.875, 23.125, 24.375, 25.625, 26.875, 28.125, 29.375, 30.625, 31.875, 33.125, 34.375, 35.625, 36.875, 38.125, 39.375, 40.625, 41.875, 43.125, 44.375, 45.625, 46.875, 48.125, 49.375, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 220, 240, 260, 280, 300, 320, 340, 360, 380, 400, 420, 440, 460, 480, 500, 550, 600, 650, 700, 750, 800, 850, 900, 950, 1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000, 2100, 2200, 2300, 2400, 2500, 2600, 2700, 2800, 2900, 3000, 3100, 3200, 3300, 3400, 3500, 3600, 3700, 3800, 3900, 4000, 4100, 4200, 4300, 4400, 4500, 4600, 4700, 4800, 4900, 5000, 5200, 5400, 5600, 5800, 6000, 6200, 6400, 6600, 6800, 7000, 7200, 7400, 7600, 7800, 8000, 8200, 8400, 8600, 8800, 9000, 9200, 9400, 9600, 9800, 10000, 10200, 10400, 10600, 10800, 11000, 11200, 11400, 11600, 11800, 12000, 12200, 12400, 12600, 12800, 13000, 13200, 13400, 13600, 13800, 14000, 14200, 14400, 14600, 14800, 15000, 15200, 15400, 15600, 15800, 16000, 16200, 16400, 16600, 16800, 17000, 17200, 17400, 17600, 17800, 18000, 18200, 18400, 18600, 18800, 19000, 19200, 19400, 19600, 19800, 20000], "child_exp_options": {"drag_gap": 100, "t1": 1000, "t2": 1000, "delay": 5000, "z_amp": -0.4, "exp_mode": "half_square", "diy_readout_point": false, "phases": "Points(31) | qarange | (0, 8, 0.261799)", "is_dynamic": false, "fake_pulse": true}, "use_simulator": true}, "analysis_options": {}}}, "DistortionPhaseScan": {"meta": {"username": "Giant", "visage_version": "*******", "monster_version": "0.5.5", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "DistortionPhaseScan", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"drag_gap": 100, "t1": 1000, "t2": 1000, "delay": 5000, "z_amp": -0.4, "exp_mode": "half_square", "diy_readout_point": false, "phases": "Points(31) | qarange | (0, 8, 0.261799)", "is_dynamic": false, "fake_pulse": true, "use_simulator": true}, "analysis_options": {}}}, "DistortionX2Y2": {"meta": {"username": "Giant", "visage_version": "*******", "monster_version": "0.5.5", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "DistortionPhaseTomo", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"drag_gap": 100, "t1": 1000, "t2": 1000, "delays": [0, 0.625, 1.25, 1.875, 2.5, 3.125, 3.75, 4.375, 5, 5.625, 6.25, 6.875, 7.5, 8.125, 8.75, 9.375, 10, 10.625, 11.875, 13.125, 14.375, 15.625, 16.875, 18.125, 19.375, 20.625, 21.875, 23.125, 24.375, 25.625, 26.875, 28.125, 29.375, 30.625, 31.875, 33.125, 34.375, 35.625, 36.875, 38.125, 39.375, 40.625, 41.875, 43.125, 44.375, 45.625, 46.875, 48.125, 49.375, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 220, 240, 260, 280, 300, 320, 340, 360, 380, 400, 420, 440, 460, 480, 500, 550, 600, 650, 700, 750, 800, 850, 900, 950, 1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000], "base_num": 4, "exp_mode": "half_square", "diy_readout_point": false, "fake_pulse": true, "is_dynamic": false}, "analysis_options": {}}}, "DistortionT1CompositeNew": {"meta": {"username": "why_y4_debug", "visage_version": "0.4.9", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "DistortionT1CompositeNew", "export_datetime": "2024-02-04 14:29:21", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "z_offset_list": "Points(81) | qarange | (-0.02, 0.02, 0.0005)", "gauss_sigma": 5.0, "gauss_width": 15.0, "const_width": 100, "ta": 2000, "add_tb_width": 100, "z_amp": -0.5, "xy_delay": 0, "run_mode": "new_case", "frequency": null, "ac_branch": "right", "is_dynamic": 0}, "iteration_times": 1, "xy_delay_start": 0.0, "xy_delay_max": 1000, "xy_step_map": {"lt_10": 0.625, "lt_50": 1.25, "lt_100": 5.0, "lt_200": 10.0, "lt_500": 20.0, "lt_1000": 50.0, "lt_5000": 100.0, "lt_10000": 200.0, "gt_10000": 300.0, "gt_20000": 400.0}, "exp_max_num": 200, "width_num": 1, "width_min": 0.02, "width_max": 0.05, "min_scan_points": 31, "max_scan_points": 101, "bit_type": "<PERSON><PERSON><PERSON>", "base_history": false, "update_xy_delay_slu": true, "update_z_offset_range": true, "z_amp": null, "z_offset_list": "Points(81) | qarange | (-0.02, 0.02, 0.0005)", "expect_z_step": null, "z_offset_min": -0.5, "z_offset_max": 0.5, "frequency": null, "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.95, 0.85, 0.75], "fit_model_name": "lorentzian", "cali_offset_method": "direct", "cut_index": true, "adjust_noise": true}, "quality_bounds": [0.9999, 0.999, 0.9], "lfilter_flag": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerDistortionZZCompositeNew": {"meta": {"username": "zyc", "visage_version": "0.4.10", "monster_version": "0.5.10", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CouplerDistortionZZCompositeNew", "export_datetime": "2024-02-04 19:41:09", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "z_offset_list": "Points(81) | qarange | (-0.02, 0.02, 0.0005)", "gauss_sigma": 5.0, "gauss_width": 15.0, "const_width": 100, "ta": 2000, "add_tb_width": 100, "z_amp": -0.5, "xy_delay": 0, "run_mode": "new_case", "frequency": null, "ac_branch": "right", "is_dynamic": 0}, "iteration_times": 1, "xy_delay_start": 0.0, "xy_delay_max": 1000, "xy_step_map": {"lt_10": 0.625, "lt_50": 1.25, "lt_100": 5.0, "lt_200": 10.0, "lt_500": 20.0, "lt_1000": 50.0, "lt_5000": 100.0, "lt_10000": 200.0, "gt_10000": 300.0, "gt_20000": 400.0}, "exp_max_num": 200, "width_num": 1, "width_min": 0.02, "width_max": 0.05, "min_scan_points": 31, "max_scan_points": 101, "bit_type": "<PERSON><PERSON><PERSON>", "base_history": false, "update_xy_delay_slu": true, "update_z_offset_range": true, "z_amp": null, "z_offset_list": "Points(81) | qarange | (-0.02, 0.02, 0.0005)", "expect_z_step": null, "z_offset_min": -0.5, "z_offset_max": 0.5, "frequency": null, "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.95, 0.85, 0.75], "fit_model_name": "lorentzian", "cali_offset_method": "direct", "cut_index": true, "adjust_noise": true}, "quality_bounds": [0.9999, 0.999, 0.9], "lfilter_flag": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitSpectrum": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "QubitSpectrum", "export_datetime": "2023-11-29 19:59:46", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"freq_list": "Points(0) | normal | None", "drive_power": null, "z_amp": null, "use_square": true, "band_width": 50.0, "fine_flag": true, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70.0, "fine_window_length": 11, "fine_freq_distance": 80.0}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 1.0, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0.0, "sigma": 5.0, "fast_m": false}, "scope": {"l": 100, "r": 100, "s": 2}, "mode": "IF"}, "analysis_options": {"quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5, "result_name": "gkkTest"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitSpectrumScanPower": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "QubitSpectrumScanPower", "export_datetime": "2025-6-12 19:58:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"freq_list": "Points(0) | normal | None", "drive_power": null, "z_amp": null, "use_square": true, "band_width": 50.0, "fine_flag": true, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70.0, "fine_window_length": 11, "fine_freq_distance": 80.0}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 1.0, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0.0, "sigma": 5.0, "fast_m": false}, "scope": {"l": 100, "r": 100, "s": 2}, "mode": "IF"}, "drive_power_list": "Points(31) | qarange | (-40, -10, 3)"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitSpectrumZAmp": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "QubitSpectrumZAmp", "export_datetime": "2023-11-29 19:58:17", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"freq_list": "Points(281) | qarange | (3800, 5200, 5)", "drive_power": -40, "z_amp": null, "use_square": true, "band_width": 50.0, "fine_flag": false, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70.0, "fine_window_length": 11, "fine_freq_distance": 80.0}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 1.0, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0.0, "sigma": 5.0, "fast_m": false}, "scope": {"l": 400, "r": 200, "s": 5}, "mode": "IF"}, "z_amp_list": "Points(54) | qarange | (-0.4, 0.4, 0.015)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5}, "clear_base": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanWidth": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "RabiScanWidth", "export_datetime": "2023-11-29 19:58:37", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "fake_pulse": true}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanAmp": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "RabiScanAmp", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.1)", "drive_power": null}, "analysis_options": {"quality_bounds": [0.005, 0.005, 0.005]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "GateExp": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "GateExp", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"gates": ["-X/2"], "cycle": 10}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShot": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01", "ac_prepare_time": 3000, "fake_pulse": true}, "analysis_options": {"quality_bounds": [2, 0.85, 0.7, 0.011], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "IQTrackSingleShot": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "IQTrackSingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShotQC": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "coupler_probe_calibration", "readout_type": "", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"quality_bounds": [2, 0.85, 0.7, 0.011], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "AmpComposite": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "AmpComposite", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"theta_type": "Xpi", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 7, "threshold_left": 0.9, "threshold_right": 1.1, "f12_opt": false}, "n_list": "Points(2) | normal | [7, 9]", "theta_type": "Xpi", "f12_opt": false}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "diff_threshold": 0.2}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "DetuneCalibration": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "DetuneCalibration", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"child_exp_options": {"sweep_name": "detune", "theta_type": "Xpi", "sweep_list": "Points(36) | qarange | (-25, 10, 1)", "phi_num": 1, "N": 9}, "detune_list": "Points(36) | qarange | (-25, 10, 1)", "n_list": "Points(3) | normal | [7, 9, 13]", "theta_type": "Xpi", "scan_type": "rough"}, "detune_list": "Points(36) | qarange | (-20, 10, 1)", "rough_n_list": "Points(3) | qarange | (6, 8, 1)", "fine_n_list": "Points(2) | qarange | (7, 9, 2)", "theta_type": "Xpi", "fine_precision": 0.1, "f12_opt": false}, "analysis_options": {"child_ana_options": {"child_ana_options": {"quality_bounds": [0.95, 0.9, 0.8], "filter": {"window_length": 5, "polyorder": 3}, "fine": false, "prominence_divisor": 4.0}, "diff_threshold": 0.2}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitFreqCalibration": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null}, "fringes": "Points(2) | qarange | (10, -10, -20)", "delays": "Points(71) | qarange | (100, 800, 10)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}, "subplots": [2, 2], "freq_gap_threshold": 0.1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ReadoutPowerCalibrate": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "ReadoutPowerCalibrate", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"level_str": "01"}, "sweep_list": "Points(21) | qarange | (-35, -15, 1)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [2, 0.85, 0.7, 0.011], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "XpiDetection", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "expect_value": 0.7, "scope": 0.1, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RBSingle": {"meta": {"username": "zyc", "visage_version": "0.4.6", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "RBSingle", "export_datetime": "2023-12-19 13:39:48", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"times": 30, "depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(8) | qarange | (15, 50, 5)", "depth3": "Points(5) | qarange | (60, 100, 10)", "depth4": "Points(14) | qarange | (120, 380, 20)", "interleaved_gate": "Y", "gate_split": false, "mode": "cpp", "open_seed": false, "seed": null, "check_matrix": false, "is_dynamic": 0, "is_amend": true, "fake_pulse": true}, "analysis_options": {"quality_bounds": [0.01, 0.01, 0.01], "std_bound": 0.8, "fidelity_threshold": 0.0001}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RBMultiple": {"meta": {"username": "zyc", "visage_version": "0.4.6", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "RBMultiple", "export_datetime": "2023-12-19 13:42:10", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": ["q4q5"]}, "options_for_regular_exec": {"experiment_options": {"depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(0) | normal | None", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 30, "interleaved_gate": null, "gate_split": false, "open_seed": false, "seed": null, "mode": "cpp", "check_matrix": false, "fake_pulse": true}, "analysis_options": {"quality_bounds": [0.9, 0.85, 0.77]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "T1": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "T1", "export_datetime": "2024-01-11 14:00:57", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "delays": "Points(100) | qarange | (200, 29900, 300)", "z_amp": null, "frequency": null, "ac_branch": "right", "is_amend": true}, "analysis_options": {"quality_bounds": [0.9, 0.85, 0.77]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitCouplerT1Spectrum": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "T1", "export_datetime": "2025-07-01 09:00:57", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "delays": "Points(100) | qarange | (200, 29900, 300)", "z_amp": null, "frequency": null, "ac_branch": "right", "is_amend": true}, "analysis_options": {"quality_bounds": [0.9, 0.85, 0.77]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerAmpComposite": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CouplerAmpComposite", "export_datetime": "2024-01-17 15:02:06", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "theta_type": "Xpi", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 7, "threshold_left": 0.9, "threshold_right": 1.1, "f12_opt": false, "head_time": 0, "tail_time": 0, "right_delay": 0, "add_z_delay": 0}, "n_list": "Points(3) | normal | [7, 9, 13]", "theta_type": "Xpi", "f12_opt": false}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "diff_threshold": 0.2}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "AmpOptimize": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "AmpOptimize", "export_datetime": "2024-01-17 15:07:15", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "theta_type": "Xpi", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 7, "threshold_left": 0.9, "threshold_right": 1.1, "f12_opt": false}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerAmpOptimize": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CouplerAmpOptimize", "export_datetime": "2024-01-17 15:07:15", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "theta_type": "Xpi", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 7, "threshold_left": 0.9, "threshold_right": 1.1, "f12_opt": false, "head_time": 0, "tail_time": 0, "right_delay": 0, "add_z_delay": 0}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "APEComposite": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "APEComposite", "export_datetime": "2024-01-17 15:07:15", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "sweep_name": "detune", "theta_type": "Xpi", "sweep_list": "Points(36) | qarange | (-25, 10, 1)", "phi_num": 1, "N": 9}, "detune_list": "Points(36) | qarange | (-25, 10, 1)", "n_list": "Points(3) | normal | [7, 9, 13]", "theta_type": "Xpi", "scan_type": "rough", "simulator_data_path": "C:\\Users\\<USER>\\Downloads\\2024-06-18\\15.51.29-Xpi\\child\\APEComposite\\15-51-29 RoughScanAPE"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.95, 0.9, 0.8], "filter": {"window_length": 5, "polyorder": 3}, "fine": false, "prominence_divisor": 4.0}, "diff_threshold": 0.2}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerAPEComposite": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CouplerAPEComposite", "export_datetime": "2024-01-17 15:07:15", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "sweep_name": "detune", "theta_type": "Xpi", "sweep_list": "Points(36) | qarange | (-25, 10, 1)", "phi_num": 1, "N": 9, "head_time": 0, "tail_time": 0, "right_delay": 0, "add_z_delay": 0}, "detune_list": "Points(36) | qarange | (-25, 10, 1)", "n_list": "Points(3) | normal | [7, 9, 13]", "theta_type": "Xpi", "scan_type": "rough"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.95, 0.9, 0.8], "filter": {"window_length": 5, "polyorder": 3}, "fine": false, "prominence_divisor": 4.0}, "diff_threshold": 0.2}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerDetuneCalibration": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CouplerDetuneCalibration", "export_datetime": "2024-01-17 15:07:15", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "sweep_name": "detune", "theta_type": "Xpi", "sweep_list": "Points(36) | qarange | (-25, 10, 1)", "phi_num": 1, "N": 9, "head_time": 0, "tail_time": 0, "right_delay": 0, "add_z_delay": 0}, "detune_list": "Points(36) | qarange | (-25, 10, 1)", "n_list": "Points(3) | normal | [7, 9, 13]", "theta_type": "Xpi", "scan_type": "rough"}, "detune_list": "Points(36) | qarange | (-25, 10, 1)", "rough_n_list": "Points(3) | qarange | (6, 8, 1)", "fine_n_list": "Points(2) | qarange | (7, 9, 2)", "theta_type": "Xpi", "fine_precision": 0.1, "f12_opt": false}, "analysis_options": {"child_ana_options": {"child_ana_options": {"quality_bounds": [0.95, 0.9, 0.8], "filter": {"window_length": 5, "polyorder": 3}, "fine": false, "prominence_divisor": 4.0}, "diff_threshold": 0.2}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XYZTimingComposite": {"meta": {"username": "dpY2", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "231204-设计验证-72bit-300pin-V9.2.3-Base-23#", "env_name": "Y2", "point_label": "batch_test"}, "exp_class_name": "XYZTimingComposite", "export_datetime": "2024-01-15 16:45:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"is_dynamic": 0, "time_perform": true, "delays": "Points(161) | qarange | (0.0, 100, 1.25)", "const_delay": 80, "z_pulse_params": {"time": 15, "amp": 0.1, "sigma": 0.1, "buffer": 5}}, "max_count": 2}, "analysis_options": {"child_ana_options": {"extract_mode": "fit_params", "quality_bounds": [0.95, 0.9, 0.75], "fit_model_name": "gauss_lo<PERSON>zian"}, "force": true}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "VoltageDriftGradientCalibration": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "VoltageDriftGradientCalibration", "export_datetime": "2023-12-22 17:41:32", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (10, 70, 0.625)", "fringe": 100, "z_amp": null, "frequency": null, "ac_branch": null, "is_dynamic": 0}, "threshold": 0.05, "guess_step": 0.2, "iteration": 10, "cali_point": "sweet_point"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ZZShiftSweetPointCalibrationNew": {"meta": {"username": "dpY4", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "ZZShiftSweetPointCalibrationNew", "export_datetime": "2024-01-10 20:47:12", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(41) | qarange | (20, 120, 2.5)", "fringe": 50, "z_amp": null, "frequency": null, "ac_branch": "right", "is_dynamic": 0}, "iteration": 10, "threshold": 0.1, "guess_step": 0.5, "mode": "awg_bias"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.9, 0.81], "factor": 3.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XEBMultiple": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "0.5.8", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "XEBMultiple", "export_datetime": "2023-12-27 15:50:05", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q11q17"}, "options_for_regular_exec": {"experiment_options": {"depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(10) | qarange | (15, 60, 5)", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 30, "goal_gate": "CZ", "open_seed": false, "seed": null, "is_amend": false}, "analysis_options": {"fidelity_threshold": 0.1, "quality_bounds": [0.001, 0.001, 0.001]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XEBComposite": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "XEBComposite", "export_datetime": "2024-01-23 18:06:04", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "child_exp_options": {"time_perform": false, "depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(10) | qarange | (15, 60, 5)", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 30, "goal_gate": null, "open_seed": false, "seed": null}, "scan_list": "Points(0) | normal | [0.1, 0.15, 0.2]", "scan_name": "qc"}, "analysis_options": {"child_ana_options": {"fidelity_threshold": 0.95}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "LeakageNum": {"meta": {"username": "zyc", "visage_version": "0.4.9", "monster_version": "0.5.9", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "LeakageNum", "export_datetime": "2024-01-24 20:52:07", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "swap_state": "11", "scan_name": "qc", "z_amp_list": "Points(0) | qarange | (0.1, 0.6, 0.1)", "cz_num": 1, "scope": {"l": 30, "r": 30, "p": 31}, "scope_detune": false, "label": "cz", "freq_list": "Points(0) | normal | None", "ac_branch": "right"}, "cz_num_list": "Points(0) | qarange | (1, 40, 2)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.8, 0.6, 0.5], "fit_model_name": "skewed_lorentzian", "cali_offset_method": "direct", "adjust_noise": true}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ProcessTomography": {"meta": {"username": "zyc", "visage_version": "0.4.9", "monster_version": "0.5.9", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "ProcessTomography", "export_datetime": "2024-01-29 16:50:21", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q61"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "base_gate": "Points(3) | normal | ['I', 'X/2', 'Y/2']", "pre_gate": "Points(1) | normal | ['I']", "is_amend": true}, "base_gates": "Points(4) | normal | ['I', 'X/2', 'Y/2', '-X/2']", "qst_base_gates": "Points(3) | normal | ['I', 'X/2', 'Y/2']", "goal_gate": "I"}, "analysis_options": {"child_ana_options": {"use_mle": true}, "use_mle": true, "fidelity_accuracy": 3}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ReadoutFreqCalibrate": {"meta": {"username": "zyc", "visage_version": "0.4.9", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "ReadoutFreqCalibrate", "export_datetime": "2024-01-31 16:36:31", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "child_exp_options": {"time_perform": false, "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "extend_f12": false, "scope": 3, "z_amp": null, "mode": "IF"}, "readout_power": null, "readout_type": "01"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "save_mode": "max_distance_point", "diff_threshold": 0.1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "F12Calibration": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion_group3"}, "exp_class_name": "F12Calibration", "export_datetime": "2024-01-10 16:13:56", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "child_exp_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right", "is_dynamic": 0}, "fringes": "Points(2) | normal | [25, -25]", "delays": "Points(49) | qarange | (20, 140, 2.5)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}, "subplots": [2, 2], "freq_gap_threshold": 0.6}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XEBSingle": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "XEBSingle", "export_datetime": "2024-02-01 09:32:10", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"times": 30, "depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(8) | qarange | (15, 50, 5)", "depth3": "Points(5) | qarange | (60, 100, 10)", "depth4": "Points(14) | qarange | (120, 380, 20)", "goal_gate": null, "open_seed": false, "seed": null, "is_amend": true}, "analysis_options": {"fidelity_threshold": 0.001, "quality_bounds": [0.001, 0.001, 0.001]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PopulationLossOnce": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "PopulationLossOnce", "export_datetime": "2024-02-02 17:29:49", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q61"}, "options_for_regular_exec": {"experiment_options": {"delay": 1000, "z_amp_list": "Points(21) | qarange | (-0.1, 0.1, 0.01)", "freq_range_mode": "normal", "point": 20}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PopulationLossSpectrum": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "PopulationLossSpectrum", "export_datetime": "2024-02-02 17:29:49", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q61"}, "options_for_regular_exec": {"experiment_options": {"delay_list": "Points(3) | normal | [100, 1000, 10000]", "z_amp_list": "Points(21) | qarange | (-0.1, 0.1, 0.01)", "ac_branch": "right"}, "analysis_options": {"child_ana_options": {}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "DistortionT1": {"meta": {"username": "zyc", "visage_version": "0.4.10", "monster_version": "0.5.10", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "DistortionT1", "export_datetime": "2024-02-04 19:17:00", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "z_offset_list": "Points(65) | qarange | (-0.14, 0.02, 0.0025)", "gauss_sigma": 5.0, "gauss_width": 15.0, "const_width": 100, "ta": 2000, "add_tb_width": 100, "z_amp": -0.5, "xy_delay": 50, "run_mode": "new_case", "frequency": null, "ac_branch": "right"}, "analysis_options": {"quality_bounds": [0.95, 0.85, 0.75], "fit_model_name": "bi_lorentz_tilt", "cali_offset_method": "direct", "cut_index": true, "adjust_noise": true}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerDistortionZZ": {"meta": {"username": "zyc", "visage_version": "0.4.10", "monster_version": "0.5.10", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CouplerDistortionZZ", "export_datetime": "2024-02-04 19:31:01", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "QH", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "z_offset_list": "Points(65) | qarange | (-0.14, 0.02, 0.0025)", "gauss_sigma": 5.0, "gauss_width": 15.0, "const_width": 100, "ta": 2000, "add_tb_width": 100, "z_amp": -0.5, "xy_delay": 50, "run_mode": "new_case", "frequency": null, "ac_branch": "right"}, "analysis_options": {"quality_bounds": [0.95, 0.85, 0.75], "fit_model_name": "bi_lorentz_tilt", "cali_offset_method": "direct", "cut_index": true, "adjust_noise": true}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ProcessTomographyV2": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "ProcessTomographyV2", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q61"}, "options_for_regular_exec": {"experiment_options": {"goal_gate": "I", "use_diag_in": true}, "analysis_options": {"neglect_mode": "neglect_chi_prep", "cal_diag_in": false, "phase_opt": true}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ProcessTomographyV2_2Q": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "ProcessTomographyV2", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q67q73"}, "options_for_regular_exec": {"experiment_options": {"goal_gate": "CZ", "use_diag_in": true}, "analysis_options": {"neglect_mode": "neglect_chi_prep", "cal_diag_in": false, "phase_opt": true}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerRabiScanAmp": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CouplerRabiScanAmp", "export_datetime": "2024-01-17 15:02:06", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerRabiScanWidth": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "CouplerRabiScanWidth", "export_datetime": "2023-11-29 19:58:37", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "fake_pulse": true}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanWidthDetune": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "RabiScanWidthDetune", "export_datetime": "2023-11-29 19:58:37", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "fake_pulse": true}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerRabiScanWidthDetune": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "CouplerRabiScanWidthDetune", "export_datetime": "2023-11-29 19:58:37", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "fake_pulse": true}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerAPE": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CouplerAPE", "export_datetime": "2024-01-17 15:02:06", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"sweep_name": "detune", "theta_type": "Xpi", "sweep_list": "Points(41) | qarange | [-1, 1, 0.05]", "N": 7}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanWidthF12": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "RabiScanWidthF12", "export_datetime": "2024-05-14 09:12:14", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "widths": "Points(41) | qarange | (100, 300, 5)", "f12_xpi": null, "f12_freq": null}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanAmpF12": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "RabiScanAmpF12", "export_datetime": "2024-05-14 09:17:04", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null, "f12_time": null}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RamseyF12": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "RamseyF12", "export_datetime": "2024-05-14 10:56:03", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "analysis_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitSpectrumF12": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "QubitSpectrumF12", "export_datetime": "2024-05-14 11:06:36", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "f12_list": "Points(0) | normal | None", "f12_xpi": null, "f12_width": 500, "scope": {"l": 250, "r": -150, "s": 2}}, "analysis_options": {"quality_bounds": [0.8, 0.6, 0.5], "window_length": 11, "freq_distance": 80}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QubitSpectrumF12_2D": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "QubitSpectrumF12_2D", "export_datetime": "2024-05-14 11:16:55", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "f12_list": "Points(0) | normal | None", "f12_xpi": null, "f12_width": 500, "scope": {"l": 250, "r": -150, "s": 2}, "z_amp": 0, "delay": 100}, "analysis_options": {"quality_bounds": [0.8, 0.6, 0.5], "window_length": 11, "freq_distance": 80}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShotF012": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "SingleShotF012", "export_datetime": "2024-05-14 11:20:16", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "level_str": "01"}, "analysis_options": {"quality_bounds": [3, 0.85, 0.7, 0.6, 0.011], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShotF02": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "SingleShotF02", "export_datetime": "2024-05-14 11:20:12", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "level_str": "01"}, "analysis_options": {"quality_bounds": [2, 0.85, 0.7, 0.011], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "StateTomography": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "StateTomography", "export_datetime": "2024-05-14 13:47:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "base_gate": "Points(3) | normal | ['I', 'X/2', 'Y/2']", "pre_gate": "Points(1) | normal | ['I']"}, "analysis_options": {"use_mle": true}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerT1": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CouplerT1", "export_datetime": "2024-05-14 14:42:29", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "delays": "Points(100) | qarange | (200, 29900, 300)", "z_amp": 0.0, "frequency": null, "ac_branch": "right", "head_time": 0, "tail_time": 0, "right_delay": 0, "add_z_delay": 0}, "analysis_options": {"quality_bounds": [0.9, 0.85, 0.77]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QCT1": {"meta": {"username": "zyc", "visage_version": "0.4.8", "monster_version": "0.5.8", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "QCT1", "export_datetime": "2024-01-11 14:00:57", "description": null}, "context_options": {"name": "coupler_probe_calibration", "readout_type": "01", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "delays": "Points(100) | qarange | (200, 29900, 300)", "z_amp": null, "frequency": null, "ac_branch": "right"}, "analysis_options": {"quality_bounds": [0.9, 0.85, 0.77]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "T1Extend": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "T1Extend", "export_datetime": "2024-05-14 16:12:07", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "01", "physical_unit": ["q4q5", "q4", "q5", "c1-7"]}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "delays": "Points(100) | qarange | (200, 29900, 300)", "z_amp": null, "frequency": null, "ac_branch": "right", "tq_name": "ql", "bq_name_list": "Points(0) | normal | ['qh', 'qc']", "bq_z_amp_list": "Points(0) | normal | [0.1, 0.2]", "bq_freq_list": "Points(0) | normal | None", "auto_set_coupler_zamp": false, "auto_set_label": "cz"}, "analysis_options": {"quality_bounds": [0.9, 0.85, 0.77]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerXYZTiming": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CouplerXYZTiming", "export_datetime": "2024-05-14 16:59:18", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "delays": "Points(129) | qarange | (0.0, 80.0, 0.625)", "const_delay": 50, "z_pulse_params": {"time": 50, "amp": 0.5, "sigma": 2.5, "buffer": 10}, "head_time": 0, "tail_time": 0, "right_delay": 0, "add_z_delay": 0}, "analysis_options": {"extract_mode": "fit_params", "quality_bounds": [0.95, 0.9, 0.75], "fit_model_name": "gauss_lo<PERSON>zian"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ACCrosstalkOnce": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "ACCrosstalkOnce", "export_datetime": "2024-05-15 11:52:07", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "drive_freq": null, "tq_name": "q4", "bq_name": "q5", "tq_ac_list": "Points(0) | qarange | (0.1, 0.2, 0.001)", "bq_ac": 0.2, "drive_type": "Drag", "ac_buffer_pre": 200, "ac_buffer_after": 200}, "analysis_options": {"quality_bounds": [0.91, 0.8, 0.7]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerACCrosstalkOnce": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CouplerACCrosstalkOnce", "export_datetime": "2024-05-15 14:06:13", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": "c1-7q4"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "drive_freq": null, "tq_name": "c1-7", "bq_name": "q4", "tq_ac_list": "Points(0) | qarange | (0.1, 0.2, 0.001)", "bq_ac": 0.2, "drive_type": "Drag", "ac_buffer_pre": 200, "ac_buffer_after": 200, "head_time": 0, "tail_time": 0, "right_delay": 0, "add_z_delay": 0}, "analysis_options": {"quality_bounds": [0.91, 0.8, 0.7]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QCZShiftOnce": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "QCZShiftOnce", "export_datetime": "2024-05-15 14:58:19", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "qh-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "drive_freq": null, "tq_name": "qh", "bq_name": "qc", "tq_ac_list": "Points(0) | qarange | (0.1, 0.2, 0.001)", "bq_ac": 0.2, "drive_type": "Drag", "ac_buffer_pre": 200, "ac_buffer_after": 200}, "analysis_options": {"quality_bounds": [0.91, 0.8, 0.7], "fit_model_name": "gauss_lo<PERSON>zian", "cali_offset_method": "direct", "cut_index": false, "adjust_noise": true}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ACCrosstalkFixF": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "ACCrosstalkFixF", "export_datetime": "2024-05-15 15:32:21", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "drive_freq": null, "tq_ac_list": "Points(0) | qarange | (0.1, 0.2, 0.001)", "bq_ac": 0.2, "drive_type": "Drag", "ac_buffer_pre": 200, "ac_buffer_after": 200}, "bq_ac_list": "Points(0) | qarange | (-0.2, 0.2, 0.02)", "init_tq_ac_center": "Points(3) | qarange | (0.1, 0.3, 0.1)", "init_tq_scan_range": 0.05, "init_tq_scan_gap": 0.005, "fix_tq_ac": true}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.91, 0.8, 0.7]}, "fit_type": "linear", "popt": "Points(8) | normal | [-4, 0, 10, 4, 0, 7, 0.01, 0.001]"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerACCrosstalkFixF": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CouplerACCrosstalkFixF", "export_datetime": "2024-05-15 15:32:21", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": "c1-7q4"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "drive_freq": null, "tq_name": "c1-7", "bq_name": "q4", "tq_ac_list": "Points(0) | qarange | (0.1, 0.2, 0.001)", "bq_ac": 0.2, "drive_type": "Drag", "ac_buffer_pre": 200, "ac_buffer_after": 200}, "bq_ac_list": "Points(0) | qarange | (-0.2, 0.2, 0.02)", "init_tq_ac_center": "Points(3) | qarange | (0.1, 0.3, 0.1)", "init_tq_scan_range": 0.05, "init_tq_scan_gap": 0.005, "fix_tq_ac": true}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.91, 0.8, 0.7]}, "fit_type": "linear", "popt": "Points(8) | normal | [-4, 0, 10, 4, 0, 7, 0.01, 0.001]"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QCZShift": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "QCZShift", "export_datetime": "2024-05-15 15:32:21", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "qh-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "drive_freq": null, "tq_name": "q1", "bq_name": "c1-7", "tq_ac_list": "Points(0) | qarange | (0.1, 0.2, 0.001)", "bq_ac": 0.2, "drive_type": "Drag", "ac_buffer_pre": 200, "ac_buffer_after": 200}, "bq_ac_list": "Points(0) | qarange | (-0.2, 0.2, 0.02)", "init_tq_ac_center": "Points(3) | qarange | (0.1, 0.3, 0.1)", "init_tq_scan_range": 0.05, "init_tq_scan_gap": 0.005, "fix_tq_ac": true}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.91, 0.8, 0.7]}, "fit_type": "linear", "popt": "Points(8) | normal | [-4, 0, 10, 4, 0, 7, 0.01, 0.001]"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CZAssist": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CZAssist", "export_datetime": "2024-05-15 16:39:22", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "qh-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "cz_num": 1, "add_cz": true, "control_gate": "I", "phase_list": "Points(15) | qarange | (0.0, 6.283, 0.449)", "ramsey_bit": "ql"}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CPhaseTMSE": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "CPhaseTMSE", "export_datetime": "2023-12-21 18:02:26", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q10"}, "options_for_regular_exec": {"experiment_options": {"mode": "SE-TM", "ramsey_bit": "ql", "phase_mode": "control", "leakage_mode": "fit", "k": 2, "is_amend": true}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SwapOnce": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "SwapOnce", "export_datetime": "2024-05-15 18:50:04", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q69q76"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "swap_state": "11", "scan_buffer": false, "time_list": "Points(25) | qarange | (20.0, 80.0, 2.5)", "label": "cz"}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.85], "interaction_location": 1, "data_key": "Points(0) | normal | None", "fit_model_name": "swap_once_cosine", "cut_width": 27.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "Swap": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "<PERSON><PERSON><PERSON>", "export_datetime": "2024-05-15 19:11:43", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "swap_state": "11", "scan_buffer": false, "time_list": "Points(25) | qarange | (20.0, 80.0, 2.5)", "label": "cz"}, "scan_name": "qh", "z_amp_list": "Points(0) | normal | None", "scope": {"l": 0.1, "r": 0.1, "p": 20}, "auto_check": false, "scope_detune": false, "freq_list": "Points(0) | normal | None", "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85], "interaction_location": 1, "data_key": "Points(0) | normal | None", "fit_model_name": "swap_once_cosine", "cut_width": 27.5}, "quality_bounds": [0.98, 0.95, 0.85], "goal_width": null, "var_limit": 1, "is_check_child_exp": true}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SpinEcho": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "SpinEcho", "export_datetime": "2024-05-15 19:37:12", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "analysis_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RamseyZZ": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "RamseyZZ", "export_datetime": "2024-05-15 19:43:33", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right", "drag_bit": "q5", "ramsey_bit": "q4", "amp_c_bit": "c1-7", "gate": "I", "amp_bit": null, "bit_z_amp": null}, "analysis_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SpinEchoZZ": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "SpinEchoZZ", "export_datetime": "2024-05-15 19:45:10", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right", "drag_bit": "q4", "ramsey_bit": "q5", "amp_c_bit": "c1-7", "gate": "I", "amp_bit": null, "bit_z_amp": null}, "analysis_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ZZTimingOnce": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "ZZTimingOnce", "export_datetime": "2024-05-15 19:53:44", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "swap_state": "11", "const_delay": {"ql": 20, "qh": 20, "qc": 20}, "delays": "Points(49) | qarange | (0.0, 39.984, 0.833)", "scan_bit": "qc", "z_pulse_params": {"sigma": null, "buffer": null}}, "analysis_options": {"extract_mode": "fit_params", "fit_model_name": "gauss_lo<PERSON>zian", "filter": {"window_length": 5, "polyorder": 3}, "data_key": "Points(0) | normal | None"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ZZTimingComposite": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "ZZTimingComposite", "export_datetime": "2024-05-15 19:57:22", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "swap_state": "11", "const_delay": {"ql": 20, "qh": 20, "qc": 20}, "delays": "Points(49) | qarange | (0.0, 100, 0.833)", "scan_bit": "qc", "z_pulse_params": {"sigma": null, "buffer": null}}, "delay": 50, "iteration": 2, "iter_names": "Points(3) | normal | ['ql', 'qh', 'qc']"}, "analysis_options": {"child_ana_options": {"extract_mode": "fit_params", "fit_model_name": "gauss_lo<PERSON>zian", "filter": {"window_length": 5, "polyorder": 3}, "data_key": "Points(0) | normal | None"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanWidthAmp": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "RabiScanWidthAmp", "export_datetime": "2024-05-15 20:20:05", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "amp": 0.8}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerSpectrum": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CouplerSpectrum", "export_datetime": "2024-05-16 11:38:47", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "freq_list": "Points(0) | normal | None", "drive_power": null, "z_amp": null, "use_square": true, "band_width": 50, "fine_flag": false, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70, "fine_window_length": 11, "fine_freq_distance": 80}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 0.5, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0, "sigma": 5, "fast_m": false}, "scope": {"l": 50, "r": 50, "s": 1}, "f02_scope": {"l": 150, "r": -50, "s": 2}, "mode": "IF", "f_type": "f01", "head_time": 0, "tail_time": 0, "right_delay": 0, "add_z_delay": 0}, "analysis_options": {"quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerXYZTimingByZZShift": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CouplerXYZTimingByZZShift", "export_datetime": "2024-05-16 13:43:12", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "driveQ", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "delays": "Points(129) | qarange | (0.0, 80.0, 0.625)", "const_delay": 50, "z_pulse_params": {"time": 50, "amp": 0.5, "sigma": 2.5, "buffer": 10}}, "analysis_options": {"extract_mode": "fit_params", "quality_bounds": [0.95, 0.9, 0.75], "fit_model_name": "gauss_lo<PERSON>zian"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerTunableByQS": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CouplerTunableByQS", "export_datetime": "2024-05-17 09:26:33", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "driveQ", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "freq_list": "Points(0) | qarange | (4800, 4900, 1)", "drive_power": -30, "z_amp": null, "use_square": true, "band_width": 50, "fine_flag": false, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70, "fine_window_length": 11, "fine_freq_distance": 80}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 1, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0, "sigma": 5, "fast_m": false}, "scope": {"l": 400, "r": 200, "s": 5}, "f02_scope": {"l": 150, "r": -50, "s": 2}, "mode": "IF", "f_type": "f01"}, "flux_list": "Points(54) | qarange | (-0.4, 0.4, 0.015)", "scan_name": "ac_bias", "run_mode": "async"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ConditionalPhaseFixed": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "ConditionalPhaseFixed", "export_datetime": "2024-05-17 11:18:41", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "qh-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "cz_num": 1, "add_cz": true, "control_gate": "I", "phase_list": "Points(15) | qarange | (0.0, 6.283, 0.449)", "ramsey_bit": "qh"}, "z_amp_list": "Points(0) | qarange | (-0.1, 0.1, 0.01)", "scan_name": "qh", "adapter_name": "qc", "adapter_amp_list": "Points(0) | qarange | (-0.1, 0.1, 0.01)", "leakage_mode": "fit", "freq_list": "Points(0) | normal | None", "ac_branch": "right", "adapter_freq_list": "Points(0) | normal | None", "adapter_ac_branch": "right"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "quality_bounds": [0.99, 0.9, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ConditionalPhaseAdjust": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "ConditionalPhaseAdjust", "export_datetime": "2024-05-17 12:08:02", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "cz_num": 1, "add_cz": true, "control_gate": "I", "phase_list": "Points(15) | qarange | (0.0, 6.283, 0.449)", "ramsey_bit": "qh"}, "z_amp_list": "Points(0) | qarange | (-0.1, 0.1, 0.01)", "scan_name": "qh", "adapter_name": "qc", "adapter_amp_list": "Points(0) | qarange | (-0.1, 0.1, 0.01)", "leakage_mode": "fit", "freq_list": "Points(0) | normal | None", "ac_branch": "right", "adapter_freq_list": "Points(0) | normal | None", "adapter_ac_branch": "right"}, "z_amp_list": "Points(0) | qarange | (-0.1, 0.1, 0.01)", "scan_name": "ql", "freq_list": "Points(0) | normal | None", "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "quality_bounds": [0.99, 0.9, 0.85]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ConditionalPhaseAdjustNGate": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "ConditionalPhaseAdjustNGate", "export_datetime": "2024-05-17 13:39:05", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "cz_num": 1, "add_cz": true, "control_gate": "I", "phase_list": "Points(15) | qarange | (0.0, 6.283, 0.449)", "ramsey_bit": "qh"}, "z_amp_list": "Points(0) | qarange | (-0.1, 0.1, 0.01)", "scan_name": "qh", "adapter_name": "qc", "adapter_amp_list": "Points(0) | qarange | (-0.1, 0.1, 0.01)", "leakage_mode": "fit", "freq_list": "Points(0) | normal | None", "ac_branch": "right", "adapter_freq_list": "Points(0) | normal | None", "adapter_ac_branch": "right"}, "gate_nums": "Points(8) | qarange | (1, 8, 1)"}, "analysis_options": {"child_ana_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "quality_bounds": [0.99, 0.9, 0.85]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ConditionalPhaseTMSE": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "ConditionalPhaseTMSE", "export_datetime": "2024-05-17 13:51:26", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"mode": "SE-TM", "ramsey_bit": "q1", "phase_mode": "control", "scan_name": "q1", "adapter_name": "c1-7", "z_amp_list": "Points(0) | qarange | (0.0, 0.039, 0.001)", "adapter_amp_list": "Points(0) | qarange | (0.0, 0.039, 0.001)", "k": 4, "cz_num": 1, "leakage_mode": "fit", "freq_list": "Points(0) | normal | None", "ac_branch": "right", "adapter_freq_list": "Points(0) | normal | None", "adapter_ac_branch": "right"}, "z_amp_list": "Points(21) | qarange | (0.3, 0.4, 0.005)", "scan_name": "q1", "freq_list": "Points(0) | normal | None", "ac_branch": "right"}, "analysis_options": {"child_ana_options": {}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ConditionalPhaseTMSENGate": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "ConditionalPhaseTMSENGate", "export_datetime": "2024-05-17 13:51:30", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"mode": "SE-TM", "ramsey_bit": "q1", "phase_mode": "control", "scan_name": "q1", "adapter_name": "c1-7", "z_amp_list": "Points(0) | qarange | (0.0, 0.039, 0.001)", "adapter_amp_list": "Points(0) | qarange | (0.0, 0.039, 0.001)", "k": 4, "cz_num": 1, "leakage_mode": "fit", "freq_list": "Points(0) | normal | None", "ac_branch": "right", "adapter_freq_list": "Points(0) | normal | None", "adapter_ac_branch": "right"}, "gate_nums": "Points(8) | qarange | (1, 8, 1)"}, "analysis_options": {"child_ana_options": {}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SampleWidthOptimize": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "SampleWidthOptimize", "export_datetime": "2024-05-17 14:27:37", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "level_str": "01"}, "sweep_list": "Points(17) | qarange | (600, 2200, 100)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [2, 0.85, 0.7, 0.011], "method": "GMM", "n_multiple": 3, "set_proportion": false, "heat_stimulate": false}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerSpectrumZAmp": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CouplerSpectrumZAmp", "export_datetime": "2024-05-17 14:41:11", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "freq_list": "Points(0) | qarange | (4800, 4900, 1)", "drive_power": -30, "z_amp": null, "use_square": true, "band_width": 50, "fine_flag": false, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70, "fine_window_length": 11, "fine_freq_distance": 80}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 1, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0, "sigma": 5, "fast_m": false}, "scope": {"l": 400, "r": 200, "s": 5}, "f02_scope": {"l": 150, "r": -50, "s": 2}, "mode": "IF", "f_type": "f01", "head_time": 0, "tail_time": 0, "right_delay": 0, "add_z_delay": 0}, "z_amp_list": "Points(0) | qarange | (-0.1, 0.1, 0.01)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5}, "clear_base": false, "diff_ratio": 0.5, "point_nums": 5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SweepDetuneRabiWidth": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "SweepDetuneRabiWidth", "export_datetime": "2024-05-17 15:01:58", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "detune": 0}, "detune_list": "Points(41) | qarange | (-40, 40, 2)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerSweepDetuneRabiWidth": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CouplerSweepDetuneRabiWidth", "export_datetime": "2024-05-17 15:02:05", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "head_time": 0, "tail_time": 0, "right_delay": 0, "add_z_delay": 0, "detune": 0}, "detune_list": "Points(41) | qarange | (-40, 40, 2)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ReadoutPowerF02Calibrate": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "ReadoutPowerF02Calibrate", "export_datetime": "2024-05-17 15:39:15", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "level_str": "01", "repeat": 10000}, "sweep_list": "Points(21) | qarange | (-35, -15, 1)", "optimize_field": "probe_power"}, "analysis_options": {"child_ana_options": {"quality_bounds": [2, 0.85, 0.7, 0.011], "method": "GMM", "n_multiple": 3, "set_proportion": false, "heat_stimulate": false}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RBInterleavedMultiple": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "RBInterleavedMultiple", "export_datetime": "2024-05-17 16:09:48", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "depth1": "Points(10) | qarange | (2, 20, 2)", "depth2": "Points(0) | normal | None", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 5, "interleaved_gate": null, "gate_split": true, "open_seed": false, "seed": null, "mode": "dynamic", "repeat": 300}, "interleaved_gate": "CZ"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.001, 0.0005, 0.0001]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "NMRBSingle": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "NMRBSingle", "export_datetime": "2024-05-17 16:25:48", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "times": 30, "depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(8) | qarange | (15, 50, 5)", "depth3": "Points(5) | qarange | (60, 100, 10)", "depth4": "Points(5) | qarange | (120, 200, 20)", "interleaved_gate": null, "gate_split": false, "mode": "cpp", "open_seed": false, "seed": null}, "input_data": {"Xpi": {"is_opt": true, "init_v": null, "iter_step": 0.001, "bound": [-1, 1], "nonzdelt": 0.05}, "Xpi2": {"is_opt": true, "init_v": null, "iter_step": 0.001, "bound": [-1, 1], "nonzdelt": 0.05}, "detune_pi": {"is_opt": true, "init_v": null, "iter_step": 0.01, "bound": null, "nonzdelt": 0.05}, "detune_pi2": {"is_opt": true, "init_v": null, "iter_step": 0.01, "bound": null, "nonzdelt": 0.05}, "alpha": {"is_opt": true, "init_v": null, "iter_step": 0.001, "bound": [0, 1], "nonzdelt": 0.05}}, "nm_params": {"ftarget": -100000000, "maxiter": 50, "maxfev": 50, "disp": true, "return_all": true, "initial_simplex": null, "xatol": 0.0001, "fatol": 0.0001, "adaptive": false}}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.9, 0.85, 0.77], "std_bound": 0.05, "fidelity_threshold": 0.995}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerRamsey": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "export_datetime": "2024-05-17 16:55:38", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right", "head_time": 0, "tail_time": 0, "right_delay": 0, "add_z_delay": 0}, "analysis_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerFreqCalibration": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CouplerFreqCalibration", "export_datetime": "2024-05-17 16:51:36", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right", "head_time": 0, "tail_time": 0, "right_delay": 0, "add_z_delay": 0}, "fringes": "Points(2) | qarange | (10, -10, -20)", "delays": "Points(71) | qarange | (100, 800, 10)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}, "subplots": [2, 2], "freq_gap_threshold": 0.1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SweepAmpRabiWidth": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "SweepAmpRabiWidth", "export_datetime": "2024-05-17 17:02:14", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "amp": 0.8}, "amp_list": "Points(101) | qarange | (0.0, 1.0, 0.01)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CheckFreqRabiWidth": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CheckFreqRabiWidth", "export_datetime": "2024-05-17 17:05:48", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false}, "freq_list": "Points(0) | normal | [4300, 4400, 4500]"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SQPhaseTMSE": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "SQPhaseTMSE", "export_datetime": "2023-12-21 18:02:26", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q10"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"mode": "SE-TM", "ramsey_bit": "ql", "phase_mode": "single", "k": 3, "is_amend": true}, "gate_nums": "Points(5) | qarange | (1, 5, 1)"}, "analysis_options": {"std_limit": 0.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleQubitPhase": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "SingleQubitPhase", "export_datetime": "2024-05-20 10:09:03", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "cz_num": 1, "add_cz": true, "control_gate": "I", "phase_list": "Points(15) | qarange | (0.0, 6.283, 0.449)", "ramsey_bit": null}, "gate_nums": "Points(8) | qarange | (1, 8, 1)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "std_limit": 0.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QCT1Spectrum": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "QCT1Spectrum", "export_datetime": "2024-05-20 10:26:54", "description": null}, "context_options": {"name": "coupler_probe_calibration", "readout_type": "01", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(100) | qarange | (200, 29900, 300)", "z_amp": null, "frequency": null, "ac_branch": "right"}, "z_amp_list": "Points(0) | qarange | (0, 0.1, 0.005)", "delays": "Points(101) | qarange | (0, 30000, 300)", "freq_range_map": {"l": 200, "r": 20, "s": 10}, "freq_list": "Points(0) | normal | None", "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.9, 0.85, 0.77]}, "r_square_threshold": 0.7, "rate_threshold": 0.38, "ymax": 40}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "T1Spectrum": {"meta": {"username": "gkk", "visage_version": "0.23.19", "monster_version": "*********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "idle_point"}, "exp_class_name": "T1Spectrum", "export_datetime": "2025-07-09 09:46:58", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"period": 100, "acq_index": "Points(0) | normal | None", "resource_filter": true, "open_half_pi": false, "union_mode": "union", "delays": "Points(100) | qarange | (200, 29900, 300)", "z_amp": null, "frequency": null, "ac_branch": "right"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "z_amp_list": "Points(0) | normal | None", "delays": "Points(101) | qarange | (0, 30000, 300)", "freq_range_map": {"l": 200, "r": 20, "s": 10}, "freq_list": "Points(0) | normal | None", "ac_branch": "right"}, "analysis_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "child_ana_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "quality_bounds": [0.9, 0.85, 0.77]}, "r_square_threshold": 0.7, "rate_threshold": 0.38, "ymax": 40}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "T1WithBiasCoupler": {"meta": {"username": "gkk", "visage_version": "0.23.18", "monster_version": "*********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "idle_point"}, "exp_class_name": "T1WithBiasCoupler", "export_datetime": "2025-07-07 09:42:18", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "01", "physical_unit": "q7,c1-7,c2-7,c7-13,c7-14"}, "options_for_regular_exec": {"experiment_options": {"delays": "Points(100) | qarange | (200, 29900, 300)", "z_amp": 0.1, "frequency": null, "bias_coupler_name_list": "Points(4) | normal | ['c1-7', 'c2-7', 'c7-13', 'c7-14']", "bias_coupler_z_amp_list": "Points(4) | normal | [0.1, 0.11, 0.12, 0.13]"}, "analysis_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "quality_bounds": [0.9, 0.85, 0.77]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "T1SpectrumWithBiasCoupler": {"meta": {"username": "gkk", "visage_version": "0.23.19", "monster_version": "*********", "chip": {"sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "point_label": "idle_point"}, "exp_class_name": "T1SpectrumWithBiasCoupler", "export_datetime": "2025-07-08 09:01:34", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "01", "physical_unit": "q7,c1-7,c2-7,c7-13,c7-14"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(100) | qarange | (200, 29900, 300)", "bias_coupler_name_list": "Points(4) | normal | ['c1-7', 'c2-7', 'c7-13', 'c7-14']", "bias_coupler_z_amp_list": "Points(4) | normal | [0.1, 0.11, 0.12, 0.13]"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "freq_range_map": {"l": 200, "r": 20, "s": 10}, "z_amp_list": "Points(11) | qarange | (-1, 1, 0.2)", "freq_list": "Points(0) | normal | None", "ac_branch": "right"}, "analysis_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "child_ana_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "quality_bounds": [0.9, 0.85, 0.77]}, "r_square_threshold": 0.7, "rate_threshold": 0.38, "ymax": 40}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerT1Spectrum": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CouplerT1Spectrum", "export_datetime": "2024-05-20 11:58:55", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(100) | qarange | (200, 29900, 300)", "z_amp": null, "frequency": null, "ac_branch": "right"}, "z_amp_list": "Points(0) | qarange | (0, 0.1, 0.01)", "delays": "Points(101) | qarange | (0, 30000, 300)", "freq_range_map": {"l": 200, "r": 20, "s": 10}, "freq_list": "Points(0) | normal | None", "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.9, 0.85, 0.77]}, "r_square_threshold": 0.7, "rate_threshold": 0.38, "ymax": 40}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerXYZTimingComposite": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CouplerXYZTimingComposite", "export_datetime": "2024-05-20 14:06:55", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "<PERSON><PERSON><PERSON>", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(129) | qarange | (0.0, 80.0, 0.625)", "const_delay": 50, "z_pulse_params": {"time": 50, "amp": 0.5, "sigma": 2.5, "buffer": 10}, "head_time": 0, "tail_time": 0, "right_delay": 0, "add_z_delay": 0}, "max_count": 2}, "analysis_options": {"child_ana_options": {"extract_mode": "fit_params", "quality_bounds": [0.95, 0.9, 0.75], "fit_model_name": "gauss_lo<PERSON>zian"}, "force": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CouplerXYZTimingZZShiftComposite": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "CouplerXYZTimingZZShiftComposite", "export_datetime": "2024-05-20 14:07:03", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "driveQ", "physical_unit": "c1-7"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(129) | qarange | (0.0, 80.0, 0.625)", "const_delay": 50, "z_pulse_params": {"time": 50, "amp": 0.5, "sigma": 2.5, "buffer": 10}}, "max_count": 2}, "analysis_options": {"child_ana_options": {"extract_mode": "fit_params", "quality_bounds": [0.95, 0.9, 0.75], "fit_model_name": "gauss_lo<PERSON>zian"}, "force": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ZZShiftRamsey": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "ZZShiftRamsey", "export_datetime": "2024-05-20 14:30:00", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right", "drag_bit": "q4", "ramsey_bit": "q5", "amp_c_bit": "c1-7", "gate": "I", "amp_bit": null, "bit_z_amp": null}, "amp_list": "Points(11) | qarange | (0.1, 0.2, 0.01)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ZZShiftSpinEcho": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "ZZShiftSpinEcho", "export_datetime": "2024-05-20 14:29:50", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right", "drag_bit": "q4", "ramsey_bit": "q5", "amp_c_bit": "c1-7", "gate": "I", "amp_bit": null, "bit_z_amp": null}, "amp_list": "Points(11) | qarange | (0.1, 0.2, 0.01)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ZZShiftSpinEchoZAmp": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "ZZShiftSpinEchoZAmp", "export_datetime": "2024-05-20 14:29:53", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right", "drag_bit": "q4", "ramsey_bit": "q5", "amp_c_bit": "c1-7", "gate": "I", "amp_bit": "q5", "bit_z_amp": null}, "amp_list": "Points(11) | qarange | (0.1, 0.2, 0.01)"}, "z_amp_list": "Points(11) | qarange | (0.1, 0.2, 0.01)", "cur_scan_name": null, "freq_list": "Points(0) | normal | None", "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ReadoutAmpCalibration": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "ReadoutAmpCalibration", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"level_str": "01"}, "sweep_list": "Points(17) | qarange | (0.03, 0.18, 0.01)", "left_rate": 0.1, "right_rate": 0.1, "point": 30}, "analysis_options": {"child_ana_options": {"quality_bounds": [2, 0.7, 0.6, 0.02], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "Ramsey": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "coupler_distortion"}, "exp_class_name": "<PERSON>", "export_datetime": "2024-01-03 20:34:11", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"delays": "Points(49) | qarange | (20, 140, 2.5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null}, "analysis_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "DistortionT1Composite": {"meta": {"username": "why_y4_debug", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "slepian point"}, "exp_class_name": "DistortionT1Composite", "export_datetime": "2024-05-15 16:01:00", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "z_offset_list": "Points(101) | qarange | (-0.02, 0.02, 0.0004)", "gauss_sigma": 5.0, "gauss_width": 15.0, "const_width": 100, "ta": 2000, "add_tb_width": 100, "z_amp": -0.5, "xy_delay": 0, "run_mode": "new_case", "frequency": null, "ac_branch": "right"}, "iteration_times": 1, "xy_delay_start": 0.0, "xy_delay_max": 10000, "xy_step_map": {"lt_10": 0.625, "lt_50": 1.25, "lt_100": 5.0, "lt_200": 10.0, "lt_500": 20.0, "lt_1000": 50.0, "lt_5000": 100.0, "lt_10000": 200.0, "gt_10000": 300.0, "gt_20000": 400.0}, "bit_type": "<PERSON><PERSON><PERSON>", "base_history": false, "update_z_offset_range": false, "scan_points": 70, "z_amp": null, "z_offset_list": "Points(101) | qarange | (-0.02, 0.02, 0.0004)", "frequency": null, "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.95, 0.85, 0.75], "fit_model_name": "lorentzian", "cali_offset_method": "direct", "cut_index": true, "adjust_noise": true}, "quality_bounds": [0.9999, 0.999, 0.9], "lfilter_flag": false, "ylim": "Points(2) | qarange | (0.95, 1.02, 0.07)"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "OptimizeFIR": {"meta": {"username": "why_y4_debug", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "slepian point"}, "exp_class_name": "OptimizeFIR", "export_datetime": "2024-05-15 17:16:34", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "z_offset_list": "Points(65) | qarange | (-0.008, 0.008, 0.00025)", "gauss_sigma": 5.0, "gauss_width": 15.0, "const_width": 100, "ta": 2000, "add_tb_width": 100, "z_amp": -0.5, "xy_delay": 0, "run_mode": "normal", "frequency": null, "ac_branch": "right"}, "iteration_times": 2, "average_times": 2, "xy_delay_start": 0.0, "xy_delay_max": 320, "add_width": 100.0, "xy_step_first_map": {"lt_10": 0.625, "lt_50": 1.25, "lt_100": 5.0, "lt_200": 10.0, "lt_500": 20.0, "lt_1000": 50.0, "lt_5000": 100.0, "lt_10000": 200.0, "gt_10000": 300.0, "gt_20000": 400.0}, "xy_step_avg_map": {"lt_10": 0.625, "lt_50": 1.25, "lt_100": 2.5, "lt_200": 5.0, "lt_400": 10.0, "lt_600": 15.0, "lt_1000": 20.0, "lt_10000": 50.0, "gt_10000": 100.0}, "bit_type": "<PERSON><PERSON><PERSON>", "mean_smooth": false, "first_flag": false, "update_z_offset_range": false, "scan_points": 70, "z_amp": null, "z_offset_list": "Points(65) | qarange | (-0.008, 0.008, 0.00025)", "frequency": null, "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.95, 0.85, 0.75], "fit_model_name": "lorentzian", "cali_offset_method": "direct", "cut_index": true, "adjust_noise": true}, "target_std": 0.00015, "target_fir_width": 30.0, "t_min": null, "t_max": null, "adjust_noise": true, "use_inline": false, "rate": 0.92}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XYCrossRabiWidthOnce": {"meta": {"username": "why_y4_debug", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "slepian point"}, "exp_class_name": "XYCrossRabiWidthOnce", "export_datetime": "2024-05-16 15:02:28", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "01", "physical_unit": "q4, q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "widths": "Points(66) | normal | [5.0, 7.5, 10.0, 12.5, 15.0, 17.5, 20.0, 22.5, 25.0, 27.5, 30.0, 32.5, 35.0, 37.5, 40.0, 42.5, 45.0, 47.5, 50.0, 52.5, 60.0, 85.0, 110.0, 135.0, 160.0, 185.0, 210.0, 235.0, 260.0, 285.0, 310.0, 335.0, 360.0, 385.0, 410.0, 435.0, 460.0, 485.0, 510.0, 535.0, 600.0, 850.0, 1100.0, 1350.0, 1600.0, 1850.0, 2100.0, 2350.0, 2600.0, 2850.0, 3100.0, 3350.0, 3600.0, 3850.0, 4100.0, 4350.0, 4600.0, 4850.0, 5100.0, 5350.0, 6000.0, 8500.0, 11000.0, 13500.0, 16000.0, 18500.0]", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "xy_name": "q5", "rd_name": "q4", "direct_execute": true}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91], "loga_fit": true}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XYCrossRabiWidth": {"meta": {"username": "why_a2", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "XYCrossRabiWidth", "export_datetime": "2024-05-16 17:34:32", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "01", "physical_unit": "q4,q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "xy_name": "", "rd_name": "", "direct_execute": false}, "case_mode": "simple", "target_name": "q4", "bias_name_list": "Points(3) | normal | [3, 4, 5]", "cali_freq_flag": false, "max_count": 3, "max_distance": 3, "run_once_mode": "strength", "update_target_strength": false, "target_widths": "Points(40) | qarange | (5, 200, 5)", "bias_widths_1": "Points(66) | normal | [5.0, 7.5, 10.0, 12.5, 15.0, 17.5, 20.0, 22.5, 25.0, 27.5, 30.0, 32.5, 35.0, 37.5, 40.0, 42.5, 45.0, 47.5, 50.0, 52.5, 60.0, 85.0, 110.0, 135.0, 160.0, 185.0, 210.0, 235.0, 260.0, 285.0, 310.0, 335.0, 360.0, 385.0, 410.0, 435.0, 460.0, 485.0, 510.0, 535.0, 600.0, 850.0, 1100.0, 1350.0, 1600.0, 1850.0, 2100.0, 2350.0, 2600.0, 2850.0, 3100.0, 3350.0, 3600.0, 3850.0, 4100.0, 4350.0, 4600.0, 4850.0, 5100.0, 5350.0, 6000.0, 8500.0, 11000.0, 13500.0, 16000.0, 18500.0]", "bias_widths_2": "Points(80) | qarange | (5, 2000, 25)", "target_expect": "Points(2) | normal | [5, 100]", "bias_expect_1": "Points(2) | normal | [0.1, 1.0]", "bias_expect_2": "Points(2) | normal | [0.5, 10]"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91], "loga_fit": false}, "n_multiple": 100}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XYCrossPlusRabiWidth": {"meta": {"username": "why_a2", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "XYCrossPlusRabiWidth", "export_datetime": "2024-05-16 18:25:11", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "01", "physical_unit": "q4,q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "xy_name": "", "rd_name": "", "direct_execute": false}, "case_mode": "simple", "target_name": "", "bias_name_list": "Points(0) | normal | None", "cali_freq_flag": false, "max_count": 3, "max_distance": 3, "run_once_mode": "strength", "update_target_strength": false, "target_widths": "Points(40) | qarange | (5, 200, 5)", "bias_widths_1": "Points(66) | normal | [5.0, 7.5, 10.0, 12.5, 15.0, 17.5, 20.0, 22.5, 25.0, 27.5, 30.0, 32.5, 35.0, 37.5, 40.0, 42.5, 45.0, 47.5, 50.0, 52.5, 60.0, 85.0, 110.0, 135.0, 160.0, 185.0, 210.0, 235.0, 260.0, 285.0, 310.0, 335.0, 360.0, 385.0, 410.0, 435.0, 460.0, 485.0, 510.0, 535.0, 600.0, 850.0, 1100.0, 1350.0, 1600.0, 1850.0, 2100.0, 2350.0, 2600.0, 2850.0, 3100.0, 3350.0, 3600.0, 3850.0, 4100.0, 4350.0, 4600.0, 4850.0, 5100.0, 5350.0, 6000.0, 8500.0, 11000.0, 13500.0, 16000.0, 18500.0]", "bias_widths_2": "Points(80) | qarange | (5, 2000, 25)", "target_expect": "Points(2) | normal | [5, 100]", "bias_expect_1": "Points(2) | normal | [0.1, 1.0]", "bias_expect_2": "Points(2) | normal | [0.5, 10]"}, "target_name_list": "Points(0) | normal | None"}, "analysis_options": {"child_ana_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91], "loga_fit": false}, "n_multiple": 100}, "n_multiple": 100}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XYCrosstalkOnce": {"meta": {"username": "why_a2", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "XYCrosstalkOnce", "export_datetime": "2024-05-17 16:44:51", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": "q4q16"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "target_name": "q4", "theta_type": "Xpi", "amp_coe": 0.1, "b_amp": 0.8, "b_drive_power": null, "b_freq": null, "x_num": 1, "x_width": 30.0, "delay": 10.0, "bounds": {}, "sweep_name": "phase", "sweep_list": "Points(121) | normal | [0.0, 0.0****************, 0.10471975511965977, 0.15707963267948966, 0.20943951023931953, 0.2617993877991494, 0.3141592653589793, 0.36651914291880916, 0.*****************, 0.*****************, 0.****************, 0.5759586531581287, 0.6283185307179586, 0.6806784082777885, 0.7330382858376183, 0.7853981633974483, 0.8377580409572781, 0.890117918517108, 0.9424777960769379, 0.9948376736367678, 1.0471975511965976, 1.0995574287564276, 1.1519173063162573, 1.2042771838760873, 1.2566370614359172, 1.308996938995747, 1.361356816555577, 1.413716694115407, 1.4660765716752366, 1.5184364492350666, 1.5707963267948966, 1.6231562043547263, 1.6755160819145563, 1.7278759594743862, 1.780235837034216, 1.832595714594046, 1.8849555921538759, 1.9373154697137056, 1.9896753472735356, 2.0420352248333655, 2.0943951023931953, 2.146754979953025, 2.199114857512855, 2.251474735072685, 2.3038346126325147, 2.356194490192345, 2.4085543677521746, 2.4609142453120043, 2.5132741228718345, 2.5656340004316642, 2.617993877991494, 2.670353755551324, 2.722713633111154, 2.7750735106709836, 2.827433388230814, 2.8797932657906435, 2.9321531433504733, 2.9845130209103035, 3.036872898470133, 3.089232776029963, 3.141592653589793, 3.193952531149623, 3.2463124087094526, 3.2986722862692828, 3.3510321638291125, 3.4033920413889422, 3.4557519189487724, 3.508111796508602, 3.560471674068432, 3.612831551628262, 3.665191429188092, 3.7175513067479216, 3.7699111843077517, 3.8222710618675815, 3.874630939427411, 3.9269908169872414, 3.979350694547071, 4.031710572106901, 4.084070449666731, 4.13643032722656, 4.1887902047863905, 4.241150082346221, 4.29350995990605, 4.34586983746588, 4.39822971502571, 4.45058959258554, 4.50294947014537, 4.5553093477052, 4.607669225265029, 4.6600291028248595, 4.71238898038469, 4.764748857944519, 4.817108735504349, 4.869468613064179, 4.921828490624009, 4.974188368183839, 5.026548245743669, 5.078908123303498, 5.1312680008633285, 5.183627878423159, 5.235987755982988, 5.288347633542818, 5.340707511102648, 5.393067388662478, 5.445427266222308, 5.497787143782138, 5.550147021341967, 5.602506898901797, 5.654866776461628, 5.707226654021457, 5.759586531581287, 5.811946409141117, 5.864306286700947, 5.916666164260777, 5.969026041820607, 6.021385919380436, 6.073745796940266, 6.126105674500097, 6.178465552059926, 6.230825429619756, 6.283185307179586]", "phase": 3.141592653589793}, "analysis_options": {"phase_bounds": {"coe": [0.01, 1], "offset": [0, 1], "omega1": [0, 40], "omega2": [2, 4], "x0": [0, 6.283185307179586]}, "amp_coe_bounds": {"coe": [0.01, 1], "offset": [0, 1], "rabi": [0, 40], "omega2": [2, 4]}, "phase_bias": 3.141592653589793}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XYCrosstalk": {"meta": {"username": "why_a2", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "XYCrosstalk", "export_datetime": "2024-05-17 17:29:37", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": "q4q16"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "target_name": "q4", "theta_type": "Xpi/2", "amp_coe": 0.1, "b_amp": 0.8, "b_drive_power": null, "b_freq": null, "x_num": 1, "x_width": 30.0, "delay": 10.0, "bounds": {}, "sweep_name": "phase", "sweep_list": "Points(121) | normal | [0.0, 0.0****************, 0.10471975511965977, 0.15707963267948966, 0.20943951023931953, 0.2617993877991494, 0.3141592653589793, 0.36651914291880916, 0.*****************, 0.*****************, 0.****************, 0.5759586531581287, 0.6283185307179586, 0.6806784082777885, 0.7330382858376183, 0.7853981633974483, 0.8377580409572781, 0.890117918517108, 0.9424777960769379, 0.9948376736367678, 1.0471975511965976, 1.0995574287564276, 1.1519173063162573, 1.2042771838760873, 1.2566370614359172, 1.308996938995747, 1.361356816555577, 1.413716694115407, 1.4660765716752366, 1.5184364492350666, 1.5707963267948966, 1.6231562043547263, 1.6755160819145563, 1.7278759594743862, 1.780235837034216, 1.832595714594046, 1.8849555921538759, 1.9373154697137056, 1.9896753472735356, 2.0420352248333655, 2.0943951023931953, 2.146754979953025, 2.199114857512855, 2.251474735072685, 2.3038346126325147, 2.356194490192345, 2.4085543677521746, 2.4609142453120043, 2.5132741228718345, 2.5656340004316642, 2.617993877991494, 2.670353755551324, 2.722713633111154, 2.7750735106709836, 2.827433388230814, 2.8797932657906435, 2.9321531433504733, 2.9845130209103035, 3.036872898470133, 3.089232776029963, 3.141592653589793, 3.193952531149623, 3.2463124087094526, 3.2986722862692828, 3.3510321638291125, 3.4033920413889422, 3.4557519189487724, 3.508111796508602, 3.560471674068432, 3.612831551628262, 3.665191429188092, 3.7175513067479216, 3.7699111843077517, 3.8222710618675815, 3.874630939427411, 3.9269908169872414, 3.979350694547071, 4.031710572106901, 4.084070449666731, 4.13643032722656, 4.1887902047863905, 4.241150082346221, 4.29350995990605, 4.34586983746588, 4.39822971502571, 4.45058959258554, 4.50294947014537, 4.5553093477052, 4.607669225265029, 4.6600291028248595, 4.71238898038469, 4.764748857944519, 4.817108735504349, 4.869468613064179, 4.921828490624009, 4.974188368183839, 5.026548245743669, 5.078908123303498, 5.1312680008633285, 5.183627878423159, 5.235987755982988, 5.288347633542818, 5.340707511102648, 5.393067388662478, 5.445427266222308, 5.497787143782138, 5.550147021341967, 5.602506898901797, 5.654866776461628, 5.707226654021457, 5.759586531581287, 5.811946409141117, 5.864306286700947, 5.916666164260777, 5.969026041820607, 6.021385919380436, 6.073745796940266, 6.126105674500097, 6.178465552059926, 6.230825429619756, 6.283185307179586]", "phase": 3.141592653589793}, "target_name": "q4", "sweep_name": "amp_coe", "sweep_list": "Points(81) | qarange | (0.0, 0.8, 0.01)"}, "analysis_options": {"child_ana_options": {"phase_bounds": {"coe": [0.01, 1], "offset": [0, 1], "omega1": [0, 40], "omega2": [2, 4], "x0": [0, 6.283185307179586]}, "amp_coe_bounds": {"coe": [0.01, 1], "offset": [0, 1], "rabi": [0, 40], "omega2": [2, 4]}, "phase_bias": 3.141592653589793}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XYCrosstalkNpiOnce": {"meta": {"username": "why_a2", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "XYCrosstalkNpiOnce", "export_datetime": "2024-05-17 17:46:02", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": "q4q16"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "target_name": "q4", "amp_coe": 0.1, "phase": 0.0, "num": 61, "sweep_name": "amp_coe", "sweep_list": "Points(61) | qarange | (0.0, 0.6, 0.01)"}, "analysis_options": {"prominence_divisor": 5.0}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XYCrosstalkNpi": {"meta": {"username": "why_a2", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "XYCrosstalkNpi", "export_datetime": "2024-05-17 18:24:31", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": "q4q16"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "target_name": null, "amp_coe": 0.1, "phase": 0.0, "num": 1, "sweep_name": "amp_coe", "sweep_list": "Points(61) | qarange | (0.0, 0.6, 0.01)"}, "target_name": "q4", "num_list": "Points(3) | normal | [23, 38, 61]"}, "analysis_options": {"child_ana_options": {"prominence_divisor": 5.0}, "use_child_fit": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XYCrosstalkRB": {"meta": {"username": "why_a2", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "XYCrosstalkRB", "export_datetime": "2024-05-20 13:50:59", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "union-01", "physical_unit": "q4q16"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "times": 30, "depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(8) | qarange | (15, 50, 5)", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "interleaved_gate": null, "gate_split": false, "mode": "cpp", "open_seed": false, "seed": null, "target_name": "q4", "amp_coe": 0.2, "phase": 0.9, "only_readout_target": false}, "analysis_options": {"quality_bounds": [0.9, 0.85, 0.77], "std_bound": 0.05, "fidelity_threshold": 0.995}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SlepianLamNum": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SlepianLamNum", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q11q17"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "child_exp_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "least-sq", "post_select_type": null, "is_amend": false, "plot_iq": false, "freq_list": "Points(0) | normal | None", "lam1_list": "Points(0) | normal | [0.1]", "lam2": 0.1, "swap_state": "11", "cz_num": 1}, "cz_num_list": "Points(20) | qarange | (0, 40, 5)"}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "child_ana_options": {}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "LeakageOnce": {"meta": {"username": "zyc", "visage_version": "0.4.6", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "LeakageOnce", "export_datetime": "2023-12-19 13:39:48", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "bind_baseband_freq": true, "fidelity_correct_type": "least-sq", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "swap_state": "11", "scan_name": "q1", "z_amp_list": "Points(61) | qarange | (-0.271716, -0.259534, 0.0002)", "cz_num": 1, "scope": {"l": 30, "r": 30, "p": 31}, "scope_detune": false, "label": "cz", "freq_list": "Points(0) | normal | None", "ac_branch": null}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "quality_bounds": [0.8, 0.6, 0.5], "fit_model_name": "skewed_lorentzian", "cali_offset_method": "direct", "adjust_noise": true}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "LeakagePre": {"meta": {"username": "tuple_01", "visage_version": "0.4.12", "monster_version": "********", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "LeakagePre", "export_datetime": "2024-04-01 09:25:45", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "swap_state": "11", "scan_name": "qc", "z_amp_list": "Points(0) | normal | None", "cz_num": 1, "scope": {"l": 0.3, "r": 0.3, "p": 61}, "scope_detune": false, "label": "cz", "freq_list": "Points(0) | normal | None", "ac_branch": "right"}, "iteration": 10}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.8, 0.6, 0.5], "fit_model_name": "skewed_lorentzian", "cali_offset_method": "direct", "adjust_noise": true}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "LeakageAmp": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "LeakageAmp", "export_datetime": "2024-05-20 13:47:18", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "child_exp_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "least-sq", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "swap_state": "11", "scan_name": "qc", "z_amp_list": "Points(0) | normal | None", "cz_num": 1, "scope": {"l": 30, "r": 30, "p": 31}, "scope_detune": false, "label": "cz", "freq_list": "Points(0) | normal | None", "ac_branch": null}, "scan_name": "qh", "z_amp_list": "Points(0) | normal | None", "scope": {"l": 0.3, "r": 0.3, "p": 41}, "scope_detune": true, "freq_list": "Points(0) | normal | None", "ac_branch": null}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "child_ana_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "quality_bounds": [0.8, 0.6, 0.5], "fit_model_name": "skewed_lorentzian", "cali_offset_method": "direct", "adjust_noise": true}, "use_qc": true, "point": 40, "leak_threshold": 0.3}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PurityRBSingle": {"meta": {"username": "tuple_01", "visage_version": "0.4.12", "monster_version": "********", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "PurityRBSingle", "export_datetime": "2024-03-20 19:34:19", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "save_label": null, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "least-sq", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "times": 10, "depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(8) | qarange | (15, 50, 5)", "depth3": "Points(5) | qarange | (60, 100, 10)", "depth4": "Points(5) | qarange | (120, 380, 20)", "interleaved_gate": null, "gate_split": false, "mode": "cache", "open_seed": false, "seed": null, "base_gate": "Points(3) | normal | ['I', 'X/2', 'Y/2']"}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "quality_bounds": [0.001, 0.001, 0.001], "std_bound": 0.8, "fidelity_threshold": 0.0001}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SlepianLam": {"meta": {"username": "zyc", "visage_version": "0.4.6", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "SlepianLam", "export_datetime": "2024-05-16 16:26:10", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q11q17"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "child_exp_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "save_label": null, "is_dynamic": 0, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "ibu", "post_select_type": null, "is_amend": true, "plot_iq": false, "fill_readout_point": true, "freq_list": "Points(41) | qarange | (4606, 4806, 5)", "lam1_list": "Points(0) | normal | None", "lam2": 0.1, "swap_state": "11", "cz_num": 1}, "lam2_list": "Points(41) | qarange | (0.05, 0.25, 0.05)"}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "child_ana_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "plot"}, "percent": 0.2, "leak_threshold": 0.3}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "APE": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "APE", "export_datetime": "2024-05-21 14:04:32", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "sweep_name": "detune", "theta_type": "Xpi", "sweep_list": "Points(36) | qarange | (-25, 10, 1)", "phi_num": 1, "N": 9}, "analysis_options": {"quality_bounds": [0.95, 0.9, 0.8], "filter": {"window_length": 5, "polyorder": 3}, "fine": false, "prominence_divisor": 4}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "StabilitySingleShot": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "StabilitySingleShot", "export_datetime": "2024-05-21 15:02:39", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "level_str": "01"}, "loops": 20}, "analysis_options": {"child_ana_options": {"quality_bounds": [2, 0.85, 0.7, 0.011], "method": "GMM", "n_multiple": 3, "set_proportion": false, "heat_stimulate": false}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "StabilityT1": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "StabilityT1", "export_datetime": "2024-05-21 15:02:49", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(100) | qarange | (200, 29900, 300)", "z_amp": null, "frequency": null, "ac_branch": "right"}, "loops": 20, "show_color_map": false}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.9, 0.85, 0.77]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "StabilityT2Ramsey": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "StabilityT2Ramsey", "export_datetime": "2024-05-21 15:02:55", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "delays": "Points(100) | qarange | (200, 20000, 200)", "fringe": 0.5, "z_amp": null, "rate_down": 0.3, "rate_up": 0.5, "max_loops": 5, "frequency": null, "ac_branch": "right"}, "loops": 10, "show_color_map": false}, "analysis_options": {"child_ana_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}, "fit_type": "osc"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XEBPhase": {"meta": {"username": "zyc", "visage_version": "0.4.6", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "XEBPhase", "export_datetime": "2024-05-21 15:03:47", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "least-sq", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "depth1": "Points(15) | qarange | (1, 15, 1)", "depth2": "Points(0) | normal | None", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 30, "goal_gate": "CZ", "open_seed": false, "seed": null}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "fidelity_threshold": 0.95, "input_data": {"phase_qh": {"is_opt": true, "init_v": null, "iter_step": 0.0001, "bound": [-6.3, 6.3], "nonzdelt": 0.05}, "phase_ql": {"is_opt": true, "init_v": null, "iter_step": 0.0001, "bound": [-6.3, 6.3], "nonzdelt": 0.05}}, "nm_params": {"ftarget": -100000000.0, "maxiter": 50, "maxfev": 50, "disp": true, "return_all": true, "initial_simplex": null, "xatol": 1e-07, "fatol": 1e-07, "adaptive": false}, "de_opts": {"NIND": 8, "MAXGEN": 15, "mutF": 0.7, "XOVR": 0.7}, "opt_phase": true, "opt_method": "NM"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "NMXEBPhaseOpt": {"meta": {"username": "zyc", "visage_version": "0.4.6", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "NMXEBPhaseOpt", "export_datetime": "2024-05-21 16:13:19", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "child_exp_options": {"show_result": true, "save_result": true, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "save_label": null, "is_dynamic": 1, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "least-sq", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(10) | qarange | (15, 60, 5)", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 30, "goal_gate": "CZ", "open_seed": true, "seed": 666}, "input_data": {"ql_amp": {"is_opt": false, "init_v": null, "iter_step": 0.0001, "bound": [-0.5, 0.5], "nonzdelt": 0.05}, "qh_amp": {"is_opt": false, "init_v": null, "iter_step": 0.0001, "bound": [-0.5, 0.5], "nonzdelt": 0.05}, "ql_freq": {"is_opt": false, "init_v": null, "iter_step": 0.1, "bound": [4000, 8000], "nonzdelt": 0.05}, "qh_freq": {"is_opt": false, "init_v": null, "iter_step": 0.1, "bound": [4000, 8000], "nonzdelt": 0.05}, "qc_amp": {"is_opt": false, "init_v": null, "iter_step": 0.0001, "bound": [-0.5, 0.5], "nonzdelt": 0.05}, "qh_phase": {"is_opt": true, "init_v": 1.52, "iter_step": 1e-05, "bound": [-6.1415, 6.1416], "nonzdelt": 2e-05}, "ql_phase": {"is_opt": true, "init_v": 1.52, "iter_step": 1e-05, "bound": [-6.1415, 6.1416], "nonzdelt": 2e-05}, "detune": {"is_opt": false, "init_v": 0, "iter_step": 0.1, "bound": [-30, 30], "nonzdelt": 0.05}, "detune1": {"is_opt": false, "init_v": 0, "iter_step": 0.001, "bound": [-0.15, 0.15], "nonzdelt": 0.005}, "detune2": {"is_opt": false, "init_v": 0, "iter_step": 0.001, "bound": [-0.15, 0.15], "nonzdelt": 0.005}}, "nm_params": {"ftarget": -100000000.0, "maxiter": null, "maxfev": null, "disp": true, "return_all": true, "initial_simplex": null, "xatol": 0.0001, "fatol": 0.0001, "adaptive": false}, "scan_name": "qh"}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "child_ana_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "fidelity_threshold": 0.95, "input_data": {"phase_qh": {"is_opt": true, "init_v": 1.52, "iter_step": 0.0001, "bound": [-6.3, 6.3], "nonzdelt": 0.05}, "phase_ql": {"is_opt": true, "init_v": 1.52, "iter_step": 0.0001, "bound": [-6.3, 6.3], "nonzdelt": 0.05}}, "nm_params": {"ftarget": -100000000.0, "maxiter": 30, "maxfev": 50, "disp": true, "return_all": true, "initial_simplex": null, "xatol": 0.0001, "fatol": 0.0001, "adaptive": false}, "de_opts": {"NIND": 8, "MAXGEN": 15, "mutF": 0.7, "XOVR": 0.7}, "opt_phase": true, "opt_method": "NM"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "MicSourceRamseySpectrum": {"meta": {"username": "why_a2", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "MicSourceRamseySpectrum", "export_datetime": "2024-05-21 15:47:50", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q3"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(97) | qarange | (10, 70, 0.625)", "fringe": 100, "z_amp": null, "frequency": null, "ac_branch": "right"}, "mic_source_name": "N5173B", "mic_source_channel": 3, "ip": "*************", "port": 6001, "divice_id": "0x23240004", "freq_list": "Points(16) | qarange | (4200, 5500, 100)", "power_min": -38.0, "power_max": -10.0}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiWidthComposite": {"meta": {"username": "why_a2", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "RabiWidthComposite", "export_datetime": "2024-05-21 17:20:09", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q3"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false}, "diff": 0.5, "max_count": 3}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "T2RamseyExtend": {"meta": {"username": "why_a2", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "T2RamseyExtend", "export_datetime": "2024-05-21 17:29:03", "description": null}, "context_options": {"name": "union_read_measure", "readout_type": "01", "physical_unit": "q4, q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right", "tq_name": null, "bq_name_list": "Points(2) | normal | ['q3', 'q10']", "bq_z_amp_list": "Points(0) | normal | [0.05, 0.08]", "bq_freq_list": "Points(0) | normal | None", "auto_set_coupler_zamp": false, "auto_set_label": "cz"}, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "rate_down": 0.3, "rate_up": 0.5, "max_loops": 5, "frequency": null, "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}, "fit_type": "osc"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "NewCouplerSpectrum": {"meta": {"username": "why_a2", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "240117-设计验证-72bit-300pin-V9.2.3-TK3-Base-4#（V9.2.3-TK3-Flip-2#-A1）", "env_name": "A2", "point_label": "sweetpoint"}, "exp_class_name": "NewCouplerSpectrum", "export_datetime": "2024-05-21 17:42:04", "description": null}, "context_options": {"name": "coupler_calibration", "readout_type": "01", "physical_unit": "c2-3"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "freq_list": "Points(61) | qarange | (5000, 5600, 10)", "drive_power": -35.0, "z_amp": null, "use_square": true, "band_width": 50.0, "fine_flag": false, "smooth_params": {"rough_window_length": 7, "rough_freq_distance": 70.0, "fine_window_length": 11, "fine_freq_distance": 80.0}, "xpulse_params": {"time": 5000, "offset": 15, "amp": 1.0, "detune": 0, "freq": 466.667}, "zpulse_params": {"time": 5100, "amp": 0.0, "sigma": 5.0, "fast_m": false}, "scope": {"l": 400, "r": 200, "s": 5}, "f02_scope": {"l": 150, "r": -50, "s": 2}, "mode": "IF", "f_type": "f01", "head_time": 0, "tail_time": 0, "right_delay": 0, "add_z_delay": 0}, "analysis_options": {"quality_bounds": [0.8, 0.6, 0.5], "snr_bounds": 1.5}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "Distortion_RB": {"meta": {"username": "zyc", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "Distortion_RB", "export_datetime": "2024-05-22 14:52:58", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "save_label": null, "is_dynamic": 0, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "least-sq", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "times": 30, "depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(8) | qarange | (15, 50, 5)", "depth3": "Points(5) | qarange | (60, 100, 10)", "depth4": "Points(5) | qarange | (120, 200, 20)", "interleaved_gate": null, "gate_split": false, "mode": "cpp", "open_seed": false, "seed": null, "z_pulse_params": {"t_head": 50, "t_bottom": 100, "t_wait": 30, "amp_bottom": 0, "amp_tail": 0}}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "quality_bounds": [0.9, 0.85, 0.77], "std_bound": 0.05, "fidelity_threshold": 0.995}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "DistortionPolesOpt": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "********", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "DistortionPolesOpt", "export_datetime": "2024-05-20 16:04:26", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_data_path": null, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "child_exp_options": {"show_result": true, "save_result": true, "simulator_data_path": "F:\\monster9\\monster\\leakage_pre\\pyqcat-monster\\scripts\\simulator\\data\\RB2", "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "save_label": null, "is_dynamic": 0, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "least-sq", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "times": 20, "depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(8) | qarange | (15, 50, 5)", "depth3": "Points(5) | qarange | (60, 100, 10)", "depth4": "Points(5) | qarange | (120, 380, 20)", "interleaved_gate": null, "gate_split": false, "mode": "cpp", "open_seed": false, "seed": null, "z_pulse_params": {"t_head": 50, "t_bottom": 100, "t_wait": 30, "amp_bottom": 0, "amp_tail": 0}}, "z_pulse_params": {"t_head": 50, "t_bottom": 100, "t_wait": 30, "amp_bottom": 0, "amp_tail": 0}, "nm_options": {"nonzdelt": 0.04, "maxiter": 10, "maxfev": 7, "fatol": 0.5, "xatol": 0.1, "adaptive": false, "return_all": true, "disp": true}, "de_options": {"NIND": 30, "MAXGEN": 20, "mutF": 0.7, "XOVR": 0.7, "init_population_path": null}, "poles_num_list": "Points(1) | normal | [[3, 0]]", "poles_bounds_path": "F:\\2024-01\\monster_dev\\pyqcat-monster\\conf\\poles_bounds_p0.json", "opti_method": "DE", "fixed_m": 20}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "child_ana_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "quality_bounds": [0.9, 0.85, 0.77], "std_bound": 0.05, "fidelity_threshold": 0.995}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "OptimizeFirDicarlo": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "********", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "OptimizeFirDicarlo", "export_datetime": "2024-02-21 16:12:30", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "simulator_data_path": null, "child_exp_options": {"time_perform": true, "separation": 200, "width_list": "Points(0) | normal | None", "z_amp": 0.13, "phi": 0, "fringe": 0, "sample_step": 2.5, "xy_width": null, "separa_num": 2, "fake_pulse": true}, "fringe": 0, "iter": 1, "separation": 240, "width_list": "Points(0) | normal | None", "z_amp": 0.13, "repeat_times": 2, "separa_num": 2, "ignore_history": true, "fir_length": 30, "branch": "right", "fname": "E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\", "ac_data": null, "sample_step": 0.8333}, "analysis_options": {"child_ana_options": {}, "ac_spectrum_params": "Points(0) | normal | None", "z_amp": null, "drive_freq": null, "cali_offset": true, "freq_switch": false, "savgol_win_length": 21, "smooth_win_length": 5}}, "options_for_parallel_exec": {"experiment_options": {"same_options": false, "z_amp": {}, "branch": {}, "fname": {}, "ac_data": {}, "fir_length": {}, "separation": {}}, "analysis_options": {}}}, "PurityRBMultiple": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "********", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "PurityRBMultiple", "export_datetime": "2024-05-21 17:57:40", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"show_result": true, "save_result": true, "simulator_remote_path": null, "record_text": true, "save_context": false, "time_perform": true, "file_flag": 0, "repeat": 1000, "register_pulse_save": false, "save_label": null, "is_dynamic": 0, "ac_prepare_time": 0, "use_dcm": true, "fake_pulse": true, "bind_baseband_freq": true, "fidelity_correct_type": "least-sq", "post_select_type": null, "is_amend": false, "plot_iq": false, "fill_readout_point": true, "depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(0) | normal | None", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 4, "interleaved_gate": "CZ", "gate_split": true, "open_seed": true, "seed": 666, "mode": "cpp", "base_gate": "Points(3) | normal | ['I', 'X/2', 'Y/2']"}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "raw_data_format": "scatter", "quality_bounds": [0.9, 0.85, 0.77]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SweepDetuneRabiZamp": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "********", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "SweepDetuneRabiZamp", "export_datetime": "2024-05-28 14:54:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "simulator_shape": [31, 31, 31, 31, 31], "child_exp_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "widths": "Points(40) | qarange | (5, 200, 4)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "detune": 0}, "detune_list": "Points(31) | qarange | (-30, 30, 2)"}, "d_start": 5, "d_end": 5, "d_step": -1, "ac_branch": "right"}, "analysis_options": {"child_ana_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PurityRBInterleavedSingle": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "********", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "PurityRBInterleavedSingle", "export_datetime": "2024-05-28 19:12:56", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "times": 10, "depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(8) | qarange | (15, 50, 5)", "depth3": "Points(5) | qarange | (60, 100, 10)", "depth4": "Points(14) | qarange | (120, 380, 20)", "interleaved_gate": null, "gate_split": false, "mode": "dynamic", "open_seed": true, "seed": 123, "base_gate": "Points(3) | normal | ['I', 'X/2', 'Y/2']"}, "interleaved_gate": "I"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.9, 0.85, 0.77], "std_bound": 0.05, "fidelity_threshold": 0.995}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PurityRBInterleavedMultiple": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "********", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "PurityRBInterleavedMultiple", "export_datetime": "2024-05-29 09:53:04", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_exp_options": {"time_perform": true, "depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(0) | normal | None", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 4, "interleaved_gate": "CZ", "gate_split": true, "open_seed": true, "seed": 666, "mode": "cpp", "base_gate": "Points(3) | normal | ['I', 'X/2', 'Y/2']"}, "interleaved_gate": "CZ"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.001, 0.001, 0.001]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PurityXEBSingle": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "********", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "PurityXEBSingle", "export_datetime": "2024-02-01 09:32:10", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"times": 30, "depth1": "Points(5) | qarange | (2, 14, 4)", "depth2": "Points(8) | qarange | (20, 50, 10)", "depth3": "Points(5) | qarange | (60, 100, 20)", "depth4": "Points(14) | qarange | (150, 600, 80)", "goal_gate": null, "open_seed": true, "seed": "123", "is_amend": true, "fidelity_correct_type": "ibu"}, "analysis_options": {"fidelity_threshold": 0.001, "use_mle": false, "quality_bounds": [0.001, 0.001, 0.001]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PurityXEBMultiple": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "********", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "PurityXEBMultiple", "export_datetime": "2023-12-27 15:50:05", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q4q5"}, "options_for_regular_exec": {"experiment_options": {"depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(0) | normal | None", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 4, "goal_gate": "CZ", "open_seed": true, "is_amend": true, "fake_pulse": true}, "analysis_options": {"fidelity_threshold": 0.001, "quality_bounds": [1e-06, 1e-06, 1e-06]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ACSpectrum": {"meta": {"username": "tuple_01", "visage_version": "0.8.2", "monster_version": "0.9.2", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "ACSpectrum", "export_datetime": "2024-06-14 13:49:54", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_isolate": false, "child_exp_options": {"time_perform": true, "child_isolate": false, "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null}, "run_mode": "sync", "async_time_out": 10, "z_amps": "Points(22) | qarange | (0, 0.3, 0.01)", "delays": "Points(61) | qarange | (100, 160, 0.625)", "freq_bound": 800, "osc_freq_limit": 300, "init_fringe": 10, "spectrum_type": "standard", "use_init_fringe": false, "simulator_data_path": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}, "quality_bounds": [0.995, 0.992, 0.99], "fit_model_name": "amp2freq_formula"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SweepDetuneCom": {"meta": {"username": "zyc", "visage_version": "0.8.4", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "SweepDetuneCom", "export_datetime": "2024-06-19 16:43:07", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q3q4"}, "options_for_regular_exec": {"experiment_options": {"time_perform": true, "child_isolate": false, "child_exp_options": {"time_perform": true, "child_isolate": false, "detune_list": "Points(0) | normal | None", "adapter_detune_list": "Points(0) | normal | None", "label": "detune1", "scan_name": "qh", "gate_num": 1, "auto_fetch": false, "detune_freq_list": "Points(0) | normal | None", "ac_branch": "right", "adapter_detune_freq_list": "Points(0) | normal | None"}, "run_mode": "sync", "async_time_out": 10, "detune_list": "Points(0) | normal | None", "scan_name": "qh", "scope": {"l": 30, "r": 30, "p": 31}, "detune_freq_list": "Points(0) | normal | None", "ac_branch": "right"}, "analysis_options": {"child_ana_options": {}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "QCZShiftFixPointCalibration": {"meta": {"username": "simulator", "visage_version": "0.5.1", "monster_version": "0.6.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "simulator"}, "exp_class_name": "QCZShiftFixPointCalibration", "export_datetime": "2024-05-15 15:32:21", "description": null}, "context_options": {"name": "cz_gate_calibration", "readout_type": "ql-01", "physical_unit": "q45q46"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"child_exp_options": {"delays": "Points(61) | qarange | (0, 200, 5)", "fringe": 25}, "iteration": 10}, "qc_ac_point": 20, "run_mode": "async"}, "analysis_options": {}}}, "CPMGExperiment": {"meta": {"username": "", "monster_version": "0.22.4", "device": "B", "exp_class_name": "CPMGExperiment", "export_datetime": "2025-03-05 12:23:11", "description": ""}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"delays": "Points(41) | qarange | (5000, 30000, 30)", "fringe": 25, "z_amp": null, "mode": "CP", "N": 6}, "analysis_options": {"is_plot": true, "figsize": [12, 8], "result_name": null, "pure_exp_mode": false, "quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "data_key": ["P0", "P1"], "fft_freq": "Points(41) | qarange | (0.001, 40, 0.001)"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CPMGComposite": {"meta": {"username": "", "monster_version": "0.22.4", "device": "B", "exp_class_name": "CPMGComposite", "export_datetime": "2025-03-05 12:23:11", "description": ""}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"n_list": "Points(41) | qarange | (6, 20, 2)", "child_exp_options": {"delays": "Points(41) | qarange | (5000, 30000, 30)", "fringe": 25, "z_amp": null, "mode": "CP", "N": 6}}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "McmQubitPopulation": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "McmQubitPopulation", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"trigger_delay_list": "Points(0) | normal | [30, 40, 50, 60]", "init_state": 0, "fake_pulse": true, "acq_pulse_params": {"Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100}, "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100}}, "acq_pulse_type": "Phase"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "McmQubitDePhase": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "McmQubitDePhase", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"first_trigger_delay": 100, "fake_pulse": true, "delay": 5000, "scale_coef": 1.0, "acq_pulse_params": {"Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100}, "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100}}, "acq_pulse_type": "Phase"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "McmQubitDePhaseComposite": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "McmQubitDePhaseComposite", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"first_trigger_delay": 100, "fake_pulse": true, "delay": 5000, "scale_coef": 1.0, "acq_pulse_params": {"Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100}, "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100}}, "acq_pulse_type": "Phase"}, "scale_coef_list": "Points(0) | normal | [1, 0.99, 0.98, 0.97]"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "McmSpectator": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "McmSpectator", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": "q1q2"}, "options_for_regular_exec": {"experiment_options": {"trigger_delay_list": "Points(0) | normal | [30, 0, 0, 0]", "bias_init_state": 0, "fake_pulse": true, "bias_acq_pulse_params": {"Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100}, "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100}}, "bias_acq_pulse_type": "Phase", "add_delay": 30000}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "McmSpectatorComposite": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "McmSpectatorComposite", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": "q1q2"}, "options_for_regular_exec": {"experiment_options": {"trigger_delay_list": "Points(0) | normal | [30, 0, 0, 0]", "child_exp_options": {"bias_init_state": 0, "fake_pulse": true, "bias_acq_pulse_params": {"Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100}, "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100}}, "bias_acq_pulse_type": "Phase", "add_delay": 30000}}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "McmSpectatorDePhase": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "McmSpectatorDePhase", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": "q1q2"}, "options_for_regular_exec": {"experiment_options": {"bias_trigger_delay": 100, "fake_pulse": true, "delay": 5000, "add_bq_readout": true, "bias_acq_pulse_params": {"Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100}, "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100}}, "bias_acq_pulse_type": "Phase"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "McmSpectatorDePhaseComposite": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "McmSpectatorDePhaseComposite", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": "q1q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"bias_trigger_delay": 100, "fake_pulse": true, "delay": 5000, "add_bq_readout": true, "bias_acq_pulse_params": {"Clear": {"amp1": -0.23, "amp2": 0.17, "tkick": 100}, "Phase": {"amp": 0.15, "phase": 1086, "tkick": 100}}, "bias_acq_pulse_type": "Phase"}, "scale_coef_list": "Points(0) | normal | [1, 0.99, 0.98, 0.97]"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShotExtend": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShotExtend", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01", "ac_prepare_time": 3000, "fake_pulse": true}, "analysis_options": {"quality_bounds": [2, 0.85, 0.7, 0.011], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShotExtendComposite": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShotExtendComposite", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"scale_coef_list": "Points(0) | normal | [1, 0.99, 0.98, 0.97]"}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "DePhaseRamsey": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "DePhaseRamseyComposite": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "DePhaseRamseyComposite", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PhotonScanReadoutFreq": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "PhotonScanReadoutFreq", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PhotonNumMeas": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "PhotonNumMeas", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PhotonNumMeasVsTime": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "PhotonNumMeasVsTime", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PhotonNumMeasVsAmp": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "PhotonNumMeasVsAmp", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PhotonNumMeasVsFreq": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "PhotonNumMeasVsFreq", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShotExtendVsSampleWidth": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShotExtendVsSampleWidth", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PhotonRamsey": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "<PERSON>n<PERSON><PERSON><PERSON>", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "AmpToPhoton": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "AmpToPhoton", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "NMClearParams": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "NMClearParams", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "NMPhaseParams": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "NMPhaseParams", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "NMPhaseParamsBoth": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "NMPhaseParamsBoth", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "NMClearParamsBoth": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "NMClearParamsBoth", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}