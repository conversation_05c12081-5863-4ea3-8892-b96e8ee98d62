{"QubitFreqCalibration": {"meta": {"username": "why_y4_debug", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2024-08-20 09:53:26", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "auto_set_fringe": false, "frequency": null, "ac_branch": "right"}, "run_mode": "async", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "fringes": "Points(2) | qarange | (50, -50, -100)", "delays": "Points(41) | qarange | (20, 120, 2.5)"}, "analysis_options": {"pure_exp_mode": false, "child_ana_options": {"pure_exp_mode": false, "quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}, "subplots": [2, 2], "freq_gap_threshold": 0.1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "RabiScanWidth": {"meta": {"username": "why_y4_debug", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "RabiScanWidth", "export_datetime": "2024-08-21 18:17:01", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"widths": "Points(40) | qarange | (5, 200, 5)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false}, "analysis_options": {"pure_exp_mode": false, "quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XYCrossRabiWidthOnce": {"meta": {"username": "why_y4_debug", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "XYCrossRabiWidthOnce", "export_datetime": "2024-08-20 17:50:11", "description": null}, "context_options": {"name": "crosstalk_measure", "readout_type": "01", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"widths": "Points(40) | qarange | (5, 10000, 250)", "drive_power": null, "drive_freq": null, "adjust_power_flag": false, "xy_name": "", "rd_name": "", "direct_execute": true}, "analysis_options": {"pure_exp_mode": false, "quality_bounds": [0.9, 0.8, 0.7], "loga_fit": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}