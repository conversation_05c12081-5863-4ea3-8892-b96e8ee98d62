# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/20
# __author:       <PERSON><PERSON><PERSON>

from app.config import init_backend
from pyQCat.experiments.batch.batch_cz import Batch<PERSON><PERSON>


if __name__ == "__main__":
    zz_groups = [
        [
            "q1q2",
            "q13q14",
            "q21q22",
            "q33q34",
            "q41q42",
            "q53q54",
            "q61q62",
            "q65q66",
        ]
    ]

    backend = init_backend()

    batch = BatchCZ(backend)
    batch.set_experiment_options(
        param_path=r"../json_data/cz.json",
        swap_state="11",
        # fix_interaction_freq=True,
        auto_set_interaction_freq=True,
        pair_names=zz_groups,
        cz_freq_cali_flows=[
            # "FixedPointCalibration_cz_qh",
            # "FixedPointCalibration_cz_ql",
            # "FixedSwapFreqCaliCoupler2_cz"
        ],
        cz_flows=[
            "Swap",
            # "LeakageOnce",
            # "LeakageAmp",
            "LeakagePre",
            "LeakageAmp_use_qc",
            "CPhaseTMSE",
            "SQPhaseTMSE",
            # "RBInterleavedMultiple",
            # "XEBMultiple_1",
            # "NMXEBMultiple",
            # "NMRBMulitple",
            "XEBMultiple_2",
        ],
        cz_check_flows=[
            # "XEBMultiple_2",
        ],
        cz_optimize_flows=[
            # "XEBMultiple_1",
            # "NMRBMulitple",
            # "NMXEBMultiple",
            # "NMXEBPhaseOpt",
            # "XEBMultiple_2",
        ],
        exp_retry=0,
        use_simulator=False,
        record_batch=True,
        simulator_pass_rate=0,
        quality_block_exp=["Swap", "LeakageOnce"],
        save_db=False,
        cz_threshold=900,
        start_index=0,
        sweep_step=30,
    )
    batch.run()
