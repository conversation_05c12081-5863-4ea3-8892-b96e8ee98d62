# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/22
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchVoltageCalibration

    backend = init_backend()

    batch = BatchVoltageCalibration(backend)
    batch.set_experiment_options(
        param_path=r"../json_data/voltage_calibration.json",
        coupler_physical_units=["c1-2", "c3-4"],
        qubit_physical_units=["q1", "q2", "q3", "q4"],
        qubit_init_amp=0.05,
        coupler_init_amp=0.05,
        auto_set_amp=True,
        coupler_scheme="new",
        qubit_scheme="idle",
        exp_retry=0,
        use_simulator=True,
        record_batch=True,
        simulator_pass_rate=0.8,
        save_db=False,
        quality_block_exp=["SingleShot_1"],
    )
    batch.run()
    # backend.context_manager.extract_hot_data()
