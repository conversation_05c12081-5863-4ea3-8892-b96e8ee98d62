# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/31
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchOptimizeReadout02

    backend = init_backend()
    batch = BatchOptimizeReadout02(backend)
    batch.set_experiment_options(
        param_path=r"../json_data/qubit_02readout.json",
        exp_retry=0,
        use_simulator=True,
        simulator_pass_rate=0.95,
        record_batch=True,
        quality_filter=True,
        flows=[
            "SingleShot_01",
            "QubitSpectrumF12_0",
            "RabiScanAmpF12_0",
            "F12Calibration_0",
            "RabiScanAmpF12_0",
            "DetuneCalibration_f12",
            "AmpComposite_f12",
            "ReadoutFreqCalibrate_02",
            "ReadoutFreqSSCalibrate_02",
            "ReadoutAmpCalibration_02",
            "SampleWidthOptimize_02",
            "SingleShot_02",
        ],
        quality_block_exp=["DetuneCalibration_f12"],
        physical_units=["q1", "q2", "q3", "q4", "q5", "q6"],
    )
    batch.run()
