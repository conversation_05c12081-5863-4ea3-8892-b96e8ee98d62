# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/2/14
# __author:       xw

from app.config import init_backend
from pyQCat.experiments.batch import BatchOptDicarlo


if __name__ == "__main__":

    param_path = r"../json_data/opt_dicarlo.json"
    backend = init_backend()

    qubit_groups = [
        "q21", "q2", "q5", "q15", "q26", "q30", "q33", "q35", "q39", "q43", "q49", "q53", "q64", "q67",
    ]

    batch = BatchOptDicarlo(backend)

    batch.set_experiment_options(
        param_path=param_path,
        parallel_units=qubit_groups,
        exp_retry=0,
        use_simulator=False,
        record_batch=True,
        save_db=False,
        simulator_pass_rate=1,
    )
    batch.run()
