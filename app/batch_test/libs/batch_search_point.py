# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/19
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchSearchPoint

    backend = init_backend()
    batch = BatchSearchPoint(backend)
    batch.set_experiment_options(
        param_path=r"../json_data/search_point.json",
        physical_units=["q1", "q3", "q5"],
        exp_retry=0,
        use_simulator=True,
        record_batch=True,
        refresh_context=True,
        simulator_pass_rate=0.8,
        save_db=False,
    )
    batch.run()
