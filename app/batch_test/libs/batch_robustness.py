# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/02/10
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchRobustness

    backend = init_backend()
    batch = BatchRobustness(backend)
    batch.set_experiment_options(
        param_path=r"app/batch_test/json_data/simulator.json",
        flows=["CavityFreqSpectrum"],
        physical_units=["q62"],
        quality_filter=False,
        scope_map={"Mwave.width": [500, 500, 11]},
    )
    batch.run()

    # BatchRobustness.plot_records(
    #     r"/var/test/data/241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）/BatchRobustness/2025-02-10_18.41.18/result/robustness_records.json",
    #     r"/var/test/data/241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）/BatchRobustness/2025-02-10_18.41.18/result"
    # )
