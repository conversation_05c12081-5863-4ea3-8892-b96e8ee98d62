# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/29
# __author:       <PERSON>


def main():
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchStability

    backend = init_backend()
    batch = BatchStability(backend)
    batch.set_experiment_options(
        # Experiment options available for all batch experiments
        param_path=r"app/batch_test/json_data/simulator.json",
        flows=["CavityFreqSpectrum"],
        # filter_params={"RabiScanAmp": ["Amp", "Phase"]},
        y_limit_params={
            "CavityFreqSpectrum": {
                "power": (-40, -10)
            }
        },
        exp_retry=0,
        refresh_context=False,
        record_experiment_data=True,
        physical_units=["q61"],
        loops=10,
        interval=0,
        is_statistics=True,
    )
    # batch.set_analysis_options(interval_of_analysis=-1)

    batch.run()


if __name__ == "__main__":
    import timeit

    print(timeit.timeit(lambda: main(), number=1))
