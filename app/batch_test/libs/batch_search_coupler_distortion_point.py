# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/02/01
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchSearchCouplerDistortionPoint

    backend = init_backend()
    batch = BatchSearchCouplerDistortionPoint(backend)
    batch.set_experiment_options(
        param_path=r"..\json_data\coupler_distortion_point.json",
        physical_units=["c28-29", "c38-39", "c40-41", "c56-57", "c68-69"],
        qc2qh_ac_spectrum_flows=["ACSpectrumByCoupler_3"],
        point_check_flows=[
            "QubitSpectrum_0",
            "RabiScanAmp_0",
            "QubitFreqCalibration_0"
        ],
        distortion_cali_flows=["CouplerDistortionZZ_0"],
        qh_test_detune_list=[20, 30, 40],
        distortion_zamp=[0.3, -0.3],
        distortion_xy_delay=[1000, 200, 100, 50, 20],
        exp_retry=0,
        use_simulator=True,
        record_batch=True,
        quality_filter=False,
        quality_block_exp=[],
        run_ac_spec_flag=True,
        simulator_data_path="0.8",
        # ac_spec_json_path=r"D:\qh_ac_spec_by_qc.json",
    )
    batch.run()
