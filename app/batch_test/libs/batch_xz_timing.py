# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/15
# __author:       <PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchXZTiming

    backend = init_backend()
    batch = BatchXZTiming(backend)
    batch.set_experiment_options(
        param_path=r"../json_data/xz_timing.json",
        physical_units=["q1", "q10"],
        z_amp_list=[0.1, -0.1, 0.2, -0.2],
        exp_retry=1,
        use_simulator=True,
        record_batch=True,
        simulator_pass_rate=0.9,
        save_db=False,
        quality_block_exp=["SingleShot", "SingleShot_1"],
    )
    batch.run()
    backend.update_context_from_hot_data()
