# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/20
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.tools import qarange
    from pyQCat.experiments.batch import BatchZZTiming

    _backend = init_backend()
    batch = BatchZZTiming(_backend)
    batch.set_experiment_options(
        param_path=r"../json_data/batch_zztiming.json",
        point_map={
            "q1q2": qarange(4500, 4600, 20),
            "q2q3": qarange(4500, 4600, 20),
            "q3q4": qarange(4500, 4600, 20),
            "q4q5": qarange(4500, 4600, 20),
            "q5q6": qarange(4500, 4600, 20),
            "q1q7": qarange(4500, 4600, 20),
            "q2q8": qarange(4500, 4600, 20),
            "q7q13": qarange(4500, 4600, 20),
            "q8q14": qarange(4500, 4600, 20),
        },
        physical_units=[
            "q1q2",
            "q3q4",
            "q5q6",
            "q2q3",
            "q4q5",
            "q1q7",
            "q2q8",
            "q7q13",
            "q8q14",
        ],
        iter_swap_freq=[25, 30, 35],
        fix_flows=["FixedPointCalibration_for_qh", "FixedPointCalibration_for_ql"],
        time_flows=["FixedSwapFreqCaliCoupler", "SwapOnce", "ZZTimingComposite"],
        exp_retry=1,
        use_simulator=True,
        record_batch=True,
        simulator_pass_rate=0.7,
    )
    batch.run()
