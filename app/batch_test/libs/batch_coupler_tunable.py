# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/11/25
# __author:       <PERSON><PERSON><PERSON>


def update_coupler_tunable_batch_result(result_path: str, backend):
    import json
    from loguru import logger

    with open(result_path, "r", encoding="utf-8") as f:
        result = json.load(f)

    update_coupler_list = []
    for coupler, data in result["suc"].items():
        logger.info(f"update {coupler} | {data}")
        update_coupler_list.append(coupler)
        coupler_obj = backend.chip_data.cache_coupler[coupler]
        coupler_obj.dc_min = data["dc_min"]
        coupler_obj.dc_max = data["dc_max"]
        coupler_obj.fc_min = data["fc_min"]
        coupler_obj.fc_max = data["fc_max"]
        coupler_obj.tunable = True
    backend.save_chip_data_to_db(update_coupler_list)


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchCouplerTunable

    backend = init_backend()

    batch = BatchCouplerTunable(backend)
    batch.set_experiment_options(
        mode="circle",
        refresh_context=False,
        param_path=r"../json_data/cavity_batch.json",
    )
    batch.run()
