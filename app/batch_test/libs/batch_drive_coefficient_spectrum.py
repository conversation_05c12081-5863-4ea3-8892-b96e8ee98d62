# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/19
# __author:       <PERSON><PERSON><PERSON>

from pyQCat.experiments.batch.batch_drive_coefficient_spectrum import (
    BatchDriveCoefficientSpectrum,
    TraversalPattern,
)

if __name__ == "__main__":
    from app.config import init_backend

    backend = init_backend()
    batch = BatchDriveCoefficientSpectrum(backend)
    batch.set_experiment_options(
        param_path=r"app/batch_test/json_data/drive_coe_spectrum.json",
        physical_units=[f"q{i}" for i in range(1, 10) if i not in [6]],
        use_simulator=True,
        record_batch=True,
        simulator_pass_rate=0.9,
        traversal_pattern=TraversalPattern.Expand,
        prepare_flows=[
            "SingleShot_0",
            "QubitFreqCalibration_preliminary",
            "XpiDetection",
            "QubitFreqCalibration_detail",
            "SingleShot_1",
        ],
        flows=["RabiScanWidth"],
    )
    batch.run()
