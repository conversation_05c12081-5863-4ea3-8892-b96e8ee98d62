# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/09
# __author:       <PERSON>Kang <PERSON>

"""
BatchT1SpectrumWithBiasCoupler startup script
"""

import os
import sys

from app.config import init_backend
from pyQCat.experiments.batch.batch_t1_spectrum_with_bias_coupler import (
    BatchT1SpectrumWithBiasCoupler,
)

sys.path.append(os.path.join(os.path.dirname(__file__), "..", ".."))

if __name__ == "__main__":

    working_points = [
        {
            "target_qubit": "q1",
            "ratio_map": {"c1-7": [0, 1, 1]},
        },
        {
            "target_qubit": "q8",
            "ratio_map": {
                "c2-8": [0, 1, 2],
                "c3-8": [0, 1, 1],
                "c8-14": [0, 1, 2],
                "c8-15": [0, 1, 1],
            },
        },
        {
            "target_qubit": "q4",
            "ratio_map": {"c4-9": [0, 1, 1], "c4-10": [0, 1, 2]},
        },
    ]

    backend = init_backend()
    batch = BatchT1SpectrumWithBiasCoupler(backend)

    batch.set_experiment_options(
        param_path=r"/home/<USER>/work/PYQCAT/pyqcat-apps/app/batch_test/json_data/t1_spectrum_with_bias_coupler.json",
        working_points=working_points,
        exp_retry=0,
        use_simulator=True,
        record_batch=True,
        refresh_context=True,
        simulator_pass_rate=0.8,
    )

    print(f"The number of work point groups: {len(working_points)}")
    for i, wp_group in enumerate(working_points):
        target_qubit = wp_group["target_qubit"]
        ratio_map = wp_group["ratio_map"]
        coupler_names = list(ratio_map.keys())
        total_combinations = 1
        for coupler_name, (min_ratio, max_ratio, num_points) in ratio_map.items():
            total_combinations *= num_points
        print(
            f"  work point groups {i+1}: target={target_qubit}, couplers={coupler_names}, combinations={total_combinations}"
        )

    print("Start running the batch experiment...")

    batch.run()
    print("✓ Batch experiment complete!")
