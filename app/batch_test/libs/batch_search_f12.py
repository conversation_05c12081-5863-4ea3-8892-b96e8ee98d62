# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/15
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchSearchF12

    backend = init_backend()
    batch = BatchSearchF12(backend)
    batch.set_experiment_options(
        param_path=r"app\batch_test\json_data\search_f12.json",
        record_batch=True,
        simulator_pass_rate=0.9,
        use_simulator=True,
        refresh_context=True,
        flows=[
            "QubitFreqCalibration",
            "XpiDetection",
            "SingleShot",
            "QubitSpectrumF12",
        ],
        physical_units=["q1", "q2"],
    )
    batch.run()

    if batch.experiment_options.use_simulator is False:
        backend.save_chip_data_to_db(names=batch.run_options.pass_units)