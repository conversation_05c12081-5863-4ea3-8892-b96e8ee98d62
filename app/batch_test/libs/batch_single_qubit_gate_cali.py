# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/20
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchSingleQubitCalibration

    bad_qubits = [6, 21, 22, 24, 44, 52, 54, 59, 60, 64, 65]
    qubit_group = [f"q{i}" for i in range(1, 73) if i not in bad_qubits]

    backend = init_backend()

    batch = BatchSingleQubitCalibration(backend)
    batch.set_experiment_options(
        param_path=r"../json_data/sq_cali.json",
        qubit_groups=[qubit_group],
        exp_retry=1,
        use_simulator=False,
        record_batch=True,
        simulator_pass_rate=0,
        save_db=True,
        # quality_block_exp=["SingleShot_1"],
    )
    batch.run()
    backend.context_manager.extract_hot_data()
