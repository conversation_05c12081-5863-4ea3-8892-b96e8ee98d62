# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/15
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchTunable

    backend = init_backend()
    batch = BatchTunable(backend)
    batch.set_experiment_options(
        param_path=r"../json_data/cavity_batch.json",
        exp_retry=0,
        use_simulator=True,
        record_batch=True,
        simulator_pass_rate=0.7,
        open_cavity_power=False,
        save_db=True,
        parallel_qubit=[f"q{i}" for i in range(65, 73)],
        parallel_coupler=[
            ["c49-50", "c55-56", "c61-62", "c67-68"],
            ["c50-51", "c56-57", "c62-63", "c68-69"],
            ["c49-55", "c50-56", "c51-57", "c52-58"],
            ["c55-61", "c56-62", "c57-63", "c58-64"],
        ],
    )
    batch.run()
    batch.backend.update_context_from_hot_data()
