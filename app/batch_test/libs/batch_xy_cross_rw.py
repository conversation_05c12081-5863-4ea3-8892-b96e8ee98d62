# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/20
# __author:       <PERSON> Fang

"""
Use BatchExperiment method, develop XYCrossRabiWidth logic.
"""

if __name__ == '__main__':
    from app.config import init_backend
    from pyQCat.experiments.batch.batch_xy_cross_rabi_width import BatchXYCrossRabiWidth

    backend = init_backend()
    target_bias_map = {
        "q1": ["q2", "q3", "q4", "q7", "q8", "q9", "q10", "q11", "q13"],
        "q2": ["q1", "q3", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q3": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q4": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q5": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        # "q6": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q7": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q8": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q9": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q10": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q11": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q12": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
    }

    batch = BatchXYCrossRabiWidth(backend)
    batch.set_experiment_options(
        param_path=r"..\json_data\xy_cross_rabi_width.json",
        target_bias_map=target_bias_map,
        cali_freq_flag=True,
        update_target_strength=True,
        select_coe_threshold=0.001,

        cali_freq_flows=["QubitFreqCalibration"],
        strength_flows=["RabiScanWidth"],
        xy_cross_flows=["XYCrossRabiWidthOnce"],

        exp_retry=0,
        use_simulator=True,
        record_batch=True,
        quality_filter=False,
        use_config_unit=False,
        unified_dir=True,
        refresh_context=True,
    )
    batch.run()
