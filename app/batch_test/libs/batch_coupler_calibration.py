# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/10/28
# __author:       <PERSON><PERSON><PERSON>
# __refs:         https://document.qpanda.cn/docs/zdkyBYRexMiDwKA6



if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchCouplerCalibration

    backend = init_backend()

    batch = BatchCouplerCalibration(backend)
    batch.set_experiment_options(
        param_path=r"app\batch_test\json_data\simulator.json",
        coupler_idle_mode="middle", 
        probe_x_width=[30, 400]  # 30ns校准 ProbeQubit，400ns 校准 Coupler
        
    )
    batch.run()
