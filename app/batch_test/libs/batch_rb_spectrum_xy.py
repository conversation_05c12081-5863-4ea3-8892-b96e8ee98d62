# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/08/22
# __author:       <PERSON>

"""
BatchRBSpectrum extend xy cross flows
"""


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch.batch_rb_spectrum import TraversalPattern
    from pyQCat.experiments.batch.batch_rb_spectrum_xy_cross import BatchRBSpectrumXYCross

    backend = init_backend()
    batch = BatchRBSpectrumXYCross(backend)

    target_bias_map = {
        "q1": ["q2", "q3", "q4", "q7", "q8", "q9", "q10", "q11", "q13"],
        "q2": ["q1", "q3", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q3": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q4": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q5": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        # "q6": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q7": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q8": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q9": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q10": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q11": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
        "q12": ["q1", "q4", "q7", "q8", "q9", "q10", "q14"],
    }

    batch.set_experiment_options(
        param_path=r"../json_data/rb_spectrum.json",
        physical_units=[f"q{i}" for i in range(1, 13) if i not in [6]],
        flows=[
            "QubitFreqCalibration_0",
            "XpiDetection_0",
            "QubitFreqCalibration_1",
            "RabiScanAmp_0",
            "SingleShot_0",
            "DetuneCalibration_0",
            "RabiScanAmp_1",
            "AmpComposite_0",
            "AmpComposite_1",
            "SingleShot_1",
            "RBSingle_0",
        ],
        exp_retry=1,
        use_simulator=True,
        record_batch=True,
        # simulator_pass_rate=0.7,
        traversal_pattern=TraversalPattern.Expand,

        # Set XYCrossFlows options.
        run_xy_cross=True,
        target_bias_map=target_bias_map,
        cali_freq_flag=True,
        update_target_strength=True,
        select_coe_threshold=0.001,

        cali_freq_flows=["QubitFreqCalibration"],
        strength_flows=["RabiScanWidth"],
        xy_cross_flows=["XYCrossRabiWidthOnce"],
    )
    batch.run()
