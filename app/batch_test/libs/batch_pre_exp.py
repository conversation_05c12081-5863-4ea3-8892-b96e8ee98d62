# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/19
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from pathlib import Path
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchPreExp

    backend = init_backend()
    batch = BatchPreExp(backend)

    bus_list = [i for i in range(1, 13)]
    # bus_list = [1, 3]

    batch.set_experiment_options(
        param_path=str(
            Path(Path.cwd().parent, "../json_data", "pre_exp.json")
        ),
        exp_retry=0,
        ip="*************",
        port=10001,
        net_power=-15,
        low_net_power=-45,
        use_simulator=False,
        record_batch=True,
        refresh_context=True,
        simulator_pass_rate=1,
        low_power_point_label="sweetpoint",
        bus_list=bus_list,
        save_db=True,
    )
    batch.run()
