# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/20
# __author:       <PERSON><PERSON><PERSON>

from app.config import init_backend
from pyQCat.experiments.batch.batch_zz import BatchZZ


if __name__ == "__main__":
    zz_groups = [["q15q16", "q20q21", "q27q28", "q32q33", "q41q42", "q61q62"]]

    backend = init_backend()
    batch = BatchZZ(backend)
    batch.set_experiment_options(
        param_path=r"../json_data/swap.json",
        swap_state="10",
        fix_interaction_freq=False,
        auto_set_interaction_freq=True,
        pair_names=zz_groups,
        iter_swap_freq=[15, 20, 25],
        extra_widths=None,
        zz_freq_cali_flows=[
            "FixedPointCalibration_zz_qh",
            "FixedPointCalibration_zz_ql",
        ],
        zz_swap_cali_flows=["FixedSwapFreqCaliCoupler2_zz", "SwapOnce_zz"],
        zz_flows=["ZZTimingComposite"],
        exp_retry=0,
        use_simulator=False,
        record_batch=True,
        simulator_pass_rate=0,
        # quality_block_exp=["Swap"],
        save_db=False,
        # save_at_pass=False
    )
    batch.run()
