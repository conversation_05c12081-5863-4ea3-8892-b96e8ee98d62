# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/02/04
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchQubitDistortionT1New

    batch = BatchQubitDistortionT1New(init_backend())
    batch.set_experiment_options(
        param_path=r"..\json_data\qubit_distortion.json",
        exp_retry=0,
        use_simulator=True,
        simulator_pass_rate=0.8,
        record_batch=True,
        quality_filter=True,
        change_xpi_width=True,
        iir_distortion_flows=[
            "SingleShot",
            "VoltageDriftGradientCalibration",
            "DistortionT1CompositeNew",
        ],
        # rb_opt_flows=[
        #     "SingleShot",
        #     "VoltageDriftGradientCalibration",
        #     "DistortionPolesOpt"
        # ],
        physical_units=["q24", "q51"],
    )
    batch.run()
