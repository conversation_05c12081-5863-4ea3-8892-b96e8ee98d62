# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/02/05
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

from app.config import init_backend
from pyQCat.experiments.batch import BatchCouplerDistortionT1New

if __name__ == "__main__":

    batch = BatchCouplerDistortionT1New(init_backend())
    batch.set_experiment_options(
        param_path=r".\experiment_options\coupler_distortion_point.json",
        exp_retry=0,
        use_simulator=False,
        record_batch=True,
        quality_filter=True,
        coupler_shift_flows=["ZZShiftFixedPointCalibration_0"],
        qubit_cali_flows=[
            "QubitSpectrum_0",
            "XpiDetection_0",
            "QubitFreqCalibration_0",
        ],
        coupler_timing_flows=["CouplerXYZTimingByZZShift_0"],
        xpi_flows=["XpiDetection_1"],
        distortion_flows=["CouplerDistortionZZCompositeNew_0"],
        physical_units=["c4-10"],
    )
    batch.run()
