# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/20
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    import json
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchCZ_OnlineCali

    # time.sleep(10*60)
    # with open(r'E:\code\dp\pyqcat-visage-0.4.5\conf\qubit_pair_group_8.json', 'r') as f:
    with open(r'E:\code\dp\pyqcat-visage-0.4.5\conf\qubit_pair_group_passed.json', 'r') as f:
        zz_group = json.load(f)
    # zz_groups = list(zz_group.values())
    # group2 = zz_group['2']
    # group2.remove("q23q24")
    # group5 = zz_group['5']
    # group3.remove("q70q71")
    # group5.remove("q18q24")

    # gropu6

    backend = init_backend()
    batch = BatchCZ_OnlineCali(backend)
    batch.set_experiment_options(
        param_path=r"../json_data/batch_zz_cz_v2.json",
        swap_state="11",
        fix_interaction_freq=False,
        auto_set_interaction_freq=True,
        pair_names=[[
        "q4q10",
        "q15q21",
        "q41q47",
        "q50q56",
        "q62q68"
    ]],
        # iter_swap_freq=[20, 25],
        # extra_widths=[0.8333, 4.166],
        raw_flows=None,
        zz_flows=None,
        # cz_flows=None,
        cz_check_flows=None,
        cz_optimize_flows=None,
        cz_freq_cali_flows=[],
        exp_retry=0,
        use_simulator=False,
        record_batch=True,
        simulator_pass_rate=0,
        quality_block_exp=["Swap"],
        save_db=True
    )
    batch.run()
    backend.context_manager.extract_hot_data()
