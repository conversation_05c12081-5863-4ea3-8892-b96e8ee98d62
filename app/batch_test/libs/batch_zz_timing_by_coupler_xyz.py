# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/11/20
# __author:       SS Fang

import numpy as np


if __name__ == "__main__":
    from pyQCat.experiments.batch import BatchZZTimingByCouplerXYZ
    from app.config import init_backend

    backend = init_backend()
    batch = BatchZZTimingByCouplerXYZ(backend)

    C_list =  [
        "c20-21", "c20-26", "c8-9", "c21-27", "c22-28",
        "c17-23", "c10-16", "c11-17", "c28-29", "c14-20",
        "c16-17", "c9-15", "c15-16", "c21-22", "c14-15",
        "c8-14", "c22-23", "c16-22", "c26-27", "c10-11",
        "c9-10", "c27-28", "c23-29", "c15-21"
    ]

    batch.set_experiment_options(
        param_path=r"../json_data/zz_timing_by_coupler_xyz.json",
        physical_units=C_list,
        step_freq=20,
        select_zamp_rules=[0.55, 0.6, 0.66],
        use_simulator=False,
        simulator_pass_rate=0.8,
        refresh_context=True,
        save_db=False,
        record_batch=True,
        exp_retry=1,
        z_amp_list=np.linspace(0.15, 0.25, 10)

        # qubit_check_flows=[],
        # qubit_dcm_flows=[],
        # coupler_shift_flows=[],
        # coupler_timing_flows=["CouplerXYZTimingByZZShift"]
    )
    batch.run()
