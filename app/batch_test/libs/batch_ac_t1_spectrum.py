# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/15
# __author:       <PERSON><PERSON><PERSON>

from pathlib import Path

from app.config import init_backend
from pyQCat.experiments.batch import BatchACT1Spectrum

if __name__ == "__main__":

    backend = init_backend()
    batch = BatchACT1Spectrum(backend)
    batch.set_experiment_options(
        param_path=r"../json_data/ac_t1_spectrum.json",
        physical_units=["q31", "q32", "q33", "q34", "q35", "q36"],
        exp_retry=0,
        use_simulator=True,
        record_batch=True,
        refresh_context=True,
        simulator_pass_rate=0.7,
        t1_spec_flows=["T1Spectrum"],
        # t1_spec_flows=["PopulationLossSpectrum"],
        max_cali_flows=[
            # "SweetPointCalibration",
            # "SweetPointCalibrationVMin"
        ]
    )
    batch.run()
