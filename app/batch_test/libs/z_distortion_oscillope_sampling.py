#

from pyQCat.types import StandardContext
from pyQCat.experiments.single.z_distortion import ZExp


# 使用实验模式输出Z波形，用于Z畸变和时序采集。注意一次打开的通道数不要超过48个，
# 否则可能超出一体机服务器的内存，能打开的最多通道数视一体机服务器的配置。


if __name__ == "__main__":
    import asyncio

    from app.config import init_backend

    backend = init_backend()

    physical_unit = [
        'q1', 'q10', 'q100', 'q101', 'q102', 'q11', 'q12', 'q13', 'q14', 'q15', 'q16', 'q17', 'q18', 'q19',
        'q2', 'q20', 'q21', 'q22', 'q23', 'q24', 'q25', 'q26', 'q27', 'q28', 'q29', 'q3', 'q30', 'q31',
        'q32', 'q33', 'q34', 'q35', 'q36', 'q37', 'q38', 'q39', 'q4', 'q40', 'q41', 'q42', 'q43', 'q44',
        'q45', 'q46', 'q47', 'q48', 'q49', 'q5', 'q50', 'q51', 'q52', 'q53', 'q54', 'q55', 'q56', 'q57',
        'q58', 'q59', 'q6', 'q60', 'q61', 'q62', 'q63', 'q64', 'q65', 'q66', 'q67', 'q68', 'q69', 'q7',
        'q70', 'q71', 'q72', 'q73', 'q74', 'q75', 'q76', 'q77', 'q78', 'q79', 'q8', 'q80', 'q81', 'q82',
        'q83', 'q84', 'q85', 'q86', 'q87', 'q88', 'q89', 'q9', 'q90', 'q91', 'q92', 'q93', 'q94', 'q95',
        'q96', 'q97', 'q98', 'q99', 'c1-7', 'c10-16', 'c10-17', 'c100-105', 'c100-106', 'c101-106',
        'c101-107', 'c102-107', 'c11-17', 'c11-18', 'c12-18', 'c13-19', 'c14-19', 'c14-20', 'c15-20',
        'c15-21', 'c16-21', 'c16-22', 'c17-22', 'c17-23', 'c18-23', 'c18-24', 'c19-25', 'c19-26', 'c2-7',
        'c2-8', 'c20-26', 'c20-27', 'c21-27', 'c21-28', 'c22-28', 'c22-29', 'c23-29', 'c23-30', 'c24-30',
        'c25-31', 'c26-31', 'c26-32', 'c27-32', 'c27-33', 'c28-33', 'c28-34', 'c29-34', 'c29-35', 'c3-8',
        'c3-9', 'c30-35', 'c30-36', 'c31-37', 'c31-38', 'c32-38', 'c32-39', 'c33-39', 'c33-40', 'c34-40',
        'c34-41', 'c35-41', 'c35-42', 'c36-42', 'c37-43', 'c38-43', 'c38-44', 'c39-44', 'c39-45', 'c4-10',
        'c4-9', 'c40-45', 'c40-46', 'c41-46', 'c41-47', 'c42-47', 'c42-48', 'c43-49', 'c43-50', 'c44-50',
        'c44-51', 'c45-51', 'c45-52', 'c46-52', 'c46-53', 'c47-53', 'c47-54', 'c48-54', 'c49-55', 'c5-10',
        'c5-11', 'c50-55', 'c50-56', 'c51-56', 'c51-57', 'c52-57', 'c52-58', 'c53-58', 'c53-59', 'c54-59',
        'c54-60', 'c55-61', 'c55-62', 'c56-62', 'c56-63', 'c57-63', 'c57-64', 'c58-64', 'c58-65', 'c59-65',
        'c59-66', 'c6-11', 'c6-12', 'c60-66', 'c61-67', 'c62-67', 'c62-68', 'c63-68', 'c63-69', 'c64-69',
        'c64-70', 'c65-70', 'c65-71', 'c66-71', 'c66-72', 'c67-73', 'c67-74', 'c68-74', 'c68-75', 'c69-75',
        'c69-76', 'c7-13', 'c7-14', 'c70-76', 'c70-77', 'c71-77', 'c71-78', 'c72-78', 'c73-79', 'c74-79',
        'c74-80', 'c75-80', 'c75-81', 'c76-81', 'c76-82', 'c77-82', 'c77-83', 'c78-83', 'c78-84', 'c79-85',
        'c79-86', 'c8-14', 'c8-15', 'c80-86', 'c80-87', 'c81-87', 'c81-88', 'c82-88', 'c82-89', 'c83-89',
        'c83-90', 'c84-90', 'c85-91', 'c86-91', 'c86-92', 'c87-92', 'c87-93', 'c88-93', 'c88-94', 'c89-94',
        'c89-95', 'c9-15', 'c9-16', 'c90-95', 'c90-96', 'c91-97', 'c91-98', 'c92-98', 'c92-99', 'c93-100',
        'c93-99', 'c94-100', 'c94-101', 'c95-101', 'c95-102', 'c96-102', 'c97-103', 'c98-103', 'c98-104',
        'c99-104', 'c99-105'
    ]

    # physical_unit = ['q51', 'q52', 'q53', 'q54',]

    #string = "q49,q50,q51,q52,q53,q54,q55,q56,q57,q58,q59,q60,q61,q62,q63,q64,q65,q66,q67,q68,q69,q70,q71,q72,q73,q74,q75,q76,q77,q78,q79,q80,q81,q82,q83,q84,q85,q86,q87,q88,q89,q90,q91,q92,q93,q94,q95,q96"
    # string = "q73,c25-31,c26-32,c26-31,c27-33,c27-32,c28-34,c28-33,c29-35,c29-34,c30-36,c30-35,c31-37,c31-38,c32-39,c32-38,c33-39,c33-40,c34-40,c34-41,c35-42,c35-41,c36-42,c37-43,c38-43,c38-44,c39-45,c39-44,c40-46,c40-45,c41-47,c41-46,c42-47,c42-48,c43-50,c43-49,c44-51,c44-50,c45-51,c45-52,c46-53,c46-52,c47-53,c47-54,c48-54"
    # physical_unit = string.split(",")
    print(len(physical_unit))

    context_params = {
        "name": StandardContext.URM.value,
        "physical_unit": physical_unit,
        "readout_type": "",
    }

    while 1:
        context = backend.context_manager.generate_context(**context_params)
        exp = ZExp.from_experiment_context(context)

        z_pulse_params = {
            "time": 65000,
            "amp": 0.00001,
        }
        xy_pulse_params = {
            "time": 65000,
            "amp": 0.00001,
        }
        exp.set_experiment_options(
            z_pulse_params=z_pulse_params,
            xy_pulse_params=xy_pulse_params,
            loop=100,
            repeat=1000,
            period=400,
        )

        asyncio.run(exp.run_experiment())
