# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/14
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch.batch_rb_spectrum import (
        BatchRBSpectrum,
        TraversalPattern,
    )

    backend = init_backend()
    batch = BatchRBSpectrum(backend)
    batch.set_experiment_options(
        param_path=r"app/batch_test/json_data/rb_spectrum.json",
        physical_units=[f"q{i}" for i in range(1, 73)],
        flows=[
            "QubitFreqCalibration_0",
            "XpiDetection_0",
            "QubitFreqCalibration_1",
            "RabiScanAmp_0",
            "SingleShot_0",
            "DetuneCalibration_0",
            "RabiScanAmp_1",
            "AmpComposite_0",
            "AmpComposite_1",
            "SingleShot_1",
            "RBSingle_0",
        ],
        exp_retry=1,
        use_simulator=True,
        record_batch=True,
        simulator_pass_rate=0.7,
        traversal_pattern=TraversalPattern.Expand
    )
    batch.run()
