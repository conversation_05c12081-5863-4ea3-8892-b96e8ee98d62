# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/20
# __author:       <PERSON><PERSON><PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchCouplerRefer

    coupler_groups = [
        ['c1-7', 'c3-9']
    ]

    qubit_groups = [
        ["q1", "q7", "q3", "q9"]
    ]

    backend = init_backend()

    batch = BatchCouplerRefer(backend)
    batch.set_experiment_options(
        param_path=r"..\json_data\voltage_calibration.json",
        qubit_groups=qubit_groups,
        coupler_groups=coupler_groups,
        # coupler_q_flows=[],
        # coupler_c_flows=[],
        exp_retry=1,
        use_simulator=False,
        record_batch=True,
        simulator_pass_rate=0,
        save_db=True,
        quality_block_exp=["SingleShot_1"],
    )
    batch.run()
    backend.context_manager.extract_hot_data()
