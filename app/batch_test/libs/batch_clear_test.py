# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/04/02
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchClearExp

    _backend = init_backend()
    batch = BatchClearExp(_backend)
    batch.set_experiment_options(
        param_path=r".\clear_batch.json",
        exp_retry=0,
        use_simulator=False,
        record_batch=True,
        refresh_context=True,
        physical_units=["q75"],
        use_config_unit=False,
        simulator_pass_rate=1,
        save_db=False,
        clear_flow=[
            "ReadoutFreqCalibrate",
            "SingleShot_0",
            "PhotonScanReadoutFreq",
            "SingleShot_1",
            "PhotonNumMeasVsAmp",
            "DePhaseRamseyComposite",
            "NMClearParamsBoth",
        ],
        quality_block_exp=["SingleShot_0", "SingleShot_1"],
    )
    batch.run()

    # format  init qubit params
    # tau 数据库内读取波形的长度, 单位为us
    # tkick 期望的重置step，一般为1/kappa
    # 频率单位都是MHz，传入batch都需要成2pi，转成rad/s
    # data = {}
    # data.update({
    #     "q75": {
    #         "cavity_decay": 1.07,
    #         "idle_2chi": -1.103 * 2,
    #         "t2_echo": 15.267,
    #         "QCavity_couple": 182.554,
    #         "anharm": -246.591,
    #         "idle_freq": 4860.515,
    #         "bare_cavity": 7435.162,
    #         "tau": 1,
    #         "tkick": 0.1
    #     }
    # })
    #
    # with open(r"./qubit_data.json", "w", encoding="utf-8") as f:
    #     json.dump(data, f, indent=4)
