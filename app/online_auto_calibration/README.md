# online-auto-calibration

## 1.项目进展

第一阶段项目 | 进度 |
------------|-----
Qubit 和 Coupler的电压校准脚本 | ✅ |
Qubit的读取判据刷新脚本 | 🔲 | 
单比特门自动化校准脚本 | ✅| 
读取参数以及读取工作点自动化校准脚本 |✅|
CZ门参数自动化校准脚本 | ✅|
所有校准流程串联的自动化测试脚本 | ✅|
收集各个校准阶段的结果，进行汇总，方便节点间传参和离线分析 | 🔲|

第二阶段项目 | 进度
------------|------
加入自动化校准开始和结束的信号，方便storm以及司南订阅 | 🔲
梳理并优化BatchExperiment，并将其转为monster中 | 🔲


## 2.使用方法
### 2.1 运行
（1）在线自动化校准使用配置文件进行参数设置，支持command line一键运行；当前版本暂不支持脚本传递参数，如果需要更改校准过程中的参数，
需要到各个对应的配置文件下进行修改。  
全流程运行：
```angular2html
例如：
cd C:\Project\pyqcat-monster\scripts\online_auto_calibration
python runner.py
```
运行单独某个脚本
```angular2html
cd C:\Project\pyqcat-monster\scripts\online_auto_calibration
python calibrate_single_gate.py
```

（2）正常情况下，只需要根据芯片参数对conf目录和templates目录下的相关文件进行编辑，然后直接运行online_auto_calibration/runner.py即可。  

（3）当前版本暂未对各个校准流程的结果做详细统计处理，采用log filter的办法来提取重要信息，所有被[AutoCalibration]包裹的log信息都会被写入
到一个名为online_calibration_log.log的文档中，由于时间仓促，暂未实现更多的功能，只是把每个校准程序的通过/失败情况进行了一个记录，
方便后续人工介入的时候进行快速的有效信息提取。默认这个log存放的路径就是用户的visage conf中log的路径。如果记不住，可以在程序运行的
log中寻找，一般不管是一键运行还是单独脚本运行，都会打印一句：Online auto calibration log save at: xxx

### 2.2 配置文件
- **conf目录**：主要是一些系统运行的基础配置文件：
  - batch_conf.json：
    用于配置各个校准脚本中的BatchExperiment参数，它通过BatchExperiment基类中的save_options接口生成，
    所以每个Batch的参数都可以支持；需要注意的是：
    - 该文件一般分成 2层结构，第一层默认为校准的脚本名称 + “_options”后缀，代表某个校准的脚本程序；
    - 第二层默认是各个脚本中的函数名，代表某个校准函数；  
    例如：
    ```
    {
      "calibrate_single_gate_options":{
        "check_gate_params_drift":{...}
        "calibrate_single_gate":{...}
      }
    }
    ```  
    表示这是calibrate_single_gate.py中的Batch配置项，分别给check_gate_params_drift函数和calibrate_single_gate
    函数做参数传递；
  - best_ever_thresholds.json： 历史最佳阈值的存储文件，在程序初始化的时候会被加载，可以通过online_auto_calibration
  /utilities.py中的 update_local_thresholds接口来选定某个账户的数据，更新历史最佳阈值；
  - calibration_config.conf：用于各个校准流程的一些配置参数设置，比如历史最佳阈值的容忍度，用户登录visage账户的参数等
  - online_conf.json：storm上线的配置文件，和visage的context参数类似，可以参考Backend的参数来对比没一项目的含义；
  - qubit_pair_group_passed.json：两比特门的分组文件；
