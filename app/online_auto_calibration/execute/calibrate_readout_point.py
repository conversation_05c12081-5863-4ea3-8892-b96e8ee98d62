# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/12
# __author:       <PERSON><PERSON><PERSON>

"""Calibrate qubit readout point parameters."""

import json
import pathlib
from typing import List, Dict

from loguru import logger

from app.online_auto_calibration.manage.records import BaseRecords
from app.online_auto_calibration.utilities import (
    filter_retried_experiment,
    get_nested_dict_value,
)
from app.online_auto_calibration.utilities import (
    get_online_params,
    get_backend,
    get_thresholds,
    get_qubit_groups,
    parse_calibration_config,
    get_batch_options,
    get_discriminator_names,
    emit_calibration_signal,
)
from pyQCat.executor import Backend
from pyQCat.experiments.batch import Batch<PERSON><PERSON><PERSON>, BatchSearchReadoutPoint

__MODULE__ = pathlib.Path(__file__).stem


def quality_assessment(
    records: Dict, thresholds: Dict, tolerance: float
):
    drift_qubits = []
    clean_records = filter_retried_experiment(
        records, ["SingleShot"], ["analysis_data"]
    )
    analysis_datas: Dict = get_nested_dict_value(
        clean_records, ["SingleShot", "analysis_data"]
    )
    if analysis_datas:
        for qubit, analysis_data in analysis_datas.items():
            current_fidelity = get_nested_dict_value(
                analysis_data, ["result", "dcm", "fidelity"]
            )
            if current_fidelity:
                threshold_fidelity = get_nested_dict_value(thresholds, ["dcm", qubit])
                if not threshold_fidelity:
                    logger.error(f"Thresholds lack {qubit} fidelity data.")
                    continue
                current_f0, current_f1 = current_fidelity
                threshold_f0, threshold_f1 = threshold_fidelity
                if current_f0 < threshold_f0 - tolerance:
                    logger.warning(
                        f"[AutoCalibration]{qubit} readout fidelity drift : F0={threshold_f0}->{current_f0}"
                    )
                    if qubit not in drift_qubits:
                        drift_qubits.append(qubit)
                if current_f1 < threshold_f1 - tolerance:
                    logger.warning(
                        f"[AutoCalibration]{qubit} readout fidelity drift : F1={threshold_f1}->{current_f1}"
                    )
                    if qubit not in drift_qubits:
                        drift_qubits.append(qubit)
            else:
                # remove raise exception logic
                logger.error(
                    f"[AutoCalibration]{qubit} readout fidelity exp execute error: "
                    f"because f{qubit} has no readout fidelity."
                )
                drift_qubits.append(qubit)
    else:
        logger.error(
            "[AutoCalibration]Parallel SingleShot has no analysis data in records.json."
        )
    return drift_qubits


def _filter_qubit_by_threshold(
    unit: str, result: Dict, thresholds: Dict, tolerance: float
) -> str:
    calibrated_f0, calibrated_f1 = result.get("f0", 0), result.get("f1", 0)
    threshold_f0, thresholds_f1 = get_nested_dict_value(
        thresholds, ["dcm", unit], default=1
    )
    if (
        calibrated_f0 < threshold_f0 - tolerance
        and calibrated_f1 < thresholds_f1 - tolerance
    ):
        return unit
    return ""


def handle_readout_point_calibration_result(
    batch: BatchSearchReadoutPoint,
    thresholds: Dict,
    tolerance: float
):
    batch.run_options.pass_units = list(batch.run_options.best_fidelity_map.keys())
    batch.run_options.fail_units = [
        unit
        for unit in batch.experiment_options.parallel_unit
        if unit not in batch.run_options.pass_units
    ]
    absolute_pass_units = []
    for unit, params in batch.run_options.best_fidelity_map.items():
        pass_unit = _filter_qubit_by_threshold(unit, params, thresholds, tolerance)
        if pass_unit:
            absolute_pass_units.append(pass_unit)

    for unit, params in batch.run_options.best_fidelity_map.items():
        json_name = f"{unit}-{params.get('amp')}.json"
        with open(str(pathlib.Path(batch.run_options.dirs, json_name)), mode="r") as f:
            bit_data = json.load(f)
        qubit = batch.context_manager.chip_data.get_physical_unit(unit)
        new_qubit = qubit.from_dict(bit_data)
        new_qubit.save_data()
        batch.context_manager.chip_data.cache_qubit.update({unit: new_qubit})

    logger.info(f"[AutoCalibration]Absolute pass units: {absolute_pass_units}")

    return absolute_pass_units


def check_readout_drift(
    backend: Backend,
    thresholds: Dict,
    tolerance: float,
    result_record: BaseRecords,
    **kwargs,
) -> List[str]:
    single_shot_batch = BatchRunner(backend)
    single_shot_batch.set_experiment_options(**kwargs)
    single_shot_batch.set_experiment_options(batch_label="CheckReadoutDrift")
    single_shot_batch.set_experiment_options(
        publish_to_storm_units=get_discriminator_names(
            single_shot_batch.experiment_options.physical_units
        )
    )
    # Shq 2024/01/30
    # Notify storm some messages.
    with emit_calibration_signal(
        single_shot_batch.experiment_options.batch_label,
        need_calibrated_units=single_shot_batch.experiment_options.publish_to_storm_units,
        publish=single_shot_batch.experiment_options.publish_to_storm,
    ):
        single_shot_batch.run()
    result_record.set_mongo_records(single_shot_batch.batch_records, "readout", send=True)
    return quality_assessment(
        single_shot_batch.batch_records, thresholds, tolerance
    )


def calibrate_readout_point(
    backend: Backend,
    thresholds: Dict,
    tolerance: float,
    result_record: BaseRecords,
    **kwargs,
):
    calibrate_readout_point_batch = BatchSearchReadoutPoint(backend)
    calibrate_readout_point_batch.set_experiment_options(**kwargs)
    calibrate_readout_point_batch.set_experiment_options(
        batch_label="CalibrateReadoutPoint"
    )
    calibrate_readout_point_batch.set_experiment_options(
        publish_to_storm_units=get_discriminator_names(
            calibrate_readout_point_batch.experiment_options.physical_units
        )
        + calibrate_readout_point_batch.experiment_options.physical_units
    )
    # Shq 2024/01/30
    # Notify storm some messages.
    with emit_calibration_signal(
        calibrate_readout_point_batch.experiment_options.batch_label,
        calibrate_readout_point_batch.experiment_options.publish_to_storm_units,
        calibrate_readout_point_batch.experiment_options.publish_to_storm,
    ):
        calibrate_readout_point_batch.run()
    result_record.set_mongo_records(calibrate_readout_point_batch.batch_records, "readout")
    pass_units = handle_readout_point_calibration_result(
        calibrate_readout_point_batch, thresholds, tolerance
    )
    return pass_units


def run(
    backend: Backend,
    history_best_thresholds: Dict,
    qubit_list: List[str],
    tolerance: float,
    checker_config: Dict,
    calibration_config: Dict,
    result_record: BaseRecords,
):
    logger.info(
        f"[AutoCalibration]all {len(qubit_list)} qubits will enter readout point calibration..."
    )

    if qubit_list:
        checker_config.update({"physical_units": qubit_list})

    need_calibrated_qubits = check_readout_drift(
        backend,
        history_best_thresholds,
        tolerance,
        result_record,
        **checker_config,
    )

    if need_calibrated_qubits:
        logger.info(
            f"[AutoCalibration]The readout point of qubits:{need_calibrated_qubits} "
            f"bits drifted into the readout point automation calibration."
        )
        calibration_config.update({"parallel_unit": need_calibrated_qubits})
        pass_qubits = calibrate_readout_point(
            backend=backend,
            thresholds=history_best_thresholds,
            tolerance=tolerance,
            result_record=result_record,
            **calibration_config,
        )
        failed_qubits = [
            qubit for qubit in need_calibrated_qubits if qubit not in pass_qubits
        ]
        if pass_qubits:
            logger.info(
                f"[AutoCalibration]Calibrate readout point done. Passed qubits: {pass_qubits}"
            )
        if failed_qubits:
            logger.warning(
                f"[AutoCalibration]Calibrate readout point done. Failed qubits: {failed_qubits}"
            )
    else:
        logger.info(
            "[AutoCalibration]Normal readout point for all qubits, no calibration required."
        )


if __name__ == "__main__":
    # initialize parameters.
    calibration_conf = parse_calibration_config(r"./conf/calibration_config.conf")
    account = calibration_conf.get("account")
    calibration_params = calibration_conf.get("readout_point")
    batch_options = get_batch_options(
        r"./conf/batch_conf.json", indicate_one=__MODULE__
    )
    system_params = get_online_params(account.username, account.password)
    my_backend = get_backend(system_params)
    reference = get_thresholds()

    # TODO Unify the data structure of qubits to a dictionary.
    test_qubits = get_qubit_groups()["1"]
    path = my_backend.context_manager.config.system.local_root or "./"
    result_records = BaseRecords(my_backend.context_manager.chip_data, path)
    # run all flow.
    run(
        my_backend,
        reference,
        test_qubits,
        float(calibration_params.history_offset),
        batch_options.get(check_readout_drift.__name__),
        batch_options.get(calibrate_readout_point.__name__),
        result_record=result_records,
    )
