# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/15
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

"""Calibrate CZ gate."""
import pathlib
from typing import Dict, List, Union

from loguru import logger

from app.online_auto_calibration.manage.records import BaseRecords
from app.online_auto_calibration.utilities import (
    emit_calibration_signal,
    filter_bad_qubit_pairs,
    filter_retried_experiment,
    get_backend,
    get_batch_options,
    get_nested_dict_value,
    get_online_params,
    get_qubit_pair_groups,
    get_thresholds,
    parse_calibration_config,
    pretty_show_dict,
)
from pyQCat.errors import AnalysisError
from pyQCat.executor import Backend
from pyQCat.executor.batch.tools import divide_cz_parallel_group
from pyQCat.experiments.batch import BatchRunner
from pyQCat.experiments.batch_experiment import BatchExperiment
from pyQCat.log import pyqlog

__MODULE__ = pathlib.Path(__file__).stem


class BatchCZCalibration(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.physical_units = None
        options.quality_block_exp = ["Swap"]
        options.flows = [
            # "FixedPointCalibration_cz_qh",
            # "FixedPointCalibration_cz_ql",
            "Swap",
            "LeakagePre",
            "LeakageAmp",
            "CPhaseTMSE",
            # "ConditionalPhaseFixed",
            "SQPhaseTMSE",
            # "XEBMultiple_1",
            # "NMXEBMultiple",
            "XEBMultiple_2",
        ]
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.pass_units = None
        options.fail_units = None
        return options

    def _run_batch(self):
        good_units = []
        working_units = self.experiment_options.physical_units

        if working_units:
            pass_units = self._run_flow(
                flows=self.experiment_options.flows,
                physical_units=working_units,
            )
            if pass_units:
                for unit in pass_units:
                    good_units.append(unit)

        bad_units = [unit for unit in working_units if unit not in good_units]

        self.run_options.pass_units = good_units
        self.run_options.fail_units = bad_units

        pyqlog.info(f"pass units ({good_units}) | fail units ({bad_units})")


def quality_assessment(records: Dict, thresholds: Union[Dict, float]):
    drift_pairs = []

    clean_records = filter_retried_experiment(
        records, ["XEBMultiple"], ["analysis_data"]
    )

    analysis_datas: Dict = get_nested_dict_value(
        clean_records, ["XEBMultiple", "analysis_data"]
    )

    if analysis_datas:
        for pair, analysis_data in analysis_datas.items():
            current_fidelity = get_nested_dict_value(analysis_data, ["result", "f_xeb"])
            # Shq 2024/02/02
            # fixed bug: Some qubit pairs have bad quality but high XEB fidelity.
            # check quality first.
            current_quality = analysis_data.get("quality", "")
            if "bad" in current_quality or "abnormal" in current_quality:
                if pair not in drift_pairs:
                    drift_pairs.append(pair)
                    logger.warning(
                        f"[AutoCalibration]{pair} xeb fidelity drift: "
                        f"Bad quality: {current_quality}, current_fidelity: {float(current_fidelity)}."
                    )
                continue
            # then check fidelity.
            if current_fidelity:
                current_fidelity = float(current_fidelity)
                if isinstance(thresholds, Dict):
                    threshold_fidelity = get_nested_dict_value(
                        thresholds, ["cz_gate", pair]
                    )
                else:
                    threshold_fidelity = thresholds

                if not threshold_fidelity:
                    logger.error(f"Thresholds lack {pair} fidelity data.")
                    continue
                    # raise ValueError(f"Thresholds lack {pair} fidelity data.")

                if current_fidelity < threshold_fidelity:
                    logger.warning(
                        f"[AutoCalibration]{pair} xeb fidelity drift : "
                        f"{threshold_fidelity}->{current_fidelity}"
                    )
                    if pair not in drift_pairs:
                        drift_pairs.append(pair)
            else:
                logger.error(f"{pair} has no xeb fidelity.")
                # raise AnalysisError(f"{pair} has no xeb fidelity.")
    else:
        logger.error("Parallel SingleShot has no analysis data in records.json.")
        # raise AnalysisError("Parallel SingleShot has no analysis data in records.json.")
    return drift_pairs


def quality_assessment_check(
    records: Dict,
    thresholds: Union[Dict, float],
    tolerance: float,
    nm_tolerance: float,
):
    need_nm_pairs = []
    need_cz_flow_pairs = []
    clean_records = filter_retried_experiment(
        records, ["XEBMultiple"], ["analysis_data"]
    )

    analysis_datas: Dict = get_nested_dict_value(
        clean_records, ["XEBMultiple", "analysis_data"]
    )

    if analysis_datas:
        for pair, analysis_data in analysis_datas.items():
            current_fidelity = get_nested_dict_value(analysis_data, ["result", "f_xeb"])
            # Shq 2024/02/02
            # fixed bug: Some qubit pairs have bad quality but high XEB fidelity.
            # check quality first.
            current_quality = analysis_data.get("quality", "")
            if "bad" in current_quality or "abnormal" in current_quality:
                if pair not in need_cz_flow_pairs:
                    need_cz_flow_pairs.append(pair)
                    logger.warning(
                        f"[AutoCalibration]{pair} xeb fidelity drift, waiting for cz flows calibration: "
                        f"Bad quality: {current_quality}, current_fidelity: {float(current_fidelity)}."
                    )
                continue
            # then check fidelity.
            if current_fidelity:
                current_fidelity = float(current_fidelity)
                if isinstance(thresholds, Dict):
                    threshold_fidelity = get_nested_dict_value(
                        thresholds, ["cz_gate", pair]
                    )
                else:
                    threshold_fidelity = thresholds

                if not threshold_fidelity:
                    logger.error(f"Thresholds lack {pair} fidelity data.")
                    continue
                    # raise ValueError(f"Thresholds lack {pair} fidelity data.")
                if current_fidelity < threshold_fidelity - tolerance:
                    if current_fidelity < nm_tolerance:
                        logger.warning(
                            f"[AutoCalibration]{pair} xeb fidelity drift, waiting for cz flows calibration: "
                            f"{threshold_fidelity}-->{current_fidelity}"
                        )
                        if pair not in need_cz_flow_pairs:
                            need_cz_flow_pairs.append(pair)
                    else:
                        logger.warning(
                            f"[AutoCalibration]{pair} xeb fidelity drift, waiting for NM optimization : "
                            f"{threshold_fidelity}-->{current_fidelity}"
                        )
                        if pair not in need_nm_pairs:
                            need_nm_pairs.append(pair)
            else:
                logger.error(f"{pair} has no xeb fidelity.")
                # raise AnalysisError(f"{pair} has no xeb fidelity.")
    else:
        logger.error("Parallel CZ flow has no analysis data in records.json.")
        # raise AnalysisError("Parallel SingleShot has no analysis data in records.json.")
    return need_nm_pairs, need_cz_flow_pairs


def calibrate_nm_xeb_flow(
    backend: Backend, threshold: float, result_record: BaseRecords, **kwargs
) -> List[str]:
    xeb_batch = BatchRunner(backend)
    xeb_batch.set_experiment_options(batch_label="CalibrateCZ")
    xeb_batch.set_experiment_options(**kwargs)
    # Shq 2024/01/30
    # Notify storm some messages.
    with emit_calibration_signal(
        "Calibrate CZ gate with NM-XEB",
        xeb_batch.experiment_options.physical_units,
        xeb_batch.experiment_options.publish_to_storm,
    ):
        xeb_batch.run()

    result_record.set_mongo_records(xeb_batch.batch_records, "cz_gate", send=True)
    try:
        result = quality_assessment(xeb_batch.batch_records, thresholds=threshold)
    except AnalysisError as error:
        logger.error(f"[AutoCalibration]{error}\n")
        result = xeb_batch.experiment_options.get("physical_units")
    return result


def check_xeb_drift(
    backend: Backend,
    thresholds: Dict,
    tolerance: float,
    nm_tolerance: float,
    result_record: BaseRecords,
    **kwargs,
) -> tuple[List[str], List[str]]:
    nm_xeb_batch = BatchRunner(backend)
    nm_xeb_batch.set_experiment_options(batch_label="CheckXebDrift")
    nm_xeb_batch.set_experiment_options(**kwargs)
    nm_xeb_batch.run()
    result_record.set_mongo_records(nm_xeb_batch.batch_records, "cz_gate", send=True)
    try:
        result = quality_assessment_check(
            nm_xeb_batch.batch_records,
            thresholds,
            tolerance,
            nm_tolerance,
        )
    except AnalysisError as error:
        logger.error(f"[AutoCalibration]{error}\n")
        result = nm_xeb_batch.experiment_options.get("physical_units")
    return result


def calibrate_cz_flow(
    backend: Backend,
    physical_units: List[str],
    threshold: float,
    result_record: BaseRecords,
    **kwargs,
):
    calibrate_cz_batch = BatchCZCalibration(backend)
    kwargs.update({"physical_units": physical_units})
    calibrate_cz_batch.set_experiment_options(batch_label="CalibrateCZ")
    calibrate_cz_batch.set_experiment_options(**kwargs)
    # Shq 2024/01/30
    # Notify storm some messages.
    with emit_calibration_signal(
        "Calibrate CZ gate with CZ flows",
        physical_units,
        calibrate_cz_batch.experiment_options.publish_to_storm,
    ):
        calibrate_cz_batch.run()
    result_record.set_mongo_records(calibrate_cz_batch.batch_records, "cz_gate", send=True)
    try:
        result = quality_assessment(
            calibrate_cz_batch.batch_records,
            thresholds=threshold,
        )
    except AnalysisError as error:
        logger.error(f"[AutoCalibration]{error}\n")
        result = calibrate_cz_batch.experiment_options.get("physical_units")
    return result


def run(
    backend: Backend,
    history_best_thresholds: Dict,
    tolerance: float,
    threshold: float,
    nm_check_threshold: float,
    pair_dict: Dict[str, List[str]],
    checker_config: Dict,
    nm_calibration_config: Dict,
    cz_calibration_config: Dict,
    result_record: BaseRecords,
    bad_qubits: List[str] = None,
    bad_couplers: List[str] = None,
):
    logger.info(
        f"[AutoCalibration]CZ gate calibration start:\n"
        f"{pretty_show_dict(pair_dict)}"
    )
    qubit_pair_failed_result = {}
    all_need_cz_flow_pairs = []
    for group_index, pair_list in pair_dict.items():
        logger.info(
            f"[AutoCalibration]Group{group_index}, {len(pair_list)} qubit pairs will enter "
            f"cz gate drift check."
        )

        if pair_list:
            checker_config.update({"physical_units": pair_list})

        need_nm_opt_pairs, need_cz_flow_pairs = check_xeb_drift(
            backend,
            history_best_thresholds,
            tolerance,
            nm_check_threshold,
            result_record,
            **checker_config,
        )

        if bad_couplers or bad_qubits:
            logger.info("[AutoCalibration]Filter bad qubits for next calibration step.")
            need_nm_opt_pairs = filter_bad_qubit_pairs(
                bad_qubits=bad_qubits,
                bad_couplers=bad_couplers,
                qubit_pairs=need_nm_opt_pairs,
            )
            need_cz_flow_pairs = filter_bad_qubit_pairs(
                bad_qubits=bad_qubits,
                bad_couplers=bad_couplers,
                qubit_pairs=need_cz_flow_pairs,
            )

        if need_nm_opt_pairs:
            logger.info(
                f"[AutoCalibration]Need nm_opt_pairs:\n"
                f"{pretty_show_dict({group_index: need_nm_opt_pairs})}"
            )

            nm_calibration_config.update({"physical_units": need_nm_opt_pairs})

            # bugfix zyc: pass pairs save to database
            fail_pairs = calibrate_nm_xeb_flow(
                backend=backend,
                threshold=threshold,
                result_record=result_record,
                **nm_calibration_config,
            )
            pass_pairs = [pair for pair in need_nm_opt_pairs if pair not in fail_pairs]
            backend.save_chip_data_to_db(pass_pairs)

            need_cz_flow_pairs += fail_pairs
        else:
            logger.info(
                "[AutoCalibration]no need for nm calibration: xeb quality bad, or xeb fidelity are comparable "
                "with history thresholds or small than NM tolerance."
            )

        all_need_cz_flow_pairs += need_cz_flow_pairs

    if all_need_cz_flow_pairs:
        # Failed qubit pairs need to be regrouped.
        regrouped_qubit_pair_groups = divide_cz_parallel_group(
            all_need_cz_flow_pairs, backend.context_manager.chip_data
        )
        for group_index, pair_list in regrouped_qubit_pair_groups.items():
            logger.info(
                f"[AutoCalibration]Need cz_flow_pairs:\n"
                f"{pretty_show_dict({group_index: pair_list})}"
            )
            cz_calibration_config.update({"physical_units": pair_list})
            pass_pairs = calibrate_cz_flow(
                backend=backend,
                threshold=threshold,
                result_record=result_record,
                **cz_calibration_config,
            )

            # bugfix zyc: pass pairs save to database
            if pass_pairs:
                backend.save_chip_data_to_db(pass_pairs)

            failed_pairs = [pair for pair in pair_list if pair not in pass_pairs]
            if pass_pairs:
                logger.info(
                    f"[AutoCalibration]Calibrate cz flow done. Passed pairs: {pass_pairs}"
                )

            if failed_pairs:
                logger.warning(
                    f"[AutoCalibration]Calibrate cz flow done. Failed pairs: {failed_pairs}"
                )
                qubit_pair_failed_result.setdefault(group_index, failed_pairs)
    else:
        logger.info(
            "[AutoCalibration]Normal xeb fidelity for all pairs after NM optimization."
        )
    result_record.save_result_to_db(item_type="cz_gate")
    logger.info(
        f"[AutoCalibration]CZ gate calibration completed, failed pairs:\n"
        f"{pretty_show_dict(qubit_pair_failed_result)}\n"
    )
    return qubit_pair_failed_result


if __name__ == "__main__":
    # initialize parameters.
    calibration_conf = parse_calibration_config(r"../conf/calibration_config.conf")
    account = calibration_conf.get("account")
    calibration_params = calibration_conf.get("cz_gate")
    batch_options = get_batch_options(
        r"../conf/batch_conf.json", indicate_one=__MODULE__
    )
    system_params = get_online_params(account.username, account.password)
    my_backend = get_backend(system_params)

    history_offset = float(calibration_params.get("history_offset"))
    cali_threshold = float(calibration_params.get("cali_threshold"))
    nm_threshold = float(calibration_params.get("nm_check_threshold"))

    reference = get_thresholds()
    test_pairs = get_qubit_pair_groups()
    path = my_backend.context_manager.config.system.local_root or "./"
    result_records = BaseRecords(my_backend.context_manager.chip_data, path)
    # debug with a little qubit pairs.
    # test_pairs = {"1": test_pairs["1"]}

    # run all flow.
    run(
        backend=my_backend,
        history_best_thresholds=reference,
        tolerance=history_offset,
        threshold=cali_threshold,
        nm_check_threshold=nm_threshold,
        pair_dict=test_pairs,
        checker_config=batch_options.get(check_xeb_drift.__name__),
        nm_calibration_config=batch_options.get(calibrate_nm_xeb_flow.__name__),
        cz_calibration_config=batch_options.get(calibrate_cz_flow.__name__),
        result_record=result_records,
    )
