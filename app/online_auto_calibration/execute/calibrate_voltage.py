# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/13
# __author:       <PERSON><PERSON><PERSON> Shi

"""Calibrate qubit and coupler voltage."""
import copy
import pathlib
from typing import List, Dict

import numpy as np
from loguru import logger

from app.online_auto_calibration.utilities import (
    get_online_params,
    get_backend,
    get_qubit_groups,
    get_coupler_groups,
    get_batch_options,
    parse_calibration_config,
    pretty_show_dict,
)
from app.online_auto_calibration.manage.records import BaseRecords
from pyQCat.analysis import AnalysisResult, TopAnalysis
from pyQCat.executor import Backend
from pyQCat.experiments.batch_experiment import BatchExperiment
from pyQCat.qubit import Qubit, <PERSON><PERSON>ler

__MODULE__ = pathlib.Path(__file__).stem


class BatchVoltageCalibration(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()

        options.set_validator("coupler_scheme", ["new", "old"])
        options.set_validator("qubit_scheme", ["idle", "max"])
        options.set_validator("auto_set_amp", bool)

        # keep the same as before
        options.coupler_groups = {}  # {"1": ["c1-2", "c2-3"]}
        options.qubit_groups = {}  # {"1": ["q1", "q2", "q3"]}

        options.coupler_physical_units = None
        options.qubit_physical_units = None
        options.auto_set_amp = False
        options.qubit_init_amp = 0.05
        options.coupler_init_amp = 0.05
        options.update_threshold = 0.025
        options.coupler_cali_paras = {}

        options.coupler_scheme = "old"  # new or old
        options.coupler_new_flows = [
            "ZZShiftSweetPointCalibrationNew",
        ]
        options.coupler_old_flows = [
            "ZZShiftSweetPointCalibration",
        ]

        options.qubit_scheme = "idle"  # max or idle
        options.qubit_idle_flows = [
            "SingleShot_1",
            "VoltageDriftGradientCalibration_sw",
        ]
        options.qubit_max_flows = [
            "SingleShot_1",
            "SweetPointCalibration",
        ]
        options.qubit_max_flows_idle = [
            "VoltageDriftGradientCalibration",
        ]

        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.amp_map = {}

        # keep the same as before
        options.bad_qubits = {}
        options.bad_couplers = {}

        return options

    def _count_max_sweep_count(self, physical_units: List[str], init_zamp: float):
        max_count = 1
        if self.experiment_options.auto_set_amp:
            for unit in physical_units:
                zamp_list = self._get_zamp(unit, init_zamp)
                self.experiment_options.amp_map.update({unit: zamp_list})
                max_count = max(max_count, len(zamp_list))
        return max_count

    def _change_amp(
        self, i: int, physical_units: List[str], exp_name: str
    ) -> List[str]:
        if self.experiment_options.auto_set_amp:
            working_units = []
            exp_collection = self.params_manager.exp_map.get(exp_name)
            eop = exp_collection.options_for_parallel_exec.get("experiment_options", {})
            if "z_amp" not in eop:
                eop.update({"z_amp": {}})
            amp_map = self.experiment_options.amp_map
            for unit in physical_units:
                if len(amp_map.get(unit, [])) > i:
                    working_units.append(unit)
                    amp = amp_map[unit][i]
                    eop["z_amp"][unit] = amp
                    logger.info(f"{unit} sweet point calibration: init z amp {amp} V")
            return working_units
        else:
            return physical_units

    def _set_coupler_cali_paras(self, physical_units: List[str], exp_name: str):
        exp_collection = self.params_manager.exp_map.get(exp_name)
        eop = exp_collection.options_for_parallel_exec.get("experiment_options", {})
        if "child_exp_options" not in eop:
            eop.update({"child_exp_options": {}})
        if "fix_q_zamp" not in eop.get("child_exp_options"):
            eop.get("child_exp_options").update({"fix_q_zamp": {}})

        coupler_cali_paras = self.experiment_options.coupler_cali_paras
        for unit in physical_units:
            if unit in coupler_cali_paras.keys():
                read_type = coupler_cali_paras[unit].get("readout type")
                self._set_coupler_probe(unit, read_type)
                amp = coupler_cali_paras[unit].get("fix_q_zamp", None)
                eop.get("child_exp_options")["fix_q_zamp"][unit] = amp
                logger.info(f"{unit} sweet point calibration: fix_q_zamp {amp} V")

    def _run_batch(self):
        coupler_bad_group = {}
        for group, couplers in self.experiment_options.coupler_groups.items():
            logger.info(
                f"start calibration coupler voltage of group {group}: {couplers}"
            )

            if self.experiment_options.coupler_scheme == "new":
                bad_units = self._run_coupler_new(couplers)
            else:
                bad_units = self._run_coupler_old(couplers)
            if bad_units:
                coupler_bad_group.update({group: bad_units})
                self.run_options.bad_couplers[group] = bad_units
                logger.info(f"coupler failed units: {bad_units}")
        logger.info(
            f"complete calibration of coupler voltage, "
            f"failed units:\n{pretty_show_dict(coupler_bad_group)}"
        )

        qubit_bad_group = {}
        for group, qubits in self.experiment_options.qubit_groups.items():
            logger.info(f"start calibration qubit voltage of group {group}: {qubits}")

            if self.experiment_options.qubit_scheme == "idle":
                bad_units = self._run_qubit_idle(qubits)
            else:
                bad_units = self._run_qubit_max(qubits)
            if bad_units:
                qubit_bad_group.update({group: bad_units})
                self.run_options.bad_qubits[group] = bad_units
                logger.info(f"qubit failed units: {bad_units}")
        logger.info(
            f"complete calibration of qubit voltage, "
            f"failed units:\n{pretty_show_dict(qubit_bad_group)}"
        )

    def _run_coupler_old(self, coupler_physical_units: List[str]) -> List[str]:
        if self.experiment_options.coupler_old_flows:
            sweep_count = self._count_max_sweep_count(
                coupler_physical_units, self.experiment_options.coupler_init_amp
            )
            for i in range(sweep_count):
                working_units = self._change_amp(
                    i, coupler_physical_units, exp_name="ZZShiftSweetPointCalibration"
                )
                self._set_coupler_cali_paras(
                    working_units, exp_name="ZZShiftSweetPointCalibration"
                )
                if working_units:
                    pass_units = self._run_flow(
                        flows=self.experiment_options.coupler_old_flows,
                        physical_units=working_units,
                    )
                    if pass_units:
                        for unit in pass_units:
                            coupler_physical_units.remove(unit)
        return coupler_physical_units

    def _run_coupler_new(self, coupler_physical_units: List[str]) -> List[str]:
        if self.experiment_options.coupler_new_flows:
            bad_units = []

            # run high freq qubit idle point calibration
            probe_qubit_units = []
            qc_qp_map = {}
            for unit in coupler_physical_units:
                qc = self.context_manager.chip_data.get_physical_unit(unit)
                probe_qubit_units.append(f"q{qc.probe_bit}")
                qc_qp_map[unit] = f"q{qc.probe_bit}"
            probe_qubit_units = list(set(probe_qubit_units))

            cur_pass_units = self._run_flow(
                flows=self.experiment_options.qubit_idle_flows,
                physical_units=coupler_physical_units,
            )
            if cur_pass_units:
                for unit in cur_pass_units:
                    probe_qubit_units.remove(unit)
            # remove fail coupler unit
            if probe_qubit_units:
                for coupler_unit, qp_unit in qc_qp_map.items():
                    if qp_unit in probe_qubit_units:
                        coupler_physical_units.remove(coupler_unit)
                        bad_units.append(coupler_unit)
                        logger.warning(
                            f"{coupler_unit} probe qubit idle calibration fail ..."
                        )

            # run coupler sweet point calibration flow
            cur_pass_units = self._run_flow(
                flows=self.experiment_options.coupler_new_flows,
                physical_units=coupler_physical_units,
            )
            if cur_pass_units:
                for unit in cur_pass_units:
                    coupler_physical_units.remove(unit)
            return coupler_physical_units
        else:
            return coupler_physical_units

    def _run_qubit_idle(self, physical_units: List[str]) -> List[str]:
        if self.experiment_options.qubit_idle_flows:
            pass_units = self._run_flow(
                flows=self.experiment_options.qubit_idle_flows,
                physical_units=physical_units,
            )
            if pass_units:
                for unit in pass_units:
                    physical_units.remove(unit)
        return physical_units

    def _run_qubit_max(self, physical_units: List[str]) -> List[str]:
        if self.experiment_options.qubit_max_flows:
            sweep_count = self._count_max_sweep_count(
                physical_units, self.experiment_options.qubit_init_amp
            )
            sweet_point_pass_units = []
            for idx in range(sweep_count):
                working_units = self._change_amp(
                    idx, physical_units, exp_name="SweetPointCalibration"
                )
                if working_units:
                    pass_units = self._run_flow(
                        flows=self.experiment_options.qubit_max_flows,
                        physical_units=working_units,
                    )
                    if pass_units:
                        sweet_point_pass_units.extend(pass_units)
                        for unit in pass_units:
                            physical_units.remove(unit)
            if sweet_point_pass_units:
                pass_units = self._run_flow(
                    flows=self.experiment_options.qubit_max_flows_idle,
                    physical_units=sweet_point_pass_units,
                )
                if pass_units:
                    for unit in pass_units:
                        physical_units.remove(unit)
        return physical_units

    def _get_zamp(self, physical_unit: str, init_zamp: float = 0.05):
        base_qubit = self.context_manager.chip_data.get_physical_unit(physical_unit)

        if physical_unit.startswith("c"):
            if self.experiment_options.coupler_cali_paras:
                singe_dict = self.experiment_options.coupler_cali_paras.get(
                    physical_unit, None
                )
                if singe_dict:
                    init_zamp = singe_dict.get("z_amp")
                    zamp_list = [init_zamp, init_zamp + 0.03, init_zamp - 0.03]
                    return zamp_list

        dc_max = base_qubit.dc_max
        dc_min = base_qubit.dc_min
        zamp_max = 0.47 - abs(dc_max)
        zamp_max = np.min([zamp_max, abs(dc_max - dc_min) / 3 * 2])
        zamp_list = [init_zamp, (zamp_max + abs(init_zamp)) / 2, zamp_max]
        return zamp_list

    def _get_coupler_qh(self, coupler_physical_units: List[str]) -> Dict[str, str]:
        qh_map = {}
        for coupler_name in coupler_physical_units:
            qc: Coupler = self.context_manager.chip_data.get_physical_unit(coupler_name)
            dq: Qubit = self.context_manager.chip_data.get_physical_unit(
                f"q{qc.drive_bit}"
            )
            pq: Qubit = self.context_manager.chip_data.get_physical_unit(
                f"q{qc.probe_bit}"
            )

            counts = 0
            qh = None

            if dq and dq.goodness and dq.tunable:
                qh = dq.name
                counts += 1

            if pq and pq.goodness and pq.tunable:
                qh = pq.name
                counts += 1

            if counts == 2:
                if dq.drive_freq > pq.drive_freq:
                    qh = dq.name
                else:
                    qh = pq.name
                qh_map.update({coupler_name: qh})
            elif counts == 1:
                qh_map.update({coupler_name: qh})
            else:
                logger.warning(
                    f"Both probe and drive bits are not available when calibrating coupler: {coupler_name}"
                )

        return qh_map

    def _set_coupler_probe(self, coupler_name: str, read_type: str):
        qc: Coupler = self.context_manager.chip_data.get_physical_unit(coupler_name)
        dq: Qubit = self.context_manager.chip_data.get_physical_unit(f"q{qc.drive_bit}")
        pq: Qubit = self.context_manager.chip_data.get_physical_unit(f"q{qc.probe_bit}")

        counts = 0
        qh = None
        ql = None

        if dq and dq.goodness and dq.tunable:
            qh = dq.name
            ql = pq.name
            counts += 1

        if pq and pq.goodness and pq.tunable:
            qh = pq.name
            ql = dq.name
            counts += 1

        if counts == 2:
            if dq.drive_freq > pq.drive_freq:
                qh = dq.name
                ql = pq.name
            else:
                qh = pq.name
                ql = dq.name
        elif counts == 1:
            pass
        else:
            logger.warning(
                f"Both probe and drive bits are not available when calibrating coupler: {coupler_name}"
            )
        if read_type == "QH":
            qc.probe_bit = qh[1:]
            qc.drive_bit = ql[1:]
        else:
            qc.probe_bit = ql[1:]
            qc.drive_bit = qh[1:]

    def _check_update_voltage(
        self, name: str, result: "AnalysisResult", update_threshold: float
    ):
        logger.info(result)
        if result.value is None:
            return False

        extra_name = result.extra.get("name")
        if "c" in extra_name:
            # if name == "vol":
            #     return False
            qc_obj: Coupler = self.context_manager.chip_data.cache_coupler.get(
                extra_name
            )
        else:
            # if name == "dc_max":
            #     return False
            qc_obj: Qubit = self.context_manager.chip_data.cache_qubit.get(extra_name)
        if "dc_max" in result.extra.get("path"):
            old_value = qc_obj.dc_max
            new_value = result.value
            msg = f"dc_max {old_value} --> {new_value}"
            diff = abs(new_value - old_value)
        elif "idle_point" in result.extra.get("path"):
            old_value = qc_obj.idle_point
            new_value = result.value
            msg = f"idle_point {old_value} --> {new_value}"
            diff = abs(new_value - old_value)

        if diff <= update_threshold:
            logger.info(f"[AutoCalibration]After calibrate {extra_name}: {msg}")
            return True
        else:
            logger.warning(
                f"[AutoCalibration]No Update calibrate {extra_name}: {msg}, "
                f"update_threshold: {update_threshold}"
            )
            return False

    def _refresh_context_from_analysis(
        self, analysis_obj: TopAnalysis  # pylint: disable=arguments-renamed
    ):
        # Overload this interface for safer updating of voltage values.
        if self.experiment_options.refresh_context:
            for name, result in analysis_obj.results.items():
                if (name == "vol" or name == "dc_max") and result.extra.get(
                    "path"
                ) is not None:
                    is_update = self._check_update_voltage(
                        name,
                        result,
                        self.experiment_options.update_threshold,
                    )
                    if is_update:
                        update_records = (
                            self.context_manager.update_single_result_to_context(result)
                        )
                        if update_records:
                            logger.log("UPDATE", update_records)
                else:
                    update_records = (
                        self.context_manager.update_single_result_to_context(result)
                    )
                    if update_records:
                        logger.log("UPDATE", update_records)

            if self.experiment_options.save_db:
                self.backend.update_context_from_hot_data()


def calibrate_voltage(
    backend: Backend,
    result_record: BaseRecords,
    **kwargs,
):
    calibrate_vol_batch = BatchVoltageCalibration(backend)
    calibrate_vol_batch.set_experiment_options(batch_label="CalibrateVoltage")
    calibrate_vol_batch.set_experiment_options(**kwargs)
    calibrate_vol_batch.run()
    result_record.set_mongo_records(calibrate_vol_batch.batch_records, "voltage", send=True)
    return (
        calibrate_vol_batch.run_options.bad_qubits,
        calibrate_vol_batch.run_options.bad_couplers,
    )


def run(
    backend: Backend,
    coupler_groups: Dict[str, List[str]],
    qubit_groups: Dict[str, List[str]],
    calibration_config: Dict,
    result_record: BaseRecords,
    mode: str = "all",
) -> [Dict[str, List[str]], Dict[str, List[str]]]:
    logger.info(
        f"[AutoCalibration]Voltage calibration start:\n"
        f"{pretty_show_dict(coupler_groups)}\n"
        f"{pretty_show_dict(qubit_groups)}\n"
    )
    coupler_groups_new = {}
    qubit_groups_new = {}
    if mode == "all" or mode == "coupler":
        coupler_groups_new = copy.deepcopy(coupler_groups)
    if mode == "all" or mode == "qubit":
        qubit_groups_new = copy.deepcopy(qubit_groups)

    failed_groups_c = {}
    failed_groups_q = {}
    pass_qubits, pass_couplers = [], []
    pass_qubits_group, pass_couplers_group = {}, {}
    for group, coupler_list in coupler_groups_new.items():
        current_group_dict = {group: coupler_list}
        calibration_config.update({"coupler_groups": current_group_dict})
        calibration_config.update({"qubit_groups": {}})
        failed_qubit_groups, failed_coupler_groups = calibrate_voltage(
            backend=backend,
            result_record=result_record,
            **calibration_config,
        )
        logger.info(
            f"[AutoCalibration]Voltage couplers calibration completed, failed units:\n"
            f"{pretty_show_dict(failed_coupler_groups)}\n"
        )
        failed_groups_c.update(failed_coupler_groups)
        cur_fail_units = failed_coupler_groups.get(group, [])
        couplers_res = [unit for unit in coupler_list if unit not in cur_fail_units]
        if couplers_res:
            pass_couplers.extend(couplers_res)
            pass_couplers_group.setdefault(group, couplers_res)

    for group, qubit_list in qubit_groups_new.items():
        current_group_dict = {group: qubit_list}
        calibration_config.update({"coupler_groups": {}})
        calibration_config.update({"qubit_groups": current_group_dict})
        failed_qubit_groups, failed_coupler_groups = calibrate_voltage(
            backend=backend,
            result_record=result_record,
            **calibration_config,
        )
        logger.info(
            f"[AutoCalibration]Voltage qubits calibration completed, failed units:\n"
            f"{pretty_show_dict(failed_qubit_groups)}\n"
        )
        failed_groups_q.update(failed_qubit_groups)
        cur_fail_units = failed_qubit_groups.get(group, [])
        qubits_res = [unit for unit in qubit_list if unit not in cur_fail_units]
        if qubits_res:
            pass_qubits.extend(qubits_res)
            pass_qubits_group.setdefault(group, qubits_res)

    if pass_qubits:
        backend.save_chip_data_to_db(pass_qubits)
    if pass_couplers:
        backend.save_chip_data_to_db(pass_couplers)

    result_record.save_result_to_db(item_type="voltage")
    return (
        pass_qubits_group,
        pass_couplers_group,
        failed_groups_q,
        failed_groups_c,
    )


if __name__ == "__main__":
    # initialize parameters.
    calibration_conf = parse_calibration_config(r"../conf/calibration_config.conf")
    account = calibration_conf.get("account")
    batch_options = get_batch_options(
        r"../conf/batch_conf.json", indicate_one=__MODULE__
    )
    system_params = get_online_params(account.username, account.password)
    my_backend = get_backend(system_params)
    test_qubits = get_qubit_groups()
    test_couplers = get_coupler_groups()
    path = my_backend.context_manager.config.system.local_root or "./"
    result_records = BaseRecords(my_backend.context_manager.chip_data, path)
    run(
        my_backend,
        coupler_groups=test_couplers,
        qubit_groups=test_qubits,
        calibration_config=batch_options.get(calibrate_voltage.__name__),
        result_record=result_records,
    )
