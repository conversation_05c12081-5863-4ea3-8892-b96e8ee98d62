# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/14
# __author:       Han<PERSON><PERSON>

"""Calibrate single qubit gate."""

import pathlib
from typing import List, Dict
from loguru import logger

from app.online_auto_calibration.manage.records import BaseRecords
from app.online_auto_calibration.utilities import (
    filter_retried_experiment,
    get_nested_dict_value,
)
from app.online_auto_calibration.utilities import (
    get_online_params,
    get_backend,
    get_thresholds,
    get_qubit_groups,
    parse_calibration_config,
    get_batch_options,
    filter_bad_qubits,
    pretty_show_dict,
    emit_calibration_signal,
)
from pyQCat.errors import AnalysisError
from pyQCat.executor import Backend
from pyQCat.experiments.batch import BatchRunner, BatchSingleQubitCalibration

__MODULE__ = pathlib.Path(__file__).stem


class BatchSingleQubitOnlineCalibration(BatchSingleQubitCalibration):
    """Overwrite for emit calibration signal."""

    def _run_batch(self):
        self.run_options.failed_qubit_groups = {}
        for index, qubits in self.experiment_options.qubit_groups.items():
            # Shq 2024/01/31
            # Notify storm some messages.
            with emit_calibration_signal(
                self.experiment_options.batch_label,
                qubits,
                self.experiment_options.publish_to_storm,
            ):
                passed_units = self._run_flow(
                    flows=self.experiment_options.qubit_flows, physical_units=qubits
                )
            bad_units = [qubit for qubit in qubits if qubit not in passed_units]
            self.run_options.failed_qubit_groups.setdefault(index, bad_units)
            logger.info(f"qubit failed units in group {index}: {bad_units}")


def check_gate_params_drift(
    backend: Backend,
    thresholds: Dict,
    tolerance: float,
    result_record: BaseRecords,
    **kwargs,
) -> List[str]:
    rb_batch = BatchRunner(backend)
    rb_batch.set_experiment_options(batch_label="CheckGateParamsDrift")
    rb_batch.set_experiment_options(**kwargs)
    rb_batch.run()

    result_record.set_mongo_records(rb_batch.batch_records, "s_gate", send=True)
    return quality_assessment_by_fidelity(rb_batch.batch_records, thresholds, tolerance)


def calibrate_single_gate(
    backend: Backend,
    tolerance: float,
    result_record: BaseRecords,
    **kwargs,
):
    calibrate_single_qubit_gate_batch = BatchSingleQubitOnlineCalibration(backend)
    calibrate_single_qubit_gate_batch.set_experiment_options(**kwargs)

    calibrate_single_qubit_gate_batch.set_experiment_options(
        batch_label="CalibrateSingleGate"
    )
    calibrate_single_qubit_gate_batch.run()
    result_record.set_mongo_records(calibrate_single_qubit_gate_batch.batch_records, "s_gate", send=True)
    try:
        failed_qubit_groups = handle_single_gate_calibration_result(
            calibrate_single_qubit_gate_batch, tolerance
        )
    except AnalysisError as error:
        failed_qubit_groups = (
            calibrate_single_qubit_gate_batch.experiment_options.qubit_groups
        )
        logger.error(f"[AutoCalibration]{error}\n")

    # bugfix zyc: pass qubits save to database
    pass_qubits = []
    qubit_groups = kwargs.get("qubit_groups", {})
    for group_name, group_qubits in qubit_groups.items():
        cur_fail_units = failed_qubit_groups.get(group_name, [])
        pass_qubits.extend(
            [unit for unit in group_qubits if unit not in cur_fail_units]
        )
    backend.save_chip_data_to_db(pass_qubits)

    return failed_qubit_groups


def quality_assessment_by_fidelity(
    records: Dict, thresholds: Dict, tolerance: float
) -> List[str]:
    drift_qubits = []
    fidelity_dict = _extract_fidelity_from_record(records)
    if fidelity_dict:
        for qubit, current_fidelity in fidelity_dict.items():
            threshold_fidelity = get_nested_dict_value(
                thresholds, ["single_gate", qubit]
            )
            if not threshold_fidelity:
                logger.error(f"Thresholds lack {qubit} fidelity data.")
                continue
                # raise ValueError(f"Thresholds lack {qubit} fidelity data.")
            if current_fidelity < threshold_fidelity - tolerance:
                logger.warning(
                    f"[AutoCalibration] {qubit} Single qubit gate fidelity drift: "
                    f"{threshold_fidelity}->{current_fidelity}"
                )
                if qubit not in drift_qubits:
                    drift_qubits.append(qubit)
    else:
        logger.error("[AutoCalibration] Get a null fidelity dict.")
    return drift_qubits


def _extract_fidelity_from_record(records: Dict):
    clean_records = filter_retried_experiment(records, ["RBSingle"], ["analysis_data"])
    analysis_datas: Dict = get_nested_dict_value(
        clean_records, ["RBSingle", "analysis_data"]
    )
    fidelity_dict = {}
    if analysis_datas:
        for qubit, analysis_data in analysis_datas.items():
            current_fidelity = get_nested_dict_value(
                analysis_data, ["result", "fidelity"]
            )
            if not current_fidelity:
                logger.error(f"{qubit} has no RB fidelity.")
                continue
                # raise AnalysisError(f"{qubit} has no RB fidelity.")
            fidelity_dict.setdefault(qubit, float(current_fidelity))
    else:
        logger.error(
            "[AutoCalibration] Parallel RBSingle has no analysis data in records.json."
        )
        # raise AnalysisError(
        #     "[AutoCalibration] Parallel RBSingle has no analysis data in records.json."
        # )
    return fidelity_dict


def handle_single_gate_calibration_result(
    batch: BatchSingleQubitCalibration, tolerance: float
):
    failed_qubit_groups = {}
    failed_units = []
    fidelity_dict = _extract_fidelity_from_record(batch.batch_records)
    if fidelity_dict:
        for qubit, current_fidelity in fidelity_dict.items():
            if current_fidelity < tolerance:
                logger.warning(
                    f"[AutoCalibration] {qubit} calibrate single qubit gate failed, fidelity {current_fidelity}"
                )
                failed_units.append(qubit)
    else:
        logger.error("[AutoCalibration] Get a null fidelity dict.")
    for failed_qubit in failed_units:
        for group, qubit_list in batch.experiment_options.qubit_groups.items():
            if failed_qubit in qubit_list:
                failed_qubit_groups.setdefault(group, [])
                failed_qubit_groups[group].append(failed_qubit)
    return failed_qubit_groups


def run(
    backend: Backend,
    history_best_thresholds: Dict,
    qubit_groups: Dict[str, List[str]],
    tolerance: float,
    rb_tolerance: float,
    checker_config: Dict,
    calibration_config: Dict,
    result_record: BaseRecords,
    bad_qubit_groups: Dict[str, List[str]] = None,
    bad_coupler_groups: Dict[str, List[str]] = None,
):
    need_calibrated_qubit_groups = {}
    logger.info(
        f"[AutoCalibration]Single qubit gate calibration start:\n"
        f"{pretty_show_dict(qubit_groups)}"
    )
    for group, qubit_list in qubit_groups.items():
        logger.info(
            f"[AutoCalibration]Group{group}, all {len(qubit_list)} qubits will enter drift check."
        )

        if qubit_list:
            checker_config.update({"physical_units": qubit_list})

        need_calibrated_qubits = check_gate_params_drift(
            backend,
            history_best_thresholds,
            tolerance,
            result_record,
            **checker_config,
        )
        if need_calibrated_qubits:
            need_calibrated_qubit_groups.setdefault(group, need_calibrated_qubits)

    # Bad qubits only check but not do calibration.
    if bad_qubit_groups or bad_coupler_groups:
        logger.info(
            "[AutoCalibration]Filter qubits from bad qubits/couplers for next calibration step."
        )
        for group, need_calibrated_qubits in need_calibrated_qubit_groups.items():
            # Shq, 2024/01/20
            # fixed bug: TypeError: argument of type 'NoneType' is not iterable
            need_calibrated_qubits = filter_bad_qubits(
                bad_qubits=bad_qubit_groups.get(group, []),
                bad_couplers=bad_coupler_groups.get(group, []),
                qubits=need_calibrated_qubits,
            )
            if need_calibrated_qubits:
                need_calibrated_qubit_groups[group] = need_calibrated_qubits

    if need_calibrated_qubit_groups:
        logger.info(
            "[AutoCalibration]The following qubits have drifted in single-gate fidelity and will enter "
            "the single-gate calibration process."
            f"Drift qubits: \n{pretty_show_dict(need_calibrated_qubit_groups)}"
        )
        calibration_config.update({"qubit_groups": need_calibrated_qubit_groups})
        failed_qubit_groups = calibrate_single_gate(
            backend=backend,
            tolerance=rb_tolerance,
            result_record=result_record,
            **calibration_config,
        )

        if failed_qubit_groups:
            logger.warning(
                f"[AutoCalibration]Calibrate single-gate completed. Failed qubits:\n"
                f"{pretty_show_dict(failed_qubit_groups)}\n"
            )
        return failed_qubit_groups
    else:
        logger.info(
            "[AutoCalibration]Normal single-gate fidelity for all qubits, no calibration required.\n"
        )
        return {}


if __name__ == "__main__":
    # initialize parameters.
    calibration_conf = parse_calibration_config(r"../conf/calibration_config.conf")
    account = calibration_conf.get("account")
    calibration_params = calibration_conf.get("single_gate")
    batch_options = get_batch_options(
        r"../conf/batch_conf.json", indicate_one=__MODULE__
    )

    system_params = get_online_params(account.username, account.password)
    my_backend = get_backend(system_params)
    reference = get_thresholds()

    history_offset = float(calibration_params.get("history_offset"))
    rb_threshold = float(calibration_params.get("rb_threshold"))
    qubit_step = int(calibration_params.get("qubit_step"))

    # Every 31 qubits will be divided into groups.
    test_qubits = get_qubit_groups(step=31)
    path = my_backend.context_manager.config.system.local_root or "./"
    result_records = BaseRecords(my_backend.context_manager.chip_data, path)
    failed_result = run(
        backend=my_backend,
        history_best_thresholds=reference,
        qubit_groups=test_qubits,
        tolerance=history_offset,
        rb_tolerance=rb_threshold,
        checker_config=batch_options.get(check_gate_params_drift.__name__),
        calibration_config=batch_options.get(calibrate_single_gate.__name__),
        result_record=result_records,
    )
