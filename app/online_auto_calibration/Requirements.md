# 需求

## 1.在线自动化运行脚本支持command line参数

示例：
```shell
python /...user project path/batch_runner.py -t vol --qubits=q0,q1,q2,q3--q7,q8--q9,q10,q11 --couplers=c1-2,c2-3 --pairs=q0q1,q2q3
```

### 1.1支持五种校准模式
```shell
python /...user project path/batch_runner.py -t dcm
python /...user project path/batch_runner.py -t vol
python /...user project path/batch_runner.py -t sq
python /...user project path/batch_runner.py -t cz
python /...user project path/batch_runner.py -t all

python /...user project path/batch_runner.py --calibration_type=dcm
python /...user project path/batch_runner.py --calibration_type=vol
python /...user project path/batch_runner.py --calibration_type=single_gate
python /...user project path/batch_runner.py --calibration_type=cz_gate
python /...user project path/batch_runner.py --calibration_type=all
```
dcm参数对应main函数中的calibration_type: "refresh_dcm"
vol参数对应main函数中的calibration_type: "cali_voltage"
sq参数对应main函数中的calibration_type: "cali_single_gate"
cz参数对应main函数中的calibration_type: "cali_cz_gate"
all参数对应main函数中的calibration_type: "cali_all"


### 1.2 支持1种定时策略模式（并在sched参数输入时给出提示，-t参数将无效）
```shell
python /...user project path/batch_runner.py -sched
```
sched标志相当于main函数的schedule_mode参数

### 1.3 支持推送校准信号
```shell
python /...user project path/batch_runner.py -t dcm -pub
```
pub标志相当于main函数中的publish_to_storm参数


## 2.可视化结果
提取每个校准流程的结果进行可视化，输出表格和图

### 2.1 校准记录表
每次运行main.py结束的时候都生成一份，记录本次校准的更迭记录，没有更迭的为空白，左值为校准前的值，右值为校准后的值

Physical Units | Readout Fidelity | Single Gate Fidelity | CZ Gate Fidelity | Voltage
----------|----------|----------|----------|----------
 q1 | History 1, Current 2 | History 1, Current 3 |History 1, Current 3 |History 1, Current 3 
 q2 | History 2, Current 2 | History 2, Current 3 |History 1, Current 3 |History 1, Current 3 
 c1-2 | History 3, Current 2 | History 3, Current 3 |History 1, Current 3 |History 1, Current 3 
 c2-3 | History 3, Current 2 | History 3, Current 3 |History 1, Current 3 |History 1, Current 3 
 q1q2 | History 3, Current 2 | History 3, Current 3 |History 1, Current 3 |History 1, Current 3 
 q2q3 | History 3, Current 2 | History 3, Current 3 |History 1, Current 3 |History 1, Current 3 

- Note
表的生成目前有2种思路：
    - 1.在校准开始前查一遍数据库，保存下所有qubit\coupler\qubitpair等数据结构的所有参数，然后等校准结束后再查一遍数据库，
      再次获取所有参数，然后根据前后对比生成表格。
    - 2.在每个校准过程中即按照表格格式将校准结果保存在一个数据结构中，等结束后刷新到表格中。


### 2.2 校准记录图
待定


## 3.支持结果到入参
BatchExperiment的结果应当生成一个可以作为下一个Bacth流程的入参文件。详细格式需要讨论决定。