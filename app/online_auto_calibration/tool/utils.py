from enum import Enum
from typing import Dict, List, Union

from app.online_auto_calibration.utilities import get_nested_dict_value


class CaliProtocol(Enum):
    hearts = b"53"

    cali_result = b"54"
    cali_error = b"55"
    cali_record = b"56"

    start_cali = b"51"
    stop_cali = b"52"


def transfer_args(args_dict: Dict) -> List:
    args_list = []
    for param_key, param_value in args_dict.items():
        name = f"-{param_key}"
        if param_key in ["qubits", "couplers", "pairs"]:
            name = f"--{param_key}"
        if isinstance(param_value, bool):
            if param_value:
                args_list.append(name)
        else:
            if param_value:
                args_list.append(name)
                args_list.append(param_value)
    print(args_list)
    return args_list


def get_online_qubit_groups(
    online_params: Dict,
    step: Union[List[int], int] = None,
) -> Dict[str, List[str]]:
    total_bits = get_nested_dict_value(online_params, ["context_params", "online_bits"])
    length = len(total_bits)
    qubits = {}
    if not step:
        step = length
    if isinstance(step, int):
        count = 0
        for i in range(0, length, step):
            count += 1
            sub_qubits = total_bits[i : i + step]
            if sub_qubits:
                qubits.setdefault(f"{count}", sub_qubits)
    elif isinstance(step, list):
        current_index = 0
        for i, s in enumerate(step):
            qubits.setdefault(f"{i + 1}", total_bits[current_index: current_index + s])
            current_index += s
    return qubits
