import requests
from typing import Any, Dict, Optional, Union, List

from loguru import logger

from app.config import settings
from app.online_auto_calibration.tool.base_exception import ApiException
from pyQCat.structures import Singleton


class WebClient:
    @classmethod
    def request(
        cls,
        method: str,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Union[Dict[str, Any], str]] = None,
        json: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: int = 10,
    ) -> requests.Response:
        """
        通用 HTTP 请求方法。

        :param method: HTTP 方法（GET、POST、PUT、DELETE 等）
        :param url: 请求的 URL（可以是相对路径或完整路径）
        :param params: 查询参数（GET 请求使用）
        :param data: 表单数据（application/x-www-form-urlencoded）
        :param json: JSON 数据（application/json）
        :param headers: 请求头
        :param timeout: 请求超时时间（默认 10 秒）
        :return: requests.Response 对象
        """
        try:
            response = requests.request(
                method=method.upper(),
                url=url,
                params=params,
                data=data,
                json=json,
                headers=headers,
                timeout=timeout,
            )
            # response.raise_for_status()  # 如果返回状态码不是 200 系列，抛出异常
            return response
        except requests.RequestException as e:
            print(f"Request failed: {e}")
            raise e

    @classmethod
    def get(
        cls,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: int = 10,
    ) -> requests.Response:
        """GET 请求"""
        return cls.request("GET", url, params=params, headers=headers, timeout=timeout)

    @classmethod
    def post(
        cls,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        json: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: int = 10,
    ) -> requests.Response:
        """POST 请求"""
        return cls.request(
            "POST", url, data=data, json=json, headers=headers, timeout=timeout
        )

    @classmethod
    def put(
        cls,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        json: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: int = 10,
    ) -> requests.Response:
        """PUT 请求"""
        return cls.request(
            "PUT", url, data=data, json=json, headers=headers, timeout=timeout
        )

    @classmethod
    def delete(
        cls,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        timeout: int = 10,
    ) -> requests.Response:
        """DELETE 请求"""
        return cls.request("DELETE", url, headers=headers, timeout=timeout)


class ConfigManage(metaclass=Singleton):
    def __init__(
        self,
        username: str = settings.USERNAME,
        user_pwd: str = settings.USER_PWD,
        base_url: str = settings.BASE_NAGA_URL,
        token: str = None,
    ):
        self.username = username
        self.user_pwd = user_pwd
        self.base_url = base_url

        self.token = token
        self.headers = None
        if self.token:
            self.update_token(token)

    def update_token(self, token: str = None):
        if not token:
            token = self.login_in(self.username, self.user_pwd)
            self.token = token
        self.headers = {"Authorization": token}
        logger.info(f"Login in,the new token is {token}")

    def check_headers(self):
        if not self.headers:
            self.update_token(self.token)

    def login_in(
        self,
        username: str,
        user_pwd: str,
        url: str = "/user/login",
    ):
        full_url = f"{self.base_url}{url}"
        params = {"username": username, "password": user_pwd}
        resp = WebClient.post(url=full_url, data=params, headers=self.headers)
        if resp.status_code == 200:
            json_data = resp.json()
            access_token = json_data.get("access_token")
            return access_token
        elif resp.status_code == 500:
            raise ApiException(500, "Internal Server error.")
        else:
            json_data = resp.json()
            code = json_data.get("code") or resp.status_code
            msg = json_data.get("message")
            raise ApiException(code, msg)

    def get_thresholds(
        self,
        point_label: str,
        url: Optional[str] = "/cali/best/thresholds",
        is_first: bool = True,
    ) -> Dict:
        try:
            self.check_headers()
            full_url = f"{self.base_url}{url}"
            params = {"point_label": point_label}
            resp = WebClient.get(url=full_url, params=params, headers=self.headers)
            if resp.status_code == 200:
                json_data = resp.json()
                data = json_data.get("data")
                return data
            elif resp.status_code == 500:
                raise ApiException(500, "Internal Server error.")
            # 登录过期重新登录再次请求
            elif resp.status_code == 401:
                if not is_first:
                    json_data = resp.json()
                    code = json_data.get("code") or resp.status_code
                    msg = json_data.get("message")
                    raise ApiException(code, msg)
                self.update_token()
                return self.get_thresholds(point_label, is_first=False)
            else:
                json_data = resp.json()
                code = json_data.get("code") or resp.status_code
                msg = json_data.get("message")
                raise ApiException(code, msg)
        except ApiException as e:
            raise e
        except Exception as e:
            raise ApiException(code=400, message=f"Api request error: {str(e)}")

    def get_templates(
        self,
        point_label: str,
        type_name: Optional[str],
        url: Optional[str] = "/cali/template/params",
        is_first: bool = True,
    ) -> List:
        try:
            self.check_headers()
            full_url = f"{self.base_url}{url}"
            params = {"point_label": point_label}
            if type_name:
                params["type_name"] = type_name
            resp = WebClient.get(url=full_url, params=params, headers=self.headers)
            if resp.status_code == 200:
                json_data = resp.json()
                data = json_data.get("data")
                return data
            elif resp.status_code == 500:
                raise ApiException(500, "Internal Server error.")
            # 登录过期重新登录再次请求
            elif resp.status_code == 401:
                if not is_first:
                    json_data = resp.json()
                    code = json_data.get("code") or resp.status_code
                    msg = json_data.get("message")
                    raise ApiException(code, msg)
                self.update_token()
                return self.get_templates(point_label, type_name, is_first=False)
            else:
                json_data = resp.json()
                code = json_data.get("code") or resp.status_code
                msg = json_data.get("message")
                raise ApiException(code, msg)
        except ApiException as e:
            raise e
        except Exception as e:
            raise ApiException(code=400, message=f"Api request error: {str(e)}")

    def get_batch_options(
        self,
        point_label: str,
        url: Optional[str] = "/cali/batch/options",
        is_first: bool = True,
    ) -> Dict:
        try:
            self.check_headers()
            full_url = f"{self.base_url}{url}"
            params = {"point_label": point_label}
            resp = WebClient.get(url=full_url, params=params, headers=self.headers)
            if resp.status_code == 200:
                json_data = resp.json()
                data = json_data.get("data")
                return data
            elif resp.status_code == 500:
                raise ApiException(500, "Internal Server error.")
            # 登录过期重新登录再次请求
            elif resp.status_code == 401:
                if not is_first:
                    json_data = resp.json()
                    code = json_data.get("code") or resp.status_code
                    msg = json_data.get("message")
                    raise ApiException(code, msg)
                self.update_token()
                return self.get_batch_options(point_label, is_first=False)
            else:
                json_data = resp.json()
                code = json_data.get("code") or resp.status_code
                msg = json_data.get("message")
                raise ApiException(code, msg)
        except ApiException as e:
            raise e
        except Exception as e:
            raise ApiException(code=400, message=f"Api request error: {str(e)}")

    def get_calibration_config(
        self,
        point_label: str,
        url: Optional[str] = "/cali/config",
        is_first: bool = True,
    ) -> Dict:
        try:
            self.check_headers()
            full_url = f"{self.base_url}{url}"
            params = {"point_label": point_label}
            resp = WebClient.get(url=full_url, params=params, headers=self.headers)
            if resp.status_code == 200:
                json_data = resp.json()
                data = json_data.get("data", {}).get("conf")
                return data
            elif resp.status_code == 500:
                raise ApiException(500, "Internal Server error.")
            # 登录过期重新登录再次请求
            elif resp.status_code == 401:
                if not is_first:
                    json_data = resp.json()
                    code = json_data.get("code") or resp.status_code
                    msg = json_data.get("message")
                    raise ApiException(code, msg)
                self.update_token()
                return self.get_calibration_config(point_label, is_first=False)
            else:
                json_data = resp.json()
                code = json_data.get("code") or resp.status_code
                msg = json_data.get("message")
                raise ApiException(code, msg)
        except ApiException as e:
            raise e
        except Exception as e:
            raise ApiException(code=400, message=f"Api request error: {str(e)}")

    def get_online_params(
        self,
        point_label: str,
        url: Optional[str] = "/cali/online/params",
        is_first: bool = True,
    ) -> Dict:
        try:
            self.check_headers()
            full_url = f"{self.base_url}{url}"
            params = {"point_label": point_label}
            resp = WebClient.get(url=full_url, params=params, headers=self.headers)
            if resp.status_code == 200:
                json_data = resp.json()
                data = json_data.get("data")
                return data
            elif resp.status_code == 500:
                raise ApiException(500, "Internal Server error.")
            # 登录过期重新登录再次请求
            elif resp.status_code == 401:
                if not is_first:
                    json_data = resp.json()
                    code = json_data.get("code") or resp.status_code
                    msg = json_data.get("message")
                    raise ApiException(code, msg)
                self.update_token()
                return self.get_online_params(point_label, is_first=False)
            else:
                json_data = resp.json()
                code = json_data.get("code") or resp.status_code
                msg = json_data.get("message")
                raise ApiException(code, msg)
        except ApiException as e:
            raise e
        except Exception as e:
            raise ApiException(code=400, message=f"Api request error: {str(e)}")

    def get_cali_groups(
        self,
        point_label: str,
        url: Optional[str] = "/cali/group/passed",
        is_first: bool = True,
    ) -> Dict:
        try:
            self.check_headers()
            full_url = f"{self.base_url}{url}"
            params = {"point_label": point_label}
            resp = WebClient.get(url=full_url, params=params, headers=self.headers)
            if resp.status_code == 200:
                json_data = resp.json()
                data = json_data.get("data")
                return data
            elif resp.status_code == 500:
                raise ApiException(500, "Internal Server error.")
            # 登录过期重新登录再次请求
            elif resp.status_code == 401:
                if not is_first:
                    json_data = resp.json()
                    code = json_data.get("code") or resp.status_code
                    msg = json_data.get("message")
                    raise ApiException(code, msg)
                self.update_token()
                return self.get_cali_groups(point_label, is_first=False)
            else:
                json_data = resp.json()
                code = json_data.get("code") or resp.status_code
                msg = json_data.get("message")
                raise ApiException(code, msg)
        except ApiException as e:
            raise e
        except Exception as e:
            raise ApiException(code=400, message=f"Api request error: {str(e)}")

    def get_system_config(
        self,
        point_label: str,
        url: Optional[str] = "/cali/system/config",
        is_first: bool = True,
    ) -> Dict:
        try:
            self.check_headers()
            full_url = f"{self.base_url}{url}"
            params = {"point_label": point_label}
            resp = WebClient.get(url=full_url, params=params, headers=self.headers)
            if resp.status_code == 200:
                json_data = resp.json()
                data = json_data.get("data", {}).get("conf")
                return data
            elif resp.status_code == 500:
                raise ApiException(500, "Internal Server error.")
            # 登录过期重新登录再次请求
            elif resp.status_code == 401:
                if not is_first:
                    json_data = resp.json()
                    code = json_data.get("code") or resp.status_code
                    msg = json_data.get("message")
                    raise ApiException(code, msg)
                self.update_token()
                return self.get_system_config(point_label, is_first=False)
            else:
                json_data = resp.json()
                code = json_data.get("code") or resp.status_code
                msg = json_data.get("message")
                raise ApiException(code, msg)
        except ApiException as e:
            raise e
        except Exception as e:
            raise ApiException(code=400, message=f"Api request error: {str(e)}")

    def get_online_point_label(
        self,
        url: Optional[str] = "/cali/online/point_label",
        is_first: bool = True,
    ) -> List:
        try:
            self.check_headers()
            full_url = f"{self.base_url}{url}"
            resp = WebClient.get(url=full_url, headers=self.headers)
            if resp.status_code == 200:
                json_data = resp.json()
                data = json_data.get("data")
                return data
            elif resp.status_code == 500:
                raise ApiException(500, "Internal Server error.")
            # 登录过期重新登录再次请求
            elif resp.status_code == 401:
                if not is_first:
                    json_data = resp.json()
                    code = json_data.get("code") or resp.status_code
                    msg = json_data.get("message")
                    raise ApiException(code, msg)
                self.update_token()
                return self.get_online_point_label(is_first=False)
            else:
                json_data = resp.json()
                code = json_data.get("code") or resp.status_code
                msg = json_data.get("message")
                raise ApiException(code, msg)
        except ApiException as e:
            raise e
        except Exception as e:
            raise ApiException(code=400, message=f"Api request error: {str(e)}")

    def get_start_params(
        self,
        sample: str,
        env_name: str,
        url: Optional[str] = "/cali/start/params",
        is_first: bool = True,
    ) -> Dict:
        try:
            self.check_headers()
            full_url = f"{self.base_url}{url}"
            params = {"sample": sample, "env_name": env_name}
            resp = WebClient.get(url=full_url, params=params, headers=self.headers)
            if resp.status_code == 200:
                json_data = resp.json()
                data = json_data.get("data")
                return data
            elif resp.status_code == 500:
                raise ApiException(500, "Internal Server error.")
            # 登录过期重新登录再次请求
            elif resp.status_code == 401:
                if not is_first:
                    json_data = resp.json()
                    code = json_data.get("code") or resp.status_code
                    msg = json_data.get("message")
                    raise ApiException(code, msg)
                self.update_token()
                return self.get_start_params(sample, env_name, is_first=False)
            else:
                json_data = resp.json()
                code = json_data.get("code") or resp.status_code
                msg = json_data.get("message")
                raise ApiException(code, msg)
        except ApiException as e:
            raise e
        except Exception as e:
            raise ApiException(code=400, message=f"Api request error: {str(e)}")

    def post_cali_dataset(
        self,
        data: Dict,
        timeout: int = 5,
        url: Optional[str] = "/cali/dataset",
        is_first: bool = True,
    ) -> Dict:
        try:
            self.check_headers()
            full_url = f"{self.base_url}{url}"
            resp = WebClient.post(
                url=full_url, json=data, headers=self.headers, timeout=timeout
            )
            if resp.status_code == 200:
                json_data = resp.json()
                data = json_data.get("data")
                return data
            elif resp.status_code == 500:
                raise ApiException(500, "Internal Server error.")
            # 登录过期重新登录再次请求
            elif resp.status_code == 401:
                if not is_first:
                    json_data = resp.json()
                    code = json_data.get("code") or resp.status_code
                    msg = json_data.get("message")
                    raise ApiException(code, msg)
                self.update_token()
                return self.post_cali_dataset(data, is_first=False)
            else:
                json_data = resp.json()
                code = json_data.get("code") or resp.status_code
                msg = json_data.get("message")
                raise ApiException(code, msg)
        except ApiException as e:
            raise e
        except Exception as e:
            raise ApiException(code=400, message=f"Api request error: {str(e)}")


conf_manage = ConfigManage()
