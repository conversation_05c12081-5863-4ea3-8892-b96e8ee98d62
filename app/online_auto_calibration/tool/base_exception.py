

class CodeMessage:
    C_4001 = 4001
    C_4002 = 4002
    C_4003 = 4003
    C_4004 = 4004
    C_4005 = 4005
    C_4006 = 4006
    C_4007 = 4007
    C_4008 = 4008
    C_4009 = 4009
    C_4010 = 4010
    C_4011 = 4011

    status_msg_ch = {
        C_4001: "启动执行缺少 sample 或 env_name 参数",
        C_4002: "平台还未上线任何一个 point_label",
        C_4003: "启动的 point_label 参数中存在未上线的",
        C_4004: "未配置 online params 参数",
        C_4005: "未配置 group params 分组参数",
        C_4006: "未配置 calibration_config params 参数",
        C_4007: "未配置 threshold params 参数",
        C_4008: "未配置 batch options params 参数",
        C_4009: "未配置 start params 参数",
        C_4010: "自动化校准正在运行中，请运行结束或者停止运行后再试",
        C_4011: "自动化校准当前未运行无法停止",
    }

    status_msg_en = {
        C_4001: "The startup execution is missing the sample or env_name parameters",
        C_4002: "No point_label is available online on the platform",
        C_4003: "The enabled point_label parameter is not online",
        C_4004: "The online params parameter is not configured",
        C_4005: "The group params parameter is not configured",
        C_4006: "The calibration_config params parameter is not configured",
        C_4007: "The threshold params parameter is not configured",
        C_4008: "The batch options params parameter is not configured",
        C_4009: "The start params parameter is not configured",
        C_4010: "The automatic calibration is in progress. Please finish or stop the operation and try again",
        C_4011: "Automatic calibration is not currently running and cannot be stopped",
    }

    @classmethod
    def get_code_message(cls, code: int, en: bool = False):
        if en:
            return cls.status_msg_en.get(code, "Unknown status Code")
        return cls.status_msg_ch.get(code, "未知的状态码")


class ParamsException(Exception):
    def __init__(self, code: int):
        self.code = code
        self.msg = CodeMessage.get_code_message(code)

    def __str__(self):
        return f"ParamsException: code is {self.code}, {self.msg}"


class ApiException(Exception):
    def __init__(self, code: int = None, message: str = None):
        self.code = code or 0
        self.msg = message or "Api interface request error."

    def __str__(self):
        return f"ApiException: code is {self.code}, {self.msg}"
