# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/17
# __author:       <PERSON><PERSON><PERSON> <PERSON>
from app.online_auto_calibration.process.tran_manage import ProtocolServer


if __name__ == "__main__":
    server = ProtocolServer()
    server.run()
