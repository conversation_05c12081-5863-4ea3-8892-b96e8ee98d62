# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/18
# __author:       <PERSON><PERSON><PERSON> <PERSON>


"""Log collector module."""
from logging.handlers import TimedRotatingFileHandler
import re
import os
import time
from datetime import datetime
from loguru import logger

from pyQCat.log import LogFormat


class CaliHandler(TimedRotatingFileHandler):
    def __init__(
        self, filename, when: str, encoding: str = "utf-8", interval: int = 1
    ) -> None:
        super().__init__(
            filename=filename, when=when, encoding=encoding, interval=interval
        )
        self._pattern_str = re.compile(r"\[AutoCalibration\]")

    def emit(self, record) -> None:
        if self._pattern_str.search(record.getMessage()):
            record.msg = record.msg.replace("[AutoCalibration]", "")
            super().emit(record)


def add_cali_log(file_name: str):
    # current_date = datetime.now().strftime("%Y-%m-%d")
    log_file_with_date = f"{file_name}"
    # Create a TimedRotatingFileHandler that rotates every day.
    rotating_handler = CaliHandler(
        log_file_with_date,
        when="midnight",
    )
    rotating_handler.suffix = "%Y-%m-%d.log"
    logger.add(rotating_handler, format=LogFormat.simple, level=10)
