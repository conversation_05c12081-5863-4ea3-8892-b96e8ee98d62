# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/09
# __author:       <PERSON><PERSON><PERSON> Shi

"""Tools."""
import configparser
import json
import pathlib
import pickle
import re
import sys
import textwrap
import time
import uuid
from collections import defaultdict
from contextlib import contextmanager
from datetime import datetime
from typing import List, Dict, Union, TYPE_CHECKING, Callable

from loguru import logger
from prettytable import PrettyTable

from pyQCat import get_version
from pyQCat.config import PyqcatConfig
from pyQCat.errors import CourierError
from pyQCat.executor import Backend
from pyQCat.executor.structures import FakeTaskContext
from pyQCat.invoker import DataCenter
# from pyQCat.parallel.parallel_utils import run_parallel_backend
from pyQCat.structures import QDict
from app.online_auto_calibration.logger import add_cali_log

if TYPE_CHECKING:
    from pyQCat.analysis.algorithms import IQdiscriminator

Q_PATTERN = re.compile(r"^q\d+$")
C_PATTERN = re.compile(r"^c\d+-\d+$")
QP_PATTERN = re.compile(r"^q\d+q\d+$")
DCM_PATTERN = re.compile(r"^q\d+_\d+\.bin$")

THRESHOLD_MAP = {
    "SingleShot": "dcm",
    "RBSingle": "single_gate",
    "XEBMultiple": "cz_gate",
}


def _generate_fake_context(cali_names: List[str]):
    q_names = []
    c_names = []
    qp_names = []
    discriminators = []
    for s_name in cali_names:
        if Q_PATTERN.match(s_name):
            q_names.append(s_name)
        elif C_PATTERN.match(s_name):
            c_names.append(s_name)
        elif QP_PATTERN.match(s_name):
            qp_names.append(s_name)
        elif DCM_PATTERN.match(s_name):
            discriminators.append(s_name)
    fake_cxt = FakeTaskContext(
        qubits=q_names,
        couplers=c_names,
        pairs=qp_names,
        discriminators=discriminators,
        config_field=False,
    )
    return fake_cxt


def _send_start_calibration_signal(
    id_: str, task_data: Dict, fake_task_context: FakeTaskContext
):
    logger.info(f"Calibrate {id_}, start...")
    task_data["status"] = "running"
    res = DataCenter().add_custom_task_his(
        task_data=task_data,
        bit_data=fake_task_context.to_dict(),
    )
    if res["code"] == 200:
        doc_id = res["data"].get("doc_id")
        logger.info(f"Send start calibration to <Courier> success: {res}")
        return doc_id
    else:
        raise CourierError("Send start calibration to <Courier> error", res)


def _send_stop_calibration_signal(
    doc_id: str, task_data: Dict, fake_task_context: FakeTaskContext
):
    task_data.update({"status": "success"})
    bit_data = fake_task_context.to_dict()
    res = DataCenter().update_custom_task_his(
        doc_id=doc_id,
        task_data=task_data,
        bit_data=bit_data,
    )
    if res["code"] == 200:
        logger.info(f"Calibrate {doc_id}, end.")
        logger.debug(f"Send end calibration to <Courier> success: {res}")
    else:
        raise CourierError("Send end calibration to <Courier> error", res)


def _convert_string(input_str: str):
    match = re.match(r"([a-zA-Z]+)(\d+)([a-zA-Z]+)(\d+)", input_str)
    if match:
        group1, num1, group2, num2 = match.groups()
        result_str = f"c{int(num1)}-{int(num2)}"
        return result_str
    else:
        return input_str


def qubit_pair2coupler(json_file: str):
    with open(json_file, "r", encoding="utf-8") as read_json_file:
        pair_data: Dict = json.load(read_json_file)
    couplers = []
    for pair_list in pair_data.values():
        couplers += [_convert_string(qubit_pair) for qubit_pair in pair_list]

    return couplers


@contextmanager
def emit_calibration_signal(
    batch_name: str, need_calibrated_units: List[str], publish: bool = False, point_label: int = None
) -> bool:
    if not publish:
        yield None
    else:
        id_ = "".join(str(uuid.uuid4()).split("-"))

        task_data = {
            "task_name": "online_y4",
            "task_type": 1,
            "task_desc": "online auto calibrate ...",
            "global_options": {},
            "policy": {},
            "status": "running",
            "sub_type": "",
            "sub_name": "",
        }

        fake_task_context = _generate_fake_context(need_calibrated_units)
        logged_groups = fake_task_context.to_dict()
        doc_id = _send_start_calibration_signal(id_, task_data, fake_task_context)

        # Add log here.
        logged_field_names = ["Types", "Calibrated Units", "Size"]
        logger.info(
            f"[AutoCalibration]Emit {batch_name} start signal to storm."
            f"\n{pretty_show_dict(logged_groups, field_names=logged_field_names)}\n\n"
        )
        try:
            yield doc_id
        finally:
            # need to release even if some unexpected errors.
            try:
                _send_stop_calibration_signal(doc_id, task_data, fake_task_context)
                logger.info(
                    f"[AutoCalibration]Emit {batch_name} stop signal to storm."
                    f"\n{pretty_show_dict(logged_groups, field_names=logged_field_names)}\n\n"
                )
            except CourierError as error:
                logger.error(f"{error}")


def get_nested_dict_value(dictionary, keys, default=None):
    try:
        for key in keys:
            dictionary = dictionary[key]
        return dictionary
    except (KeyError, TypeError):
        return default


def set_nested_dict_value(dictionary, keys, value):
    for key in keys[:-1]:
        dictionary = dictionary.setdefault(key, {})
    dictionary[keys[-1]] = value


def get_user_file_path(
    username: str, default_path: str = ".pyqcat/.user", file_name: str = "config.conf"
) -> Union[pathlib.Path, None]:
    """Get the relevant files in the visage user directory.

    Args:
        username (str): Username which used to load account.
        default_path (str): Default path.
        file_name (str): File name.

    Returns:
        Path object.
    """
    config = pathlib.Path.home() / default_path / username / file_name
    if config.is_file():
        return config
    else:
        return None


def get_online_params(
    username: str,
    password: str,
    use_cache=False,
    json_conf: str = str(pathlib.Path(pathlib.Path.cwd(), "conf", "online_conf.json")),
    conf_file: str = None,
    use_simulator: bool = False,
    auto_update: bool = False,
):
    with open(json_conf, "r", encoding="utf-8") as json_file:
        online_data = json.load(json_file)
    context_params = online_data.get("context_params")
    if not context_params:
        raise ValueError("context params is null.")
    env_qubits = context_params.get("env_bits")
    env_couplers = context_params.get("env_couplers")
    env_bits = env_qubits + env_couplers

    system_params = QDict(
        backend=QDict(
            username=username,
            password=password,
            config_file=conf_file or get_user_file_path(username),
            use_cache=use_cache,
        ),
        context=QDict(
            env_bits=env_bits,
            working_type=context_params["working_type"],
            divide_type=context_params["divide_type"],
            max_point_unit=context_params["max_point_units"],
            online_unit=[],
            # Shq 2024/01/30
            # Add f12 supports 2-state readout.
            f12_opt_bits=context_params["f12_opt_bits"],
            online=False,
            crosstalk=context_params["crosstalk"],
            xy_crosstalk=context_params["xy_crosstalk"],
        ),
        use_simulator=use_simulator,
        auto_update=auto_update,
    )
    return system_params


def get_qubit_groups(
    online_json: str = str(
        pathlib.Path(pathlib.Path.cwd(), "conf", "online_conf.json")
    ),
    step: Union[List[int], int] = None,
) -> Dict[str, List[str]]:
    # Simplest way to group, either equally or by a specific step size.
    with open(online_json, "r", encoding="utf-8") as json_file:
        online_data = json.load(json_file)
    total_bits = get_nested_dict_value(online_data, ["context_params", "online_bits"])
    length = len(total_bits)
    qubits = {}
    if not step:
        step = length
    if isinstance(step, int):
        count = 0
        for i in range(0, length, step):
            count += 1
            sub_qubits = total_bits[i: i + step]
            if sub_qubits:
                qubits.setdefault(f"{count}", sub_qubits)
    elif isinstance(step, list):
        current_index = 0
        for i, s in enumerate(step):
            qubits.setdefault(f"{i + 1}", total_bits[current_index: current_index + s])
            current_index += s
    return qubits


def get_coupler_groups(
    qubit_pair_json: str = str(
        pathlib.Path(pathlib.Path.cwd(), "conf", "qubit_pair_group_passed.json")
    )
) -> Dict[str, List[str]]:
    with open(qubit_pair_json, "r", encoding="utf-8") as json_file:
        qubit_pair_groups = json.load(json_file)
    coupler_groups = {}.fromkeys(qubit_pair_groups.keys())
    for group_name, groups in qubit_pair_groups.items():
        coupler_groups[group_name] = [
            _convert_string(qubit_pair) for qubit_pair in groups
        ]

    return coupler_groups


def get_qubit_pair_groups(
    qubit_pair_json: str = str(
        pathlib.Path(pathlib.Path.cwd(), "conf", "qubit_pair_group_passed.json")
    )
) -> Dict[str, List[str]]:
    with open(qubit_pair_json, "r", encoding="utf-8") as json_file:
        qubit_pair_groups = json.load(json_file)
    return qubit_pair_groups


def update_local_thresholds(
    username: str,
    sample: str,
    env_name: str,
    point_label: str,
    local_path: str = str(
        pathlib.Path(pathlib.Path.cwd(), "conf", "best_ever_thresholds.json")
    ),
):
    threshold_dict = {
        "chip_info": {
            "sample": sample,
            "env_name": env_name,
            "point_label": point_label,
        },
        "monster_version": get_version(),
        "dcm": {},
        "single_gate": {},
        "cz_gate": {},
    }

    ret_data = DataCenter().query_chip_all(
        username=username,
        sample=sample,
        env_name=env_name,
        point_label=point_label,
    )

    if ret_data.get("code") == 200:
        chip_data = ret_data.get("data")
        for name, data in chip_data.items():
            if Q_PATTERN.match(name):
                fidelity = get_nested_dict_value(data, ["parameters", "fidelity"])
                set_nested_dict_value(threshold_dict, ["single_gate", name], fidelity)
            if QP_PATTERN.match(name):
                xeb_fidelity = get_nested_dict_value(
                    data, ["parameters", "metadata", "std", "fidelity", "xeb_fidelity"]
                )
                set_nested_dict_value(threshold_dict, ["cz_gate", name], xeb_fidelity)
            if name.endswith("01.bin"):
                dcm: "IQdiscriminator" = pickle.loads(data["bin"])
                q_name = name.split("_")[0]
                threshold_dict["dcm"][q_name] = dcm.fidelity

        logger.info("Query chip data success!")
        if local_path:
            with open(local_path, "w", encoding="utf-8") as json_file:
                json.dump(threshold_dict, json_file, ensure_ascii=False)
        return threshold_dict
    else:
        logger.error(f"Query chip data timeout! Error code: {ret_data}")
        return None


def get_backend(system):
    config = PyqcatConfig(filename=system.backend.config_file, init_log=False)
    pre_log_path = (
        config.settings["system"]["log_path"] or config.settings["system"]["local_root"]
    )
    if len(sys.argv) > 1:
        for v in sys.argv[1:]:
            if v.startswith("log"):
                new_log_path = v.split("-")[-1]
                new_path = pathlib.Path(pre_log_path) / new_log_path
                config.settings["system"]["log_path"] = str(new_path)

                new_file = (
                    pathlib.Path(system.backend.config_file).parent
                    / f"temp-{new_log_path}.conf"
                )
                config.to_file(str(new_file))
                system.backend.config_file = str(new_file)
                cali_log_path = str(new_path / "online_auto_calibration_log")
                add_cali_log(cali_log_path)
                logger.info(f"Online auto calibration log save at: {cali_log_path}")
            # Shq 2024/01/25
            # fixed bug: Ignore the -y parameter required by invoker.
            if v == "-y":
                cali_log_path = f"{pre_log_path}/online_auto_calibration_log"
                add_cali_log(cali_log_path)
                logger.info(f"Online auto calibration log save at: {cali_log_path}")
    else:
        cali_log_path = f"{pre_log_path}/online_auto_calibration_log"
        add_cali_log(cali_log_path)
        logger.info(f"Online auto calibration log save at: {cali_log_path}")

    backend = Backend(**system.backend)

    # run_parallel_backend(conf_file=backend.context_manager.config)
    # time.sleep(1)

    backend.refresh()
    backend.system = system

    if not system.backend.use_cache:
        backend.context_manager.set_global_options(**system.context)
        logger.info("Start Backend Success | Use Local Config!")
    else:
        logger.info("Start Backend Success | Use Cache Config!")

    backend.infos()
    return backend


def get_thresholds(
    json_file: str = str(
        pathlib.Path(pathlib.Path.cwd(), "conf", "best_ever_thresholds.json")
    )
):
    with open(json_file, "r", encoding="utf-8") as fp:
        data = json.load(fp)
    return data


def filter_retried_experiment(
    records: Dict, need_filter_names: List[str], fields: List[str]
) -> Dict:
    clean_records = {}.fromkeys(need_filter_names, {})
    for exp_name, result in records.items():
        for name in need_filter_names:
            if name in exp_name:
                filtered_result = {
                    key: value for key, value in result.items() if key in fields
                }
                if not clean_records.get(name):
                    clean_records[name] = filtered_result
                else:
                    for field in fields:
                        field_data = get_nested_dict_value(clean_records, [name, field])
                        field_data.update(filtered_result.get(field, {}))
    return clean_records


def parse_calibration_config(config_path: str) -> QDict:
    config = configparser.ConfigParser()
    config.read(config_path)

    parsed_config = QDict()
    for section in config.sections():
        parsed_config[section] = QDict()
        for key, value in config.items(section):
            parsed_config[section][key] = value.strip('"')

    return parsed_config


def get_batch_options(batch_conf_path: str, indicate_one: str = None) -> Dict:
    def _change_templates(_batch_dict: Dict, template: str):
        for batch_exp, batch_config in _batch_dict.items():
            if not batch_config.get("param_path"):
                if "check" in batch_exp:
                    # Shq 2024.1.23
                    # fixed bug: [Errno 2] No such file or directory.
                    batch_config["param_path"] = str(
                        pathlib.Path(
                            pathlib.Path(batch_conf_path).parent.parent,
                            "templates",
                            "checker.json",
                        )
                    )
                else:
                    template_name = template.replace("_options", "")
                    # print(template_name)
                    # Shq 2024.1.23
                    # fixed bug: [Errno 2] No such file or directory.
                    batch_config["param_path"] = str(
                        pathlib.Path(
                            pathlib.Path(batch_conf_path).parent.parent,
                            "templates",
                            f"{template_name}.json",
                        )
                    )

    with open(batch_conf_path, "r", encoding="utf-8") as json_fp:
        batch_options = json.load(json_fp)
    if indicate_one:
        batch_options: Dict = batch_options.get(f"{indicate_one}_options")
        _change_templates(batch_options, indicate_one)
    else:
        for template, every_batch_item in batch_options.items():
            _change_templates(every_batch_item, template)
    return batch_options


def filter_bad_qubit_pairs(
    bad_qubits: List[str], bad_couplers: List[str], qubit_pairs: List[str]
):
    filtered_qubit_pairs = qubit_pairs[:]
    for qubit_pair in qubit_pairs:
        if bad_qubits:
            # Shq 2024/01/23
            # fixed bug: q45q46 filtered when q4 is bad.
            matches = re.findall(r"q\d+", qubit_pair)
            sub_q_1, sub_q_2 = matches
            if sub_q_1 in bad_qubits or sub_q_2 in bad_qubits:
                filtered_qubit_pairs.remove(qubit_pair)
        if bad_couplers:
            math_coupler = _convert_string(qubit_pair)
            # Shq 2024/01/29
            # fixed bug: ValueError: list.remove(x): x not in list
            if math_coupler in bad_couplers and qubit_pair in filtered_qubit_pairs:
                filtered_qubit_pairs.remove(qubit_pair)
    return filtered_qubit_pairs


def convert_coupler_to_qubits(coupler_str):
    match = re.match(r"c(\d+)-(\d+)", coupler_str)
    if match:
        start_num, end_num = match.groups()
        qubit_strs = [f"q{i}" for i in range(int(start_num), int(end_num) + 1)]
        return qubit_strs
    else:
        return [coupler_str]  # return the original string if no match


def filter_bad_qubits(
    bad_qubits: List[str], bad_couplers: List[str], qubits: List[str]
):
    filtered_qubits = qubits[:]
    if bad_couplers:
        for bad_coupler in bad_couplers:
            convert_bad_qubits = convert_coupler_to_qubits(bad_coupler)
            for convert_bad_qubit in convert_bad_qubits:
                if convert_bad_qubit not in bad_qubits:
                    bad_qubits.append(convert_bad_qubit)
    for qubit in qubits:
        if bad_qubits and qubit in bad_qubits:
            filtered_qubits.remove(qubit)

    return filtered_qubits


def pretty_show_dict(
    need_print_data: Dict, max_line_length: int = 98, field_names: List[str] = None
):
    table = PrettyTable()
    table.field_names = field_names or ["Groups", "Physical Units", "Size"]
    for group, units in need_print_data.items():
        if isinstance(units, list):
            # Shq 2024.01.22
            # fixed bug: TypeError: object of type 'NoneType' has no len()
            size = len(units)
            units = ",".join(units)
        elif isinstance(units, str):
            size = 1
        else:
            size = None
            units = str(units)
        # Wrap text for each cell content
        group_wrapped = textwrap.fill(group, width=max_line_length)
        units_wrapped = textwrap.fill(units, width=max_line_length)
        records = [group_wrapped, units_wrapped, f"{size}"]
        table.add_row(records)
    return table


def get_discriminator_names(qubit_list: List[str]) -> List[str]:
    return [f"{q_name}_01.bin" for q_name in qubit_list]


def merge_dicts(dicts):
    merged_dict = defaultdict(list)

    for d in dicts:
        for key, value in d.items():
            merged_dict[key].extend(value)

    return dict(merged_dict)


def filter_units_by_symbol(
    unit_groups: Dict[str, List[str]], exclude_unit: int, compare_symbol: str = "<="
):
    filtered_unit_groups = {}

    def _filter_strings(strings: List[str], some_unit: int, compare_operator: Callable):
        filtered_strings = []
        for s in strings:
            numbers = re.findall(r"\d+", s)
            if numbers and all(
                compare_operator(int(number), some_unit) for number in numbers
            ):
                filtered_strings.append(s)
        return filtered_strings

    def _convert_operator(operator_str: str):
        operators = {
            "<": lambda x, y: x < y,
            "<=": lambda x, y: x <= y,
            ">": lambda x, y: x > y,
            ">=": lambda x, y: x >= y,
            "==": lambda x, y: x == y,
            "!=": lambda x, y: x != y,
        }

        return operators.get(operator_str, None)

    operator = _convert_operator(compare_symbol)

    if operator:
        for group, units in unit_groups.items():
            filtered_units = _filter_strings(units, exclude_unit, operator)
            filtered_unit_groups.setdefault(group, filtered_units)

        return filtered_unit_groups
    else:
        return unit_groups


def extract_symbol_and_number(expressions: str):
    match = re.match(r"([<>]=?)?(\d+)", expressions)
    if match:
        symbol = match.group(1)
        number = int(match.group(2))
        return symbol, number
    else:
        return None, None


def set_simulator(options):
    if isinstance(options, dict):
        if "use_simulator" in options:
            options["use_simulator"] = True
            options["save_db"] = False
            options["simulator_pass_rate"] = 0.8
        else:
            for k in list(options.keys()):
                options[k] = set_simulator(options[k])
    return options


@contextmanager
def use_vip_timing(
    username: str, time_string: str = None, prefix: str = "[AutoCalibration]"
):
    """Locking for a period of time to perform a task.

    Args:
        username (str): Username.
        time_string (str): Such as 5:00:00--3600 which means 1-hour mission starting at 5:00 a.m.
        prefix (str): "[AutoCalibration]".
    Returns:

    """
    if time_string is None:
        yield
    else:
        data_center = DataCenter()
        try:
            oclock, span = time_string.split("--")
            _format = "%Y-%m-%d %H:%M:%S"
            current_ymd, _ = datetime.now().strftime(_format).split(" ")
            start_time = " ".join([current_ymd, oclock])
        except ValueError as error:
            logger.error(f"{prefix}Use vip timing failed!\n{error}")
        else:
            span = int(float(span))
            res = data_center.add_exclusive_date(username, start_time, span)
            if res.get("code") == 200:
                logger.info(
                    f"{prefix}{username}: Lock VIP timing success, start at {time_string}, and lasts {span} secs."
                )
            else:
                logger.error(
                    f"{prefix}{username}: Lock vip timing failed! Error code: {res}"
                )
            yield data_center
        finally:
            res = data_center.del_exclusive_user()
            if res.get("code") == 200:
                logger.info(f"{prefix}{username}: Release VIP timing success.")
            else:
                logger.error(f"{prefix}{username}: Release VIP timing failed.")


def sync_batch_options(options: Dict, **kwargs):
    for option_key, option_value in kwargs.items():
        if isinstance(options, dict):
            if option_key in options:
                options[option_key] = option_value
            else:
                for k in list(options.keys()):
                    sync_batch_options(options[k], **kwargs)
