import asyncio
import json
import time

from typing import List

from loguru import logger

from app.online_auto_calibration.manage.calibration import CalibrationManage
from app.online_auto_calibration.process.connect import CaliClient
from app.online_auto_calibration.tool.base_exception import ParamsException, ApiException
from app.online_auto_calibration.tool.utils import CaliProtocol


class ProtocolServer:
    def __init__(
        self,
        url: str = "tcp://127.0.0.1:9999",
    ):
        self._naga_ctx = None
        self._url = url
        self.close_services = False
        self.cali_client = CaliClient()

        self.cali_manage = CalibrationManage()

    async def start_heart_service(self):
        while not self.close_services:
            is_running = self.cali_manage.get_running()
            result_dict = {"heart_time": time.time(), "is_running": is_running}
            send_message = [
                b"",
                CaliProtocol.hearts.value,
                json.dumps(result_dict).encode(),
            ]
            await self.cali_client.send_multipart(send_message)
            await asyncio.sleep(10.0)

    async def start_calibration(self, message_id: bytes, data: bytes):
        try:
            self.cali_manage.start(data)
            back = {"code": 200, "msg": "success"}
            back_bytes = json.dumps(back).encode()
        except (ParamsException, ApiException) as e:
            back = {"code": e.code, "msg": str(e)}
            back_bytes = json.dumps(back).encode()
        except Exception as e:
            import traceback

            logger.warning(traceback.format_exc())
            back = {"code": 4000, "msg": str(e)}
            back_bytes = json.dumps(back).encode()
        await self.cali_client.send_multipart([message_id, b"", back_bytes])

    async def stop_calibration(self, message_id: bytes, data: bytes):
        try:
            self.cali_manage.stop()
            back = {"code": 200, "msg": "success"}
            back_bytes = json.dumps(back).encode()
        except (ParamsException, ApiException) as e:
            back = {"code": e.code, "msg": str(e)}
            back_bytes = json.dumps(back).encode()
        except Exception as e:
            logger.warning(e)
            back = {"code": 4000, "msg": str(e)}
            back_bytes = json.dumps(back).encode()
        await self.cali_client.send_multipart([message_id, b"", back_bytes])

    async def message_protocol(self, message: List[bytes]):
        message_id, protocol, pub_data = message
        if protocol == CaliProtocol.start_cali.value:
            await self.start_calibration(message_id, pub_data)
        elif protocol == CaliProtocol.stop_cali.value:
            await self.stop_calibration(message_id, pub_data)
        else:
            print("暂不支持的协议")

    async def recv_data(self):
        try:
            res_ = await self.cali_client.poller.poll()
            res = dict(res_)
            # recv subscribe message
            if self.cali_client.naga_socket in res:
                msgs = await self.cali_client.recv_multipart()
                logger.info(f"Recv zmq message {msgs}")
                await self.message_protocol(msgs)
        except Exception as e:
            logger.warning(f"Zmq message recv error, {e}.")

    async def start_listen_service(self):
        logger.info(f"Start auto calibration listening service...")
        while not self.close_services:
            await self.recv_data()

    async def start_process_service(self):
        logger.info(f"Start auto calibration process service...")
        while not self.close_services:
            await asyncio.sleep(10.0)

    async def run_service(self):
        try:
            await asyncio.gather(
                self.start_listen_service(),
                self.start_heart_service(),
                # self.start_process_service(),
            )
        except asyncio.CancelledError:
            logger.warning("Calibration service was cancelled.")

    def run(self):
        import asyncio

        asyncio.run(self.run_service())


if __name__ == "__main__":
    server = ProtocolServer()
    server.run()
