from typing import Union, Any, List

import zmq
from zmq.asyncio import Socket, Poller, Context

from app.config import settings


class CaliClient:
    def __init__(
        self,
        url: str = settings.NAGA_SOCK_ADDR,
        identity: str = "cali_client",
    ):
        self._naga_ctx = None
        self._url = url
        self.close_services = False
        self._naga_socket: Union[Socket, Any] = None

        self.poller: Union[Poller, Any] = None
        # 初始化创建 zmq 连接
        self._create_sock_instance(identity)

    def _create_sock_instance(self, identity: str):
        """
        create sock for tcp connection between calibration and naga.
        """
        self._naga_ctx = Context().instance()

        # ROUTER/DEALER mode to recv api message and back command.
        self.naga_socket = self._naga_ctx.socket(zmq.DEALER)

        self.naga_socket.setsockopt(zmq.IDENTITY, identity.encode())
        self.naga_socket.connect(self._url)

        self.poller = Poller()
        self.poller.register(self.naga_socket, zmq.POLLIN)

    async def send_multipart(self, data: List[bytes]):
        await self.naga_socket.send_multipart(data)

    async def recv_multipart(self):
        return await self.naga_socket.recv_multipart()
