# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/02/17
# __author:       <PERSON><PERSON>

import re
from functools import cmp_to_key
from typing import (
    Any,
    Dict,
    List,
    Mapping,
    Optional,
    Sequence,
    Tuple,
)

import matplotlib as mpl
import matplotlib.collections as mpl_collections
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import colors
from mpl_toolkits import axes_grid1

from pyQCat.processor.heatmap import PolygonUnit, Point
from pyQCat.qubit import Qubit
from pyQCat.tools import relative_luminance

QubitTuple = Tuple[Qubit, ...]

Polygon = Sequence[Tuple[float, float]]


Q_PATTERN = re.compile(r"^q\d+$")
C_PATTERN = re.compile(r"^c\d+-\d+$")
QP_PATTERN = re.compile(r"^q\d+q\d+$")


class RecordHeatMap:
    """Distribution of a value in 2D qubit lattice as a color map."""

    def __init__(
        self,
        value_map: Mapping[str, Dict],
        env_qubits: Mapping[str, Dict],
        **kwargs,
    ):
        self._value_map: Mapping[str, Dict] = value_map
        self.env_qubits: Mapping[str, Dict] = env_qubits
        self.color_scatter = ["yellow", "green", "red"]
        self.sub_ax_index_now = 0
        self.axis_name = ""
        self.fig = None
        cmap = colors.ListedColormap(self.color_scatter, name="scatter")
        self._config = {
            "plot_colorbar": True,
            "colorbar_position": "right",
            "colorbar_size": "3%",
            "colorbar_pad": "2%",
            "annotation_format": ".4g",
            "coupler_margin": 0.03,
            "coupler_width": 0.6,
            "show_value": True,
            "collection_options": {
                "cmap": cmap,
                "linewidths": 1,
                "edgecolor": "white",
            },
        }
        self._config.update(kwargs)

    @property
    def config(self):
        return self._config

    def update_config(self, **kwargs) -> "RecordHeatMap":
        self._config.update(kwargs)
        return self

    @classmethod
    def _qubit_to_polygon(cls, qubit) -> Tuple[Polygon, Point]:
        x, y = float(qubit.row), float(qubit.col)
        return (
            [
                (y - 0.5, x - 0.5),
                (y - 0.5, x + 0.5),
                (y + 0.5, x + 0.5),
                (y + 0.5, x - 0.5),
            ],
            Point(y, x),
        )

    def _get_annotation_value(self, value) -> Optional[str]:
        format_str = self._config["annotation_format"] or ".4g"
        return format(float(value), format_str)

    def _qubits_to_polygon(self, qubits: QubitTuple) -> Tuple[Polygon, Point]:
        coupler_margin = self._config["coupler_margin"]
        coupler_width = self._config["coupler_width"]
        cwidth = coupler_width / 2.0
        setback = 0.5 - cwidth
        row1, col1 = map(float, (qubits[0].row, qubits[0].col))
        row2, col2 = map(float, (qubits[1].row, qubits[1].col))

        polygon: Polygon = []
        if coupler_width <= 0:
            polygon: Polygon = []
        elif row1 == row2:  # horizontal
            col1, col2 = min(col1, col2), max(col1, col2)
            col_center = (col1 + col2) / 2.0
            polygon = [
                (col1 + coupler_margin, row1),
                (col_center - setback, row1 + cwidth - coupler_margin),
                (col_center + setback, row1 + cwidth - coupler_margin),
                (col2 - coupler_margin, row2),
                (col_center + setback, row1 - cwidth + coupler_margin),
                (col_center - setback, row1 - cwidth + coupler_margin),
            ]
        elif col1 == col2:  # vertical
            row1, row2 = min(row1, row2), max(row1, row2)
            row_center = (row1 + row2) / 2.0
            polygon = [
                (col1, row1 + coupler_margin),
                (col1 + cwidth - coupler_margin, row_center - setback),
                (col1 + cwidth - coupler_margin, row_center + setback),
                (col2, row2 - coupler_margin),
                (col1 - cwidth + coupler_margin, row_center + setback),
                (col1 - cwidth + coupler_margin, row_center - setback),
            ]

        return polygon, Point((col1 + col2) / 2.0, (row1 + row2) / 2.0)

    def _get_polygon_units(self, value_map: Mapping[str, Dict]) -> List[PolygonUnit]:
        def sort_value(item1, item2):
            qubit_name_1, value_1 = item1
            qubit_name_2, value_2 = item2
            if len(value_1.get("qubit")) == 1:
                # for qubits
                return 1 if int(qubit_name_1[1:]) > int(qubit_name_2[1:]) else -1
            else:
                # for coupler
                qubits1 = value_1.get("qubit")
                qubits2 = value_2.get("qubit")
                bit_a1, bit_b1 = qubits1
                bit_a2, bit_b2 = qubits2
                row1 = bit_a1.row + bit_b1.row
                col1 = bit_a1.col + bit_b1.col
                row2 = bit_a2.row + bit_b2.row
                col2 = bit_a2.col + bit_b2.col

                if row1 < row2:
                    return -1
                elif row1 > row2:
                    return 1
                elif col1 < col2:
                    return -1
                elif col1 > col2:
                    return 1
                else:
                    return 0

        polygon_unit_list: List[PolygonUnit] = []
        for index, (qubit_name, value) in enumerate(
            sorted(value_map.items(), key=cmp_to_key(sort_value))
        ):
            qubits = value.get("qubit")
            # if value is not None:
            if len(qubits) == 1:
                polygon, center = self._qubit_to_polygon(qubits[0])
            else:
                polygon, center = self._qubits_to_polygon(qubits)
            try:
                value_dict = value.get(self.axis_name) or {}
                # value data
                v_v = value_dict.get("state")
                value_data = value_dict.get("value")
                if v_v is not None:
                    if isinstance(value_data, list):
                        p0, p1 = self._get_annotation_value(
                            value_data[0]
                        ), self._get_annotation_value(value_data[1])
                        pre_annot = f"{qubit_name}:\n{p0},{p1}"
                    else:
                        pre_annot = (
                            f"{qubit_name}:\n{self._get_annotation_value(value_data)}"
                        )
                else:
                    v_v = np.nan
                    pre_annot = ""
            except Exception as e:
                v_v = np.nan
                pre_annot = ""
            if len(qubits) == 2:
                bits = sorted([int(bit.name[1:]) for bit in qubits])
                if self._config.get("style") == "Coupler":
                    unit_name = f"c{bits[0]}-{bits[1]}"
                else:
                    unit_name = f"q{bits[0]}q{bits[1]}"
            else:
                unit_name = qubits[0].name

            if self._config.get("show_value") is False:
                pre_annot = unit_name

            polygon_unit_list.append(
                PolygonUnit(
                    polygon=polygon,
                    center=center,
                    value=v_v,
                    annot=pre_annot,
                )
            )
        return polygon_unit_list

    def _plot_colorbar(
        self, mappable: mpl.cm.ScalarMappable, ax: plt.Axes
    ) -> mpl.colorbar.Colorbar:
        """Plots the colorbar. Internal."""
        colorbar_ax = axes_grid1.make_axes_locatable(ax).append_axes(
            position=self._config["colorbar_position"],
            size=self._config["colorbar_size"],
            pad=self._config["colorbar_pad"],
        )
        position = self._config["colorbar_position"]
        orien = "vertical" if position in ("left", "right") else "horizontal"

        colorbar = ax.figure.colorbar(
            mappable,
            colorbar_ax,
            ax,
            ticks=[0, 1, 2],
            label="state",
            cmap=self._config.get("collection_options", {}).get("cmap"),
            orientation=orien,
            **self._config.get("colorbar_options", {}),
        )
        colorbar_ax.set_yticklabels(["Fail", "Pass", "Error"])
        colorbar_ax.tick_params(axis="y", direction="out")
        return colorbar

    def _write_annotations(
        self,
        centers_and_annot: List[Tuple[Point, Optional[str]]],
        collection: mpl_collections.Collection,
        ax: plt.Axes,
    ) -> None:
        """Writes annotations to the center of cells. Internal."""
        for (center, annotation), facecolor in zip(
            centers_and_annot, collection.get_facecolor()
        ):
            # Calculate the center of the cell, assuming that it is a square
            # centered at (x=col, y=row).
            if not annotation:
                continue
            x, y = center
            face_luminance = relative_luminance(facecolor)
            text_color = "black" if face_luminance > 0.4 else "white"
            text_kwargs = dict(color=text_color, ha="center", va="center", fontsize=7)
            text_kwargs.update(self._config.get("annotation_text_kwargs", {}))
            ax.text(x, y, annotation, **text_kwargs)

    def _plot_on_axis(self, ax: plt.Axes) -> mpl_collections.Collection:
        # Step-1: Convert value_map to a list of polygons to plot.
        polygon_list = self._get_polygon_units(value_map=self._value_map)
        collection: mpl_collections.Collection = mpl_collections.PolyCollection(
            [c.polygon for c in polygon_list],
            **self._config.get("collection_options", {}),
        )
        collection.set_clim(
            self._config.get("vmin") or 0, self._config.get("vmax") or 2
        )

        a = []
        for c in polygon_list:
            a.append(c.value if isinstance(c.value, int) else np.nan)
        collection.set_array(np.array(a))
        # Step-2: Plot the polygons
        ax.add_collection(collection)
        collection.update_scalarmappable()
        # Step-3: Write annotation texts
        if self._config.get("annotation_map") or self._config.get("annotation_format"):
            self._write_annotations(
                [(c.center, c.annot) for c in polygon_list], collection, ax
            )
        ax.set(xlabel="column", ylabel="row")
        # Step-4: Draw colorbar if applicable
        if self._config.get("plot_colorbar"):
            self._plot_colorbar(collection, ax)
        # Step-5: Set min/max limits of x/y-axis on the plot.
        rows = set(
            [q.row for value in self._value_map.values() for q in value.get("qubit")]
        )
        cols = set(
            [q.col for value in self._value_map.values() for q in value.get("qubit")]
        )
        min_row, max_row = min(rows), max(rows)
        min_col, max_col = min(cols), max(cols)
        min_xtick = np.floor(min_col)
        max_xtick = np.ceil(max_col)
        ax.set_xticks(np.arange(min_xtick, max_xtick + 1))
        min_ytick = np.floor(min_row)
        max_ytick = np.ceil(max_row)
        ax.set_yticks(np.arange(min_ytick, max_ytick + 1))
        ax.set_xlim((min_xtick - 0.6, max_xtick + 0.6))
        ax.set_ylim((max_ytick + 0.6, min_ytick - 0.6))
        # Step-6: Set title
        if self._config.get("title"):
            ax.set_title(self._config["title"], fontweight="bold")
        return collection

    def _plot_qubit_on_axis(self, ax: plt.Axes) -> mpl_collections.Collection:
        polygon_list = self._get_polygon_units(self.env_qubits)
        collection: mpl_collections.Collection = mpl_collections.PolyCollection(
            [c.polygon for c in polygon_list],
            cmap="binary",
            linewidths=1,
            edgecolor="lightgrey",
            linestyle="dashed",
        )
        a = []
        for _ in polygon_list:
            a.append(np.nan)
        collection.set_array(np.array(a))
        ax.add_collection(collection)
        collection.update_scalarmappable()
        return collection

    def plot(
        self,
        total_check_num,
        total_cali_num,
        title_his: str,
        title_check: str,
        title_cali: str,
        ax: Optional[plt.Axes] = None,
        **kwargs: Any,
    ) -> Tuple[plt.Axes, mpl_collections.Collection, mpl_collections.Collection]:
        if not ax:
            if total_check_num and total_cali_num:
                cols = 3
            else:
                cols = 2
            size_col = cols * 6
            self.fig, ax = plt.subplots(nrows=1, ncols=cols, figsize=(size_col, 8))
        plot_colorbar = False
        self.axis_name = "history"
        self.update_config(**kwargs)
        ax0 = ax[self.sub_ax_index_now]
        self.update_config(title=title_his, plot_colorbar=plot_colorbar)

        self._plot_qubit_on_axis(ax0)
        self._plot_on_axis(ax0)

        if total_check_num:
            if not total_cali_num:
                plot_colorbar = True
            self.axis_name = "check"
            self.sub_ax_index_now += 1
            ax1 = ax[self.sub_ax_index_now]
            self.update_config(title=title_check, plot_colorbar=plot_colorbar)

            self._plot_qubit_on_axis(ax1)
            self._plot_on_axis(ax1)

        if total_cali_num:
            plot_colorbar = True
            self.axis_name = "calibration"
            self.sub_ax_index_now += 1
            ax1 = ax[self.sub_ax_index_now]
            self.update_config(title=title_cali, plot_colorbar=plot_colorbar)

            self._plot_qubit_on_axis(ax1)
            self._plot_on_axis(ax1)

        return ax

    def save_fig(self, path: str):
        if path:
            self.fig.savefig(path)


class LineFigure:

    def __init__(self, x_data=None, y_data=None):
        self.x_data = x_data
        self.y_data = y_data
        fig, axs = plt.subplots(figsize=(12, 8))
        self.fig = fig
        self.axs = axs

    def set_info(self, title, x_label=None, y_label=None, x_ticks=None, legend=None):
        self.axs.set_title(title)
        if x_label:
            self.axs.set_xlabel(x_label)
        if y_label:
            self.axs.set_ylabel(y_label)
        if x_ticks:
            self.axs.set_xticks(x_ticks)
            self.axs.set_xticklabels(x_ticks)
        if legend:
            plt.legend(labels=legend)

    def set_data(self, y_data, x_data=None):
        if not x_data:
            x_data = [x for x in range(1, len(y_data) + 1)]
        self.y_data = y_data
        self.x_data = x_data
        self.axs.cla()
        self.axs.plot(self.x_data, self.y_data, linewidth=1)

    def save_fig(self, path):
        if path:
            self.fig.savefig(path)
