# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/01/17
# __author:       <PERSON><PERSON><PERSON> Shi

"""Online auto calibration start module."""
import asyncio
import json
import re
import time
import traceback
import uuid
from datetime import datetime
from typing import Dict, List

from loguru import logger

from app.online_auto_calibration.process.connect import CaliClient
from app.online_auto_calibration.tool.utils import CaliProtocol
from pyQCat.executor import Backend
from app.online_auto_calibration.execute.calibrate_cz_gate import (
    run as calibrate_cz_runner,
)
from app.online_auto_calibration.execute.calibrate_readout_point import (
    check_readout_drift,
)
from app.online_auto_calibration.execute.calibrate_single_gate import (
    run as calibrate_single_gate_runner,
)
from app.online_auto_calibration.execute.calibrate_voltage import (
    run as calibrate_voltage_runner,
)
from app.online_auto_calibration.manage.records import BaseRecords
from app.online_auto_calibration.utilities import (
    get_nested_dict_value,
    pretty_show_dict,
    use_vip_timing,
)


class RunnerType:
    READOUT_POINT = "calibrate_readout_point"
    VOLTAGE = "calibrate_voltage"
    SINGLE_GATE = "calibrate_single_gate"
    CZ_GATE = "calibrate_cz_gate"


class CalibrationType:
    REFRESH_DCM = "refresh_dcm"
    CALIBRATE_VOL = "cali_voltage"
    CALIBRATE_ALL = "cali_all"
    CALIBRATE_SINGLE_GATE = "cali_single_gate"
    CALIBRATE_CZ_GATE = "cali_cz_gate"


class VipTiming:
    REFRESH_DCM = "5min"
    CALIBRATE_VOL = "40min"
    CALIBRATE_ALL = "3h"


def _wrapper_readout_point_runner(
    runner_name: str,
    backend: Backend,
    test_qubits: Dict[str, List[str]],
    calibration_config: Dict,
    history_threshold: Dict,
    batch_options: Dict,
    result_record: BaseRecords,
):
    bad_qubit_groups = {}
    logger.info(
        f"[AutoCalibration]Readout fidelity drift check start:\n"
        f"{pretty_show_dict(test_qubits)}"
    )
    for group, test_qubit_list in test_qubits.items():
        logger.info(
            f"[AutoCalibration]All {len(test_qubit_list)} "
            f"qubits will enter readout fidelity drift check..."
        )
        fidelity_tolerance = get_nested_dict_value(
            calibration_config, ["readout_point", "history_offset"]
        )
        batch_kwargs = get_nested_dict_value(
            batch_options, [f"{runner_name}_options", "check_readout_drift"]
        )
        if test_qubit_list:
            batch_kwargs.update({"physical_units": test_qubit_list})
        bad_qubits = check_readout_drift(
            backend=backend,
            thresholds=history_threshold,
            tolerance=float(fidelity_tolerance),
            result_record=result_record,
            **batch_kwargs,
        )
        bad_qubit_groups.setdefault(group, bad_qubits)
    result_record.save_result_to_db(item_type="readout")
    logger.info(
        f"[AutoCalibration]Readout fidelity drift check finished, bad qubits:\n"
        f"{pretty_show_dict(bad_qubit_groups)}\n"
    )
    return bad_qubit_groups


def _wrapper_single_gate_runner(
    runner_name: str,
    backend: Backend,
    test_qubits: Dict[str, List[str]],
    calibration_config: Dict,
    history_threshold: Dict,
    batch_options: Dict,
    result_record: BaseRecords,
    bad_qubit_groups: Dict[str, List[str]] = None,
    bad_coupler_groups: Dict[str, List[str]] = None,
) -> Dict[str, List[str]]:
    check_batch_kwargs: Dict = get_nested_dict_value(
        batch_options, [f"{runner_name}_options", "check_gate_params_drift"]
    )
    cali_batch_kwargs: Dict = get_nested_dict_value(
        batch_options, [f"{runner_name}_options", "calibrate_single_gate"]
    )
    failed_qubits = calibrate_single_gate_runner(
        backend=backend,
        history_best_thresholds=history_threshold,
        qubit_groups=test_qubits,
        tolerance=float(
            get_nested_dict_value(calibration_config, ["single_gate", "history_offset"])
        ),
        rb_tolerance=float(
            get_nested_dict_value(calibration_config, ["single_gate", "rb_threshold"])
        ),
        checker_config=check_batch_kwargs,
        calibration_config=cali_batch_kwargs,
        bad_qubit_groups=bad_qubit_groups,
        bad_coupler_groups=bad_coupler_groups,
        result_record=result_record,
    )
    result_record.save_result_to_db(item_type="s_gate")
    return failed_qubits


def _wrapper_voltage_runner(
    runner_name: str,
    backend: Backend,
    test_couplers: Dict[str, List[str]],
    test_qubits: Dict[str, List[str]],
    batch_options: Dict,
    result_record: BaseRecords,
    mode: str = "all",
) -> [Dict[str, List[str]], Dict[str, List[str]]]:
    batch_kwargs: Dict = get_nested_dict_value(
        batch_options, [f"{runner_name}_options", runner_name]
    )
    return calibrate_voltage_runner(
        backend=backend,
        coupler_groups=test_couplers,
        qubit_groups=test_qubits,
        calibration_config=batch_kwargs,
        result_record=result_record,
        mode=mode,
    )


def _wrapper_cz_gate_runner(
    runner_name: str,
    backend: Backend,
    test_pairs: Dict[str, List[str]],
    calibration_config: Dict,
    history_threshold: Dict,
    batch_options: Dict,
    result_record: BaseRecords,
    bad_qubits: List[str] = None,
    bad_couplers: List[str] = None,
):
    history_offset = get_nested_dict_value(
        calibration_config, ["cz_gate", "history_offset"]
    )
    cali_threshold = get_nested_dict_value(
        calibration_config, ["cz_gate", "cali_threshold"]
    )
    nm_threshold = get_nested_dict_value(
        calibration_config, ["cz_gate", "nm_check_threshold"]
    )
    check_batch_kwargs: Dict = get_nested_dict_value(
        batch_options, [f"{runner_name}_options", "check_xeb_drift"]
    )
    nm_cali_batch_kwargs: Dict = get_nested_dict_value(
        batch_options, [f"{runner_name}_options", "calibrate_nm_xeb_flow"]
    )
    cz_cali_batch_kwargs: Dict = get_nested_dict_value(
        batch_options, [f"{runner_name}_options", "calibrate_cz_flow"]
    )
    return calibrate_cz_runner(
        backend=backend,
        history_best_thresholds=history_threshold,
        tolerance=float(history_offset),
        threshold=float(cali_threshold),
        nm_check_threshold=float(nm_threshold),
        pair_dict=test_pairs,
        checker_config=check_batch_kwargs,
        nm_calibration_config=nm_cali_batch_kwargs,
        cz_calibration_config=cz_cali_batch_kwargs,
        bad_qubits=bad_qubits,
        bad_couplers=bad_couplers,
        result_record=result_record,
    )


def calibrate_all(
    backend: Backend,
    config: Dict,
    history_threshold: Dict,
    batch_options: Dict,
    initial_qubit_groups: Dict[str, List[str]],
    initial_coupler_groups: Dict[str, List[str]],
    initial_qubit_pair_groups: Dict[str, List[str]],
    result_record: BaseRecords,
    mode: str = "all",
):
    """Online auto calibration flow. References for all code implementations
    are as follows:
    https://document.qpanda.cn/docs/rp3OV8oomXumBNAm
    """
    # step a .
    # The qubit that fails the single shot is only recorded for the time being, and nothing else is done.
    _wrapper_readout_point_runner(
        runner_name=RunnerType.READOUT_POINT,
        backend=backend,
        test_qubits=initial_qubit_groups,
        calibration_config=config,
        history_threshold=history_threshold,
        batch_options=batch_options,
        result_record=result_record,
    )

    # step b & c.
    (
        calibrate_voltage_failed_qubits,
        calibrate_voltage_failed_couplers,
    ) = _wrapper_voltage_runner(
        runner_name=RunnerType.VOLTAGE,
        backend=backend,
        test_couplers=initial_coupler_groups,
        test_qubits=initial_qubit_groups,
        batch_options=batch_options,
        result_record=result_record,
        mode=mode,
    )
    # debug code.
    # calibrate_voltage_failed_qubits = {}
    # calibrate_voltage_failed_couplers = {}

    # step d.
    calibrate_single_gate_failed_qubits = _wrapper_single_gate_runner(
        runner_name=RunnerType.SINGLE_GATE,
        backend=backend,
        test_qubits=initial_qubit_groups,
        calibration_config=config,
        history_threshold=history_threshold,
        batch_options=batch_options,
        bad_qubit_groups=calibrate_voltage_failed_qubits,
        bad_coupler_groups=calibrate_voltage_failed_couplers,
        result_record=result_record,
    )
    # debug code.
    # calibrate_single_gate_failed_qubits = {"1":["q1","q2"]}

    # step f.
    do_not_need_cz_calibration_qubits = []
    do_not_need_cz_calibration_couplers = []
    # collect all failed qubits and couplers.
    for bad_couplers in calibrate_voltage_failed_couplers.values():
        do_not_need_cz_calibration_couplers += bad_couplers
    for bad_qubits in calibrate_voltage_failed_qubits.values():
        do_not_need_cz_calibration_qubits += bad_qubits
    for single_gate_cali_failed_qubits in calibrate_single_gate_failed_qubits.values():
        do_not_need_cz_calibration_qubits += single_gate_cali_failed_qubits

    _wrapper_cz_gate_runner(
        runner_name=RunnerType.CZ_GATE,
        backend=backend,
        test_pairs=initial_qubit_pair_groups,
        calibration_config=config,
        history_threshold=history_threshold,
        batch_options=batch_options,
        bad_qubits=do_not_need_cz_calibration_qubits,
        bad_couplers=do_not_need_cz_calibration_couplers,
        result_record=result_record,
    )


def _parse_vip_timing(time_span: str):
    pattern = r"(?:(\d+\.?\d*)h)?(?:(\d+\.?\d*)min)?(?:(\d+\.?\d*)s)?"
    match = re.match(pattern, time_span)
    if match:
        hours = 0
        minutes = 0
        seconds = 0
        if match.group(1):
            hours = float(match.group(1))
        if match.group(2):
            minutes = float(match.group(2))
        if match.group(3):
            seconds = float(match.group(3))
        total_seconds = hours * 3600 + minutes * 60 + seconds
    else:
        raise ValueError(f"Invalid time span: unsupported format {time_span}")

    if total_seconds < 60 or total_seconds > 36000:
        raise ValueError(
            f"Invalid time span {total_seconds}s: total time must be between 1 minute and 10 hours"
        )

    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S").split(" ")[-1]
    vip_time_str = f"{current_time}--{total_seconds}"
    return vip_time_str


def run(
    calibration_type: str,
    backend: Backend,
    config: Dict,
    history_threshold: Dict,
    batch_options: Dict,
    initial_qubit_groups: Dict[str, List[str]],
    initial_coupler_groups: Dict[str, List[str]],
    initial_qubit_pair_groups: Dict[str, List[str]],
    vip_timing: str = None,
):
    point_label = backend._config.system.point_label
    sample = backend._config.system.sample
    env_name = backend._config.system.env_name
    result_client = CaliClient(identity="result_client")
    result_record = BaseRecords(
        sample, env_name, point_label, history_threshold, config, result_client
    )
    username = backend._username
    if vip_timing:
        time_span = _parse_vip_timing(vip_timing)
    else:
        time_span = None
    try:
        with use_vip_timing(username, time_span):
            if calibration_type == CalibrationType.CALIBRATE_ALL:
                return calibrate_all(
                    backend,
                    config,
                    history_threshold,
                    batch_options,
                    initial_qubit_groups,
                    initial_coupler_groups,
                    initial_qubit_pair_groups,
                    result_record,
                )
            elif calibration_type == CalibrationType.CALIBRATE_VOL:
                return _wrapper_voltage_runner(
                    runner_name=RunnerType.VOLTAGE,
                    backend=backend,
                    test_couplers=initial_coupler_groups,
                    test_qubits=initial_qubit_groups,
                    batch_options=batch_options,
                    result_record=result_record,
                )
            elif calibration_type == CalibrationType.REFRESH_DCM:
                return _wrapper_readout_point_runner(
                    runner_name=RunnerType.READOUT_POINT,
                    backend=backend,
                    test_qubits=initial_qubit_groups,
                    calibration_config=config,
                    history_threshold=history_threshold,
                    batch_options=batch_options,
                    result_record=result_record,
                )
            elif calibration_type == CalibrationType.CALIBRATE_SINGLE_GATE:
                return _wrapper_single_gate_runner(
                    runner_name=RunnerType.SINGLE_GATE,
                    backend=backend,
                    test_qubits=initial_qubit_groups,
                    calibration_config=config,
                    history_threshold=history_threshold,
                    batch_options=batch_options,
                    result_record=result_record,
                )
            elif calibration_type == CalibrationType.CALIBRATE_CZ_GATE:
                # only calibrate CZ gate.
                return _wrapper_cz_gate_runner(
                    runner_name=RunnerType.CZ_GATE,
                    backend=backend,
                    test_pairs=initial_qubit_pair_groups,
                    calibration_config=config,
                    history_threshold=history_threshold,
                    batch_options=batch_options,
                    result_record=result_record,
                )
            else:
                logger.error(
                    f"[AutoCalibration]Invalid calibration type: {calibration_type}"
                )
    except Exception as unexpected_error:
        data = {
            "alarm_type": 0x01,
            "alarm_level": 0x04,
            "alarm_id": str(uuid.uuid4()),
            "alarm_time": int(time.time() * 1000),
            "alarm_des": "platform calibration run error",
            "alarm_detail": {
                "error_msg": f"{traceback.format_exc()}"
            },
            "task_msg": {
                "task_id": "",
                "task_user": "",
                "exp_name": "",
                "retry_time": 0
            }
        }
        asyncio.run(
            result_client.send_multipart(
                [
                    b"",
                    CaliProtocol.cali_error.value,
                    json.dumps(data).encode(),
                ]
            )
        )
        logger.critical(
            f"[AutoCalibration]Some unexpected error occurred: {unexpected_error}\n{traceback.format_exc()}"
        )
