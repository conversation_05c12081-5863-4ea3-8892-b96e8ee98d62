import datetime
import json
import multiprocessing
import os
import time
from typing import Dict, List

from loguru import logger

from app.online_auto_calibration import scheduler
from app.online_auto_calibration.logger import add_cali_log
from app.online_auto_calibration.strategy.runner import CalibrationType, VipTiming
from app.online_auto_calibration.tool.base_exception import ParamsException, CodeMessage
from app.online_auto_calibration.tool.utils import (
    get_online_qubit_groups,
)
from app.online_auto_calibration.strategy.runner import run as execute_calibration
from app.online_auto_calibration.tool.web_client import conf_manage
from app.online_auto_calibration.utilities import (
    _convert_string,
    get_nested_dict_value,
    pretty_show_dict,
    set_simulator,
    sync_batch_options,
    filter_units_by_symbol,
    extract_symbol_and_number,
)
from pyQCat.concurrent import cleanup_resources
from pyQCat.executor import Backend
from pyQCat.structures import QDict


class ParamManage:
    def __init__(self):
        self._args = None
        self._split_mark = "--"

        self.now_point_label = None

        self.sample = None
        self.env_name = None

    def set_init_args(self, sample: str, env_name: str, arg_parse: Dict):
        self._args = arg_parse
        self.sample = sample
        self.env_name = env_name

    def check_init_params(self) -> List:
        if not self.sample or not self.env_name:
            raise ParamsException(CodeMessage.C_4001)

        point_labels = self._args.get("point_labels")

        online_point_label = conf_manage.get_online_point_label()
        if not online_point_label:
            raise ParamsException(CodeMessage.C_4002)

        # 没有上传 point_label 参数则默认校准所有 point_label
        if not point_labels:
            return online_point_label
        # 上传了 point_label 则校验是否都上线
        else:
            point_map = []
            online_pls = {ol.get("point_label"): ol for ol in online_point_label}
            point_label_list = (
                point_labels.split(",")
                if isinstance(point_labels, str)
                else point_labels
            )
            for exec_pl in point_label_list:
                if exec_pl not in online_pls.keys():
                    raise ParamsException(CodeMessage.C_4003)
                else:
                    point_map.append(online_pls.get(exec_pl))
            return point_map

    @classmethod
    def get_max_unit_point_label(cls):
        online_point_label = conf_manage.get_online_point_label()
        if not online_point_label:
            raise ParamsException(CodeMessage.C_4002)
        max_unit = 0
        max_point_label = ""
        for point_label_value in online_point_label:
            point_label = point_label_value.get("point_label")
            online_params = conf_manage.get_online_params(point_label=point_label)
            if not online_params:
                raise ParamsException(CodeMessage.C_4004)
            context_params = online_params.get("context_params")
            online_bits = context_params.get("online_bits")
            online_couplers = context_params.get("online_couplers")
            now_units = len(online_bits) + len(online_couplers)
            if now_units > max_unit:
                max_point_label = point_label
                max_unit = now_units
        return max_point_label

    def get_online_system_params(
        self,
        username: str,
        password: str,
        conf_path: str,
        use_cache: bool = False,
        use_simulator: bool = False,
        auto_update: bool = False,
    ):
        online_params = conf_manage.get_online_params(point_label=self.now_point_label)
        if not online_params:
            raise ParamsException(CodeMessage.C_4004)
        context_params = online_params.get("context_params")
        env_qubits = context_params.get("env_bits")
        env_couplers = context_params.get("env_couplers")
        env_bits = env_qubits + env_couplers

        system_params = QDict(
            backend=QDict(
                username=username,
                password=password,
                config_file=conf_path,
                use_cache=use_cache,
            ),
            context=QDict(
                env_bits=env_bits,
                working_type=context_params["working_type"],
                divide_type=context_params["divide_type"],
                max_point_unit=context_params["max_point_units"],
                online_unit=[],
                # Add f12 supports 2-state readout.
                f12_opt_bits=context_params["f12_opt_bits"],
                online=False,
                crosstalk=context_params["crosstalk"],
                xy_crosstalk=context_params["xy_crosstalk"],
            ),
            use_simulator=use_simulator,
            auto_update=auto_update,
        )
        return system_params, online_params

    def get_all_batch_options(self):
        all_batch_options = conf_manage.get_batch_options(
            point_label=self.now_point_label
        )
        for template, every_batch_item in all_batch_options.items():
            if not isinstance(every_batch_item, Dict):
                continue
            for batch_exp, batch_config in every_batch_item.items():
                if not batch_config.get("param_path"):
                    if "check" in batch_exp:
                        template_name = "checker"
                    else:
                        template_name = template.replace("_options", "")
                    param_options = conf_manage.get_templates(
                        point_label=self.now_point_label, type_name=template_name
                    )
                    if not param_options:
                        logger.info(template_name)
                        continue
                    param_options_dict = param_options.pop().get("templates")
                    batch_config["param_options"] = param_options_dict
        return all_batch_options

    def get_system_backend(self, system: QDict):
        config = conf_manage.get_system_config(point_label=self.now_point_label)
        pre_log_path = config["system"]["log_path"] or config["system"]["local_root"]

        cali_log_path = f"{pre_log_path}/online_auto_calibration_log"
        add_cali_log(cali_log_path)
        logger.info(f"Online auto calibration log save at: {cali_log_path}")

        backend = Backend(**system.backend)

        # todo: update system conf
        time.sleep(1)

        backend.refresh()

        if not system.backend.use_cache:
            backend.context_manager.set_global_options(**system.context)
            logger.info("Start Backend Success | Use Local Config!")
        else:
            logger.info("Start Backend Success | Use Cache Config!")

        backend.infos()
        return backend

    def get_online_group(self):
        group_data = conf_manage.get_cali_groups(point_label=self.now_point_label)
        if not group_data:
            raise ParamsException(CodeMessage.C_4005)
        coupler_groups = {}.fromkeys(group_data.keys())
        for group_name, groups in group_data.items():
            coupler_groups[group_name] = [
                _convert_string(qubit_pair) for qubit_pair in groups
            ]

        return coupler_groups, group_data

    def init_system(self, point_label: str):
        self.now_point_label = point_label
        calibration_config = conf_manage.get_calibration_config(
            point_label=self.now_point_label
        )
        if not calibration_config:
            raise ParamsException(CodeMessage.C_4006)
        account = calibration_config.get("account")
        system_params, online_params = self.get_online_system_params(
            account.get("username"),
            account.get("password"),
            account.get("conf_path"),
        )
        calibration_backend = self.get_system_backend(system_params)

        reference = conf_manage.get_thresholds(point_label=self.now_point_label)
        if not reference:
            raise ParamsException(CodeMessage.C_4007)

        all_batch_options = self.get_all_batch_options()
        if not all_batch_options:
            raise ParamsException(CodeMessage.C_4008)

        qubit_step = get_nested_dict_value(
            calibration_config, ["single_gate", "qubit_step"]
        )
        initial_qubit_groups = get_online_qubit_groups(
            online_params=online_params, step=int(qubit_step)
        )

        initial_coupler_groups, initial_qubit_pair_groups = self.get_online_group()

        return (
            calibration_backend,
            calibration_config,
            reference,
            all_batch_options,
            initial_qubit_groups,
            initial_coupler_groups,
            initial_qubit_pair_groups,
        )

    def init_settings(self, point_label: str) -> Dict:
        self.now_point_label = point_label
        (
            backend,
            config,
            history_threshold,
            batch_options,
            qubit_groups_from_config,
            coupler_groups_from_config,
            qubit_pair_groups_from_config,
        ) = self.init_system(point_label=self.now_point_label)

        qubits = self._args.get("qubits")
        qubits_group = {}
        if qubits:
            group_list = qubits.split(self._split_mark)
            for index, qubit_str in enumerate(group_list):
                qubits_group[str(index + 1)] = qubit_str.split(",")
        initial_qubit_groups = (
            qubits_group if qubits_group else qubit_groups_from_config
        )

        couplers = self._args.get("couplers")
        couplers_group = {}
        if couplers:
            group_list = couplers.split(self._split_mark)
            for index, coupler_str in enumerate(group_list):
                couplers_group[str(index + 1)] = coupler_str.split(",")
        initial_coupler_groups = (
            couplers_group if couplers_group else coupler_groups_from_config
        )

        qubit_pairs = self._args.get("pairs")
        pairs_group = {}
        if qubit_pairs:
            group_list = qubit_pairs.split(self._split_mark)
            for index, pair_str in enumerate(group_list):
                pairs_group[str(index + 1)] = pair_str.split(",")
        initial_qubit_pair_groups = (
            pairs_group if pairs_group else qubit_pair_groups_from_config
        )

        filter_expression = self._args.get("filter_expression")
        if filter_expression is not None:
            filter_symbol, filter_num = extract_symbol_and_number(filter_expression)
            if filter_symbol and filter_num:
                logger.info(
                    f"[AutoCalibration]Physical unit which {filter_symbol} {filter_num} "
                    f"will enter the calibration flow."
                )
                initial_qubit_groups = filter_units_by_symbol(
                    initial_qubit_groups, filter_num, filter_symbol
                )
                initial_coupler_groups = filter_units_by_symbol(
                    initial_coupler_groups, filter_num, filter_symbol
                )
                initial_qubit_pair_groups = filter_units_by_symbol(
                    initial_qubit_pair_groups, filter_num, filter_symbol
                )

        calibration_type_param = self._args.get("t")
        if calibration_type_param == "dcm":
            calibration_type = CalibrationType.REFRESH_DCM
        elif calibration_type_param == "vol":
            calibration_type = CalibrationType.CALIBRATE_VOL
        elif calibration_type_param == "sq":
            calibration_type = CalibrationType.CALIBRATE_SINGLE_GATE
        elif calibration_type_param == "cz":
            calibration_type = CalibrationType.CALIBRATE_CZ_GATE
        else:
            calibration_type = CalibrationType.CALIBRATE_ALL

        vip_timing = self._args.get("vip_timing")
        _runner_settings = {
            "calibration_type": calibration_type,
            "backend": backend,
            "vip_timing": vip_timing,
        }
        _runner_json_configs = {
            "config": config,
            "history_threshold": history_threshold,
            "batch_options": batch_options,
            "initial_qubit_groups": initial_qubit_groups,
            "initial_coupler_groups": initial_coupler_groups,
            "initial_qubit_pair_groups": initial_qubit_pair_groups,
        }
        runner_kwargs = _runner_settings | _runner_json_configs

        verbose = self._args.get("verbose")
        if verbose:
            runner_kwargs_show = {}
            runner_kwargs_show.update({"user": config["account"]["username"]})
            runner_kwargs_show.update(_runner_settings)
            logger.info(
                f"[AutoCalibration]Runner arguments:\n"
                f"{pretty_show_dict(runner_kwargs_show, field_names=['Items', 'Arguments', 'Size'])}\n"
            )

        use_simulator = self._args.get("use_simulator")
        if use_simulator:
            batch_options = set_simulator(batch_options)

        publish_to_storm = self._args.get("pub")
        # This interface can be used to replace all Batch options with a single keystroke.
        sync_batch_options(batch_options, publish_to_storm=publish_to_storm)
        return runner_kwargs


class CalibrationManage:
    def __init__(self):
        self._args_parse = None

        self.sample = None
        self.env_name = None

        self._day_process = None

        self.last_run_time = ""

    def get_database_start_params(self) -> Dict:
        start_params = conf_manage.get_start_params(
            sample=self.sample, env_name=self.env_name
        )
        if not start_params:
            raise ParamsException(CodeMessage.C_4009)
        return start_params

    def get_running(self):
        if self._day_process and self._day_process.is_alive():
            return True
        else:
            return False

    def start(self, args: bytes):
        if self.get_running():
            logger.warning(
                "The auto calibration is running now,you can't run it again at this point."
            )
            raise ParamsException(CodeMessage.C_4010)

        logger.info("start process.")
        json_data = json.loads(args)

        # 获取启动参数
        self.sample = json_data.get("sample")
        self.env_name = json_data.get("env_name")
        start_params = self.get_database_start_params()
        # 创建进程对象
        self._day_process = SchedulerDaily(
            self.sample, self.env_name, start_params, token=conf_manage.token
        )
        self._day_process.start()

    def stop(self):
        logger.info("stop process.")
        if not self.get_running():
            logger.warning(
                "The auto calibration is not running now,you can't stop it at this point."
            )
            raise ParamsException(CodeMessage.C_4011)
        # 停止调度
        self._day_process.close_scheduler()
        cleanup_resources()
        if self._day_process.is_alive():
            logger.info("Process is alive, run terminate")
            self._day_process.terminate()
            self._day_process.join()
        self._day_process = None


class SchedulerBase(multiprocessing.Process):
    def __init__(self):
        self._runner_kwargs = None

        self.param_manage = ParamManage()

        self._close_flag = False

        super().__init__()

    def set_kwargs(self, runner_kwargs):
        self._runner_kwargs = runner_kwargs


class SchedulerDaily(SchedulerBase):
    def __init__(self, sample: str, env_name: str, start_args: Dict, token: str = None):
        super().__init__()

        self.os_id = os.getpid()
        logger.info(f"Process pid is {self.os_id}")

        self.start_args = start_args

        self.param_manage.set_init_args(sample, env_name, self.start_args)

        self._point_label_map_list = self.param_manage.check_init_params()
        self.max_unit_point_label = self.param_manage.get_max_unit_point_label()

        self.schedule_mode = self.start_args.get("sched")

        self.token = token
        self.now_point_label = None
        self.daily_date = None

    def close_scheduler(self):
        self._close_flag = True

    def daily_scheduler_run_plan(self, calibration_type: CalibrationType):
        vip_timing = self._runner_kwargs.get("vip_timing")

        if calibration_type == CalibrationType.CALIBRATE_ALL:
            # Full automated calibration at 5:00 a.m. daily
            self._runner_kwargs["calibration_type"] = CalibrationType.CALIBRATE_ALL
            if not vip_timing:
                self._runner_kwargs["vip_timing"] = VipTiming.CALIBRATE_ALL
            scheduler.every(1).days.at("05:00:00").do(
                execute_calibration, **self._runner_kwargs
            )

        if calibration_type in [CalibrationType.CALIBRATE_ALL, CalibrationType.CALIBRATE_VOL]:
            # Perform voltage calibration every 1 day 3 times
            self._runner_kwargs["calibration_type"] = CalibrationType.CALIBRATE_VOL
            if not vip_timing:
                self._runner_kwargs["vip_timing"] = VipTiming.CALIBRATE_VOL
            scheduler.every(1).days.at("11:00:00").do(
                execute_calibration, **self._runner_kwargs
            )
            scheduler.every(1).days.at("17:00:00").do(
                execute_calibration, **self._runner_kwargs
            )
            scheduler.every(1).days.at("23:00:00").do(
                execute_calibration, **self._runner_kwargs
            )
        if calibration_type in [CalibrationType.CALIBRATE_ALL, CalibrationType.REFRESH_DCM]:
            # Perform discriminator refresh every 60 minutes.
            self._runner_kwargs["calibration_type"] = CalibrationType.REFRESH_DCM
            if not vip_timing:
                self._runner_kwargs["vip_timing"] = VipTiming.REFRESH_DCM
            scheduler.every(60).minutes.do(execute_calibration, **self._runner_kwargs)

    def run(self):
        # 更新子进程的 web 客户端用户的 token
        conf_manage.update_token(self.token)
        # 定时模式
        if self.schedule_mode:
            if not self.daily_date:
                self.daily_date = datetime.date.today()

            point_label_count = len(self._point_label_map_list)
            for point_label_map in self._point_label_map_list:
                self.now_point_label = point_label_map.get("point_label")
                self._runner_kwargs = self.param_manage.init_settings(
                    self.now_point_label
                )
                calibration_type = self._runner_kwargs["calibration_type"]
                self.daily_scheduler_run_plan(calibration_type)

                while not self._close_flag:
                    scheduler.run_pending()
                    time.sleep(1)
                    # 如果 point_label 数量超过一个则需要按天轮换执行
                    if point_label_count > 1:
                        now_date = datetime.date.today()
                        if (now_date - self.daily_date).day >= 1:
                            self.daily_date = now_date
                            break
        # 非定时模式直接运行
        else:
            for point_label_map in self._point_label_map_list:
                self.now_point_label = point_label_map.get("point_label")
                self._runner_kwargs = self.param_manage.init_settings(
                    self.now_point_label
                )
                execute_calibration(**self._runner_kwargs)
        # 停止调度
        self.close_scheduler()
        cleanup_resources()
        logger.info(f"Run over the process.")
