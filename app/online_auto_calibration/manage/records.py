import asyncio
import json
import re
from collections import Counter
from datetime import datetime
from enum import Enum
from typing import Dict, Union, List, Tuple

from app.online_auto_calibration.process.connect import CaliClient
from app.online_auto_calibration.tool.utils import CaliProtocol
from app.online_auto_calibration.tool.web_client import conf_manage

Q_PATTERN = re.compile(r"^q\d+$")
C_PATTERN = re.compile(r"^c\d+-\d+$")
QP_PATTERN = re.compile(r"^q\d+q\d+$")

CALI_TYPES = ["readout", "s_gate", "cz_gate", "voltage", "voltage_sp"]


class ItemType(Enum):
    readout = "readout"
    voltage = "voltage"
    voltage_sp = "voltage_sp"
    s_gate = "s_gate"
    cz_gate = "cz_gate"


class BaseRecords:
    def __init__(
        self,
        sample: str,
        env_name: str,
        point_label: str,
        thresholds: Dict,
        calibration_config: Dict,
        result_client: CaliClient = None,
    ):
        self.sample = sample
        self.env_name = env_name
        self.point_label = point_label

        self.now_datetime = self._get_datetime_str()
        self.threshold_dict = thresholds

        # readout
        self.readout_history_offset = None

        self.s_gate_history_offset = None
        self.rb_threshold = None

        self.cz_gate_history_offset = None
        self.cz_gate_cali_threshold = None

        self.readout_check_records = None

        self.voltage_cali_records = None

        self.voltage_sp_cali_records = None

        self.s_gate_check_records = None
        self.s_gate_cali_records = None

        self.cz_gate_check_records = None
        self.cz_gate_cali_records = None

        self.init_records()
        self.result_client = result_client if result_client else CaliClient(identity="result_client")
        self.init_calibration_config(calibration_config)

    def init_calibration_config(self, calibration_config: Dict):
        # readout
        self.readout_history_offset = calibration_config.get("readout_point").get(
            "history_offset"
        )

        self.s_gate_history_offset = calibration_config.get("single_gate").get(
            "history_offset"
        )
        self.rb_threshold = calibration_config.get("single_gate").get("rb_threshold")

        self.cz_gate_history_offset = calibration_config.get("cz_gate").get(
            "history_offset"
        )
        self.cz_gate_cali_threshold = calibration_config.get("cz_gate").get(
            "cali_threshold"
        )

    @classmethod
    def _get_datetime_str(cls, show_date: bool = True):
        if show_date:
            return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        else:
            return datetime.now().strftime("%H%M%S")

    def init_records(self):
        self.readout_check_records = {}

        self.voltage_cali_records = {}

        self.voltage_sp_cali_records = {}

        self.s_gate_check_records = {}
        self.s_gate_cali_records = {}

        self.cz_gate_check_records = {}
        self.cz_gate_cali_records = {}

    @classmethod
    def filter_analysis_data(cls, analysis_data: Dict, depth_keys: Tuple[str]):
        try:
            filter_result = analysis_data
            for key in depth_keys:
                filter_result = filter_result.get(key)
            return filter_result
        except (KeyError, TypeError):
            return None

    @classmethod
    def quality_assessment(
        cls,
        exp_name_curr: str,
        current_fidelity: Union[List, float],
        threshold_fidelity: Union[List, float, None],
        tolerance: float,
    ):
        exp_name = ""
        status = 0
        try:
            if threshold_fidelity is None:
                return status, exp_name
            elif isinstance(threshold_fidelity, list):
                if current_fidelity:
                    current_f0, current_f1 = current_fidelity
                    threshold_f0, threshold_f1 = threshold_fidelity
                    if current_f0 < threshold_f0 - tolerance:
                        status = 1
                        exp_name = exp_name_curr
                    if current_f1 < threshold_f1 - tolerance:
                        status = 1
                        exp_name = exp_name_curr
                else:
                    status = 2
                    exp_name = exp_name_curr
            elif isinstance(threshold_fidelity, float):
                if current_fidelity:
                    if current_fidelity < threshold_fidelity - tolerance:
                        status = 1
                        exp_name = exp_name_curr
                else:
                    status = 2
                    exp_name = exp_name_curr
        except Exception as _:
            status = 2
            exp_name = exp_name_curr
        return status, exp_name

    @classmethod
    def filter_records(cls, batch_records: Dict, filter_label_name: str) -> Dict:
        clean_records = {}
        for key_name, result in batch_records.items():
            label_name = result.get("label_name")
            if label_name == filter_label_name:
                clean_records[key_name] = result
        return clean_records

    def get_readout_status(self, qubit_name: str, fidelity: Union[List, None]):
        threshold_fidelity = self.threshold_dict.get("dcm", {}).get(qubit_name)

        if not threshold_fidelity:
            return 1
        status = 0
        if fidelity:
            current_f0, current_f1 = fidelity
            threshold_f0, threshold_f1 = threshold_fidelity
            if current_f0 < threshold_f0 - self.readout_history_offset:
                status = 1
            if current_f1 < threshold_f1 - self.readout_history_offset:
                status = 1
        else:
            status = 2
        return status

    def get_check_single_gate_status(
        self,
        qubit_name: str,
        fidelity: Union[float, None],
    ):
        threshold_fidelity = self.threshold_dict.get("single_gate", {}).get(qubit_name)
        if not threshold_fidelity:
            return 1
        status = 0
        if fidelity:
            if fidelity < threshold_fidelity - self.s_gate_history_offset:
                status = 1
        else:
            status = 2
        return status

    def get_cali_single_gate_status(
        self,
        fidelity: Union[float, None],
    ):
        status = 0
        if fidelity:
            if fidelity < self.rb_threshold:
                status = 1
        else:
            status = 2
        return status

    def get_check_cz_gate_status(
        self,
        qubit_name: str,
        fidelity: Union[float, None],
    ):
        threshold_fidelity = self.threshold_dict.get("cz_gate", {}).get(qubit_name)
        if not threshold_fidelity:
            return 1
        status = 0
        if fidelity:
            if fidelity < threshold_fidelity - self.cz_gate_history_offset:
                status = 1
        else:
            status = 2
        return status

    def get_cali_cz_gate_status(
        self,
        fidelity: Union[float, None],
    ):
        status = 0
        if fidelity:
            if fidelity < self.cz_gate_cali_threshold:
                status = 1
        else:
            status = 2
        return status

    def readout_result(
        self,
        batch_records: Dict,
        filter_label_name: str = "CheckReadoutDrift",
        data_depth: Tuple = ("dcm", "fidelity"),
        send: bool = False,
    ):
        save_result = {}
        records = self.filter_records(batch_records, filter_label_name)
        if not records:
            return
        for key_name, result in records.items():
            analysis_data = result.get("analysis_data", {})
            exp_name = result.get("exp_name", "")
            pass_units = result.get("pass_units", [])
            bad_units = result.get("bad_units", [])
            pass_units.extend(bad_units)
            for unit in pass_units:
                unit_ana_data = analysis_data.get(unit)
                # 没有分析数据则说明实验执行异常
                if not unit_ana_data:
                    status = 2
                    current_fidelity = None
                else:
                    current_fidelity = self.filter_analysis_data(
                        unit_ana_data.get("result", {}), data_depth
                    )
                    status = self.get_readout_status(unit, current_fidelity)
                save_result[unit] = {
                    "exp": exp_name if status else "",
                    "tip": "Drift" if status else "",
                    # 如果存在分析数据则说明执行成功但结果不理想，如果结果都没有说明执行报错或者实验异常了
                    "status": status,
                    "value": current_fidelity,
                }
        if save_result:
            if self.readout_check_records:
                self.readout_check_records.update(save_result)
            else:
                self.readout_check_records = save_result
            if send:
                self.send_calibration_result(save_result, protocol=CaliProtocol.cali_record. value)

    def voltage_result(
        self,
        batch_records: Dict,
        filter_label_name: str = "CalibrateVoltage",
        data_depth: Tuple = ("vol",),
        send: bool = False,
    ):
        save_result = {}
        records = self.filter_records(batch_records, filter_label_name)
        if not records:
            return
        for key_name, result in records.items():
            analysis_data = result.get("analysis_data", {})
            exp_name = result.get("exp_name", "")
            fail_reason = result.get("fail_reason", "")
            pass_units = result.get("pass_units", [])
            bad_units = result.get("bad_units", [])
            for unit in pass_units:
                unit_ana_data = analysis_data.get(unit)
                if not unit_ana_data:
                    current_vol = 0.0
                else:
                    current_vol = (
                        self.filter_analysis_data(
                            unit_ana_data.get("result", {}), data_depth
                        )
                        or 0.0
                    )
                save_result[unit] = {
                    "exp": "",
                    "tip": "",
                    "status": 0,
                    "value": current_vol,
                }
            for unit in bad_units:
                unit_ana_data = analysis_data.get(unit)
                if not unit_ana_data:
                    current_vol = None
                else:
                    current_vol = self.filter_analysis_data(
                        unit_ana_data.get("result", {}), data_depth
                    )
                save_result[unit] = {
                    "exp": exp_name,
                    "tip": fail_reason if current_vol is None else "",
                    "status": 2 if current_vol is None else 1,
                    "value": current_vol,
                }
        if save_result:
            if self.voltage_cali_records:
                self.voltage_cali_records.update(save_result)
            else:
                self.voltage_cali_records = save_result
            if send:
                self.send_calibration_result(save_result, protocol=CaliProtocol.cali_record. value)

    def voltage_sp_result(
        self,
        batch_records: Dict,
        filter_label_name: str = "CalibrateVoltage_sp",
        data_depth: Tuple = ("dc_max",),
        send: bool = False,
    ):
        save_result = {}
        records = self.filter_records(batch_records, filter_label_name)
        if not records:
            return
        for key_name, result in records.items():
            analysis_data = result.get("analysis_data", {})
            exp_name = result.get("exp_name", "")
            fail_reason = result.get("fail_reason", "")
            pass_units = result.get("pass_units", [])
            bad_units = result.get("bad_units", [])
            for unit in pass_units:
                unit_ana_data = analysis_data.get(unit)
                if not unit_ana_data:
                    current_vol = 0.0
                else:
                    current_vol = (
                        self.filter_analysis_data(
                            unit_ana_data.get("result", {}), data_depth
                        )
                        or 0.0
                    )
                save_result[unit] = {
                    "exp": "",
                    "tip": "",
                    "status": 0,
                    "value": current_vol,
                }
            for unit in bad_units:
                unit_ana_data = analysis_data.get(unit)
                if not unit_ana_data:
                    current_vol = None
                else:
                    current_vol = self.filter_analysis_data(
                        unit_ana_data.get("result", {}), data_depth
                    )
                save_result[unit] = {
                    "exp": exp_name,
                    "tip": fail_reason if current_vol is None else "",
                    "status": 2 if current_vol is None else 1,
                    "value": current_vol,
                }
        if save_result:
            if self.voltage_sp_cali_records:
                self.voltage_sp_cali_records.update(save_result)
            else:
                self.voltage_sp_cali_records = save_result
            if send:
                self.send_calibration_result(save_result, protocol=CaliProtocol.cali_record. value)

    def check_single_gate_result(
        self,
        batch_records: Dict,
        filter_label_name: str = "CheckGateParamsDrift",
        data_depth: Tuple = ("fidelity",),
        send: bool = False,
    ):
        save_result = {}
        records = self.filter_records(batch_records, filter_label_name)
        if not records:
            return
        for key_name, result in records.items():
            analysis_data = result.get("analysis_data", {})
            exp_name = result.get("exp_name", "")
            fail_reason = result.get("fail_reason", "")
            pass_units = result.get("pass_units", [])
            bad_units = result.get("bad_units", [])
            pass_units.extend(bad_units)
            for unit in pass_units:
                unit_ana_data = analysis_data.get(unit)
                if not unit_ana_data:
                    status = 2
                    current_fidelity = None
                else:
                    current_fidelity = self.filter_analysis_data(
                        unit_ana_data.get("result", {}), data_depth
                    )
                    status = self.get_check_single_gate_status(unit, current_fidelity)
                save_result[unit] = {
                    "exp": exp_name if status else "",
                    "tip": fail_reason if status else "",
                    # 如果存在分析数据则说明执行成功但结果不理想，如果结果都没有说明执行报错或者实验异常了
                    "status": status,
                    "value": current_fidelity,
                }
        if save_result:
            if self.s_gate_check_records:
                self.s_gate_check_records.update(save_result)
            else:
                self.s_gate_check_records = save_result
            if send:
                self.send_calibration_result(save_result, protocol=CaliProtocol.cali_record. value)

    def cali_single_gate_result(
        self,
        batch_records: Dict,
        filter_label_name: str = "CalibrateSingleGate",
        data_depth: Tuple = ("fidelity",),
        send: bool = False,
    ):
        save_result = {}
        records = self.filter_records(batch_records, filter_label_name)
        if not records:
            return
        for key_name, result in records.items():
            analysis_data = result.get("analysis_data", {})
            exp_name = result.get("exp_name", "")
            fail_reason = result.get("fail_reason", "")
            pass_units = result.get("pass_units", [])
            bad_units = result.get("bad_units", [])
            pass_units.extend(bad_units)
            for unit in pass_units:
                unit_ana_data = analysis_data.get(unit)
                if not unit_ana_data:
                    status = 2
                    current_fidelity = None
                else:
                    current_fidelity = self.filter_analysis_data(
                        unit_ana_data.get("result", {}), data_depth
                    )
                    status = self.get_cali_single_gate_status(current_fidelity)
                save_result[unit] = {
                    "exp": exp_name if status else "",
                    "tip": fail_reason if status else "",
                    # 如果存在分析数据则说明执行成功但结果不理想，如果结果都没有说明执行报错或者实验异常了
                    "status": status,
                    "value": current_fidelity,
                }
        if save_result:
            if self.s_gate_cali_records:
                self.s_gate_cali_records.update(save_result)
            else:
                self.s_gate_cali_records = save_result
            if send:
                self.send_calibration_result(save_result, protocol=CaliProtocol.cali_record. value)

    def check_cz_gate_result(
        self,
        batch_records: Dict,
        filter_label_name: str = "CheckXebDrift",
        data_depth: Tuple = ("f_xeb",),
        send: bool = False,
    ):
        save_result = {}
        records = self.filter_records(batch_records, filter_label_name)
        if not records:
            return
        for key_name, result in records.items():
            analysis_data = result.get("analysis_data", {})
            exp_name = result.get("exp_name", "")
            fail_reason = result.get("fail_reason", "")
            pass_units = result.get("pass_units", [])
            bad_units = result.get("bad_units", [])
            pass_units.extend(bad_units)
            for unit in pass_units:
                unit_ana_data = analysis_data.get(unit)
                if not unit_ana_data:
                    status = 2
                    current_fidelity = None
                else:
                    current_fidelity = self.filter_analysis_data(
                        unit_ana_data.get("result", {}), data_depth
                    )
                    status = self.get_check_cz_gate_status(unit, current_fidelity)
                save_result[unit] = {
                    "exp": exp_name if status else "",
                    "tip": fail_reason if status else "",
                    # 如果存在分析数据则说明执行成功但结果不理想，如果结果都没有说明执行报错或者实验异常了
                    "status": status,
                    "value": current_fidelity,
                }
        if save_result:
            if self.cz_gate_check_records:
                self.cz_gate_check_records.update(save_result)
            else:
                self.cz_gate_check_records = save_result
            if send:
                self.send_calibration_result(save_result, protocol=CaliProtocol.cali_record. value)

    def cali_cz_gate_result(
        self,
        batch_records: Dict,
        filter_label_name: str = "CalibrateCZ",
        data_depth: Tuple = ("f_xeb",),
        send: bool = False,
    ):
        save_result = {}
        records = self.filter_records(batch_records, filter_label_name)
        if not records:
            return
        for key_name, result in records.items():
            analysis_data = result.get("analysis_data", {})
            exp_name = result.get("exp_name", "")
            fail_reason = result.get("fail_reason", "")
            pass_units = result.get("pass_units", [])
            bad_units = result.get("bad_units", [])
            pass_units.extend(bad_units)
            for unit in pass_units:
                unit_ana_data = analysis_data.get(unit)
                if not unit_ana_data:
                    status = 2
                    current_fidelity = None
                else:
                    current_fidelity = self.filter_analysis_data(
                        unit_ana_data.get("result", {}), data_depth
                    )
                    status = self.get_cali_cz_gate_status(current_fidelity)
                save_result[unit] = {
                    "exp": exp_name if status else "",
                    "tip": fail_reason if status else "",
                    # 如果存在分析数据则说明执行成功但结果不理想，如果结果都没有说明执行报错或者实验异常了
                    "status": status,
                    "value": current_fidelity,
                }
        if save_result:
            if self.cz_gate_cali_records:
                self.cz_gate_cali_records.update(save_result)
            else:
                self.cz_gate_cali_records = save_result
            if send:
                self.send_calibration_result(save_result, protocol=CaliProtocol.cali_record. value)

    def set_mongo_records(
        self,
        batch_records: Dict,
        item_type: str,
        send: bool = False,
    ):
        if item_type == ItemType.readout.value:
            # check
            self.readout_result(batch_records, send=send)
        elif item_type == ItemType.voltage.value:
            # calibration
            self.voltage_result(batch_records, send=send)
        elif item_type == ItemType.voltage_sp.value:
            # calibration
            self.voltage_sp_result(batch_records, send=send)
        elif item_type == ItemType.s_gate.value:
            # check
            self.check_single_gate_result(batch_records, send=send)
            # calibration
            self.cali_single_gate_result(batch_records, send=send)
        elif item_type == ItemType.cz_gate.value:
            # check
            self.check_cz_gate_result(batch_records, send=send)
            # calibration
            self.cali_cz_gate_result(batch_records, send=send)

    def request_to_save(self, data: Dict):
        try:
            asyncio.run(
                self.result_client.send_multipart(
                    [
                        b"",
                        CaliProtocol.cali_result.value,
                        json.dumps(data).encode(),
                    ]
                )
            )
            resp_data = conf_manage.post_cali_dataset(data=data)
            return resp_data
        except Exception as e:
            print(e)
            return {}

    @classmethod
    def divide_type_data(cls, records: Dict):
        qubit_records = {}
        coupler_records = {}
        qubit_pair_records = {}
        for qubit_name, result in records.items():
            if Q_PATTERN.match(qubit_name):
                qubit_records[qubit_name] = result
            elif C_PATTERN.match(qubit_name):
                coupler_records[qubit_name] = result
            elif QP_PATTERN.match(qubit_name):
                qubit_pair_records[qubit_name] = result
        return qubit_records, coupler_records, qubit_pair_records

    @classmethod
    def get_type_threshold_history(cls, result: Dict):
        qubit_result = {}
        coupler_result = {}
        qubit_pair_result = {}
        for name, value in result.items():
            if Q_PATTERN.match(name):
                qubit_result[name] = {
                    "exp": "",
                    "tip": "",
                    "status": 0,
                    "value": value,
                }
            elif C_PATTERN.match(name):
                coupler_result[name] = {
                    "exp": "",
                    "tip": "",
                    "status": 0,
                    "value": value,
                }
            elif QP_PATTERN.match(name):
                qubit_pair_result[name] = {
                    "exp": "",
                    "tip": "",
                    "status": 0,
                    "value": value,
                }
        return qubit_result, coupler_result, qubit_pair_result

    def get_best_history(self, item_type: str):
        if item_type == ItemType.readout.value:
            dcm = self.threshold_dict.get("dcm")
            (
                qubit_result,
                coupler_result,
                qubit_pair_result,
            ) = self.get_type_threshold_history(dcm)
        elif item_type == ItemType.voltage.value:
            voltage = self.threshold_dict.get("voltage")
            (
                qubit_result,
                coupler_result,
                qubit_pair_result,
            ) = self.get_type_threshold_history(voltage)
        elif item_type == ItemType.voltage_sp.value:
            voltage_sp = self.threshold_dict.get("voltage_sp")
            (
                qubit_result,
                coupler_result,
                qubit_pair_result,
            ) = self.get_type_threshold_history(voltage_sp)
        elif item_type == ItemType.s_gate.value:
            s_gate = self.threshold_dict.get("single_gate")
            (
                qubit_result,
                coupler_result,
                qubit_pair_result,
            ) = self.get_type_threshold_history(s_gate)
        else:
            cz_gate = self.threshold_dict.get("cz_gate")
            (
                qubit_result,
                coupler_result,
                qubit_pair_result,
            ) = self.get_type_threshold_history(cz_gate)
        return qubit_result, coupler_result, qubit_pair_result

    def combine_data(
        self, records: Dict, item_type: str, qubit_type: str, execute_type
    ):
        if execute_type == "best thresholds":
            title = f"{item_type} {execute_type}"
        else:
            counter = Counter([str(bit.get("status")) for bit in records.values()])
            pass_c = counter["0"]
            fail_c = counter["1"]
            error_c = counter["2"]
            total_c = pass_c + fail_c + error_c
            title = f"{item_type} {execute_type}(total:{total_c} pass:{pass_c} fail:{fail_c} error:{error_c})"
        return {
            "sample": self.sample,
            "env_name": self.env_name,
            "point_label": self.point_label,
            "item_type": item_type,
            "qubit_type": qubit_type,
            "title": title,
            "execute_type": execute_type,
            "data": records,
            "date_time": self.now_datetime,
        }

    def send_calibration_result(
        self, data: Union[List, Dict],
        protocol: CaliProtocol = CaliProtocol.cali_result.value,
    ):
        asyncio.run(
            self.result_client.send_multipart(
                [
                    b"",
                    protocol,
                    json.dumps(data).encode(),
                ]
            )
        )

    def check_type_result(self, records: Dict, item_type: str, execute_type: str):
        (
            qubit_records,
            coupler_records,
            qubit_pair_records,
        ) = self.divide_type_data(records)
        qubit_result = {}
        coupler_result = {}
        qubit_pair_result = {}
        if qubit_records:
            qubit_result = self.combine_data(
                qubit_records, item_type, "qubit", execute_type
            )
        if coupler_records:
            coupler_result = self.combine_data(
                coupler_records, item_type, "coupler", execute_type
            )
        if qubit_pair_records:
            qubit_pair_result = self.combine_data(
                qubit_pair_records, item_type, "qubit_pair", execute_type
            )
        return qubit_result, coupler_result, qubit_pair_result

    def save_result_to_db(self, item_type: str):
        qubit_result, coupler_result, qubit_pair_result = {}, {}, {}
        if item_type == ItemType.readout.value:
            if self.readout_check_records:
                (
                    qubit_result,
                    coupler_result,
                    _,
                ) = self.check_type_result(
                    self.readout_check_records, item_type, "check"
                )
            qubit_his, coupler_his, _ = self.get_best_history(item_type)
            if qubit_result:
                qubit_his = self.combine_data(
                    qubit_his, item_type, "qubit", "best thresholds"
                )
                conf_manage.post_cali_dataset(data=qubit_result)
                self.send_calibration_result([qubit_his, qubit_result])
            if coupler_result:
                coupler_his = self.combine_data(
                    coupler_his, item_type, "coupler", "best thresholds"
                )
                conf_manage.post_cali_dataset(data=coupler_result)
                self.send_calibration_result([coupler_his, coupler_result])
        elif item_type == ItemType.voltage.value:
            # calibration
            if self.voltage_cali_records:
                (
                    qubit_result,
                    coupler_result,
                    _,
                ) = self.check_type_result(
                    self.voltage_cali_records, item_type, "calibration"
                )
            qubit_his, coupler_his, _ = self.get_best_history(item_type)
            if qubit_result:
                qubit_his = self.combine_data(
                    qubit_his, item_type, "qubit", "best thresholds"
                )
                conf_manage.post_cali_dataset(data=qubit_result)
                self.send_calibration_result([qubit_his, qubit_result])
            if coupler_result:
                coupler_his = self.combine_data(
                    coupler_his, item_type, "coupler", "best thresholds"
                )
                conf_manage.post_cali_dataset(data=coupler_result)
                self.send_calibration_result([coupler_his, coupler_result])
        elif item_type == ItemType.voltage_sp.value:
            # calibration
            if self.voltage_sp_cali_records:
                (
                    qubit_result,
                    coupler_result,
                    _,
                ) = self.check_type_result(
                    self.voltage_sp_cali_records, item_type, "calibration"
                )
            qubit_his, coupler_his, _ = self.get_best_history(item_type)
            if qubit_result:
                qubit_his = self.combine_data(
                    qubit_his, item_type, "qubit", "best thresholds"
                )
                conf_manage.post_cali_dataset(data=qubit_result)
                self.send_calibration_result([qubit_his, qubit_result])
            if coupler_result:
                coupler_his = self.combine_data(
                    coupler_his, item_type, "coupler", "best thresholds"
                )
                conf_manage.post_cali_dataset(data=coupler_result)
                self.send_calibration_result([coupler_his, coupler_result])
        elif item_type == ItemType.s_gate.value:
            # check
            if self.s_gate_check_records:
                (
                    qubit_result,
                    _,
                    _,
                ) = self.check_type_result(
                    self.s_gate_check_records, item_type, "check"
                )
            # calibration
            qubit_cali_result = {}
            if self.s_gate_cali_records:
                (
                    qubit_cali_result,
                    _,
                    _,
                ) = self.check_type_result(
                    self.s_gate_cali_records, item_type, "calibration"
                )
            qubit_his, _, _ = self.get_best_history(item_type)
            if qubit_result:
                qubit_his = self.combine_data(
                    qubit_his, item_type, "qubit", "best thresholds"
                )
                data = [qubit_his, qubit_result]
                conf_manage.post_cali_dataset(data=qubit_result)
                if qubit_cali_result:
                    conf_manage.post_cali_dataset(data=qubit_cali_result)
                    data.append(qubit_cali_result)
                self.send_calibration_result(data)
        elif item_type == ItemType.cz_gate.value:
            # check
            if self.cz_gate_check_records:
                (
                    _,
                    _,
                    qubit_pair_result,
                ) = self.check_type_result(
                    self.cz_gate_check_records, item_type, "check"
                )
            # calibration
            qubit_pair_cali_result = {}
            if self.cz_gate_cali_records:
                (
                    _,
                    _,
                    qubit_pair_result,
                ) = self.check_type_result(
                    self.cz_gate_cali_records, item_type, "calibration"
                )
            _, _, qubit_pair_his = self.get_best_history(item_type)
            if qubit_pair_result:
                qubit_pair_his = self.combine_data(
                    qubit_pair_his, item_type, "qubit_pair", "best thresholds"
                )
                data = [qubit_pair_his, qubit_pair_result]
                conf_manage.post_cali_dataset(data=qubit_pair_result)
                if qubit_pair_cali_result:
                    data.append(qubit_pair_cali_result)
                    conf_manage.post_cali_dataset(data=qubit_pair_cali_result)
                self.send_calibration_result(data)
        self.init_records()
