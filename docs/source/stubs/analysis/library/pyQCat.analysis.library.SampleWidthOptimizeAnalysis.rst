﻿pyQCat.analysis.library.SampleWidthOptimizeAnalysis
===================================================

.. currentmodule:: pyQCat.analysis.library

.. autoclass:: SampleWidthOptimizeAnalysis

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~SampleWidthOptimizeAnalysis.__init__
      ~SampleWidthOptimizeAnalysis.from_sub_analysis
      ~SampleWidthOptimizeAnalysis.run_analysis
      ~SampleWidthOptimizeAnalysis.set_options
      ~SampleWidthOptimizeAnalysis.show_results
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~SampleWidthOptimizeAnalysis.analysis_datas
      ~SampleWidthOptimizeAnalysis.data_filter
      ~SampleWidthOptimizeAnalysis.drawer
      ~SampleWidthOptimizeAnalysis.experiment_data
      ~SampleWidthOptimizeAnalysis.has_child
      ~SampleWidthOptimizeAnalysis.options
      ~SampleWidthOptimizeAnalysis.quality
      ~SampleWidthOptimizeAnalysis.results
   
   