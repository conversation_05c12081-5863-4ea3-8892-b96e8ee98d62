﻿pyQCat.analysis.library.ACSpectrumAnalysis
==========================================

.. currentmodule:: pyQCat.analysis.library

.. autoclass:: ACSpectrumAnalysis

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~ACSpectrumAnalysis.__init__
      ~ACSpectrumAnalysis.from_sub_analysis
      ~ACSpectrumAnalysis.run_analysis
      ~ACSpectrumAnalysis.set_options
      ~ACSpectrumAnalysis.show_results
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~ACSpectrumAnalysis.analysis_datas
      ~ACSpectrumAnalysis.data_filter
      ~ACSpectrumAnalysis.drawer
      ~ACSpectrumAnalysis.experiment_data
      ~ACSpectrumAnalysis.has_child
      ~ACSpectrumAnalysis.options
      ~ACSpectrumAnalysis.quality
      ~ACSpectrumAnalysis.results
   
   