﻿pyQCat.analysis.library.DistortionT1CompositeAnalysis
=====================================================

.. currentmodule:: pyQCat.analysis.library

.. autoclass:: DistortionT1CompositeAnalysis

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~DistortionT1CompositeAnalysis.__init__
      ~DistortionT1CompositeAnalysis.from_sub_analysis
      ~DistortionT1CompositeAnalysis.run_analysis
      ~DistortionT1CompositeAnalysis.set_options
      ~DistortionT1CompositeAnalysis.show_results
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~DistortionT1CompositeAnalysis.analysis_datas
      ~DistortionT1CompositeAnalysis.data_filter
      ~DistortionT1CompositeAnalysis.drawer
      ~DistortionT1CompositeAnalysis.experiment_data
      ~DistortionT1CompositeAnalysis.has_child
      ~DistortionT1CompositeAnalysis.options
      ~DistortionT1CompositeAnalysis.quality
      ~DistortionT1CompositeAnalysis.results
   
   