﻿pyQCat.analysis.library.RabiAmpAnalysis
=======================================

.. currentmodule:: pyQCat.analysis.library

.. autoclass:: RabiAmpAnalysis

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~RabiAmpAnalysis.__init__
      ~RabiAmpAnalysis.from_sub_analysis
      ~RabiAmpAnalysis.run_analysis
      ~RabiAmpAnalysis.set_options
      ~RabiAmpAnalysis.show_results
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~RabiAmpAnalysis.analysis_datas
      ~RabiAmpAnalysis.data_filter
      ~RabiAmpAnalysis.drawer
      ~RabiAmpAnalysis.experiment_data
      ~RabiAmpAnalysis.has_child
      ~RabiAmpAnalysis.options
      ~RabiAmpAnalysis.quality
      ~RabiAmpAnalysis.results
   
   