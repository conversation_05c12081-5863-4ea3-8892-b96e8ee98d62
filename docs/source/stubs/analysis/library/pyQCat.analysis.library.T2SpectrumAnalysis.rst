﻿pyQCat.analysis.library.T2SpectrumAnalysis
==========================================

.. currentmodule:: pyQCat.analysis.library

.. autoclass:: T2SpectrumAnalysis

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~T2SpectrumAnalysis.__init__
      ~T2SpectrumAnalysis.from_sub_analysis
      ~T2SpectrumAnalysis.run_analysis
      ~T2SpectrumAnalysis.set_options
      ~T2SpectrumAnalysis.show_results
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~T2SpectrumAnalysis.analysis_datas
      ~T2SpectrumAnalysis.data_filter
      ~T2SpectrumAnalysis.drawer
      ~T2SpectrumAnalysis.experiment_data
      ~T2SpectrumAnalysis.has_child
      ~T2SpectrumAnalysis.options
      ~T2SpectrumAnalysis.quality
      ~T2SpectrumAnalysis.results
   
   