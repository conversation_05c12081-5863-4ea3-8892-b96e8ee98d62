﻿pyQCat.analysis.algorithms.IQdiscriminator
==========================================

.. currentmodule:: pyQCat.analysis.algorithms

.. autoclass:: IQdiscriminator

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~IQdiscriminator.__init__
      ~IQdiscriminator.calculate_radius
      ~IQdiscriminator.get_k_recommend
      ~IQdiscriminator.get_probability
      ~IQdiscriminator.plot
      ~IQdiscriminator.predict
      ~IQdiscriminator.screen_iq
      ~IQdiscriminator.train
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~IQdiscriminator.centers
      ~IQdiscriminator.fidelity
      ~IQdiscriminator.k_recommend
      ~IQdiscriminator.outlier
      ~IQdiscriminator.probability
      ~IQdiscriminator.radius
   
   