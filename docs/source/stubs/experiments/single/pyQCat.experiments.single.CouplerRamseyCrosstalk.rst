﻿pyQCat.experiments.single.CouplerRamseyCrosstalk
================================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: CouplerRamseyCrosstalk

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CouplerRamseyCrosstalk.__init__
      ~CouplerRamseyCrosstalk.acquire_pulse
      ~CouplerRamseyCrosstalk.cal_fidelity
      ~CouplerRamseyCrosstalk.experiment_info
      ~CouplerRamseyCrosstalk.from_experiment_context
      ~CouplerRamseyCrosstalk.get_qubit_str
      ~CouplerRamseyCrosstalk.get_xy_pulse
      ~CouplerRamseyCrosstalk.get_xy_pulse_pre
      ~CouplerRamseyCrosstalk.get_z_pulse
      ~CouplerRamseyCrosstalk.get_z_pulse_pre
      ~CouplerRamseyCrosstalk.jupyter_schedule
      ~CouplerRamseyCrosstalk.options_table
      ~CouplerRamseyCrosstalk.play_bias_pulse
      ~CouplerRamseyCrosstalk.play_pulse
      ~CouplerRamseyCrosstalk.plot_schedule
      ~CouplerRamseyCrosstalk.run
      ~CouplerRamseyCrosstalk.set_analysis_options
      ~CouplerRamseyCrosstalk.set_experiment_options
      ~CouplerRamseyCrosstalk.set_multiple_IF
      ~CouplerRamseyCrosstalk.set_multiple_index
      ~CouplerRamseyCrosstalk.set_parent_file
      ~CouplerRamseyCrosstalk.set_run_options
      ~CouplerRamseyCrosstalk.set_sweep_order
      ~CouplerRamseyCrosstalk.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CouplerRamseyCrosstalk.analysis
      ~CouplerRamseyCrosstalk.analysis_options
      ~CouplerRamseyCrosstalk.experiment_options
      ~CouplerRamseyCrosstalk.run_options
   
   