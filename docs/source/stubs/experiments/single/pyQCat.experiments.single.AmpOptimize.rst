﻿pyQCat.experiments.single.AmpOptimize
=====================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: AmpOptimize

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~AmpOptimize.__init__
      ~AmpOptimize.acquire_pulse
      ~AmpOptimize.cal_fidelity
      ~AmpOptimize.experiment_info
      ~AmpOptimize.from_experiment_context
      ~AmpOptimize.get_qubit_str
      ~AmpOptimize.get_xy_pulse
      ~AmpOptimize.jupyter_schedule
      ~AmpOptimize.options_table
      ~AmpOptimize.play_pulse
      ~AmpOptimize.plot_schedule
      ~AmpOptimize.run
      ~AmpOptimize.set_analysis_options
      ~AmpOptimize.set_experiment_options
      ~AmpOptimize.set_multiple_IF
      ~AmpOptimize.set_multiple_index
      ~AmpOptimize.set_parent_file
      ~AmpOptimize.set_run_options
      ~AmpOptimize.set_sweep_order
      ~AmpOptimize.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~AmpOptimize.analysis
      ~AmpOptimize.analysis_options
      ~AmpOptimize.experiment_options
      ~AmpOptimize.run_options
   
   