﻿pyQCat.experiments.single.CouplerRabiScanWidth
==============================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: CouplerRabiScanWidth

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CouplerRabiScanWidth.__init__
      ~CouplerRabiScanWidth.acquire_pulse
      ~CouplerRabiScanWidth.cal_fidelity
      ~CouplerRabiScanWidth.experiment_info
      ~CouplerRabiScanWidth.from_experiment_context
      ~CouplerRabiScanWidth.get_qubit_str
      ~CouplerRabiScanWidth.get_xy_pulse
      ~CouplerRabiScanWidth.jupyter_schedule
      ~CouplerRabiScanWidth.options_table
      ~CouplerRabiScanWidth.play_pulse
      ~CouplerRabiScanWidth.plot_schedule
      ~CouplerRabiScanWidth.run
      ~CouplerRabiScanWidth.set_analysis_options
      ~CouplerRabiScanWidth.set_experiment_options
      ~CouplerRabiScanWidth.set_multiple_IF
      ~CouplerRabiScanWidth.set_multiple_index
      ~CouplerRabiScanWidth.set_parent_file
      ~CouplerRabiScanWidth.set_run_options
      ~CouplerRabiScanWidth.set_sweep_order
      ~CouplerRabiScanWidth.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CouplerRabiScanWidth.analysis
      ~CouplerRabiScanWidth.analysis_options
      ~CouplerRabiScanWidth.experiment_options
      ~CouplerRabiScanWidth.run_options
   
   