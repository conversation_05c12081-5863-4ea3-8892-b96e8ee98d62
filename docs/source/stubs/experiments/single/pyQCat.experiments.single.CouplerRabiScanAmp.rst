﻿pyQCat.experiments.single.CouplerRabiScanAmp
============================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: CouplerRabiScanAmp

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CouplerRabiScanAmp.__init__
      ~CouplerRabiScanAmp.acquire_pulse
      ~CouplerRabiScanAmp.cal_fidelity
      ~CouplerRabiScanAmp.experiment_info
      ~CouplerRabiScanAmp.from_experiment_context
      ~CouplerRabiScanAmp.get_qubit_str
      ~CouplerRabiScanAmp.get_xy_pulse
      ~CouplerRabiScanAmp.jupyter_schedule
      ~CouplerRabiScanAmp.options_table
      ~CouplerRabiScanAmp.play_pulse
      ~CouplerRabiScanAmp.plot_schedule
      ~CouplerRabiScanAmp.run
      ~CouplerRabiScanAmp.set_analysis_options
      ~CouplerRabiScanAmp.set_experiment_options
      ~CouplerRabiScanAmp.set_multiple_IF
      ~CouplerRabiScanAmp.set_multiple_index
      ~CouplerRabiScanAmp.set_parent_file
      ~CouplerRabiScanAmp.set_run_options
      ~CouplerRabiScanAmp.set_sweep_order
      ~CouplerRabiScanAmp.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CouplerRabiScanAmp.analysis
      ~CouplerRabiScanAmp.analysis_options
      ~CouplerRabiScanAmp.experiment_options
      ~CouplerRabiScanAmp.run_options
   
   