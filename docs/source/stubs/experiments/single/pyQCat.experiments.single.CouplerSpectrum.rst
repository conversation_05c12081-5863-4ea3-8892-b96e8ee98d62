﻿pyQCat.experiments.single.CouplerSpectrum
=========================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: CouplerSpectrum

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CouplerSpectrum.__init__
      ~CouplerSpectrum.acquire_pulse
      ~CouplerSpectrum.cal_fidelity
      ~CouplerSpectrum.experiment_info
      ~CouplerSpectrum.from_experiment_context
      ~CouplerSpectrum.get_qubit_str
      ~CouplerSpectrum.get_xy_pulse
      ~CouplerSpectrum.get_z_pulse
      ~CouplerSpectrum.jupyter_schedule
      ~CouplerSpectrum.options_table
      ~CouplerSpectrum.play_pulse
      ~CouplerSpectrum.plot_schedule
      ~CouplerSpectrum.run
      ~CouplerSpectrum.set_analysis_options
      ~CouplerSpectrum.set_experiment_options
      ~CouplerSpectrum.set_multiple_IF
      ~CouplerSpectrum.set_multiple_index
      ~CouplerSpectrum.set_parent_file
      ~CouplerSpectrum.set_run_options
      ~CouplerSpectrum.set_sweep_order
      ~CouplerSpectrum.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CouplerSpectrum.analysis
      ~CouplerSpectrum.analysis_options
      ~CouplerSpectrum.experiment_options
      ~CouplerSpectrum.run_options
   
   