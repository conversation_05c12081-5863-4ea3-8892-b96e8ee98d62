﻿pyQCat.experiments.single.StateTomography
=========================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: StateTomography

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~StateTomography.__init__
      ~StateTomography.acquire_pulse
      ~StateTomography.cal_fidelity
      ~StateTomography.experiment_info
      ~StateTomography.from_experiment_context
      ~StateTomography.get_qubit_str
      ~StateTomography.jupyter_schedule
      ~StateTomography.options_table
      ~StateTomography.play_pulse
      ~StateTomography.plot_schedule
      ~StateTomography.run
      ~StateTomography.set_analysis_options
      ~StateTomography.set_experiment_options
      ~StateTomography.set_multiple_IF
      ~StateTomography.set_multiple_index
      ~StateTomography.set_parent_file
      ~StateTomography.set_run_options
      ~StateTomography.set_sweep_order
      ~StateTomography.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~StateTomography.analysis
      ~StateTomography.analysis_options
      ~StateTomography.experiment_options
      ~StateTomography.run_options
   
   