﻿pyQCat.experiments.single.RBSingle
==================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: RBSingle

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~RBSingle.__init__
      ~RBSingle.acquire_pulse
      ~RBSingle.cal_fidelity
      ~RBSingle.experiment_info
      ~RBSingle.from_experiment_context
      ~RBSingle.get_qubit_str
      ~RBSingle.jupyter_schedule
      ~RBSingle.options_table
      ~RBSingle.play_pulse
      ~RBSingle.plot_schedule
      ~RBSingle.run
      ~RBSingle.set_analysis_options
      ~RBSingle.set_experiment_options
      ~RBSingle.set_multiple_IF
      ~RBSingle.set_multiple_index
      ~RBSingle.set_parent_file
      ~RBSingle.set_run_options
      ~RBSingle.set_sweep_order
      ~RBSingle.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~RBSingle.analysis
      ~RBSingle.analysis_options
      ~RBSingle.experiment_options
      ~RBSingle.run_options
   
   