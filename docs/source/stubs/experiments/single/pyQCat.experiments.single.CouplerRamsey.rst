﻿pyQCat.experiments.single.CouplerRamsey
=======================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: CouplerRamsey

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CouplerRamsey.__init__
      ~CouplerRamsey.acquire_pulse
      ~CouplerRamsey.cal_fidelity
      ~CouplerRamsey.experiment_info
      ~CouplerRamsey.from_experiment_context
      ~CouplerRamsey.get_qubit_str
      ~CouplerRamsey.get_xy_pulse
      ~CouplerRamsey.get_xy_pulse_pre
      ~CouplerRamsey.get_z_pulse
      ~CouplerRamsey.get_z_pulse_pre
      ~CouplerRamsey.jupyter_schedule
      ~CouplerRamsey.options_table
      ~CouplerRamsey.play_pulse
      ~CouplerRamsey.plot_schedule
      ~CouplerRamsey.run
      ~CouplerRamsey.set_analysis_options
      ~CouplerRamsey.set_experiment_options
      ~CouplerRamsey.set_multiple_IF
      ~CouplerRamsey.set_multiple_index
      ~CouplerRamsey.set_parent_file
      ~CouplerRamsey.set_run_options
      ~CouplerRamsey.set_sweep_order
      ~CouplerRamsey.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CouplerRamsey.analysis
      ~CouplerRamsey.analysis_options
      ~CouplerRamsey.experiment_options
      ~CouplerRamsey.run_options
   
   