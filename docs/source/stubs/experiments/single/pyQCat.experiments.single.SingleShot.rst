﻿pyQCat.experiments.single.SingleShot
====================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: SingleShot

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~SingleShot.__init__
      ~SingleShot.acquire_pulse
      ~SingleShot.cal_fidelity
      ~SingleShot.experiment_info
      ~SingleShot.from_experiment_context
      ~SingleShot.get_qubit_str
      ~SingleShot.jupyter_schedule
      ~SingleShot.options_table
      ~SingleShot.play_pulse
      ~SingleShot.plot_schedule
      ~SingleShot.run
      ~SingleShot.save_bin_file
      ~SingleShot.set_analysis_options
      ~SingleShot.set_experiment_options
      ~SingleShot.set_multiple_IF
      ~SingleShot.set_multiple_index
      ~SingleShot.set_parent_file
      ~SingleShot.set_run_options
      ~SingleShot.set_sweep_order
      ~SingleShot.stimulate_state_one
      ~SingleShot.stimulate_state_zero
      ~SingleShot.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~SingleShot.analysis
      ~SingleShot.analysis_options
      ~SingleShot.experiment_options
      ~SingleShot.run_options
   
   