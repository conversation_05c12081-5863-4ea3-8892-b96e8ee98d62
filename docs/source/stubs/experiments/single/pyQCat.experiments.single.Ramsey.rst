﻿pyQCat.experiments.single.Ramsey
================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: <PERSON>

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~<PERSON>.__init__
      ~<PERSON>.acquire_pulse
      ~<PERSON>.cal_fidelity
      ~<PERSON>.experiment_info
      ~<PERSON>.from_experiment_context
      ~<PERSON>.get_qubit_str
      ~<PERSON>.get_xy_pulse
      ~<PERSON>.get_xy_pulse_pre
      ~<PERSON>.get_z_pulse
      ~<PERSON>.get_z_pulse_pre
      ~<PERSON>.jupyter_schedule
      ~<PERSON>.options_table
      ~<PERSON>.play_pulse
      ~<PERSON>.plot_schedule
      ~<PERSON>.run
      ~<PERSON>.set_analysis_options
      ~<PERSON>.set_experiment_options
      ~<PERSON>.set_multiple_IF
      ~<PERSON>.set_multiple_index
      ~<PERSON>.set_parent_file
      ~<PERSON>.set_run_options
      ~<PERSON>.set_sweep_order
      ~<PERSON>.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~<PERSON>.analysis
      ~<PERSON>.analysis_options
      ~<PERSON>.experiment_options
      ~<PERSON>.run_options
   
   