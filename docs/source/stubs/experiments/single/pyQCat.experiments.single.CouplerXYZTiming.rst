﻿pyQCat.experiments.single.CouplerXYZTiming
==========================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: CouplerXYZTiming

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CouplerXYZTiming.__init__
      ~CouplerXYZTiming.acquire_pulse
      ~CouplerXYZTiming.cal_fidelity
      ~CouplerXYZTiming.experiment_info
      ~CouplerXYZTiming.from_experiment_context
      ~CouplerXYZTiming.get_qubit_str
      ~CouplerXYZTiming.get_xy_pulse
      ~CouplerXYZTiming.get_z_pulse
      ~CouplerXYZTiming.jupyter_schedule
      ~CouplerXYZTiming.options_table
      ~CouplerXYZTiming.play_pulse
      ~CouplerXYZTiming.plot_schedule
      ~CouplerXYZTiming.run
      ~CouplerXYZTiming.set_analysis_options
      ~CouplerXYZTiming.set_experiment_options
      ~CouplerXYZTiming.set_multiple_IF
      ~CouplerXYZTiming.set_multiple_index
      ~CouplerXYZTiming.set_parent_file
      ~CouplerXYZTiming.set_run_options
      ~CouplerXYZTiming.set_sweep_order
      ~CouplerXYZTiming.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CouplerXYZTiming.analysis
      ~CouplerXYZTiming.analysis_options
      ~CouplerXYZTiming.experiment_options
      ~CouplerXYZTiming.run_options
   
   