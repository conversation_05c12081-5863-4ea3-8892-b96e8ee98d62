﻿pyQCat.experiments.single.CouplerT2Ramsey
=========================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: CouplerT2Ramsey

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CouplerT2Ramsey.__init__
      ~CouplerT2Ramsey.acquire_pulse
      ~CouplerT2Ramsey.cal_fidelity
      ~CouplerT2Ramsey.experiment_info
      ~CouplerT2Ramsey.from_experiment_context
      ~CouplerT2Ramsey.get_qubit_str
      ~CouplerT2Ramsey.get_xy_pulse
      ~CouplerT2Ramsey.get_xy_pulse_pre
      ~CouplerT2Ramsey.get_z_pulse
      ~CouplerT2Ramsey.get_z_pulse_pre
      ~CouplerT2Ramsey.jupyter_schedule
      ~CouplerT2Ramsey.options_table
      ~CouplerT2Ramsey.play_pulse
      ~CouplerT2Ramsey.plot_schedule
      ~CouplerT2Ramsey.run
      ~CouplerT2Ramsey.run_once
      ~CouplerT2Ramsey.set_analysis_options
      ~CouplerT2Ramsey.set_experiment_options
      ~CouplerT2Ramsey.set_multiple_IF
      ~CouplerT2Ramsey.set_multiple_index
      ~CouplerT2Ramsey.set_parent_file
      ~CouplerT2Ramsey.set_run_options
      ~CouplerT2Ramsey.set_sweep_order
      ~CouplerT2Ramsey.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CouplerT2Ramsey.analysis
      ~CouplerT2Ramsey.analysis_options
      ~CouplerT2Ramsey.experiment_options
      ~CouplerT2Ramsey.run_options
   
   