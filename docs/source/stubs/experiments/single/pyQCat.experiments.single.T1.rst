﻿pyQCat.experiments.single.T1
============================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: T1

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~T1.__init__
      ~T1.acquire_pulse
      ~T1.cal_fidelity
      ~T1.experiment_info
      ~T1.from_experiment_context
      ~T1.get_qubit_str
      ~T1.get_xy_pulse
      ~T1.get_z_pulse
      ~T1.jupyter_schedule
      ~T1.options_table
      ~T1.play_pulse
      ~T1.plot_schedule
      ~T1.run
      ~T1.set_analysis_options
      ~T1.set_experiment_options
      ~T1.set_multiple_IF
      ~T1.set_multiple_index
      ~T1.set_parent_file
      ~T1.set_run_options
      ~T1.set_sweep_order
      ~T1.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~T1.analysis
      ~T1.analysis_options
      ~T1.experiment_options
      ~T1.run_options
   
   