﻿pyQCat.experiments.single.QubitSpectrum
=======================================

.. currentmodule:: pyQCat.experiments.single

.. autoclass:: QubitSpectrum

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~QubitSpectrum.__init__
      ~QubitSpectrum.acquire_pulse
      ~QubitSpectrum.cal_fidelity
      ~QubitSpectrum.experiment_info
      ~QubitSpectrum.from_experiment_context
      ~QubitSpectrum.get_qubit_str
      ~QubitSpectrum.get_xy_pulse
      ~QubitSpectrum.get_z_pulse
      ~QubitSpectrum.jupyter_schedule
      ~QubitSpectrum.options_table
      ~QubitSpectrum.play_pulse
      ~QubitSpectrum.plot_schedule
      ~QubitSpectrum.run
      ~QubitSpectrum.set_analysis_options
      ~QubitSpectrum.set_experiment_options
      ~QubitSpectrum.set_multiple_IF
      ~QubitSpectrum.set_multiple_index
      ~QubitSpectrum.set_parent_file
      ~QubitSpectrum.set_run_options
      ~QubitSpectrum.set_sweep_order
      ~QubitSpectrum.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~QubitSpectrum.analysis
      ~QubitSpectrum.analysis_options
      ~QubitSpectrum.experiment_options
      ~QubitSpectrum.run_options
   
   