﻿pyQCat.experiments.CouplerBaseExperiment
========================================

.. currentmodule:: pyQCat.experiments

.. autoclass:: CouplerBaseExperiment

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CouplerBaseExperiment.__init__
      ~CouplerBaseExperiment.acquire_pulse
      ~CouplerBaseExperiment.cal_fidelity
      ~CouplerBaseExperiment.experiment_info
      ~CouplerBaseExperiment.from_experiment_context
      ~CouplerBaseExperiment.get_qubit_str
      ~CouplerBaseExperiment.jupyter_schedule
      ~CouplerBaseExperiment.options_table
      ~CouplerBaseExperiment.play_pulse
      ~CouplerBaseExperiment.plot_schedule
      ~CouplerBaseExperiment.run
      ~CouplerBaseExperiment.set_analysis_options
      ~CouplerBaseExperiment.set_experiment_options
      ~CouplerBaseExperiment.set_multiple_IF
      ~CouplerBaseExperiment.set_multiple_index
      ~CouplerBaseExperiment.set_parent_file
      ~CouplerBaseExperiment.set_run_options
      ~CouplerBaseExperiment.set_sweep_order
      ~CouplerBaseExperiment.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CouplerBaseExperiment.analysis
      ~CouplerBaseExperiment.analysis_options
      ~CouplerBaseExperiment.experiment_options
      ~CouplerBaseExperiment.run_options
   
   