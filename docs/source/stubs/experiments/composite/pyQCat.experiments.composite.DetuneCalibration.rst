﻿pyQCat.experiments.composite.DetuneCalibration
==============================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: DetuneCalibration

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~DetuneCalibration.__init__
      ~DetuneCalibration.component_experiment
      ~DetuneCalibration.from_experiment_context
      ~DetuneCalibration.get_qubit_str
      ~DetuneCalibration.options_table
      ~DetuneCalibration.run
      ~DetuneCalibration.set_analysis_options
      ~DetuneCalibration.set_experiment_options
      ~DetuneCalibration.set_parent_file
      ~DetuneCalibration.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~DetuneCalibration.analysis
      ~DetuneCalibration.analysis_options
      ~DetuneCalibration.child_experiment
      ~DetuneCalibration.experiment_options
      ~DetuneCalibration.run_options
   
   