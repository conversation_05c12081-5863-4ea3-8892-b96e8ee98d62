﻿pyQCat.experiments.composite.ConditionalPhase
=============================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: ConditionalPhase

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~ConditionalPhase.__init__
      ~ConditionalPhase.component_experiment
      ~ConditionalPhase.from_experiment_context
      ~ConditionalPhase.get_qubit_str
      ~ConditionalPhase.options_table
      ~ConditionalPhase.run
      ~ConditionalPhase.set_analysis_options
      ~ConditionalPhase.set_experiment_options
      ~ConditionalPhase.set_parent_file
      ~ConditionalPhase.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~ConditionalPhase.analysis
      ~ConditionalPhase.analysis_options
      ~ConditionalPhase.child_experiment
      ~ConditionalPhase.experiment_options
      ~ConditionalPhase.run_options
   
   