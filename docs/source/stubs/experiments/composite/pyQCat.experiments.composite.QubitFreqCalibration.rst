﻿pyQCat.experiments.composite.QubitFreqCalibration
=================================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: QubitFreqCalibration

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~QubitFreqCalibration.__init__
      ~QubitFreqCalibration.component_experiment
      ~QubitFreqCalibration.from_experiment_context
      ~QubitFreqCalibration.get_qubit_str
      ~QubitFreqCalibration.options_table
      ~QubitFreqCalibration.run
      ~QubitFreqCalibration.set_analysis_options
      ~QubitFreqCalibration.set_experiment_options
      ~QubitFreqCalibration.set_parent_file
      ~QubitFreqCalibration.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~QubitFreqCalibration.analysis
      ~QubitFreqCalibration.analysis_options
      ~QubitFreqCalibration.child_experiment
      ~QubitFreqCalibration.experiment_options
      ~QubitFreqCalibration.run_options
   
   