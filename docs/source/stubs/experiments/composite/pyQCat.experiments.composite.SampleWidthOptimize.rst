﻿pyQCat.experiments.composite.SampleWidthOptimize
================================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: SampleWidthOptimize

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~SampleWidthOptimize.__init__
      ~SampleWidthOptimize.component_experiment
      ~SampleWidthOptimize.from_experiment_context
      ~SampleWidthOptimize.get_qubit_str
      ~SampleWidthOptimize.options_table
      ~SampleWidthOptimize.run
      ~SampleWidthOptimize.set_analysis_options
      ~SampleWidthOptimize.set_experiment_options
      ~SampleWidthOptimize.set_parent_file
      ~SampleWidthOptimize.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~SampleWidthOptimize.analysis
      ~SampleWidthOptimize.analysis_options
      ~SampleWidthOptimize.child_experiment
      ~SampleWidthOptimize.experiment_options
      ~SampleWidthOptimize.run_options
   
   