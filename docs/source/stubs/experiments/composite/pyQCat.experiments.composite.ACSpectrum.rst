﻿pyQCat.experiments.composite.ACSpectrum
=======================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: ACSpectrum

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~ACSpectrum.__init__
      ~ACSpectrum.component_experiment
      ~ACSpectrum.from_experiment_context
      ~ACSpectrum.get_qubit_str
      ~ACSpectrum.options_table
      ~ACSpectrum.run
      ~ACSpectrum.set_analysis_options
      ~ACSpectrum.set_experiment_options
      ~ACSpectrum.set_parent_file
      ~ACSpectrum.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~ACSpectrum.analysis
      ~ACSpectrum.analysis_options
      ~ACSpectrum.child_experiment
      ~ACSpectrum.experiment_options
      ~ACSpectrum.run_options
   
   