﻿pyQCat.experiments.composite.APEComposite
=========================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: APEComposite

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~APEComposite.__init__
      ~APEComposite.component_experiment
      ~APEComposite.from_experiment_context
      ~APEComposite.get_qubit_str
      ~APEComposite.options_table
      ~APEComposite.run
      ~APEComposite.set_analysis_options
      ~APEComposite.set_experiment_options
      ~APEComposite.set_parent_file
      ~APEComposite.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~APEComposite.analysis
      ~APEComposite.analysis_options
      ~APEComposite.child_experiment
      ~APEComposite.experiment_options
      ~APEComposite.run_options
   
   