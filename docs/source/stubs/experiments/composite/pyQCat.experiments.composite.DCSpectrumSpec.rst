﻿pyQCat.experiments.composite.DCSpectrumSpec
===========================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: DCSpectrumSpec

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~DCSpectrumSpec.__init__
      ~DCSpectrumSpec.component_experiment
      ~DCSpectrumSpec.from_experiment_context
      ~DCSpectrumSpec.get_qubit_str
      ~DCSpectrumSpec.options_table
      ~DCSpectrumSpec.run
      ~DCSpectrumSpec.set_analysis_options
      ~DCSpectrumSpec.set_experiment_options
      ~DCSpectrumSpec.set_parent_file
      ~DCSpectrumSpec.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~DCSpectrumSpec.analysis
      ~DCSpectrumSpec.analysis_options
      ~DCSpectrumSpec.child_experiment
      ~DCSpectrumSpec.experiment_options
      ~DCSpectrumSpec.run_options
   
   