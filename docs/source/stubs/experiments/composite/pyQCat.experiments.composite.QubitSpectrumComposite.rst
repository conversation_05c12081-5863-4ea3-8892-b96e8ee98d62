﻿pyQCat.experiments.composite.QubitSpectrumComposite
===================================================

.. currentmodule:: pyQCat.experiments.composite

.. autoclass:: QubitSpectrumComposite

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~QubitSpectrumComposite.__init__
      ~QubitSpectrumComposite.component_experiment
      ~QubitSpectrumComposite.from_experiment_context
      ~QubitSpectrumComposite.get_qubit_str
      ~QubitSpectrumComposite.options_table
      ~QubitSpectrumComposite.run
      ~QubitSpectrumComposite.set_analysis_options
      ~QubitSpectrumComposite.set_experiment_options
      ~QubitSpectrumComposite.set_parent_file
      ~QubitSpectrumComposite.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~QubitSpectrumComposite.analysis
      ~QubitSpectrumComposite.analysis_options
      ~QubitSpectrumComposite.child_experiment
      ~QubitSpectrumComposite.experiment_options
      ~QubitSpectrumComposite.run_options
   
   