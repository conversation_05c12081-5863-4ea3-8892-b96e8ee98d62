﻿pyQCat.experiments.TopExperiment
================================

.. currentmodule:: pyQCat.experiments

.. autoclass:: TopExperiment

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~TopExperiment.__init__
      ~TopExperiment.acquire_pulse
      ~TopExperiment.cal_fidelity
      ~TopExperiment.experiment_info
      ~TopExperiment.from_experiment_context
      ~TopExperiment.get_qubit_str
      ~TopExperiment.jupyter_schedule
      ~TopExperiment.options_table
      ~TopExperiment.play_pulse
      ~TopExperiment.plot_schedule
      ~TopExperiment.run
      ~TopExperiment.set_analysis_options
      ~TopExperiment.set_experiment_options
      ~TopExperiment.set_multiple_IF
      ~TopExperiment.set_multiple_index
      ~TopExperiment.set_parent_file
      ~TopExperiment.set_run_options
      ~TopExperiment.set_sweep_order
      ~TopExperiment.sweep_readout_trigger_delay
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~TopExperiment.analysis
      ~TopExperiment.analysis_options
      ~TopExperiment.experiment_options
      ~TopExperiment.run_options
   
   