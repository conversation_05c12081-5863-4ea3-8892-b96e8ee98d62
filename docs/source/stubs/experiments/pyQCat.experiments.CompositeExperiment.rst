﻿pyQCat.experiments.CompositeExperiment
======================================

.. currentmodule:: pyQCat.experiments

.. autoclass:: CompositeExperiment

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~CompositeExperiment.__init__
      ~CompositeExperiment.component_experiment
      ~CompositeExperiment.from_experiment_context
      ~CompositeExperiment.get_qubit_str
      ~CompositeExperiment.options_table
      ~CompositeExperiment.run
      ~CompositeExperiment.set_analysis_options
      ~CompositeExperiment.set_experiment_options
      ~CompositeExperiment.set_parent_file
      ~CompositeExperiment.set_run_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~CompositeExperiment.analysis
      ~CompositeExperiment.analysis_options
      ~CompositeExperiment.child_experiment
      ~CompositeExperiment.experiment_options
      ~CompositeExperiment.run_options
   
   