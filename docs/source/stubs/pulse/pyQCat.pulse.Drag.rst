﻿pyQCat.pulse.Drag
=================

.. currentmodule:: pyQCat.pulse

.. autoclass:: Drag

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~Drag.__init__
      ~Drag.correct_ac_crosstalk
      ~Drag.correct_compensate
      ~Drag.correct_delay
      ~Drag.correct_distortion
      ~Drag.correct_pulse
      ~Drag.get_pulse
      ~Drag.get_raw_pulse
      ~Drag.plot
      ~Drag.validate_parameters
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~Drag.attach
      ~Drag.bit
      ~Drag.delay
      ~Drag.envelop
      ~Drag.id
      ~Drag.parameters
      ~Drag.pulse
      ~Drag.raw_pulse
      ~Drag.sweep
      ~Drag.type
      ~Drag.width
   
   