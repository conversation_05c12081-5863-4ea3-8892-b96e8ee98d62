﻿pyQCat.preliminary.FindBusCavityFreq
====================================

.. currentmodule:: pyQCat.preliminary

.. autoclass:: FindBusCavityFreq

   
   .. automethod:: __init__

   
   .. rubric:: Methods

   .. autosummary::
   
      ~FindBusCavityFreq.__init__
      ~FindBusCavityFreq.from_experiment_context
      ~FindBusCavityFreq.get_qubit_str
      ~FindBusCavityFreq.options_table
      ~FindBusCavityFreq.run
      ~FindBusCavityFreq.select_dc_source
      ~FindBusCavityFreq.select_mic_source
      ~FindBusCavityFreq.select_net_analyzer
      ~FindBusCavityFreq.set_analysis_options
      ~FindBusCavityFreq.set_experiment_options
      ~FindBusCavityFreq.set_parent_file
      ~FindBusCavityFreq.set_run_options
      ~FindBusCavityFreq.set_sub_analysis_options
   
   

   
   
   .. rubric:: Attributes

   .. autosummary::
   
      ~FindBusCavityFreq.analysis
      ~FindBusCavityFreq.analysis_options
      ~FindBusCavityFreq.coupler
      ~FindBusCavityFreq.experiment_options
      ~FindBusCavityFreq.qaio
      ~FindBusCavityFreq.qubit
      ~FindBusCavityFreq.run_options
      ~FindBusCavityFreq.sub_analysis_options
   
   