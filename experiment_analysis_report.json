{"structure_overview": {"total_fields": 10, "main_sections": ["_id", "context_meta", "execute_meta", "version", "device", "username", "env_id", "exp_type", "exp_class", "date"], "data_size_estimate": 4264, "has_nested_structure": true}, "basic_info": {"experiment_id": "67dd37327e210bccc33cf351", "experiment_class": "SingleShot", "experiment_type": "TOP", "version": "0.23.0", "device": "C", "username": "monitor_y2", "date": "2025-03-21", "environment_id": "67dd2ecf3ede1508c6415073"}, "context_meta": {"has_physical_units": true, "has_experiment_options": true, "has_analysis_options": true, "physical_units_count": 1, "experiment_options_count": 39, "analysis_options_count": 15, "physical_units_names": ["q7"], "physical_unit_parameters": ["qid", "bit", "name", "tunable", "goodness", "drive_freq", "drive_power", "probe_freq", "probe_power", "tls_freq", "anharmonicity", "dc", "dc_max", "dc_min", "ac", "T1", "T2", "TS2", "z_flux_channel", "z_dc_channel", "update_time", "idle_point", "_row", "_col", "ac_spectrum", "dc_spectrum", "readout_point_model", "readout_point", "ac_prepare_rise_model", "ac_prepare_rise_options", "awg_bias", "q_int", "q_ext", "fc_max", "fc_min", "open_half_pi", "xy_channel", "readout_channel", "sample_delay", "sample_width", "XYwave", "Zwave", "Mwave", "union_readout", "fidelity", "rb_purity_fidelity", "xeb_fidelity", "xeb_purity_fidelity", "qpt_chi_fidelity", "qpt_process_fidelity", "rb_depth", "xeb_depth", "f12_options", "inst", "prepare_measure", "zeta"], "key_experiment_options": ["save_result", "use_simulator", "repeat", "data_type", "level_str"], "key_analysis_options": ["is_plot", "quality_bounds", "method", "n_clusters"]}, "execute_meta": {"has_timing_info": true, "has_quality_info": true, "has_result_paths": true, "execution_fields": ["start_time", "end_time", "exp_label", "physical_units", "parent_id", "parallel_id", "task_id", "child_record_ids", "state", "analysis_result", "quality", "save_path", "png_results", "epd_results"], "execution_duration_seconds": 8.0}, "design_logic": {"separation_of_concerns": {"description": "数据结构采用分层设计，将不同类型的信息分别存储", "layers": ["基础标识层（_id, exp_class, version等）", "上下文配置层（context_meta）", "执行记录层（execute_meta）"]}, "reproducibility_support": {"description": "支持实验的完整重现", "features": ["完整的物理单元参数记录", "详细的实验选项配置", "精确的时间戳记录", "版本信息保存"]}, "traceability_support": {"description": "支持实验的完整追溯", "features": ["唯一实验标识符", "用户信息记录", "执行环境信息", "质量评估结果"]}, "extensibility": {"description": "设计具有良好的可扩展性", "features": ["嵌套字典结构便于添加新参数", "模块化的元数据组织", "灵活的结果存储机制"]}}}