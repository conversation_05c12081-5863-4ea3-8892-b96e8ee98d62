
## level 20
![image.png](https://qiniu.kanes.top/blog/20250806162657441.png)

共计107行，版本为`C.0.3.0`，主要错误

```json
Traceback (most recent call last):
  File "D:\projects\pyqcat-naga\pyqcat_naga\api\process\task\task_core.py", line 122, in deal_task
    await getattr(self, func)(
  File "D:\projects\pyqcat-naga\pyqcat_naga\api\process\task\task_core.py", line 1104, in copy_data_v1
    await copy_other_data_all_v1(
  File "D:\projects\pyqcat-naga\pyqcat_naga\api\common\chip_extend.py", line 1590, in copy_other_data_all_v1
    vali_res = validate_copy_parameters(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\pyqcat-naga\pyqcat_naga\api\common\chip_extend.py", line 1038, in validate_copy_parameters
    if to_user != user.username:
                  ^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'username'

```

```json
Traceback (most recent call last):
  File "/app/pyqcat_naga/api/process/task/task_core_main.py", line 122, in deal_task
    await getattr(self, func)(
  File "/app/pyqcat_naga/api/process/task/task_core_main.py", line 1116, in copy_data_v1
    await copy_other_data_all_v1(
  File "/app/pyqcat_naga/api/common/chip_extend.py", line 1695, in copy_other_data_all_v1
    vali_res = validate_copy_parameters(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/pyqcat_naga/api/common/chip_extend.py", line 1142, in validate_copy_parameters
    if to_user != user.username:
                  ^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'username'

```

## level 15

![image.png](https://qiniu.kanes.top/blog/20250806163112224.png)

共计2392行，

```json
Task Timeout
--------------------------------------------------
Task[RID(7134019e-8cae-4c0a-aca9-48087bf473bc) | TID(44b058ef-2e0f-456c-8419-054eb0cb41e4) | DataType(iq) | Loop(450)]
```

```json
DataCollector Error || send_execute_data_to_courier | {'code': 300, 'data': {}, 'msg': "get recv msg timeout.{'require_id': b'\\xb4|\\xc2\\xf2\\x00\\x0eA\\xfc\\xa6m\\xda\\xb2\\xb5\\xf1\\x93]', 'start_time': datetime.datetime(2025, 6, 13, 14, 54, 26, 374162), 'true_time': None, 'pre_time': 87726.0748717, 'now_time_perf': 87746.0846878, 'now_time': datetime.datetime(2025, 6, 13, 14, 54, 46, 395218), 'cache_id_len': 2, 'cache_id': [b'\\xe9\\xe9\\xcf\\x02\\xeb\\x06DF\\x89\\xa1\\x0ew\\x13\\x8bCq', b'\\xb4|\\xc2\\xf2\\x00\\x0eA\\xfc\\xa6m\\xda\\xb2\\xb5\\xf1\\x93]'], 'cache_id_pre_len': 0, 'cache_id_pre': []} 2025-06-13 14:54:46.396214"}
```

```json
DataCollector Error || send_execute_data_to_courier | {'code': 500, 'data': {}, 'msg': "Internal Server Error: cannot encode object: array([-0.36, -0.35, -0.34, -0.33, -0.32, -0.31, -0.3 , -0.29, -0.28,\n       -0.27, -0.26, -0.25, -0.24, -0.23, -0.22, -0.21, -0.2 , -0.19,\n       -0.18, -0.17, -0.16, -0.15, -0.14, -0.13, -0.12, -0.11, -0.1 ,\n       -0.09, -0.08, -0.07, -0.06, -0.05, -0.04, -0.03, -0.02, -0.01,\n        0.  ,  0.01,  0.02,  0.03,  0.04,  0.05,  0.06]), of type: <class 'numpy.ndarray'>"}
```

```json
DataCollector Error || batch_end_signal | {'code': 404, 'data': {}, 'msg': 'batch(684f7d27178836d68f5240eb) is not exist'}
```

```json
DataCollector Error || batch_exp_end_signal | {'code': 403, 'data': {}, 'msg': 'Not allowed update other user(monitor_y3)`s batch(6864c5fe607634b2a530a43d)'}
```


## level 10

1. Experiment Crash、Experiment Fail 等单独作为一个字段用于查看分布。（优先级较低）
2. 复现实验，查看具体的错误；分析，增加日志提示。（下一步）
3. 感觉Experiment Crash提示不够具体

```json
Experiment Crash
--------------------------------------------------
q1-XYZTimingComposite-XYZTiming(1-unknow)-count-0-xy_delay-0.0-z_delay-0.0-687386ee97cdb725f3079942
--------------------------------------------------
zero-size array to reduction operation maximum which has no identity
--------------------------------------------------
Traceback (most recent call last):
  File "F:\mycode\gitlab\apps\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "F:\mycode\gitlab\apps\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 610, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\mycode\gitlab\apps\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\mycode\gitlab\apps\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 171, in top_experiment_analysis
    return base_analysis_process(
  File "F:\mycode\gitlab\apps\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 214, in base_analysis_process
    return run_analysis_process(
  File "F:\mycode\gitlab\apps\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 254, in run_analysis_process
    analysis_obj.run_analysis()
  File "F:\mycode\gitlab\apps\pyqcat-apps\pyQCat\analysis\library\xyztiming_analysis.py", line 269, in run_analysis
    super().run_analysis()
  File "F:\mycode\gitlab\apps\pyqcat-apps\pyQCat\analysis\curve_analysis.py", line 410, in run_analysis
    res = self._run_fitting()
  File "F:\mycode\gitlab\apps\pyqcat-apps\pyQCat\analysis\curve_analysis.py", line 213, in _run_fitting
    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)
  File "F:\mycode\gitlab\apps\pyqcat-apps\pyQCat\analysis\library\xyztiming_analysis.py", line 130, in _guess_fit_param
    idx_half = np.max(np.argwhere(y < (np.max(y) + np.min(y)) / 2))
  File "C:\Users\<USER>\AppData\Local\miniconda3\envs\monster\lib\site-packages\numpy\core\fromnumeric.py", line 2810, in max
    return _wrapreduction(a, np.maximum, 'max', axis, None, out,
  File "C:\Users\<USER>\AppData\Local\miniconda3\envs\monster\lib\site-packages\numpy\core\fromnumeric.py", line 88, in _wrapreduction
    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)
ValueError: zero-size array to reduction operation maximum which has no identity
```

```json
Experiment Crash
--------------------------------------------------
q1-XYZTimingComposite-6873485597cdb725f3079868
--------------------------------------------------
'dict' object has no attribute 'line'
--------------------------------------------------
Traceback (most recent call last):
  File "F:\mycode\gitlab\apps\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "F:\mycode\gitlab\apps\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 516, in run_experiment
    await self._sync_composite_run()
  File "F:\mycode\gitlab\apps\pyqcat-apps\pyQCat\experiments\composite\single_gate\xyz_timing_com.py", line 131, in _sync_composite_run
    line = xyz_exp.analysis.results.line.value
AttributeError: 'dict' object has no attribute 'line'
```




## level 5

```json
Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(RabiScanAmp) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\project\lzw\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 482, in run
    self._validate_and_merge()
  File "D:\code\project\lzw\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 236, in _validate_and_merge
    assert len(set(status_list)) == 1, (
AssertionError: Different sub experiment status participating in parallel, details:
[4, 4, 4, 4, 4, 1]

```

```json
Experiment Fail
--------------------------------------------------
q57-Ramsey-689412d9d6c1a0f0a3d84374
--------------------------------------------------
<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: 
"""
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\app39\lib\concurrent\futures\process.py", line 246, in _process_worker
    r = call_item.fn(*call_item.args, **call_item.kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 134, in parallel_top_experiment_analysis
    return base_analysis_process(
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 216, in base_analysis_process
    return run_analysis_process(
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 261, in run_analysis_process
    analysis_obj.run_analysis()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\analysis\curve_analysis.py", line 410, in run_analysis
    res = self._run_fitting()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\analysis\curve_analysis.py", line 213, in _run_fitting
    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\analysis\oscillation_analysis.py", line 139, in _guess_fit_param
    fit_opt.bounds.set_if_empty(
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\analysis\specification.py", line 311, in set_if_empty
    self.__setitem__(key, value)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\analysis\specification.py", line 298, in __setitem__
    super().__setitem__(key, self.format(value))
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\analysis\specification.py", line 377, in format
    raise AnalysisFitDataError(
pyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.
"""

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\calculate_resource.py", line 35, in run_in_executor
    return await loop.run_in_executor(executor, fn, *args)
pyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.

```

```json
Experiment Fail
--------------------------------------------------
q4-SpinEcho-6891ae7794a082d218d9ef76
--------------------------------------------------
<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: 
"""
Traceback (most recent call last):
  File "D:\Anaconda3\envs\visage_o\lib\concurrent\futures\process.py", line 246, in _process_worker
    r = call_item.fn(*call_item.args, **call_item.kwargs)
  File "D:\apps-test\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 132, in parallel_top_experiment_analysis
    return base_analysis_process(
  File "D:\apps-test\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 214, in base_analysis_process
    return run_analysis_process(
  File "D:\apps-test\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 254, in run_analysis_process
    analysis_obj.run_analysis()
  File "D:\apps-test\pyqcat-apps\pyQCat\analysis\curve_analysis.py", line 410, in run_analysis
    res = self._run_fitting()
  File "D:\apps-test\pyqcat-apps\pyQCat\analysis\curve_analysis.py", line 213, in _run_fitting
    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)
  File "D:\apps-test\pyqcat-apps\pyQCat\analysis\oscillation_analysis.py", line 139, in _guess_fit_param
    fit_opt.bounds.set_if_empty(
  File "D:\apps-test\pyqcat-apps\pyQCat\analysis\specification.py", line 311, in set_if_empty
    self.__setitem__(key, value)
  File "D:\apps-test\pyqcat-apps\pyQCat\analysis\specification.py", line 298, in __setitem__
    super().__setitem__(key, self.format(value))
  File "D:\apps-test\pyqcat-apps\pyQCat\analysis\specification.py", line 377, in format
    raise AnalysisFitDataError(
pyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.
"""

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\apps-test\pyqcat-apps\pyQCat\concurrent\calculate_resource.py", line 35, in run_in_executor
    return await loop.run_in_executor(executor, fn, *args)
pyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.

```



