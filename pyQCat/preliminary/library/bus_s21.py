# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/10
# __author:       <PERSON><PERSON><PERSON>

import time
import numpy as np
from pyQCat.analysis import BusS21Analysis
from pyQCat.concurrent.worker.analysis_interface import run_analysis_process
from pyQCat.preliminary.preliminary_models import PreliminaryExperiment
from pyQCat.structures import ExperimentData, QDict
from pyQCat.tools import qarange


class BusS21Collector(PreliminaryExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.network_analyzer = "E5071C"
        options.high_power = -15
        options.low_power = -45
        options.wide_scope_freq = qarange(4000, 8500, 1)
        options.small_scope_freq = qarange(6900, 7400, 1)
        options.net_IFBW = 500
        options.bus = 1
        return options

    @classmethod
    def _default_analysis_options(cls):
        options = super()._default_analysis_options()
        options.raw_data_format = "plot"
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.acq_data = None
        return options

    def _metadata(self):
        metadata = super()._metadata()
        metadata.draw_meta["BUS"] = self.experiment_options.bus
        metadata.draw_meta["High Power"] = self.experiment_options.high_power
        metadata.draw_meta["Low Power"] = self.experiment_options.low_power
        metadata.draw_meta["IFBW"] = self.experiment_options.net_IFBW
        return metadata

    def _run_experiment(self):
        high_power = self.experiment_options.high_power
        low_power = self.experiment_options.low_power
        wide_scope_freq = self.experiment_options.wide_scope_freq
        small_scope_freq = self.experiment_options.small_scope_freq
        net_IFBW = self.experiment_options.net_IFBW
        result = QDict()

        self.net_analyzer.set_scan_param(
            wide_scope_freq[0] * 1e6,
            wide_scope_freq[-1] * 1e6,
            N=len(wide_scope_freq),
            IFBW=net_IFBW,
            power=high_power,
        )
        time.sleep(1)
        freq, amp, phase = self.net_analyzer.read_result(True, True, True)
        result.hp_wide = QDict(freq=wide_scope_freq, amp=amp, phase=phase)
        time.sleep(1)

        self.net_analyzer.set_scan_param(
            small_scope_freq[0] * 1e6,
            small_scope_freq[-1] * 1e6,
            N=len(small_scope_freq),
            IFBW=net_IFBW,
            power=high_power,
        )
        time.sleep(1)
        freq, amp, phase = self.net_analyzer.read_result(True, True, True)
        result.hp_small = QDict(freq=small_scope_freq, amp=amp, phase=phase)
        time.sleep(1)

        self.net_analyzer.set_scan_param(
            small_scope_freq[0] * 1e6,
            small_scope_freq[-1] * 1e6,
            N=len(small_scope_freq),
            IFBW=net_IFBW,
            power=low_power,
        )
        time.sleep(1)
        freq, amp, phase = self.net_analyzer.read_result(True, True, True)
        result.lp_small = QDict(freq=small_scope_freq, amp=amp, phase=phase)
        time.sleep(1)

        self.run_options.acq_data = result

    def _run_analysis(self):
        acq_data = self.run_options.acq_data

        exp_data = ExperimentData(
            x_data=self.experiment_options.small_scope_freq,
            y_data=dict(
                hp_wide_amp=acq_data.hp_wide.amp,
                # hp_wide_phase=acq_data.hp_wide.phase,
                hp_small_amp=acq_data.hp_small.amp,
                # hp_small_phase=acq_data.hp_small.phase,
                lp_small_amp=acq_data.lp_small.amp,
                # lp_small_phase=acq_data.lp_small.phase,
            ),
            metadata=self._metadata(),
        )

        exp_data.replace_x_data = dict(
            hp_wide_amp=acq_data.hp_wide.freq,
            # hp_wide_phase=acq_data.hp_wide.freq,
            hp_small_amp=acq_data.hp_small.freq,
            # hp_small_phase=acq_data.hp_small.freq,
            lp_small_amp=acq_data.lp_small.freq,
            # lp_small_phase=acq_data.lp_small.freq,
        )

        self._analysis = run_analysis_process(
            BusS21Analysis, exp_data, self.analysis_options, self.file
        )

        for key in ["hp_wide_amp", "hp_small_amp", "lp_small_amp"]:
            cur_data = np.array(
                [
                    self.experiment_data.replace_x_data.get(key),
                    self.experiment_data.y_data.get(key),
                ]
            )
            self.file.save_data(cur_data, name=key)
