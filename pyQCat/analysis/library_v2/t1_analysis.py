# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/24
# __author:       <PERSON><PERSON><PERSON>

import numpy as np
from ..curve_fit_analysis import (
    CurveFitAnalysis,
    FitModel,
    Options,
    StandardCurveAnalysis,
)
from ..fit.fit_models import complex_decay
from ..specification import CurveAnalysisData, FitOptions, List, ParameterRepr, Union
from ..library import T1Analysis


class T1AnalysisInner(CurveFitAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.fit_model = FitModel(
            fit_func=complex_decay, model_description=r"amp \exp(-x/tau) + baseline"
        )
        options.quality_bounds = [0.9, 0.85, 0.77]
        options.x_label = "Delay [ns]"
        options.result_parameters = [
            ParameterRepr(name="t1r", repr="t1r", unit="us"),
            ParameterRepr(name="t1qp_tilde", repr="t1qp_tilde", unit="us"),
            "n_qp",
        ]
        return options

    def _guess_fit_param(
        self,
        fit_opt: FitOptions,
        data: CurveAnalysisData,
    ) -> Union[FitOptions, List[FitOptions]]:
        """Guess initial fit parameters.

        Args:
            fit_opt: Fit options filled with user provided guess and bounds.
            data: Formatted data collection to fit.

        Returns:
            List of fit options that are passed to the fitter function.
        """
        t = data.x
        y_data = data.y

        # 1. 估计基线 (baseline)
        baseline_guess = np.mean(y_data[-len(y_data) // 10 :])

        # 2. 估计振幅 (amp)
        amp_guess = y_data[0] - baseline_guess

        # 3. 估计弛豫时间 (t1r)
        # 选择尾部数据 (最后20%)
        tail_start = int(len(t) * 0.8)
        t_tail = t[tail_start:]
        y_tail = y_data[tail_start:]

        # 线性拟合尾部对数数据
        valid_idx = (y_tail - baseline_guess) > 1e-6
        if np.sum(valid_idx) > 2:
            y_log = np.log((y_tail[valid_idx] - baseline_guess) / amp_guess)
            slope, _ = np.polyfit(t_tail[valid_idx], y_log, 1)
            t1r_guess = -1 / slope
        else:
            t1r_guess = np.max(t) / 2  # 默认值

        # 4. 估计准粒子特征时间 (t1qp_tilde)
        # 补偿弛豫项
        y_comp = (y_data - baseline_guess) * np.exp(t / t1r_guess) / amp_guess

        # 取对数并处理无效值
        y_log = np.zeros_like(y_comp)
        valid_idx = (y_comp > 1e-6) & (t > 0)
        y_log[valid_idx] = np.log(y_comp[valid_idx])

        # 选择中段数据 (20%-80%)
        mid_start = int(len(t) * 0.2)
        mid_end = int(len(t) * 0.8)
        t_mid = t[mid_start:mid_end]
        y_log_mid = y_log[mid_start:mid_end]

        # 线性拟合
        valid_mid = (y_log_mid < 0) & (np.isfinite(y_log_mid))
        if np.sum(valid_mid) > 2:
            slope, _ = np.polyfit(t_mid[valid_mid], y_log_mid[valid_mid], 1)
            t1qp_tilde_guess = -1 / slope
        else:
            t1qp_tilde_guess = np.max(t) / 5  # 默认值

        # 5. 估计准粒子密度参数 (n_qp)
        if len(t) > 1 and y_data[0] > baseline_guess and y_data[1] > baseline_guess:
            ratio = (y_data[1] - baseline_guess) / (y_data[0] - baseline_guess)
            n_qp_guess = -np.log(ratio) * t1qp_tilde_guess / (t[1] - t[0])
        else:
            n_qp_guess = 1.0  # 默认值

        # 确保参数合理
        t1r_guess = max(t1r_guess, 0.1)
        t1qp_tilde_guess = max(t1qp_tilde_guess, 0.1)
        n_qp_guess = max(n_qp_guess, 0.01)

        fit_opt.p0.set_if_empty(
            amp=amp_guess,
            baseline=baseline_guess,
            t1r=t1r_guess,
            n_qp=n_qp_guess,
            t1qp_tilde=t1qp_tilde_guess,
        )
        # fit_opt.bounds.set_if_empty(t1r=(0, np.inf))
        return fit_opt

    def _extract_result(self):
        """Extract analysis results from important fit parameters.

        Args:
            data_key (str): The basis for selecting data.
        """
        super()._extract_result()
        t1r_result = self.results.t1r
        t1qp_tilde_result = self.results.t1qp_tilde
        t1r_result.value = round(t1r_result.value / 1e3, 4)
        t1qp_tilde_result.value = round(t1qp_tilde_result.value / 1e3, 3)


class T1AnalysisV2(StandardCurveAnalysis):
    def _data_processing(self):
        child_ana_class = [T1Analysis, T1AnalysisInner]
        child_ana = []
        for ana in child_ana_class:
            analysis = ana(self.experiment_data)
            analysis.set_options(
                quality_bounds=self.options.quality_bounds, is_plot=False
            )
            analysis.run_analysis()
            child_ana.append(analysis)
        self.experiment_data.metadata.process_meta["child_analysis"] = child_ana

    def _visualization(self) -> None:
        """Draw all data, including raw data and fit data and so on.
        Some composite experiments may need to override this function.
        """
        # define ax index
        ax_index = 0

        # Set plot title.
        self.drawer.set_options(title=self._description())

        # get experiment keys.
        exp_keys = list(self.experiment_data.y_data.keys())

        # plot raw data.
        for i, key in enumerate(exp_keys):
            if self.options.plot_raw_data is True:
                x_data = self.experiment_data.x_data
                raw_data = self.experiment_data.y_data[key]
                self.drawer.draw_raw_data(
                    x_data=x_data, y_data=raw_data, ax_index=ax_index
                )
            ax_index += 1

        colors = ["red", "green"]
        for index, ana in enumerate(
            self.experiment_data.metadata.process_meta.pop("child_analysis")
        ):
            for data_key, analysis_data in ana.analysis_datas.items():
                draw_index = exp_keys.index(data_key)
                analysis_x = analysis_data.x
                analysis_fit = analysis_data.fit_data
                if analysis_fit is not None and not np.isnan(analysis_fit.y_fit).all():
                    self.drawer.draw_fit_line(
                        x_data=analysis_x,
                        y_data=analysis_fit.y_fit,
                        ax_index=draw_index,
                        label=ana.options.fit_model.fit_func.__name__,
                        color=colors[index],
                    )

    def _evaluate_quality(self):
        child_ana = self.experiment_data.metadata.process_meta["child_analysis"]
        for ana in child_ana:
            self.results.update(ana.results)
            self._quality = ana.quality
