# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/30
# __author:       <PERSON><PERSON><PERSON>

from ..algorithms.find_peak import find_peaks_with_prominence
from ..quality.base_quality import BaseQuality, QualityDescribe
from ..specification import ParameterRepr
from ..standard_curve_analysis import Options, StandardCurveAnalysis


class BusS21Analysis(StandardCurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        options = super()._default_options()

        options.x_label = "Frequency (MHz)"
        options.y_label = ["Amp", "Amp", "Amp"]
        options.sub_title = [
            "High Power",
            "High Power",
            "Low Power",
        ]
        options.result_parameters = [
            ParameterRepr(name="cavity_scope", repr="bus cavity scope", unit="MHz")
        ]

        return options

    def _extract_result(self):
        x = self.experiment_data.replace_x_data.get("hp_small_amp")
        y = self.experiment_data.y_data.get("hp_small_amp")
        f_index, _ = find_peaks_with_prominence(y, num_peak=6, distance=10)
        if len(f_index) == 6:
            fc_min, *_, fc_max = [round(x[i], 3) for i in f_index]
            self.results.cavity_scope.value = [fc_min, fc_max]
        else:
            self._quality = BaseQuality.instantiate(QualityDescribe.normal)

    def _initialize_canvas(self):
        super()._initialize_canvas()
        self.drawer.set_options(subplots=(3, 1), figsize=(12, 12), marker="")
        self.drawer.initialize_canvas()
