# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
#
# QubitSpectrumScanPowerAnalysis: Analysis of quantum bit spectrum experiment results at different drive powers,
# automatically selecting the optimal drive power.

# __date:         2025/06/17
# __author:       KangKang Geng

import numpy as np

from ...log import pyqlog
from ...structures import Options
from ..standard_curve_analysis import StandardCurveAnalysis
from ..specification import ParameterRepr
from ..quality.base_quality import BaseQuality, QualityDescribe


class QubitSpectrumScanPowerAnalysis(StandardCurveAnalysis):
    """
    Qubit Spectrum Scan Power Analysis Class

    Analyzes quantum bit spectrum experiment results at different drive powers and automatically selects the optimal drive power.
    Selection principle: Choose the power point with only a single peak (not divergent, i.e., only one value in peaks)
    and the highest SNR among sub-experiments.
    """

    @classmethod
    def _default_options(cls) -> Options:
        """
        Define default options for the analysis.

        Returns:
            Options: Default configuration options for the analysis.
        """
        options = super()._default_options()
        options.snr_bounds = 0
        options.x_label = "Drive Power [dB]"
        options.y_label = ["Amp", "Phase"]
        options.window_length = 7  # Default smoothing window length
        options.freq_distance = 70  # Default frequency distance (MHz)
        options.result_parameters = [
            ParameterRepr(name="best_power", repr="best_power", unit="db", param_path="Qubit.drive_power"),
            ParameterRepr(name="best_snr", repr="best_snr"),
            ParameterRepr(name="freq", repr="freq", unit="Mhz", param_path="")
        ]
        return options

    def _calculate_peak_snr(self, peak_height: float, y_data: np.ndarray, key: str) -> float:
        """
        Calculate the SNR of a peak and handle the case of dips.

        Parameters:
            peak_height (float): Height of the peak.
            y_data (np.ndarray): Y-axis data.
            key (str): Extrema type ('peaks' or 'dips').

        Returns:
            float: Calculated SNR value.
        """
        from ...tools.utilities import get_snr, normalization

        # Ensure data is normalized to [0,1] range
        y_normal = normalization(y_data)

        # Normalize peak_height
        peak_height_normal = (peak_height - np.min(y_data)) / (np.max(y_data) - np.min(y_data))

        # If it's a dip, adjust the signal
        if key == "dips":
            standard = np.median(y_normal)
            peak_height_normal = standard + abs(standard - peak_height_normal)

        # Calculate SNR
        return get_snr(peak_height_normal, y_normal)

    def _data_processing(self):
        """
        Process quantum bit spectrum scan power experiment data, analyzing quantum bit spectrum experiment results
        at different drive powers to automatically select the optimal drive power.

        Selection principles:
        1. Choose power points with single features in both Amp (peaks) and Phase (dips) data
        2. Prioritize higher frequency values
        3. If frequencies are equal, prioritize higher SNR values
        4. If Amp and Phase suggest different power points, choose the lower power (more conservative)
        """
        from ..algorithms.find_peak import find_peaks_dips, distance_to_point
        from ...tools.utilities import normalization

        # Get drive power list
        drive_power_list = self.experiment_data.x_data

        # Store valid power points and their SNR values for both Amp and Phase
        valid_powers_amp = []
        snr_values_amp = []
        power_to_peak_freq_amp = {}

        valid_powers_phase = []
        snr_values_phase = []
        power_to_peak_freq_phase = {}

        # Phase 1: Analyze Amp data for each power point
        for i, power in enumerate(drive_power_list):
            # Get sub-experiment data
            child_data = self.experiment_data.child_data(index=i)
            if child_data is None or "Amp" not in child_data.y_data:
                continue

            data_type = "Amp"
            x_data = child_data.x_data
            y_data = child_data.y_data[data_type]
            y_normal = normalization(y_data)

            # Set peak detection parameters
            window_length = self.options.window_length
            freq_distance = self.options.freq_distance
            xval_distance = distance_to_point(freq_distance, x_data)

            # Detect peaks
            key, features, properties = find_peaks_dips(
                x_data,
                y_normal,
                window_length=window_length,
                interpolation_num=3,
                distance=float(xval_distance),
                prominences=False
            )

            # If only one feature, add to valid power points
            if len(features) == 1:
                feature_height = properties["peak_heights"][0] if hasattr(properties["peak_heights"],
                                                                          "__len__") else properties["peak_heights"]
                feature_snr = self._calculate_peak_snr(feature_height, y_data, key)
                valid_powers_amp.append(power)
                snr_values_amp.append(feature_snr)
                power_to_peak_freq_amp[power] = features

        # Phase 1: Analyze Phase data for each power point
        for i, power in enumerate(drive_power_list):
            # Get sub-experiment data
            child_data = self.experiment_data.child_data(index=i)
            if child_data is None or "Phase" not in child_data.y_data:
                continue

            data_type = "Phase"
            x_data = child_data.x_data
            y_data = child_data.y_data[data_type]
            y_normal = normalization(y_data)

            # Set peak detection parameters
            window_length = self.options.window_length
            freq_distance = self.options.freq_distance
            xval_distance = distance_to_point(freq_distance, x_data)

            # Detect dips (for Phase data)
            key, features, properties = find_peaks_dips(
                x_data,
                y_normal,
                window_length=window_length,
                interpolation_num=3,
                distance=float(xval_distance),
                prominences=False
            )

            # If peaks were found but we need dips, try finding dips
            if key == "peaks":
                key, features, properties = find_peaks_dips(
                    x_data,
                    -y_normal,
                    window_length=window_length,
                    interpolation_num=3,
                    distance=float(xval_distance),
                    prominences=False
                )
                key = "dips"  # Force key to be dips (for Phase data)

            # If only one feature, add to valid power points
            if len(features) == 1:
                feature_height = properties["peak_heights"][0] if hasattr(properties["peak_heights"],
                                                                          "__len__") else properties["peak_heights"]
                feature_snr = self._calculate_peak_snr(feature_height, y_data, key)
                valid_powers_phase.append(power)
                snr_values_phase.append(feature_snr)
                power_to_peak_freq_phase[power] = features


        # Amp data analysis results summary
        pyqlog.info("Amp data analysis results:")
        if valid_powers_amp:
            pyqlog.info(f"  Valid power points: {valid_powers_amp}")
            for i, power in enumerate(valid_powers_amp):
                freq_values = power_to_peak_freq_amp[power]
                snr = snr_values_amp[i]
                pyqlog.info(f"    Power point {power}dB: Frequency={[round(f, 2) for f in freq_values]}MHz, SNR={round(snr, 3)}")
        else:
            pyqlog.warning("  No valid power points found in Amp data")

        # Phase data analysis results summary
        pyqlog.info("\nPhase data analysis results:")
        if valid_powers_phase:
            pyqlog.info(f"  Valid power points: {valid_powers_phase}")
            for i, power in enumerate(valid_powers_phase):
                freq_values = power_to_peak_freq_phase[power]
                snr = snr_values_phase[i]
                pyqlog.info(f"    Power point {power}dB: Frequency={[round(f, 2) for f in freq_values]}MHz, SNR={round(snr, 3)}")
        else:
            pyqlog.warning("  No valid power points found in Phase data")

        # Find common valid power points
        common_powers = list(set(valid_powers_amp) & set(valid_powers_phase))
        if common_powers:
            pyqlog.info(f"\nCommon valid power points: {common_powers}")
        else:
            pyqlog.warning("\nNo common valid power points")

        # Phase 2: Select the best power point

        # Initialize best power point related variables
        best_power = None
        best_snr = 0
        best_freq = 0
        selection_basis = ""

        # Case 1: If Amp data has single peaks and Phase data has single dips
        if valid_powers_amp and valid_powers_phase:
            pyqlog.info("Case 1: Amp data has single peaks and Phase data has single dips")

            # Find power point with maximum SNR from Amp data
            amp_best_idx = np.argmax(snr_values_amp)
            amp_best_power = valid_powers_amp[amp_best_idx]
            amp_best_snr = snr_values_amp[amp_best_idx]
            amp_best_freq = max(power_to_peak_freq_amp[amp_best_power])

            # Find power point with maximum SNR from Phase data
            phase_best_idx = np.argmax(snr_values_phase)
            phase_best_power = valid_powers_phase[phase_best_idx]
            phase_best_snr = snr_values_phase[phase_best_idx]
            phase_best_freq = max(power_to_peak_freq_phase[phase_best_power])

            # Compare two power points, choose the smaller one
            if amp_best_power <= phase_best_power:
                best_power = amp_best_power
                best_snr = amp_best_snr
                best_freq = amp_best_freq
                selection_basis = "Amp data point with maximum SNR (power lower than Phase)"
                pyqlog.info(f"  Selecting Amp data result (more conservative choice)")
            else:
                best_power = phase_best_power
                best_snr = phase_best_snr
                best_freq = phase_best_freq
                selection_basis = "Phase data point with maximum SNR (power lower than Amp)"
                pyqlog.info(f"  Selecting Phase data result (more conservative choice)")

        # Case 2: If only one data type has single features
        elif valid_powers_amp or valid_powers_phase:
            pyqlog.info("Case 2: Only one data type has single features")

            if valid_powers_amp:
                # Find power point with maximum SNR from Amp data
                amp_best_idx = np.argmax(snr_values_amp)
                best_power = valid_powers_amp[amp_best_idx]
                best_snr = snr_values_amp[amp_best_idx]
                best_freq = max(power_to_peak_freq_amp[best_power])
                selection_basis = "Only Amp data has single peaks"
                pyqlog.info(f"  Selecting power point with maximum SNR from Amp data: {best_power}dB, SNR={round(best_snr, 3)}, Frequency={round(best_freq, 2)}MHz")
            else:
                # Find power point with maximum SNR from Phase data
                phase_best_idx = np.argmax(snr_values_phase)
                best_power = valid_powers_phase[phase_best_idx]
                best_snr = snr_values_phase[phase_best_idx]
                best_freq = max(power_to_peak_freq_phase[best_power])
                selection_basis = "Only Phase data has single dips"
                pyqlog.info(f"  Selecting power point with maximum SNR from Phase data: {best_power}dB, SNR={round(best_snr, 3)}, Frequency={round(best_freq, 2)}MHz")

        # Cases 3 and 4: Check for double peaks or double dips
        else:
            pyqlog.info("Cases 3 and 4: Check for double peaks or double dips")

            # Store power points with double features and their frequencies, SNRs
            dual_feature_powers_amp = []
            dual_feature_freqs_amp = {}
            dual_feature_snrs_amp = []

            dual_feature_powers_phase = []
            dual_feature_freqs_phase = {}
            dual_feature_snrs_phase = []

            # Check for double peaks in Amp data
            pyqlog.info("Checking for double peaks in Amp data:")
            for i, power in enumerate(drive_power_list):
                child_data = self.experiment_data.child_data(index=i)
                if child_data is None or "Amp" not in child_data.y_data:
                    continue

                x_data = child_data.x_data
                y_data = child_data.y_data["Amp"]
                y_normal = normalization(y_data)

                window_length = self.options.window_length
                freq_distance = self.options.freq_distance
                xval_distance = distance_to_point(freq_distance, x_data)

                key, features, properties = find_peaks_dips(
                    x_data,
                    y_normal,
                    window_length=window_length,
                    interpolation_num=3,
                    distance=float(xval_distance),
                    prominences=False
                )

                # If there are two features, record them
                if len(features) == 2:
                    feature_snrs = []
                    for j, feature in enumerate(features):
                        feature_height = properties["peak_heights"][j] if hasattr(properties["peak_heights"],
                                                                                  "__len__") else properties["peak_heights"]
                        feature_snr = self._calculate_peak_snr(feature_height, y_data, key)
                        feature_snrs.append(feature_snr)

                    # Record power point with double peaks, frequencies and SNR
                    dual_feature_powers_amp.append(power)
                    dual_feature_freqs_amp[power] = features.tolist() if isinstance(features, np.ndarray) else [features]
                    dual_feature_snrs_amp.append(max(feature_snrs))

                    pyqlog.info(f"  Power point {power}dB: Frequency={[round(f, 2) for f in dual_feature_freqs_amp[power]]}MHz, Max SNR={round(max(feature_snrs), 3)}")

            # Check for double dips in Phase data
            pyqlog.info("\nChecking for double dips in Phase data:")
            for i, power in enumerate(drive_power_list):
                child_data = self.experiment_data.child_data(index=i)
                if child_data is None or "Phase" not in child_data.y_data:
                    continue

                x_data = child_data.x_data
                y_data = child_data.y_data["Phase"]
                y_normal = normalization(y_data)

                window_length = self.options.window_length
                freq_distance = self.options.freq_distance
                xval_distance = distance_to_point(freq_distance, x_data)

                key, features, properties = find_peaks_dips(
                    x_data,
                    y_normal,
                    window_length=window_length,
                    interpolation_num=3,
                    distance=float(xval_distance),
                    prominences=False
                )

                # If peaks were found but we need dips, try finding dips
                if key == "peaks":
                    key, features, properties = find_peaks_dips(
                        x_data,
                        -y_normal,
                        window_length=window_length,
                        interpolation_num=3,
                        distance=float(xval_distance),
                        prominences=False
                    )
                    key = "dips"  # Force key to be dips (for Phase data)

                # If there are two features, record them
                if len(features) == 2:
                    feature_snrs = []
                    for j, feature in enumerate(features):
                        feature_height = properties["peak_heights"][j] if hasattr(properties["peak_heights"],
                                                                                  "__len__") else properties["peak_heights"]
                        feature_snr = self._calculate_peak_snr(feature_height, y_data, key)
                        feature_snrs.append(feature_snr)

                    # Record power point with double dips, frequencies and SNR
                    dual_feature_powers_phase.append(power)
                    dual_feature_freqs_phase[power] = features.tolist() if isinstance(features, np.ndarray) else [features]
                    dual_feature_snrs_phase.append(max(feature_snrs))

                    pyqlog.info(f"  Power point {power}dB: Frequency={[round(f, 2) for f in dual_feature_freqs_phase[power]]}MHz, Max SNR={round(max(feature_snrs), 3)}")

            # Handle Case 3: Double peaks or double dips exist
            if dual_feature_powers_amp or dual_feature_powers_phase:
                pyqlog.info("\nCase 3: Double peaks or double dips exist")

                # Find maximum frequency and corresponding power point from double peaks
                amp_best_freq = 0
                amp_best_power = None
                amp_best_snr = 0

                if dual_feature_powers_amp:
                    for power, freqs in dual_feature_freqs_amp.items():
                        max_freq = max(freqs)
                        if max_freq > amp_best_freq:
                            amp_best_freq = max_freq
                            amp_best_power = power
                            amp_best_snr = dual_feature_snrs_amp[dual_feature_powers_amp.index(power)]

                    pyqlog.info(f"  Amp data max frequency: {round(amp_best_freq, 2)}MHz, corresponding power point: {amp_best_power}dB, SNR: {round(amp_best_snr, 3)}")

                # Find maximum frequency and corresponding power point from double dips
                phase_best_freq = 0
                phase_best_power = None
                phase_best_snr = 0

                if dual_feature_powers_phase:
                    for power, freqs in dual_feature_freqs_phase.items():
                        max_freq = max(freqs)
                        if max_freq > phase_best_freq:
                            phase_best_freq = max_freq
                            phase_best_power = power
                            phase_best_snr = dual_feature_snrs_phase[dual_feature_powers_phase.index(power)]

                    pyqlog.info(f"  Phase data max frequency: {round(phase_best_freq, 2)}MHz, corresponding power point: {phase_best_power}dB, SNR: {round(phase_best_snr, 3)}")

                # Compare max frequencies from Amp and Phase, choose the one with higher frequency
                if amp_best_freq >= phase_best_freq and amp_best_power is not None:
                    best_power = amp_best_power
                    best_snr = amp_best_snr
                    best_freq = amp_best_freq
                    selection_basis = "Double peaks in Amp data with maximum frequency"
                    pyqlog.info(f"  Selecting Amp data result (higher frequency)")
                elif phase_best_power is not None:
                    best_power = phase_best_power
                    best_snr = phase_best_snr
                    best_freq = phase_best_freq
                    selection_basis = "Double dips in Phase data with maximum frequency"
                    pyqlog.info(f"  Selecting Phase data result (higher frequency)")

            # Case 4: Neither single features nor double features
            else:
                pyqlog.info("\nCase 4: Neither single features nor double features")
                self._quality = BaseQuality.instantiate(QualityDescribe.bad)

        # Final results
        if best_power is not None:
            best_snr = round(best_snr, 3)
            best_freq = round(best_freq, 3)

            pyqlog.info(f"[Best Drive Power]: {best_power}dB")
            pyqlog.info(f"[Best Frequency]: {best_freq}MHz")
            pyqlog.info(f"[SNR Value]: {best_snr}")
            pyqlog.info(f"[Selection Basis]: {selection_basis}")

            # Store best power, SNR, and frequency as instance variables
            self.results.best_power.value = best_power
            self.results.best_snr.value = best_snr
            self.results.freq.value = best_freq

            self._quality = BaseQuality.instantiate(QualityDescribe.normal)
        else:
            self._quality = BaseQuality.instantiate(QualityDescribe.bad)

    def _evaluate_quality(self) -> None:
        """
        Evaluate analysis quality.
        """
        if self._quality is None or self._quality.descriptor == QualityDescribe.empty:
            self._quality = BaseQuality.instantiate(QualityDescribe.normal)

    def _visualization(self):
        """
        Visualize analysis results by drawing vertical lines at the best power point.
        """
        super()._visualization()

        if hasattr(self.results, "best_power") and self.results.best_power.value:
            self.drawer.draw_axv_line(
                x=self.results.best_power.value,
                ax_index=0,
                c="r",
                linewidth=3,
            )

        if hasattr(self.results, "freq") and self.results.freq.value:
            self.drawer.draw_axh_line(
                y=self.results.freq.value,
                ax_index=1,
                c="red",
                linewidth=2,
                linestyle="dashed",
            )