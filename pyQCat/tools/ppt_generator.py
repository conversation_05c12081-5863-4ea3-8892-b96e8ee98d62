# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/24
# __author:       <PERSON><PERSON><PERSON>

from collections import defaultdict
from pptx import Presentation
from pptx.util import Pt, Inches
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR


def generate_ppt_template(data, output_file):
    """生成PPT模板"""
    # 按页分组数据
    page_pic_data = defaultdict(list)
    for item in data["ppt_data"]:
        page_pic_data[item["ppt_page"]].append(item)

    # 创建演示文稿
    prs = Presentation()
    prs.slide_width = Inches(13.333)
    prs.slide_height = Inches(7.5)
    slide_width = prs.slide_width
    slide_height = prs.slide_height

    # 创建数据表格幻灯片
    create_data_table_slide(prs, slide_width, slide_height, data["metadata"])

    # 创建图片幻灯片
    create_picture_slides(prs, page_pic_data, slide_width, slide_height)

    # 保存PPT
    prs.save(output_file)


def create_data_table_slide(prs, slide_width, slide_height, data_dict):
    """创建包含数据表格的幻灯片"""
    # 添加空白布局幻灯片
    slide = prs.slides.add_slide(prs.slide_layouts[6])

    # 表格参数
    MARGIN = Inches(0.5)
    ROW_HEIGHT = Inches(0.4)
    table_width = min(Inches(12), slide_width - 2 * MARGIN)
    page_data = list(data_dict.items())
    num_rows = len(page_data) + 1  # 表头 + 数据行

    # 计算表格位置和高度
    top = Inches(1.0)
    max_table_height = slide_height - top - Inches(1)
    table_height = min(ROW_HEIGHT * num_rows, max_table_height)
    left = (slide_width - table_width) / 2

    # 创建表格
    table_shape = slide.shapes.add_table(
        rows=num_rows,
        cols=2,
        left=left,
        top=top,
        width=table_width,
        height=table_height,
    )
    table = table_shape.table

    # 设置表头
    table.cell(0, 0).text = "标签"
    table.cell(0, 1).text = "值"
    style_header_cells(table)

    # 填充数据
    for row_idx, (key, value) in enumerate(page_data, start=1):
        table.cell(row_idx, 0).text = str(key)
        table.cell(row_idx, 1).text = str(value)

    # 美化表格
    style_table_cells(table, num_rows)
    table_shape.table.style = "Light Style 1"


def create_picture_slides(prs, page_pic_data, slide_width, slide_height):
    """创建包含图片的幻灯片"""
    MARGIN = Inches(0.5)
    SPACING = Inches(0.3)
    TITLE_HEIGHT = Inches(0.4)

    # 按页码排序并创建幻灯片
    for page_num, items in sorted(page_pic_data.items()):
        # 跳过空页
        if not items:
            continue

        # 按索引排序并创建新幻灯片
        items.sort(key=lambda x: x["index"])
        slide = prs.slides.add_slide(prs.slide_layouts[6])

        # 获取布局形状
        rows, cols = items[0].get("shape", [2, 2])  # 默认2x2布局

        # 计算内容区域尺寸
        content_width = slide_width - 2 * MARGIN
        content_height = slide_height - 2 * MARGIN

        # 计算单元格尺寸
        cell_width = (content_width - (cols - 1) * SPACING) / cols
        cell_height = (
            content_height - rows * TITLE_HEIGHT - (rows - 1) * SPACING
        ) / rows

        # 创建布局网格
        for row in range(rows):
            for col in range(cols):
                idx = row * cols + col
                if idx >= len(items):
                    break

                item = items[idx]
                left = MARGIN + col * (cell_width + SPACING)
                top_img = MARGIN + row * (cell_height + TITLE_HEIGHT + SPACING)

                # 添加图片或占位符
                if item.get("png"):
                    try:
                        picture_path = item["png"].replace(
                            "Z:", "\\\\172.16.1.249\\实验室文件"
                        )
                        # print(f"添加图片： {picture_path}")
                        slide.shapes.add_picture(
                            picture_path, left, top_img, cell_width, cell_height
                        )
                    except Exception as e:
                        # print(e)
                        create_placeholder(
                            slide,
                            left,
                            top_img,
                            cell_width,
                            cell_height,
                            f"无效图片: {item['png'][:10]}...",
                        )
                else:
                    create_placeholder(
                        slide, left, top_img, cell_width, cell_height, "未找到图片"
                    )

                # 添加标题
                text = f"{item['unit']} {item['exp_name']} {item['record_id']}\n{item['flow_name']}-{item['flow_id']}"

                top_title = top_img + cell_height
                title_box = slide.shapes.add_textbox(
                    left, top_title, cell_width, TITLE_HEIGHT
                )
                tf = title_box.text_frame
                tf.vertical_anchor = MSO_ANCHOR.MIDDLE
                # tf.margin_top = 0
                # tf.margin_bottom = 0
                # tf.margin_left = 0
                # tf.margin_right = 0
                p = tf.add_paragraph()
                p.text = text
                p.alignment = PP_ALIGN.LEFT
                p.font.size = Pt(7)
                p.font.bold = True


def style_header_cells(table):
    """设置表头单元格样式"""
    for col in range(2):
        cell = table.cell(0, col)
        tf = cell.text_frame
        tf.vertical_anchor = MSO_ANCHOR.MIDDLE

        p = tf.paragraphs[0]
        p.alignment = PP_ALIGN.CENTER
        p.font.bold = True
        p.font.size = Pt(14)
        p.font.color.rgb = RGBColor(255, 255, 255)
        p.font.name = "微软雅黑"

        cell.fill.solid()
        cell.fill.fore_color.rgb = RGBColor(59, 89, 152)


def style_table_cells(table, num_rows):
    """设置表格单元格样式"""
    for row in range(num_rows):
        for col in range(2):
            cell = table.cell(row, col)
            tf = cell.text_frame
            tf.vertical_anchor = MSO_ANCHOR.MIDDLE

            if not tf.paragraphs:
                p = tf.add_paragraph()
            else:
                p = tf.paragraphs[0]

            p.alignment = PP_ALIGN.CENTER
            p.font.size = Pt(11)
            p.font.name = "微软雅黑"

            # 设置斑马纹背景
            if row == 0:
                continue  # 表头已设置
            elif row % 2 == 1:
                cell.fill.solid()
                cell.fill.fore_color.rgb = RGBColor(240, 245, 255)
            else:
                cell.fill.solid()
                cell.fill.fore_color.rgb = RGBColor(255, 255, 255)


def create_placeholder(slide, left, top, width, height, text):
    """创建带文本的占位框"""
    textbox = slide.shapes.add_textbox(left, top, width, height)
    tf = textbox.text_frame
    tf.vertical_anchor = MSO_ANCHOR.MIDDLE  # 垂直居中

    p = tf.add_paragraph()
    p.text = text
    p.alignment = PP_ALIGN.CENTER
    p.font.size = Pt(14)

    # 设置边框和填充
    fill = textbox.fill
    fill.solid()
    fill.fore_color.rgb = RGBColor(240, 240, 240)

    line = textbox.line
    line.color.rgb = RGBColor(200, 200, 200)
    line.width = Pt(0.5)
