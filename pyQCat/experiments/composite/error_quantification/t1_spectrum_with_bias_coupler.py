# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/07
# __author:       KangKang Geng
# __corporation:  OriginQuantum

"""
T1WithBiasCoupler Spectrum Composite Experiment

This experiment systematically investigates the impact of coupler bias on qubit T1 time
by scanning different Z amplitudes and executing the T1WithBiasCoupler sub-experiment
for each Z amplitude.
"""

import numpy as np

from ....analysis.library import T1SpectrumAnalysis
from ....parameters import options_wrapper
from ....structures import MetaData, Options
from ....types import ExperimentRunMode
from ...composite_experiment import CompositeExperiment
from ...single.error_quantification import T1WithBiasCoupler


@options_wrapper
class T1SpectrumWithBiasCoupler(CompositeExperiment):
    """T1WithBiasCoupler spectrum experiment to study coupler bias effects on T1 decay."""

    _sub_experiment_class = T1WithBiasCoupler

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options

        Experiment options:
            z_amp_list (List, np.ndarray): Scan Z line amp list for target qubit.
            freq_range_map (dict): Calculate freq_list l_gap, r_gap, step value.
                                  Used for automatic frequency range generation.
        """
        options = super()._default_experiment_options()
        options.set_validator("freq_range_map", dict)
        options.set_validator("z_amp_list", list, limit_null=True)

        options.freq_range_map = {
            "l": 200,
            "r": 20,
            "s": 10,
        }
        options.z_amp_list = []
        options.run_mode = ExperimentRunMode.async_mode

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default analysis options for T1SpectrumWithBiasCoupler experiment.

        Options:
            freq_list (List, np.ndarray): The frequency calculated
                from ac spectrum paras and z amp.
                If no ac spectrum paras, this is None.
            r_square_threshold (float): To extract abnormal points,
                set threshold of the analysis quality's r_square.
            rate_threshold (float): To extract abnormal points,
                set threshold of the analysis results' rate.
        """
        options = super()._default_analysis_options()

        options.set_validator("r_square_threshold", (0, 1, 2))
        options.set_validator("rate_threshold", (0, 1, 2))

        options.freq_list = None
        options.r_square_threshold = 0.7
        options.rate_threshold = 0.38
        options.data_key = ["Pacify"]
        options.figsize = (18, 8)

        options.ymax = 40
        options.set_validator("ymax", float)

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method.

        Options:
            tau_list (List): List of t1 value, which is the analysis result of T1WithBiasCoupler experiment.
            freq_list (List, array): The frequency calculated from
                ac spectrum paras and z amp.
                If no ac spectrum paras, this is None.
        """
        options = super()._default_run_options()
        options.tau_list = []
        options.r_square_list = []
        options.rate_list = []
        options.freq_list = []

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()

        return metadata

    def _check_options(self):
        """Check options and setup run options."""
        super()._check_options()

        self.set_run_options(
            x_data=np.array(self.experiment_options.z_amp_list),
            analysis_class=T1SpectrumAnalysis,
        )

    def _setup_child_experiment(self, exp: T1WithBiasCoupler, idx: int, z_amp: float):
        """Set up the child experiment.

        Args:
            exp: Child experiment object.
            idx: Index of the child experiment.
            z_amp: Z pulse amplitude for the target qubit.
        """
        x_data = self.run_options.x_data
        freq_list = self.experiment_options.freq_list

        describe = f"z_amp={np.round(z_amp, 5)}"
        if freq_list:
            describe += f" freq={freq_list[idx]}"

        exp.set_parent_file(self, describe, idx, len(x_data))
        exp.set_experiment_options(
            z_amp=z_amp,
        )
        self._check_simulator_data(exp, idx)

    def _handle_child_result(self, exp: T1WithBiasCoupler):
        """Handle the child experiment results.

        Args:
            exp: Child experiment object.
        """
        provide_field = self.analysis_options.data_key[0]

        tau = exp.analysis.results.tau.value
        rate = exp.analysis.results.rate.value
        r_square = exp.analysis.quality.value

        exp.analysis.provide_for_parent.update({provide_field: [tau, rate, r_square]})

    def _alone_save_result(self):
        """Save specific result data."""
        pacify = self.experiment_data.y_data.get("Pacify")
        x_data = self.experiment_data.x_data

        if self.experiment_data.x_data2:
            x_data2 = np.array(self.experiment_data.x_data2)
            data = np.hstack(
                (
                    x_data.reshape(len(x_data), 1),
                    x_data2.reshape(len(x_data), 1),
                    pacify,
                )
            )
        else:
            data = np.hstack((x_data.reshape(len(x_data), 1), pacify))

        self.file.save_data(data.T, name=f"{self}(x_tau_rate_r2)")
