# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/08/11
# __author:       <PERSON><PERSON>ang Geng
# __description:  Enhanced error handling for quantum experiments


import re
import traceback
from typing import Dict, List, Optional, Tuple, TYPE_CHECKING

from ..errors import (
    PyQCatError,
    ParallelAcqError,
    ExperimentError,
    ExperimentOptionsError,
    AnalysisError,
    InstrumentError,
    CourierError,
    AcquisitionDataTypeError,
    ExperimentFlowError,
    ExperimentParallelError,
    DatabaseQueryError,
    ConfigError,
    UnCollectionError,
    ExperimentContextError,
    BatchExperimentError,
    BuildExperimentError,
    CompilerExperimentError,
    AcquisitionTaskStateError,
    AcquisitionDataTackleError,
)
from ..log import pyqlog
from ..types import ExperimentDocStatus

if TYPE_CHECKING:
    from .top_experiment_v1 import TopExperimentV1 as TopExperiment


class UserFriendlyMessage:
    """用户友好的错误消息类"""

    def __init__(
        self,
        title: str,
        description: str,
        suggestions: List[str],
        technical_details: Optional[str] = None,
        context: Optional[Dict] = None,
    ):
        self.title = title
        self.description = description
        self.suggestions = suggestions
        self.technical_details = technical_details
        self.context = context or {}

    def format_message(self) -> str:
        message_parts = [f" {self.title}", "", f" 问题描述：{self.description}", ""]

        if self.suggestions:
            message_parts.append(" 建议解决方案：")
            for i, suggestion in enumerate(self.suggestions, 1):
                message_parts.append(f"   {i}. {suggestion}")
            message_parts.append("")

        if self.context:
            message_parts.append(" 相关信息：")
            for key, value in self.context.items():
                message_parts.append(f"   • {key}: {value}")
            message_parts.append("")

        if self.technical_details:
            message_parts.extend(
                [" 问题详情（供开发人员参考）：", f"   {self.technical_details}", ""]
            )

        return "\n".join(message_parts)


class ExperimentErrorHandler:

    ERROR_MAPPINGS = {
        # 实验配置错误
        ExperimentOptionsError: {
            "title": "实验参数配置错误",
            "description": "实验的参数设置不正确或不符合要求",
            "suggestions": [
                "检查实验参数是否在有效范围内",
                "确认参数类型是否正确（如数值、列表等）",
                "参考实验文档中的参数说明",
                "使用默认参数进行测试",
            ],
        },
        # 分析错误
        AnalysisError: {
            "title": "数据分析处理错误",
            "description": "实验数据分析过程中出现问题",
            "suggestions": [
                "检查实验数据是否完整",
                "确认分析参数设置是否正确",
                "尝试使用不同的分析方法",
                "检查数据质量是否满足分析要求",
            ],
        },
        # 仪器错误
        InstrumentError: {
            "title": "仪器设备错误",
            "description": "量子设备或测量仪器出现异常",
            "suggestions": [
                "检查设备连接状态",
                "确认设备参数配置是否正确",
                "重启相关设备",
                "联系设备维护人员",
            ],
        },
        # 并行实验错误
        ParallelAcqError: {
            "title": "并行实验执行错误",
            "description": "多个实验并行执行时出现问题",
            "suggestions": [
                "减少并行实验数量",
                "检查量子比特资源分配",
                "确认实验间是否存在冲突",
                "尝试串行执行实验",
            ],
        },
        # 数据库错误
        DatabaseQueryError: {
            "title": "数据库查询错误",
            "description": "访问实验数据库时出现问题",
            "suggestions": [
                "检查数据库连接状态",
                "确认查询条件是否正确",
                "稍后重试操作",
                "联系系统管理员",
            ],
        },
        # 编译错误
        CompilerExperimentError: {
            "title": "实验程序编译错误",
            "description": "量子实验程序编译过程中出现错误",
            "suggestions": [
                "检查量子门序列是否正确",
                "确认量子比特连接关系",
                "简化实验电路复杂度",
                "检查脉冲参数设置",
            ],
        },
        # 配置错误
        ConfigError: {
            "title": "系统配置错误",
            "description": "系统配置文件或环境设置有问题",
            "suggestions": [
                "检查配置文件格式",
                "确认环境变量设置",
                "重置为默认配置",
                "联系技术支持",
            ],
        },
        # 实验流程错误
        ExperimentFlowError: {
            "title": "实验流程执行错误",
            "description": "实验执行流程中的步骤出现问题",
            "suggestions": [
                "检查实验步骤的逻辑顺序",
                "确认前置条件是否满足",
                "重新初始化实验环境",
                "简化实验流程进行测试",
            ],
        },
        # 数据采集错误
        AcquisitionDataTypeError: {
            "title": "数据采集类型错误",
            "description": "数据采集模式设置不正确",
            "suggestions": [
                "检查数据类型设置（amp_phase、I_Q、track）",
                "确认采集模式与实验类型匹配",
                "参考文档中的数据类型说明",
                "使用默认采集模式进行测试",
            ],
        },
        # 任务状态错误
        AcquisitionTaskStateError: {
            "title": "任务状态异常",
            "description": "实验任务状态不正确或状态转换失败",
            "suggestions": [
                "等待当前任务完成后重试",
                "检查任务队列状态",
                "重新提交实验任务",
                "联系系统管理员检查任务调度器",
            ],
        },
        # 数据处理错误
        AcquisitionDataTackleError: {
            "title": "数据处理错误",
            "description": "实验数据处理过程中出现异常",
            "suggestions": [
                "检查原始数据完整性",
                "确认数据处理参数设置",
                "尝试重新处理数据",
                "检查数据格式是否正确",
            ],
        },
        # 批量实验错误
        BatchExperimentError: {
            "title": "批量实验执行错误",
            "description": "批量执行多个实验时出现问题",
            "suggestions": [
                "减少批量实验的数量",
                "检查实验间的依赖关系",
                "逐个执行实验进行排查",
                "确认系统资源是否充足",
            ],
        },
        # 实验上下文错误
        ExperimentContextError: {
            "title": "实验环境上下文错误",
            "description": "实验执行环境或上下文配置有问题",
            "suggestions": [
                "重新初始化实验环境",
                "检查环境变量和路径设置",
                "确认依赖组件是否正常",
                "重启实验服务",
            ],
        },
        # 未收集错误
        UnCollectionError: {
            "title": "未预期的系统错误",
            "description": "系统出现了未被正确分类的错误",
            "suggestions": [
                "记录错误详情并联系开发团队",
                "尝试重新启动相关服务",
                "检查系统日志获取更多信息",
                "使用备用方案继续工作",
            ],
        },
    }

    @classmethod
    def create_user_friendly_message(
        cls, error: Exception, exp: "TopExperiment" = None
    ) -> UserFriendlyMessage:

        error_type = type(error)
        error_str = str(error)

        base_mapping = cls.ERROR_MAPPINGS.get(
            error_type,
            {
                "title": "实验执行错误",
                "description": "实验执行过程中出现未预期的问题",
                "suggestions": ["检查实验参数设置", "重新运行实验", "联系技术支持"],
            },
        )

        context = {}
        if exp:
            context["实验名称"] = getattr(exp, "label", "未知")
            context["实验ID"] = getattr(exp, "id", "未知")
            if hasattr(exp, "run_options") and hasattr(exp.run_options, "tq"):
                context["目标量子比特"] = str(exp.run_options.tq)

        enhanced_description, enhanced_suggestions = cls._enhance_error_details(
            error, error_str, exp
        )

        return UserFriendlyMessage(
            title=base_mapping["title"],
            description=enhanced_description or base_mapping["description"],
            suggestions=enhanced_suggestions or base_mapping["suggestions"],
            technical_details=error_str,
            context=context,
        )

    @classmethod
    def _enhance_error_details(
        cls, error: Exception, error_str: str, exp: "TopExperiment" = None
    ) -> Tuple[Optional[str], Optional[List[str]]]:
        """根据具体错误内容增强描述"""
        description = None
        suggestions = None

        # 参数范围错误
        if "not in" in error_str or "invalid" in error_str.lower():
            description = "参数值超出允许范围或格式不正确"
            suggestions = [
                "检查参数是否在文档规定的范围内",
                "确认参数数据类型是否正确",
                "参考示例代码中的参数设置",
            ]

        # 连接超时错误
        elif "timeout" in error_str.lower() or "connection" in error_str.lower():
            description = "设备连接超时或网络通信异常"
            suggestions = [
                "检查设备网络连接",
                "增加超时时间设置",
                "重启网络设备",
                "检查防火墙设置",
            ]

        # 数据格式错误
        elif "format" in error_str.lower() or "parse" in error_str.lower():
            description = "数据格式不正确或解析失败"
            suggestions = [
                "检查输入数据格式",
                "确认文件编码格式",
                "验证数据完整性",
                "使用标准格式重新保存数据",
            ]

        # 资源不足错误
        elif "memory" in error_str.lower() or "resource" in error_str.lower():
            description = "系统资源不足或资源分配冲突"
            suggestions = [
                "减少实验规模或复杂度",
                "释放不必要的系统资源",
                "等待其他任务完成后重试",
                "联系管理员增加系统资源",
            ]

        # 权限错误
        elif "permission" in error_str.lower() or "access" in error_str.lower():
            description = "访问权限不足或文件被占用"
            suggestions = [
                "检查文件访问权限",
                "确认用户权限设置",
                "关闭占用文件的其他程序",
                "以管理员权限运行",
            ]

        # 量子比特相关错误
        elif "qubit" in error_str.lower() or "bit" in error_str.lower():
            description = "量子比特配置或操作出现问题"
            suggestions = [
                "检查量子比特编号是否正确",
                "确认量子比特是否可用",
                "检查量子比特连接关系",
                "验证量子比特参数设置",
            ]

        # 脉冲相关错误
        elif "pulse" in error_str.lower() or "wave" in error_str.lower():
            description = "脉冲波形或参数设置有问题"
            suggestions = [
                "检查脉冲幅度是否在有效范围内",
                "确认脉冲时长设置",
                "验证脉冲波形参数",
                "使用默认脉冲参数进行测试",
            ]

        # 频率相关错误
        elif "frequency" in error_str.lower() or "freq" in error_str.lower():
            description = "频率设置超出范围或不匹配"
            suggestions = [
                "检查频率是否在设备支持范围内",
                "确认频率单位设置正确",
                "验证频率与量子比特匹配",
                "参考设备规格说明",
            ]

        # 校准相关错误
        elif "calibration" in error_str.lower() or "calib" in error_str.lower():
            description = "设备校准数据缺失或过期"
            suggestions = [
                "重新执行设备校准",
                "检查校准数据有效期",
                "确认校准参数完整性",
                "联系设备维护人员",
            ]

        # 编译相关错误
        elif "compile" in error_str.lower() or "build" in error_str.lower():
            description = "量子程序编译过程出现错误"
            suggestions = [
                "检查量子门序列语法",
                "简化量子电路复杂度",
                "确认量子门参数正确",
                "验证量子比特拓扑结构",
            ]

        # 批量实验参数错误
        elif "physical_units" in error_str.lower() and (
            "缺失" in error_str or "missing" in error_str.lower()
        ):
            description = "批量实验缺少必需的物理单元参数"
            suggestions = [
                "在 batch.set_experiment_options() 中添加 physical_units 参数",
                "例如: physical_units=['q0', 'q1', 'q2']",
                "确保量子比特名称与系统配置一致",
                "检查 JSON 配置文件中的参数设置",
            ]

        # 实验流程错误
        elif "flows" in error_str.lower() and (
            "缺失" in error_str or "missing" in error_str.lower()
        ):
            description = "批量实验缺少必需的实验流程参数"
            suggestions = [
                "在 batch.set_experiment_options() 中添加 flows 参数",
                "例如: flows=['RabiScanAmp', 'T1']",
                "确保实验名称在系统中已定义",
                "参考实验库文档获取可用的实验名称",
            ]

        # JSON 配置文件错误
        elif "json" in error_str.lower() and (
            "parse" in error_str.lower() or "format" in error_str.lower()
        ):
            description = "JSON 配置文件格式错误或解析失败"
            suggestions = [
                "检查 JSON 文件语法是否正确",
                "验证所有括号和引号是否匹配",
                "使用 JSON 验证工具检查文件格式",
                "参考示例配置文件进行修正",
            ]

        return description, suggestions

    @classmethod
    def handle_experiment_error(
        cls, error: Exception, exp: "TopExperiment" = None
    ) -> str:
        """处理实验错误并返回用户友好的消息"""
        try:
            user_message = cls.create_user_friendly_message(error, exp)
            return user_message.format_message()
        except Exception as handler_error:
            # 如果错误处理器本身出错，返回基础错误信息
            pyqlog.error(f"错误处理器异常: {handler_error}")
            return f" 实验执行错误\n\n 问题描述：{str(error)}\n\n 建议：请联系技术支持获取帮助"
