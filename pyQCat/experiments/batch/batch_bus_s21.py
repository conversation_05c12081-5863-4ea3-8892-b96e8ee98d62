# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/10
# __author:       <PERSON><PERSON><PERSON>

"""
节点作用：
1. 获取全部 BUS 的 4-8.5 GHz 高功率 S21 数据
2. 获取全部 BUS 的腔频范围高（-15dBm) 低（-40dBm) 功率S21数据
3. 期望的测试结果详见: BUS S21

节点流程：

1. 使用拨码开关控制网分作用的 BUS
2. 使用网络分析仪获取 S21 信号，截图保存，并保存 S2P 文件
3. 每根 BUS 重复上述流程
"""

import matplotlib.pyplot as plt

from pyQCat.experiments.batch_experiment import (
    BatchExperiment,
    BatchPhysicalUnitType,
    Path,
)
from pyQCat.instrument.INSTRUMENT import INSTRUMENT


class BatchBusS21(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.micro_switch = "MicroSwitch"
        options.flows = ["BusS21Collector"]
        options.physical_unit_type = BatchPhysicalUnitType.BUS
        options.affect_next_node = False
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.micro_switch = None
        options.s21_data = {}
        options.cavity_scope = {}
        options.ppt_template.update(
            dict(
                batch_pic_count=1,
                batch_shape=(1, 1),
                shape=(2, 3),
                split_by_unit=False,
            )
        )
        return options

    def _check_options(self):
        self.run_options.micro_switch = INSTRUMENT(
            self.context_manager.config.system.config_path
        )[self.experiment_options.micro_switch]
        return super()._check_options()

    def _batch_down(self):
        super()._batch_down()
        save_path = self._plot_s21()
        self.record_meta.execute_meta.result.origin_png_results = [save_path]

    def _plot_s21(self):
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle("All BUS S21", fontsize=16)
        color_cycle = plt.rcParams["axes.prop_cycle"].by_key()["color"][:8]
        style_cycle = ["-", "--", "-."]
        i = 0
        for k, v in self.run_options.s21_data.items():
            ax1.plot(
                v["hp_wide"]["freq"],
                v["hp_wide"]["amp"],
                label=k,
                color=color_cycle[i % 8],
                linestyle=style_cycle[i // 8],
            )
            ax2.plot(
                v["hp_small"]["freq"],
                v["hp_small"]["amp"],
                label=k,
                color=color_cycle[i % 8],
                linestyle=style_cycle[i // 8],
            )
            i += 1

        ax1.set_xlabel("Frequency (MHz)")
        ax1.set_ylabel("Amp")
        ax1.legend()
        ax2.set_xlabel("Frequency (MHz)")
        ax2.set_ylabel("Amp")
        ax2.legend()

        save_path = str(Path(Path(self.run_options.record_path).parent, "s21.png"))
        fig.savefig(save_path)
        plt.tight_layout()
        plt.close()
        return save_path

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        for unit in record.pass_units:
            self.run_options.cavity_scope[unit.split("-")[-1]] = record[
                "analysis_data"
            ][unit]["result"]["cavity_scope"]

        experiment_data = exp.experiment_data
        self.run_options.s21_data[physical_units[0]] = dict(
            hp_wide=dict(
                freq=experiment_data.replace_x_data.get("hp_wide_amp"),
                amp=experiment_data.y_data.get("hp_wide_amp"),
            ),
            hp_small=dict(
                freq=experiment_data.replace_x_data.get("hp_small_amp"),
                amp=experiment_data.y_data.get("hp_small_amp"),
            ),
        )

        return record

    def _run_batch(self):
        pass_units = []
        for bus in self.experiment_options.physical_units:
            bus_num = int(bus.split('-')[-1])
            self.run_options.micro_switch.open_bus(bus_num)
            self.change_regular_exec_exp_options(
                self.experiment_options.flows[0], bus=bus_num
            )
            flow_pass_units = self._run_flow(
                flows=self.experiment_options.flows,
                physical_units=[bus],
                name=bus,
            )
            if flow_pass_units:
                pass_units.append(bus)
        self.record_meta.execute_meta.result.hot_data = self.run_options.cavity_scope
        self.bind_pass_units(pass_units)

    def collect_record_data(self):
        self.run_options.micro_switch.close_sw()
        super().collect_record_data()

    def bind_process_data(self, process_data):
        process_data["BatchCavityQCheck"] = dict(
            run_options=dict(cavity_scope=self.run_options.cavity_scope)
        )
