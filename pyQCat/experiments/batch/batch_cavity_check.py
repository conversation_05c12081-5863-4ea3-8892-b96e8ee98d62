# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/31
# __author:       <PERSON><PERSON><PERSON>

from collections import defaultdict

import numpy as np

from pyQCat.experiments.batch_experiment import BatchExperiment


class BatchCavityCheck(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.flows = ["CavityTunable"]
        options.affect_next_node = False
        options.cavity_scope = [6800, 7300]
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.cavity_scope = {}
        options.fail_units = None
        options.ppt_template.update(
            dict(
                shape=(2, 3),
                split_by_unit=False,
            )
        )
        return options

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)
        return record

    def _extract_execute_context(self):
        # 统计各 bus 比特数
        bus_units_map = defaultdict(list)
        max_loop = 0
        for unit in self.experiment_options.physical_units:
            qubit = self.backend.chip_data.cache_qubit.get(unit)
            bus_units_map[qubit.inst.bus].append(unit)
            max_loop = max(bus_units_map[qubit.inst.bus], max_loop)

        # 计算 fc 扫描范围
        left, right = None, None
        for bus, cavity_scope in self.run_options.cavity_scope.items():
            if not left and not right:
                left, right = cavity_scope
            else:
                left = min(left, cavity_scope[0])
                right = max(right, cavity_scope[1])
        if not left or not right:
            left, right = self.experiment_options.cavity_scope
        self.change_regular_exec_exp_options(
            exp_name=self.experiment_options.flows[0],
            fc_list=list(np.round(np.linspace(left, right, 500), 3)),
        )

        return bus_units_map, max_loop

    def _run_batch(self):
        if self.run_options.fail_units:
            self.experiment_options.physical_units = self.run_options.fail_units
        bus_units_map, max_loop = self._extract_execute_context()
        for idx in range(max_loop):
            cur_units = []
            for bus, units in bus_units_map.items():
                if idx < len(units):
                    unit = units[idx]
                    cur_units.append(unit)
            self._run_flow(
                flows=self.experiment_options.flows,
                physical_units=cur_units,
                name=f"Group-{idx + 1}/{max_loop}",
            )

    def _batch_down(self):
        return super()._batch_down()

    def collect_record_data(self):
        super().collect_record_data()
