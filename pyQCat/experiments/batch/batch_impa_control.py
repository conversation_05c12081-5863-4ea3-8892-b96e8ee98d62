# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/01
# __author:       <PERSON><PERSON><PERSON>

"""
IMPA 一键打开、关闭

- 确保 config_path 中存在配置文件 config.ini(定义网分、拨码开关、微波源等商用仪器)
- 确保 config_path 中存在配置文件 impa_params.json(定义各 BUS IMPA 增益参数)
- 确保 config_path 中存在配置文件 impa.json(定义各 BUS 绑定的 DC 源、微波源地址)
"""

import json
from pyQCat.experiments.batch_experiment import (
    BatchExperiment,
    BatchPhysicalUnitType,
    pyqlog,
)
from pyQCat.instrument.INSTRUMENT import INSTRUMENT
from pathlib import Path
from pyQCat.errors import ConfigError


class BatchIMPAControl(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.set_validator("mode", ["open", "close"])
        options.micro_switch = "MicroSwitch"
        options.flows = ["ImpaGain"]
        options.physical_unit_type = BatchPhysicalUnitType.BUS
        options.mode = "open"
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.micro_switch = None
        options.impa_params = None
        options.ppt_template.update(
            dict(
                shape=(2, 3),
                split_by_unit=False,
            )
        )
        return options

    def _check_options(self):
        self.run_options.micro_switch = INSTRUMENT(self.context_manager.config.system.config_path)[
            self.experiment_options.micro_switch
        ]
        return super()._check_options()

    def _run_batch(self):
        try:
            impa_params_path = Path(self.context_manager.config.system.config_path, "impa_params.json")
            with open(str(impa_params_path), mode="r", encoding="utf-8") as fp:
                data = json.load(fp)
        except Exception:
            raise ConfigError(f"Con't load impa parameters {impa_params_path}")

        self.run_options.impa_params = {k.upper(): v for k, v in data.items()}

        self.change_regular_exec_exp_options(
            self.experiment_options.flows[0], mode=self.experiment_options.mode
        )

        all_pass_bus = []

        for bus in self.experiment_options.physical_units:
            bus_num = int(bus.split('-')[-1])
            ffp_list = self.run_options.impa_params.get(bus)
            if not ffp_list:
                pyqlog.warning(f"No find {bus} impa params!")
                continue

            count = 0
            while bus and count < 3:
                self.run_options.micro_switch.open_bus(bus_num)
                self.change_regular_exec_exp_options(
                    self.experiment_options.flows[0],
                    bus=bus_num,
                    ffp_list=ffp_list,
                )
                pass_bus = self._run_flow(
                    flows=self.experiment_options.flows,
                    physical_units=[bus],
                    name=bus,
                )
                if pass_bus:
                    pyqlog.info(
                        f"{bus} {self.experiment_options.mode} Count-{count} suc!"
                    )
                    all_pass_bus.extend(pass_bus)
                    break
                count += 1
                pyqlog.warning(
                    f"{bus} {self.experiment_options.mode} Count-{count} fail, retry!"
                )

        self.bind_pass_units(all_pass_bus)

    def collect_record_data(self):
        self.run_options.micro_switch.close_sw()
        super().collect_record_data()
