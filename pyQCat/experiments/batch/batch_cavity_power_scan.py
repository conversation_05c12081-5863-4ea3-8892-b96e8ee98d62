# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/17
# __author:       <PERSON><PERSON><PERSON>

import numpy as np

from pyQCat.experiments.batch_experiment import BatchExperiment, pyqlog


class BatchCavityPowerScan(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.set_validator("point_mode", ["zero", "max"])
        options.flows = ["CavityFreqSpectrum", "CavityPowerScan"]
        options.point_mode = "zero"
        options.affect_next_node = False
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.qubit_idle_map = {}
        options.best_power_map = {}
        options.ppt_template.update(
            dict(
                shape=(2, 3),
                split_by_unit=False,
            )
        )
        return options

    def _set_idle_point(self):
        if self.experiment_options.point_mode == "zero":
            for qubit in self.backend.chip_data.cache_qubit.values():
                qubit.idle_point = -qubit.dc_max
                qubit.dc_min = 0
            for coupler in self.backend.chip_data.cache_coupler.values():
                coupler.dc_max = 0
        else:
            for qubit in self.backend.chip_data.cache_qubit.values():
                qubit.idle_point = 0
                qubit.dc_min = qubit.dc_max

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "CavityPowerScan" == exp_name:
            for unit in record.analysis_data.keys():
                if unit in record.pass_units:
                    probe_power = (
                        record.analysis_data.get(unit).get("result").get("power")
                    )
                    self.run_options.best_power_map[unit] = float(probe_power)

        return record

    def _set_probe_power(self):
        if not self.run_options.best_power_map:
            pyqlog.warning("No find any best drive power!")
            return

        for unit in self.experiment_options.physical_units:
            qubit = self.backend.chip_data.cache_qubit.get(unit)
            power = self.run_options.best_power_map.get(unit)
            distance = 0
            while not power and distance < 5:
                neighbor_bits = self.backend.chip_data.get_same_bus_bit(unit, distance)
                find_powers = []
                for nb in neighbor_bits:
                    cur_power = self.run_options.best_power_map.get(nb)
                    if cur_power:
                        find_powers.append(cur_power)
                    if find_powers:
                        power = np.mean(find_powers, dtype=int)
                distance += 1
            if power:
                qubit.probe_power = power
                pyqlog.info(
                    f"Set {unit} probe power to {power}dB, base distance {distance}"
                )

    def _run_batch(self):
        self.set_global_pulse_period(10)
        pass_units = self._run_flow(
            self.experiment_options.flows,
            self.experiment_options.physical_units,
            name=f"PowerScan-{self.experiment_options.point_mode}",
        )
        self._set_probe_power()
        all_qubits = [qubit.name for qubit in self.backend.chip_data.cache_qubit.values() if qubit.goodness is True]
        self.readout_amp_allocator(all_qubits)
        self.bind_pass_units(pass_units)
        if self.experiment_options.save_db:
            self.backend.save_chip_data_to_db(all_qubits)
