# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/17
# __author:       <PERSON><PERSON><PERSON>


"""
流程作用
将低功率腔频更新至各个比特
将高低功率的下 q_int、q_ext 保存到各自的 point_label 中

流程描述
创建低功率 point_label (lp), 并切换；
使用拨码开关控制网分作用的 BUS
使用 FindBusCavityFreq 进行长范围粗扫，采集到 6 个腔后，按照 chip_line_connect.json 中的 bus_cavity_freq_sort_temp 配置的映射关系更新每个比特的腔频；
使用 FindBusCavityFreq 的 segm_scan 开启分段扫描，并将 analysis 中 fit_q 设置为True, 计算出每个比特的 q_int、q_ext, 更新数据库
每根 BUS 重复上述流程
创建高功率 point_label (hb), 并切换，重复上述 2 ~ 5 步
"""

import numpy as np

from pyQCat.experiments.batch_experiment import (
    BatchExperiment,
    BatchPhysicalUnitType,
    List,
)
from pyQCat.instrument.INSTRUMENT import INSTRUMENT
from pyQCat.invoker import Invoker


class BatchCavityQ(BatchExperiment):
    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.micro_switch = "MicroSwitch"
        options.flows = ["FindBusCavityFreq_rough", "FindBusCavityFreq_segma"]
        options.physical_unit_type = BatchPhysicalUnitType.BUS
        options.low_point_label = "lp"
        options.high_point_label = "hp"
        options.high_net_power = -15
        options.low_net_power = -45
        options.affect_next_node = False
        options.quality_block_exp = ["FindBusCavityFreq_segma"]
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.micro_switch = None
        options.init_point_label = None
        options.cur_point_label = None
        options.records = {}
        options.ppt_template.update(
            dict(
                shape=(2, 4),
                split_by_unit=True,
            )
        )
        options.bus_qubits_map = {}
        return options

    def _record_experiment(
        self, exp_name: str, exp, physical_units, err: Exception = None
    ):
        record = super()._record_experiment(exp_name, exp, physical_units, err)

        if "FindBusCavityFreq_segma" == exp_name:
            for unit in record.analysis_data.keys():
                pass_bits = (
                    record.analysis_data.get(unit).get("result").get("pass_bits")
                )
                fail_bits = (
                    record.analysis_data.get(unit).get("result").get("fail_bits")
                )
                if pass_bits == "None":
                    pass_bits = None
                if fail_bits == "None":
                    fail_bits = None
                if pass_bits:
                    self.run_options.records[self.run_options.cur_point_label][
                        "pass_bits"
                    ].extend(pass_bits)
                    self.run_options.records[self.run_options.cur_point_label][
                        "fail_bits"
                    ].extend(fail_bits)

        return record

    def _check_options(self):
        env = Invoker.get_env()
        self.run_options.init_point_label = env.point_label
        self.run_options.micro_switch = INSTRUMENT(
            self.context_manager.config.system.config_path
        )[self.experiment_options.micro_switch]
        self.run_options.records = {
            self.experiment_options.low_point_label: dict(pass_bits=[], fail_bits=[]),
            self.experiment_options.high_point_label: dict(pass_bits=[], fail_bits=[]),
        }
        for unit in self.experiment_options.physical_units:
            self.run_options.bus_qubits_map[unit] = self.backend.chip_data.get_qubits_from_bus(unit)

        super()._check_options()

    def _change_point_label(self, point_label: str):
        self.run_options.cur_point_label = point_label
        self.backend.change_point_label(point_label)

    def _get_qubits_from_bus(self, bus):
        goal_qubits = []
        for qubit in self.backend.chip_data.cache_qubit.values():
            if str(qubit.inst.bus) == str(bus):
                goal_qubits.append(qubit.name)
        return goal_qubits

    def _config_file_path(self, unit: str, exp):
        """Configure the parent directory for each experiment executed in the BatchExperiment.

        Args:
            unit (str): Physical working unit.
            exp (BaseExperiment) :
        """
        super()._config_file_path(
            f"{unit}-{self.run_options.cur_point_label}", exp
        )

    def _run_batch(self):
        for idx, point_label in enumerate(
            [
                self.experiment_options.low_point_label,
                self.experiment_options.high_point_label,
            ]
        ):
            cur_pass_units = []
            self._change_point_label(point_label)
            net_power = (
                self.experiment_options.low_net_power
                if idx == 0
                else self.experiment_options.high_net_power
            )
            for exp in self.experiment_options.flows:
                self.change_regular_exec_exp_options(exp, net_power=net_power)
            for bus in self.experiment_options.physical_units:
                bus_num = int(bus.split('-')[-1])
                for exp in self.experiment_options.flows:
                    self.change_regular_exec_exp_options(exp_name=exp, bus=bus_num)
                self.run_options.micro_switch.open_bus(bus_num)
                # qubits = self._get_qubits_from_bus(bus)
                # if not idx:
                #     self.run_options.all_qubits.extend(qubits)
                flow_pass_units = self._run_flow(
                    self.experiment_options.flows,
                    physical_units=bus,
                    name=f"PointLabel({point_label}) {bus}",
                )
                cur_pass_units.extend(flow_pass_units)
            if not idx:
                self.bind_pass_units(cur_pass_units)
            all_qubits = self.set_fail_qubit_probe_freq(cur_pass_units)
            self.readout_baseband_freq_allocator(all_qubits)
            if self.experiment_options.save_db is True:
                self.backend.save_chip_data_to_db(all_qubits)

        self._save_data_to_json(self.run_options.records, "state_details")
        self.record_meta.execute_meta.result.hot_data = self.run_options.records

    def _batch_down(self):
        self.backend._set_env()
        self.backend.refresh()
        return super()._batch_down()

    def collect_record_data(self):
        self.run_options.micro_switch.close_sw()
        super().collect_record_data()

    # def bind_pass_units(self, pass_units):
    #     self.record_meta.execute_meta.result.physical_units = (
    #         self.run_options.all_qubits
    #     )
    #     self.record_meta.execute_meta.result.pass_units = pass_units
    #     self.record_meta.execute_meta.result.sync()

    def bind_process_data(self, process_data):
        process_data["BatchCavityQCheck"] = dict(
            run_options=dict(fail_units=self.record_meta.execute_meta.result.fail_units)
        )

    def set_fail_qubit_probe_freq(self, pass_units: List[str]):

        all_qubits = []
        probe_freq_list = []
        for unit in pass_units:
            qubit_map = self.run_options.bus_qubits_map.get(unit)
            for bit, qubit in qubit_map.items():
                all_qubits.append(bit)
                probe_freq_list.append(qubit.probe_freq)

        if probe_freq_list:
            mean_probe_freq = round(np.mean(probe_freq_list), 3)
            fail_units = self.record_meta.execute_meta.result.fail_units

            for unit in fail_units:
                qubit_map = self.run_options.bus_qubits_map.get(unit)
                for bit, qubit in qubit_map.items():
                    all_qubits.append(bit)
                    qubit.probe_freq = mean_probe_freq
        return all_qubits
