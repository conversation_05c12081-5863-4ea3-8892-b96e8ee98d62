# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/01/XX
# __author:       AI Assistant
# __description:  Demo script for enhanced error handling

"""演示改进后的错误处理机制的示例脚本"""

from .error_handler import ExperimentErrorHandler
from ..errors import (
    ExperimentOptionsError,
    AnalysisError,
    InstrumentError,
    ParallelAcqError,
    DatabaseQueryError,
    CompilerExperimentError,
    AcquisitionDataTypeError,
    ExperimentFlowError,
)


class MockExperiment:
    """模拟实验对象用于演示"""

    def __init__(self, label="TestExperiment", exp_id="test_001"):
        self.label = label
        self.id = exp_id
        self.run_options = MockRunOptions()


class MockRunOptions:
    """模拟运行选项"""

    def __init__(self):
        self.tq = "Q0"


def demo_error_handling():
    """演示各种错误类型的处理效果"""

    # 创建模拟实验对象
    mock_exp = MockExperiment("T1实验", "t1_exp_001")

    print("=" * 80)
    print("量子实验框架错误处理机制演示")
    print("=" * 80)
    print()

    # 演示不同类型的错误处理
    error_cases = [
        {
            "title": "1. 实验参数配置错误",
            "error": ExperimentOptionsError(
                mock_exp.label,
                key="delays",
                value="invalid_value",
                msg="参数类型不匹配",
            ),
        },
        {
            "title": "2. 数据分析错误",
            "error": AnalysisError("拟合过程中数据点不足，无法完成曲线拟合"),
        },
        {
            "title": "3. 仪器设备错误",
            "error": InstrumentError("设备连接超时，无法建立通信"),
        },
        {
            "title": "4. 并行实验错误",
            "error": ParallelAcqError("量子比特资源冲突，Q0已被其他实验占用"),
        },
        {
            "title": "5. 数据库查询错误",
            "error": DatabaseQueryError("连接数据库超时，请检查网络连接"),
        },
        {
            "title": "6. 编译错误",
            "error": CompilerExperimentError(
                "量子门序列编译失败，存在不支持的量子门操作"
            ),
        },
        {
            "title": "7. 数据采集类型错误",
            "error": AcquisitionDataTypeError("invalid_type"),
        },
        {
            "title": "8. 实验流程错误",
            "error": ExperimentFlowError(mock_exp.label, "前置校准步骤未完成"),
        },
        {"title": "9. 通用系统错误", "error": ValueError("脉冲幅度超出范围 [-1, 1]")},
        {
            "title": "10. 量子比特相关错误",
            "error": RuntimeError("Qubit Q5 is not available for this experiment"),
        },
    ]

    for case in error_cases:
        print(f"{case['title']}")
        print("-" * 60)

        # 使用改进的错误处理器
        user_friendly_msg = ExperimentErrorHandler.handle_experiment_error(
            case["error"], mock_exp
        )

        print(user_friendly_msg)
        print()


def demo_before_after_comparison():
    """演示改进前后的对比效果"""

    mock_exp = MockExperiment("Rabi实验", "rabi_exp_002")
    error = ExperimentOptionsError(
        mock_exp.label,
        key="amps",
        value=[-2.0, 2.0],
        msg="amplitude values out of range",
    )

    print("=" * 80)
    print("错误处理改进前后对比")
    print("=" * 80)
    print()

    print("🔴 改进前的错误信息：")
    print("-" * 40)
    old_msg = str(error)
    print(old_msg)
    print()

    print("🟢 改进后的错误信息：")
    print("-" * 40)
    new_msg = ExperimentErrorHandler.handle_experiment_error(error, mock_exp)
    print(new_msg)
    print()


if __name__ == "__main__":
    # 运行演示
    demo_error_handling()
    demo_before_after_comparison()

    print("=" * 80)
    print("演示完成！")
    print("=" * 80)
    print()
    print("主要改进点：")
    print("1. 🎯 错误信息更加直观易懂")
    print("2. 💡 提供具体的解决建议")
    print("3. 📊 包含相关上下文信息")
    print("4. 🔧 保留技术详情供开发人员参考")
    print("5. 🚀 支持多种错误类型的智能识别")
