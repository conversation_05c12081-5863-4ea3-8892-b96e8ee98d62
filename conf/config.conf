[system]
sample = "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3"
env_name = "A2"
point_label = "P1"
invoker_addr = "tcp://***********:8088"
baseband_freq = 1050
m_baseband_freq = 1050
qaio_type = 72
save_type = "local"
local_root = "D:\\MonsterData"
log_path = "D:\\MonsterData\\logger"
config_path = "D:\\MonsterData\\conf"

[mongo]
inst_host = "127.0.0.1"
inst_port = 27017
inst_log = 27021

[minio]
s3_root = "***********:9000"
s3_access_key = "admin"
s3_secret_key = "bylz@2021"

[report]
is_report = False
id_type = "dag"
theme = None
system_theme = None
save_type = "pdf"
language = "cn"
report_detail = "detail"
file_path = "F:\\wangpeng\\code\\pyqcat-visage\\result-monster"

[run]
exp_save_mode = 0
simulator_data_path = "C:\\Users\\<USER>\\Downloads\\00.16.44\\00.16.44"
use_simulator = True
dag_save_mode = 1
use_backtrace = False
register_dag = False
simulator_delay = 0.0
