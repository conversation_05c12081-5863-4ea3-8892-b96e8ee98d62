{"shape": [12, 6], "sample": "chip_0307", "env_name": "D_0307", "QubitCount": 72, "CouplerCount": 126, "QubitParams": {"q0": {"bit": 0, "name": "q0", "xy_channel": 1, "z_dc_channel": 1, "z_flux_channel": 1, "readout_channel": 1, "_row": 0, "_col": 0}, "q1": {"bit": 1, "name": "q1", "xy_channel": 2, "z_dc_channel": 2, "z_flux_channel": 2, "readout_channel": 1, "_row": 0, "_col": 1}, "q2": {"bit": 2, "name": "q2", "xy_channel": 3, "z_dc_channel": 3, "z_flux_channel": 3, "readout_channel": 1, "_row": 0, "_col": 2}, "q3": {"bit": 3, "name": "q3", "xy_channel": 4, "z_dc_channel": 4, "z_flux_channel": 4, "readout_channel": 1, "_row": 0, "_col": 3}, "q4": {"bit": 4, "name": "q4", "xy_channel": 5, "z_dc_channel": 5, "z_flux_channel": 5, "readout_channel": 1, "_row": 0, "_col": 4}, "q5": {"bit": 5, "name": "q5", "xy_channel": 6, "z_dc_channel": 6, "z_flux_channel": 6, "readout_channel": 1, "_row": 0, "_col": 5}, "q6": {"bit": 6, "name": "q6", "xy_channel": 7, "z_dc_channel": 7, "z_flux_channel": 7, "readout_channel": 1, "_row": 1, "_col": 0}, "q7": {"bit": 7, "name": "q7", "xy_channel": 8, "z_dc_channel": 8, "z_flux_channel": 8, "readout_channel": 1, "_row": 1, "_col": 1}, "q8": {"bit": 8, "name": "q8", "xy_channel": 9, "z_dc_channel": 9, "z_flux_channel": 9, "readout_channel": 1, "_row": 1, "_col": 2}, "q9": {"bit": 9, "name": "q9", "xy_channel": 10, "z_dc_channel": 10, "z_flux_channel": 10, "readout_channel": 1, "_row": 1, "_col": 3}, "q10": {"bit": 10, "name": "q10", "xy_channel": 11, "z_dc_channel": 11, "z_flux_channel": 11, "readout_channel": 1, "_row": 1, "_col": 4}, "q11": {"bit": 11, "name": "q11", "xy_channel": 12, "z_dc_channel": 12, "z_flux_channel": 12, "readout_channel": 1, "_row": 1, "_col": 5}, "q12": {"bit": 12, "name": "q12", "xy_channel": 13, "z_dc_channel": 13, "z_flux_channel": 13, "readout_channel": 1, "_row": 2, "_col": 0}, "q13": {"bit": 13, "name": "q13", "xy_channel": 14, "z_dc_channel": 14, "z_flux_channel": 14, "readout_channel": 1, "_row": 2, "_col": 1}, "q14": {"bit": 14, "name": "q14", "xy_channel": 15, "z_dc_channel": 15, "z_flux_channel": 15, "readout_channel": 1, "_row": 2, "_col": 2}, "q15": {"bit": 15, "name": "q15", "xy_channel": 16, "z_dc_channel": 16, "z_flux_channel": 16, "readout_channel": 1, "_row": 2, "_col": 3}, "q16": {"bit": 16, "name": "q16", "xy_channel": 17, "z_dc_channel": 17, "z_flux_channel": 17, "readout_channel": 1, "_row": 2, "_col": 4}, "q17": {"bit": 17, "name": "q17", "xy_channel": 18, "z_dc_channel": 18, "z_flux_channel": 18, "readout_channel": 1, "_row": 2, "_col": 5}, "q18": {"bit": 18, "name": "q18", "xy_channel": 19, "z_dc_channel": 19, "z_flux_channel": 19, "readout_channel": 1, "_row": 3, "_col": 0}, "q19": {"bit": 19, "name": "q19", "xy_channel": 20, "z_dc_channel": 20, "z_flux_channel": 20, "readout_channel": 1, "_row": 3, "_col": 1}, "q20": {"bit": 20, "name": "q20", "xy_channel": 21, "z_dc_channel": 21, "z_flux_channel": 21, "readout_channel": 1, "_row": 3, "_col": 2}, "q21": {"bit": 21, "name": "q21", "xy_channel": 22, "z_dc_channel": 22, "z_flux_channel": 22, "readout_channel": 1, "_row": 3, "_col": 3}, "q22": {"bit": 22, "name": "q22", "xy_channel": 23, "z_dc_channel": 23, "z_flux_channel": 23, "readout_channel": 1, "_row": 3, "_col": 4}, "q23": {"bit": 23, "name": "q23", "xy_channel": 24, "z_dc_channel": 24, "z_flux_channel": 24, "readout_channel": 1, "_row": 3, "_col": 5}, "q24": {"bit": 24, "name": "q24", "xy_channel": 25, "z_dc_channel": 25, "z_flux_channel": 25, "readout_channel": 1, "_row": 4, "_col": 0}, "q25": {"bit": 25, "name": "q25", "xy_channel": 26, "z_dc_channel": 26, "z_flux_channel": 26, "readout_channel": 1, "_row": 4, "_col": 1}, "q26": {"bit": 26, "name": "q26", "xy_channel": 27, "z_dc_channel": 27, "z_flux_channel": 27, "readout_channel": 1, "_row": 4, "_col": 2}, "q27": {"bit": 27, "name": "q27", "xy_channel": 28, "z_dc_channel": 28, "z_flux_channel": 28, "readout_channel": 1, "_row": 4, "_col": 3}, "q28": {"bit": 28, "name": "q28", "xy_channel": 29, "z_dc_channel": 29, "z_flux_channel": 29, "readout_channel": 1, "_row": 4, "_col": 4}, "q29": {"bit": 29, "name": "q29", "xy_channel": 30, "z_dc_channel": 30, "z_flux_channel": 30, "readout_channel": 1, "_row": 4, "_col": 5}, "q30": {"bit": 30, "name": "q30", "xy_channel": 31, "z_dc_channel": 31, "z_flux_channel": 31, "readout_channel": 1, "_row": 5, "_col": 0}, "q31": {"bit": 31, "name": "q31", "xy_channel": 32, "z_dc_channel": 32, "z_flux_channel": 32, "readout_channel": 1, "_row": 5, "_col": 1}, "q32": {"bit": 32, "name": "q32", "xy_channel": 33, "z_dc_channel": 33, "z_flux_channel": 33, "readout_channel": 1, "_row": 5, "_col": 2}, "q33": {"bit": 33, "name": "q33", "xy_channel": 34, "z_dc_channel": 34, "z_flux_channel": 34, "readout_channel": 1, "_row": 5, "_col": 3}, "q34": {"bit": 34, "name": "q34", "xy_channel": 35, "z_dc_channel": 35, "z_flux_channel": 35, "readout_channel": 1, "_row": 5, "_col": 4}, "q35": {"bit": 35, "name": "q35", "xy_channel": 36, "z_dc_channel": 36, "z_flux_channel": 36, "readout_channel": 1, "_row": 5, "_col": 5}, "q36": {"bit": 36, "name": "q36", "xy_channel": 37, "z_dc_channel": 37, "z_flux_channel": 37, "readout_channel": 1, "_row": 6, "_col": 0}, "q37": {"bit": 37, "name": "q37", "xy_channel": 38, "z_dc_channel": 38, "z_flux_channel": 38, "readout_channel": 1, "_row": 6, "_col": 1}, "q38": {"bit": 38, "name": "q38", "xy_channel": 39, "z_dc_channel": 39, "z_flux_channel": 39, "readout_channel": 1, "_row": 6, "_col": 2}, "q39": {"bit": 39, "name": "q39", "xy_channel": 40, "z_dc_channel": 40, "z_flux_channel": 40, "readout_channel": 1, "_row": 6, "_col": 3}, "q40": {"bit": 40, "name": "q40", "xy_channel": 41, "z_dc_channel": 41, "z_flux_channel": 41, "readout_channel": 1, "_row": 6, "_col": 4}, "q41": {"bit": 41, "name": "q41", "xy_channel": 42, "z_dc_channel": 42, "z_flux_channel": 42, "readout_channel": 1, "_row": 6, "_col": 5}, "q42": {"bit": 42, "name": "q42", "xy_channel": 43, "z_dc_channel": 43, "z_flux_channel": 43, "readout_channel": 1, "_row": 7, "_col": 0}, "q43": {"bit": 43, "name": "q43", "xy_channel": 44, "z_dc_channel": 44, "z_flux_channel": 44, "readout_channel": 1, "_row": 7, "_col": 1}, "q44": {"bit": 44, "name": "q44", "xy_channel": 45, "z_dc_channel": 45, "z_flux_channel": 45, "readout_channel": 1, "_row": 7, "_col": 2}, "q45": {"bit": 45, "name": "q45", "xy_channel": 46, "z_dc_channel": 46, "z_flux_channel": 46, "readout_channel": 1, "_row": 7, "_col": 3}, "q46": {"bit": 46, "name": "q46", "xy_channel": 47, "z_dc_channel": 47, "z_flux_channel": 47, "readout_channel": 1, "_row": 7, "_col": 4}, "q47": {"bit": 47, "name": "q47", "xy_channel": 48, "z_dc_channel": 48, "z_flux_channel": 48, "readout_channel": 1, "_row": 7, "_col": 5}, "q48": {"bit": 48, "name": "q48", "xy_channel": 49, "z_dc_channel": 49, "z_flux_channel": 49, "readout_channel": 1, "_row": 8, "_col": 0}, "q49": {"bit": 49, "name": "q49", "xy_channel": 50, "z_dc_channel": 50, "z_flux_channel": 50, "readout_channel": 1, "_row": 8, "_col": 1}, "q50": {"bit": 50, "name": "q50", "xy_channel": 51, "z_dc_channel": 51, "z_flux_channel": 51, "readout_channel": 1, "_row": 8, "_col": 2}, "q51": {"bit": 51, "name": "q51", "xy_channel": 52, "z_dc_channel": 52, "z_flux_channel": 52, "readout_channel": 1, "_row": 8, "_col": 3}, "q52": {"bit": 52, "name": "q52", "xy_channel": 53, "z_dc_channel": 53, "z_flux_channel": 53, "readout_channel": 1, "_row": 8, "_col": 4}, "q53": {"bit": 53, "name": "q53", "xy_channel": 54, "z_dc_channel": 54, "z_flux_channel": 54, "readout_channel": 1, "_row": 8, "_col": 5}, "q54": {"bit": 54, "name": "q54", "xy_channel": 55, "z_dc_channel": 55, "z_flux_channel": 55, "readout_channel": 1, "_row": 9, "_col": 0}, "q55": {"bit": 55, "name": "q55", "xy_channel": 56, "z_dc_channel": 56, "z_flux_channel": 56, "readout_channel": 1, "_row": 9, "_col": 1}, "q56": {"bit": 56, "name": "q56", "xy_channel": 57, "z_dc_channel": 57, "z_flux_channel": 57, "readout_channel": 1, "_row": 9, "_col": 2}, "q57": {"bit": 57, "name": "q57", "xy_channel": 58, "z_dc_channel": 58, "z_flux_channel": 58, "readout_channel": 1, "_row": 9, "_col": 3}, "q58": {"bit": 58, "name": "q58", "xy_channel": 59, "z_dc_channel": 59, "z_flux_channel": 59, "readout_channel": 1, "_row": 9, "_col": 4}, "q59": {"bit": 59, "name": "q59", "xy_channel": 60, "z_dc_channel": 60, "z_flux_channel": 60, "readout_channel": 1, "_row": 9, "_col": 5}, "q60": {"bit": 60, "name": "q60", "xy_channel": 61, "z_dc_channel": 61, "z_flux_channel": 61, "readout_channel": 1, "_row": 10, "_col": 0}, "q61": {"bit": 61, "name": "q61", "xy_channel": 62, "z_dc_channel": 62, "z_flux_channel": 62, "readout_channel": 1, "_row": 10, "_col": 1}, "q62": {"bit": 62, "name": "q62", "xy_channel": 63, "z_dc_channel": 63, "z_flux_channel": 63, "readout_channel": 1, "_row": 10, "_col": 2}, "q63": {"bit": 63, "name": "q63", "xy_channel": 64, "z_dc_channel": 64, "z_flux_channel": 64, "readout_channel": 1, "_row": 10, "_col": 3}, "q64": {"bit": 64, "name": "q64", "xy_channel": 65, "z_dc_channel": 65, "z_flux_channel": 65, "readout_channel": 1, "_row": 10, "_col": 4}, "q65": {"bit": 65, "name": "q65", "xy_channel": 66, "z_dc_channel": 66, "z_flux_channel": 66, "readout_channel": 1, "_row": 10, "_col": 5}, "q66": {"bit": 66, "name": "q66", "xy_channel": 67, "z_dc_channel": 67, "z_flux_channel": 67, "readout_channel": 1, "_row": 11, "_col": 0}, "q67": {"bit": 67, "name": "q67", "xy_channel": 68, "z_dc_channel": 68, "z_flux_channel": 68, "readout_channel": 1, "_row": 11, "_col": 1}, "q68": {"bit": 68, "name": "q68", "xy_channel": 69, "z_dc_channel": 69, "z_flux_channel": 69, "readout_channel": 1, "_row": 11, "_col": 2}, "q69": {"bit": 69, "name": "q69", "xy_channel": 70, "z_dc_channel": 70, "z_flux_channel": 70, "readout_channel": 1, "_row": 11, "_col": 3}, "q70": {"bit": 70, "name": "q70", "xy_channel": 71, "z_dc_channel": 71, "z_flux_channel": 71, "readout_channel": 1, "_row": 11, "_col": 4}, "q71": {"bit": 71, "name": "q71", "xy_channel": 72, "z_dc_channel": 72, "z_flux_channel": 72, "readout_channel": 1, "_row": 11, "_col": 5}}, "CouplerParams": {"c0-1": {"bit": 0, "name": "c0-1", "z_dc_channel": 73, "z_flux_channel": 73, "probe_bit": 0, "drive_bit": 1}, "c1-2": {"bit": 1, "name": "c1-2", "z_dc_channel": 74, "z_flux_channel": 74, "probe_bit": 1, "drive_bit": 2}, "c2-3": {"bit": 2, "name": "c2-3", "z_dc_channel": 75, "z_flux_channel": 75, "probe_bit": 2, "drive_bit": 3}, "c3-4": {"bit": 3, "name": "c3-4", "z_dc_channel": 76, "z_flux_channel": 76, "probe_bit": 3, "drive_bit": 4}, "c4-5": {"bit": 4, "name": "c4-5", "z_dc_channel": 77, "z_flux_channel": 77, "probe_bit": 4, "drive_bit": 5}, "c0-6": {"bit": 5, "name": "c0-6", "z_dc_channel": 78, "z_flux_channel": 78, "probe_bit": 0, "drive_bit": 6}, "c1-7": {"bit": 6, "name": "c1-7", "z_dc_channel": 79, "z_flux_channel": 79, "probe_bit": 1, "drive_bit": 7}, "c2-8": {"bit": 7, "name": "c2-8", "z_dc_channel": 80, "z_flux_channel": 80, "probe_bit": 2, "drive_bit": 8}, "c3-9": {"bit": 8, "name": "c3-9", "z_dc_channel": 81, "z_flux_channel": 81, "probe_bit": 3, "drive_bit": 9}, "c4-10": {"bit": 9, "name": "c4-10", "z_dc_channel": 82, "z_flux_channel": 82, "probe_bit": 4, "drive_bit": 10}, "c5-11": {"bit": 10, "name": "c5-11", "z_dc_channel": 83, "z_flux_channel": 83, "probe_bit": 5, "drive_bit": 11}, "c6-7": {"bit": 11, "name": "c6-7", "z_dc_channel": 84, "z_flux_channel": 84, "probe_bit": 6, "drive_bit": 7}, "c7-8": {"bit": 12, "name": "c7-8", "z_dc_channel": 85, "z_flux_channel": 85, "probe_bit": 7, "drive_bit": 8}, "c8-9": {"bit": 13, "name": "c8-9", "z_dc_channel": 86, "z_flux_channel": 86, "probe_bit": 8, "drive_bit": 9}, "c9-10": {"bit": 14, "name": "c9-10", "z_dc_channel": 87, "z_flux_channel": 87, "probe_bit": 9, "drive_bit": 10}, "c10-11": {"bit": 15, "name": "c10-11", "z_dc_channel": 88, "z_flux_channel": 88, "probe_bit": 10, "drive_bit": 11}, "c6-12": {"bit": 16, "name": "c6-12", "z_dc_channel": 89, "z_flux_channel": 89, "probe_bit": 6, "drive_bit": 12}, "c7-13": {"bit": 17, "name": "c7-13", "z_dc_channel": 90, "z_flux_channel": 90, "probe_bit": 7, "drive_bit": 13}, "c8-14": {"bit": 18, "name": "c8-14", "z_dc_channel": 91, "z_flux_channel": 91, "probe_bit": 8, "drive_bit": 14}, "c9-15": {"bit": 19, "name": "c9-15", "z_dc_channel": 92, "z_flux_channel": 92, "probe_bit": 9, "drive_bit": 15}, "c10-16": {"bit": 20, "name": "c10-16", "z_dc_channel": 93, "z_flux_channel": 93, "probe_bit": 10, "drive_bit": 16}, "c11-17": {"bit": 21, "name": "c11-17", "z_dc_channel": 94, "z_flux_channel": 94, "probe_bit": 11, "drive_bit": 17}, "c12-13": {"bit": 22, "name": "c12-13", "z_dc_channel": 95, "z_flux_channel": 95, "probe_bit": 12, "drive_bit": 13}, "c13-14": {"bit": 23, "name": "c13-14", "z_dc_channel": 96, "z_flux_channel": 96, "probe_bit": 13, "drive_bit": 14}, "c14-15": {"bit": 24, "name": "c14-15", "z_dc_channel": 97, "z_flux_channel": 97, "probe_bit": 14, "drive_bit": 15}, "c15-16": {"bit": 25, "name": "c15-16", "z_dc_channel": 98, "z_flux_channel": 98, "probe_bit": 15, "drive_bit": 16}, "c16-17": {"bit": 26, "name": "c16-17", "z_dc_channel": 99, "z_flux_channel": 99, "probe_bit": 16, "drive_bit": 17}, "c12-18": {"bit": 27, "name": "c12-18", "z_dc_channel": 100, "z_flux_channel": 100, "probe_bit": 12, "drive_bit": 18}, "c13-19": {"bit": 28, "name": "c13-19", "z_dc_channel": 101, "z_flux_channel": 101, "probe_bit": 13, "drive_bit": 19}, "c14-20": {"bit": 29, "name": "c14-20", "z_dc_channel": 102, "z_flux_channel": 102, "probe_bit": 14, "drive_bit": 20}, "c15-21": {"bit": 30, "name": "c15-21", "z_dc_channel": 103, "z_flux_channel": 103, "probe_bit": 15, "drive_bit": 21}, "c16-22": {"bit": 31, "name": "c16-22", "z_dc_channel": 104, "z_flux_channel": 104, "probe_bit": 16, "drive_bit": 22}, "c17-23": {"bit": 32, "name": "c17-23", "z_dc_channel": 105, "z_flux_channel": 105, "probe_bit": 17, "drive_bit": 23}, "c18-19": {"bit": 33, "name": "c18-19", "z_dc_channel": 106, "z_flux_channel": 106, "probe_bit": 18, "drive_bit": 19}, "c19-20": {"bit": 34, "name": "c19-20", "z_dc_channel": 107, "z_flux_channel": 107, "probe_bit": 19, "drive_bit": 20}, "c20-21": {"bit": 35, "name": "c20-21", "z_dc_channel": 108, "z_flux_channel": 108, "probe_bit": 20, "drive_bit": 21}, "c21-22": {"bit": 36, "name": "c21-22", "z_dc_channel": 109, "z_flux_channel": 109, "probe_bit": 21, "drive_bit": 22}, "c22-23": {"bit": 37, "name": "c22-23", "z_dc_channel": 110, "z_flux_channel": 110, "probe_bit": 22, "drive_bit": 23}, "c18-24": {"bit": 38, "name": "c18-24", "z_dc_channel": 111, "z_flux_channel": 111, "probe_bit": 18, "drive_bit": 24}, "c19-25": {"bit": 39, "name": "c19-25", "z_dc_channel": 112, "z_flux_channel": 112, "probe_bit": 19, "drive_bit": 25}, "c20-26": {"bit": 40, "name": "c20-26", "z_dc_channel": 113, "z_flux_channel": 113, "probe_bit": 20, "drive_bit": 26}, "c21-27": {"bit": 41, "name": "c21-27", "z_dc_channel": 114, "z_flux_channel": 114, "probe_bit": 21, "drive_bit": 27}, "c22-28": {"bit": 42, "name": "c22-28", "z_dc_channel": 115, "z_flux_channel": 115, "probe_bit": 22, "drive_bit": 28}, "c23-29": {"bit": 43, "name": "c23-29", "z_dc_channel": 116, "z_flux_channel": 116, "probe_bit": 23, "drive_bit": 29}, "c24-25": {"bit": 44, "name": "c24-25", "z_dc_channel": 117, "z_flux_channel": 117, "probe_bit": 24, "drive_bit": 25}, "c25-26": {"bit": 45, "name": "c25-26", "z_dc_channel": 118, "z_flux_channel": 118, "probe_bit": 25, "drive_bit": 26}, "c26-27": {"bit": 46, "name": "c26-27", "z_dc_channel": 119, "z_flux_channel": 119, "probe_bit": 26, "drive_bit": 27}, "c27-28": {"bit": 47, "name": "c27-28", "z_dc_channel": 120, "z_flux_channel": 120, "probe_bit": 27, "drive_bit": 28}, "c28-29": {"bit": 48, "name": "c28-29", "z_dc_channel": 121, "z_flux_channel": 121, "probe_bit": 28, "drive_bit": 29}, "c24-30": {"bit": 49, "name": "c24-30", "z_dc_channel": 122, "z_flux_channel": 122, "probe_bit": 24, "drive_bit": 30}, "c25-31": {"bit": 50, "name": "c25-31", "z_dc_channel": 123, "z_flux_channel": 123, "probe_bit": 25, "drive_bit": 31}, "c26-32": {"bit": 51, "name": "c26-32", "z_dc_channel": 124, "z_flux_channel": 124, "probe_bit": 26, "drive_bit": 32}, "c27-33": {"bit": 52, "name": "c27-33", "z_dc_channel": 125, "z_flux_channel": 125, "probe_bit": 27, "drive_bit": 33}, "c28-34": {"bit": 53, "name": "c28-34", "z_dc_channel": 126, "z_flux_channel": 126, "probe_bit": 28, "drive_bit": 34}, "c29-35": {"bit": 54, "name": "c29-35", "z_dc_channel": 127, "z_flux_channel": 127, "probe_bit": 29, "drive_bit": 35}, "c30-31": {"bit": 55, "name": "c30-31", "z_dc_channel": 128, "z_flux_channel": 128, "probe_bit": 30, "drive_bit": 31}, "c31-32": {"bit": 56, "name": "c31-32", "z_dc_channel": 129, "z_flux_channel": 129, "probe_bit": 31, "drive_bit": 32}, "c32-33": {"bit": 57, "name": "c32-33", "z_dc_channel": 130, "z_flux_channel": 130, "probe_bit": 32, "drive_bit": 33}, "c33-34": {"bit": 58, "name": "c33-34", "z_dc_channel": 131, "z_flux_channel": 131, "probe_bit": 33, "drive_bit": 34}, "c34-35": {"bit": 59, "name": "c34-35", "z_dc_channel": 132, "z_flux_channel": 132, "probe_bit": 34, "drive_bit": 35}, "c30-36": {"bit": 60, "name": "c30-36", "z_dc_channel": 133, "z_flux_channel": 133, "probe_bit": 30, "drive_bit": 36}, "c31-37": {"bit": 61, "name": "c31-37", "z_dc_channel": 134, "z_flux_channel": 134, "probe_bit": 31, "drive_bit": 37}, "c32-38": {"bit": 62, "name": "c32-38", "z_dc_channel": 135, "z_flux_channel": 135, "probe_bit": 32, "drive_bit": 38}, "c33-39": {"bit": 63, "name": "c33-39", "z_dc_channel": 136, "z_flux_channel": 136, "probe_bit": 33, "drive_bit": 39}, "c34-40": {"bit": 64, "name": "c34-40", "z_dc_channel": 137, "z_flux_channel": 137, "probe_bit": 34, "drive_bit": 40}, "c35-41": {"bit": 65, "name": "c35-41", "z_dc_channel": 138, "z_flux_channel": 138, "probe_bit": 35, "drive_bit": 41}, "c36-37": {"bit": 66, "name": "c36-37", "z_dc_channel": 139, "z_flux_channel": 139, "probe_bit": 36, "drive_bit": 37}, "c37-38": {"bit": 67, "name": "c37-38", "z_dc_channel": 140, "z_flux_channel": 140, "probe_bit": 37, "drive_bit": 38}, "c38-39": {"bit": 68, "name": "c38-39", "z_dc_channel": 141, "z_flux_channel": 141, "probe_bit": 38, "drive_bit": 39}, "c39-40": {"bit": 69, "name": "c39-40", "z_dc_channel": 142, "z_flux_channel": 142, "probe_bit": 39, "drive_bit": 40}, "c40-41": {"bit": 70, "name": "c40-41", "z_dc_channel": 143, "z_flux_channel": 143, "probe_bit": 40, "drive_bit": 41}, "c36-42": {"bit": 71, "name": "c36-42", "z_dc_channel": 144, "z_flux_channel": 144, "probe_bit": 36, "drive_bit": 42}, "c37-43": {"bit": 72, "name": "c37-43", "z_dc_channel": 145, "z_flux_channel": 145, "probe_bit": 37, "drive_bit": 43}, "c38-44": {"bit": 73, "name": "c38-44", "z_dc_channel": 146, "z_flux_channel": 146, "probe_bit": 38, "drive_bit": 44}, "c39-45": {"bit": 74, "name": "c39-45", "z_dc_channel": 147, "z_flux_channel": 147, "probe_bit": 39, "drive_bit": 45}, "c40-46": {"bit": 75, "name": "c40-46", "z_dc_channel": 148, "z_flux_channel": 148, "probe_bit": 40, "drive_bit": 46}, "c41-47": {"bit": 76, "name": "c41-47", "z_dc_channel": 149, "z_flux_channel": 149, "probe_bit": 41, "drive_bit": 47}, "c42-43": {"bit": 77, "name": "c42-43", "z_dc_channel": 150, "z_flux_channel": 150, "probe_bit": 42, "drive_bit": 43}, "c43-44": {"bit": 78, "name": "c43-44", "z_dc_channel": 151, "z_flux_channel": 151, "probe_bit": 43, "drive_bit": 44}, "c44-45": {"bit": 79, "name": "c44-45", "z_dc_channel": 152, "z_flux_channel": 152, "probe_bit": 44, "drive_bit": 45}, "c45-46": {"bit": 80, "name": "c45-46", "z_dc_channel": 153, "z_flux_channel": 153, "probe_bit": 45, "drive_bit": 46}, "c46-47": {"bit": 81, "name": "c46-47", "z_dc_channel": 154, "z_flux_channel": 154, "probe_bit": 46, "drive_bit": 47}, "c42-48": {"bit": 82, "name": "c42-48", "z_dc_channel": 155, "z_flux_channel": 155, "probe_bit": 42, "drive_bit": 48}, "c43-49": {"bit": 83, "name": "c43-49", "z_dc_channel": 156, "z_flux_channel": 156, "probe_bit": 43, "drive_bit": 49}, "c44-50": {"bit": 84, "name": "c44-50", "z_dc_channel": 157, "z_flux_channel": 157, "probe_bit": 44, "drive_bit": 50}, "c45-51": {"bit": 85, "name": "c45-51", "z_dc_channel": 158, "z_flux_channel": 158, "probe_bit": 45, "drive_bit": 51}, "c46-52": {"bit": 86, "name": "c46-52", "z_dc_channel": 159, "z_flux_channel": 159, "probe_bit": 46, "drive_bit": 52}, "c47-53": {"bit": 87, "name": "c47-53", "z_dc_channel": 160, "z_flux_channel": 160, "probe_bit": 47, "drive_bit": 53}, "c48-49": {"bit": 88, "name": "c48-49", "z_dc_channel": 161, "z_flux_channel": 161, "probe_bit": 48, "drive_bit": 49}, "c49-50": {"bit": 89, "name": "c49-50", "z_dc_channel": 162, "z_flux_channel": 162, "probe_bit": 49, "drive_bit": 50}, "c50-51": {"bit": 90, "name": "c50-51", "z_dc_channel": 163, "z_flux_channel": 163, "probe_bit": 50, "drive_bit": 51}, "c51-52": {"bit": 91, "name": "c51-52", "z_dc_channel": 164, "z_flux_channel": 164, "probe_bit": 51, "drive_bit": 52}, "c52-53": {"bit": 92, "name": "c52-53", "z_dc_channel": 165, "z_flux_channel": 165, "probe_bit": 52, "drive_bit": 53}, "c48-54": {"bit": 93, "name": "c48-54", "z_dc_channel": 166, "z_flux_channel": 166, "probe_bit": 48, "drive_bit": 54}, "c49-55": {"bit": 94, "name": "c49-55", "z_dc_channel": 167, "z_flux_channel": 167, "probe_bit": 49, "drive_bit": 55}, "c50-56": {"bit": 95, "name": "c50-56", "z_dc_channel": 168, "z_flux_channel": 168, "probe_bit": 50, "drive_bit": 56}, "c51-57": {"bit": 96, "name": "c51-57", "z_dc_channel": 169, "z_flux_channel": 169, "probe_bit": 51, "drive_bit": 57}, "c52-58": {"bit": 97, "name": "c52-58", "z_dc_channel": 170, "z_flux_channel": 170, "probe_bit": 52, "drive_bit": 58}, "c53-59": {"bit": 98, "name": "c53-59", "z_dc_channel": 171, "z_flux_channel": 171, "probe_bit": 53, "drive_bit": 59}, "c54-55": {"bit": 99, "name": "c54-55", "z_dc_channel": 172, "z_flux_channel": 172, "probe_bit": 54, "drive_bit": 55}, "c55-56": {"bit": 100, "name": "c55-56", "z_dc_channel": 173, "z_flux_channel": 173, "probe_bit": 55, "drive_bit": 56}, "c56-57": {"bit": 101, "name": "c56-57", "z_dc_channel": 174, "z_flux_channel": 174, "probe_bit": 56, "drive_bit": 57}, "c57-58": {"bit": 102, "name": "c57-58", "z_dc_channel": 175, "z_flux_channel": 175, "probe_bit": 57, "drive_bit": 58}, "c58-59": {"bit": 103, "name": "c58-59", "z_dc_channel": 176, "z_flux_channel": 176, "probe_bit": 58, "drive_bit": 59}, "c54-60": {"bit": 104, "name": "c54-60", "z_dc_channel": 177, "z_flux_channel": 177, "probe_bit": 54, "drive_bit": 60}, "c55-61": {"bit": 105, "name": "c55-61", "z_dc_channel": 178, "z_flux_channel": 178, "probe_bit": 55, "drive_bit": 61}, "c56-62": {"bit": 106, "name": "c56-62", "z_dc_channel": 179, "z_flux_channel": 179, "probe_bit": 56, "drive_bit": 62}, "c57-63": {"bit": 107, "name": "c57-63", "z_dc_channel": 180, "z_flux_channel": 180, "probe_bit": 57, "drive_bit": 63}, "c58-64": {"bit": 108, "name": "c58-64", "z_dc_channel": 181, "z_flux_channel": 181, "probe_bit": 58, "drive_bit": 64}, "c59-65": {"bit": 109, "name": "c59-65", "z_dc_channel": 182, "z_flux_channel": 182, "probe_bit": 59, "drive_bit": 65}, "c60-61": {"bit": 110, "name": "c60-61", "z_dc_channel": 183, "z_flux_channel": 183, "probe_bit": 60, "drive_bit": 61}, "c61-62": {"bit": 111, "name": "c61-62", "z_dc_channel": 184, "z_flux_channel": 184, "probe_bit": 61, "drive_bit": 62}, "c62-63": {"bit": 112, "name": "c62-63", "z_dc_channel": 185, "z_flux_channel": 185, "probe_bit": 62, "drive_bit": 63}, "c63-64": {"bit": 113, "name": "c63-64", "z_dc_channel": 186, "z_flux_channel": 186, "probe_bit": 63, "drive_bit": 64}, "c64-65": {"bit": 114, "name": "c64-65", "z_dc_channel": 187, "z_flux_channel": 187, "probe_bit": 64, "drive_bit": 65}, "c60-66": {"bit": 115, "name": "c60-66", "z_dc_channel": 188, "z_flux_channel": 188, "probe_bit": 60, "drive_bit": 66}, "c61-67": {"bit": 116, "name": "c61-67", "z_dc_channel": 189, "z_flux_channel": 189, "probe_bit": 61, "drive_bit": 67}, "c62-68": {"bit": 117, "name": "c62-68", "z_dc_channel": 190, "z_flux_channel": 190, "probe_bit": 62, "drive_bit": 68}, "c63-69": {"bit": 118, "name": "c63-69", "z_dc_channel": 191, "z_flux_channel": 191, "probe_bit": 63, "drive_bit": 69}, "c64-70": {"bit": 119, "name": "c64-70", "z_dc_channel": 192, "z_flux_channel": 192, "probe_bit": 64, "drive_bit": 70}, "c65-71": {"bit": 120, "name": "c65-71", "z_dc_channel": 193, "z_flux_channel": 193, "probe_bit": 65, "drive_bit": 71}, "c66-67": {"bit": 121, "name": "c66-67", "z_dc_channel": 194, "z_flux_channel": 194, "probe_bit": 66, "drive_bit": 67}, "c67-68": {"bit": 122, "name": "c67-68", "z_dc_channel": 195, "z_flux_channel": 195, "probe_bit": 67, "drive_bit": 68}, "c68-69": {"bit": 123, "name": "c68-69", "z_dc_channel": 196, "z_flux_channel": 196, "probe_bit": 68, "drive_bit": 69}, "c69-70": {"bit": 124, "name": "c69-70", "z_dc_channel": 197, "z_flux_channel": 197, "probe_bit": 69, "drive_bit": 70}, "c70-71": {"bit": 125, "name": "c70-71", "z_dc_channel": 198, "z_flux_channel": 198, "probe_bit": 70, "drive_bit": 71}}}