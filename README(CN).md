# pyqcat-apps

**pyqcat-apps** 是本源量子公司基于实验核心库 **pyqcat-monster** 开发的量子测控小程序集合. **pyqcat-monster** 是其中一个核心的软件包，
它的设计初衷是为了更方便依赖 **pyqcat-monster** 开发一些验证性的实验,分析及特殊工具。所以，你可以使用 **pyqcat-apps** 进行相关测试和验证。


## Software Architecture
Software architecture description

## Installation
1. xxxx
2. xxxx
3. xxxx

## Instructions
1. xxxx
2. xxxx
3. xxxx

## Contribution
1. Fork the repository
2. Create Feat_xxx branch
3. Commit your code
4. Create Pull Request

## BitBucket Feature
1. You can use Readme_XXX.md to support different languages, such as Readme_en.md, Readme_zh.md
2. Explore open source project http://***********:7990/projects/QTOOL/repos/pyqcat-apps/browse

## BUG和需求

如果在使用过程中有任何问题或者新的需求，请及时向我们开发部门反馈。
开发者邮箱：(<EMAIL>)
