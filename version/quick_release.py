# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/31
# __author:       <PERSON><PERSON><PERSON>

import argparse
import json
import os

MONSTER_DEVICE = ["B", "C", "D"]
PATH = os.path.dirname(__file__)


# Generate Windows. bat file content
def generate_bat_content(versions):
    return fr"""@echo off

net session >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo.
) else (
    echo Error: Please run this script as an administrator!
    pause
    exit /b 1
)

set WORK_DIR=%~dp0
set APPS={versions["APPS"]}
set MONSTER={versions["MONSTER"]}
set INVOKER={versions["INVOKER"]}
set VISAGE={versions["VISAGE"]}

echo ****** Start auto build pyqcat bucket ******
cd /d %WORK_DIR%
echo.

echo ****** LOG: delete history directory ... ******
rmdir /s /q pyqcat-apps
rmdir /s /q pyqcat-monster
rmdir /s /q pyqcat-invoker
rmdir /s /q pyqcat-visage
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Cloning pyqcat-apps repository ... ******
git clone --branch %APPS% --depth 1 https://gitlab.qpanda.cn/spirit/pyqcat-apps.git
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Cloning pyqcat-invoker repository ... ******
git clone --branch %INVOKER% --depth 1 https://gitlab.qpanda.cn/spirit/pyqcat-invoker.git
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Cloning pyqcat-monster repository ... ******
git clone --branch %MONSTER% --depth 1 https://gitlab.qpanda.cn/spirit/pyqcat-monster.git
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Cloning pyqcat-visage repository ... ******
git clone --branch %VISAGE% --depth 1 https://gitlab.qpanda.cn/spirit/pyqcat-visage.git
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Link invoker to monster ... ******
mklink /d %WORK_DIR%pyqcat-monster\pyQCat\invoker\qmq %WORK_DIR%pyqcat-invoker\pyQCat\invoker\qmq
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** LOG: Link monster to apps ... ******
mklink /d %WORK_DIR%pyqcat-apps\pyQCat %WORK_DIR%pyqcat-monster\pyQCat
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** LOG: Link monster to visage ... ******
mklink /d %WORK_DIR%pyqcat-visage\pyQCat %WORK_DIR%pyqcat-monster\pyQCat
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** All commands executed successfully. ******
pause
"""


# Generate Linux. sh file content
def generate_sh_content(versions):
    return fr"""#!/bin/bash

WORK_DIR=$(dirname "$(readlink -f "$0")")
APPS="{versions["APPS"]}"
MONSTER="{versions["MONSTER"]}"
INVOKER="{versions["INVOKER"]}"
VISAGE="{versions["VISAGE"]}"

echo "****** Start auto build pyqcat bucket ******"
cd "$WORK_DIR" || exit
echo

echo "****** LOG: delete history directories ... ******"
rm -rf pyqcat-apps
rm -rf pyqcat-monster
rm -rf pyqcat-invoker
rm -rf pyqcat-visage
if [ $? -ne 0 ]; then
    echo "Failed to remove directories."
    exit 1
fi
echo

echo "****** LOG: Cloning pyqcat-apps repository ... ******"
git clone --branch "$APPS" --depth 1 https://gitlab.qpanda.cn/spirit/pyqcat-apps.git
if [ $? -ne 0 ]; then
    echo "Failed to clone repository."
    exit 1
fi
echo

echo "****** LOG: Cloning pyqcat-invoker repository ... ******"
git clone --branch "$INVOKER" --depth 1 https://gitlab.qpanda.cn/spirit/pyqcat-invoker.git
if [ $? -ne 0 ]; then
    echo "Failed to clone repository."
    exit 1
fi
echo

echo "****** LOG: Cloning pyqcat-monster repository ... ******"
git clone --branch "$MONSTER" --depth 1 https://gitlab.qpanda.cn/spirit/pyqcat-monster.git
if [ $? -ne 0 ]; then
    echo "Failed to clone repository."
    exit 1
fi
echo

echo "****** LOG: Cloning pyqcat-visage repository ... ******"
git clone --branch "$VISAGE" --depth 1 https://gitlab.qpanda.cn/spirit/pyqcat-visage.git
if [ $? -ne 0 ]; then
    echo "Failed to clone repository."
    exit 1
fi
echo

echo "****** LOG: Link invoker to monster ... ******"
ln -sf "$WORK_DIR/pyqcat-invoker/pyQCat/invoker/qmq" "$WORK_DIR/pyqcat-monster/pyQCat/invoker/qmq"
if [ $? -ne 0 ]; then
    echo "Failed to create symbolic link."
    exit 1
fi
echo

echo "****** LOG: Link monster to apps ... ******"
ln -sf "$WORK_DIR/pyqcat-monster/pyQCat" "$WORK_DIR/pyqcat-apps/pyQCat"
if [ $? -ne 0 ]; then
    echo "Failed to create symbolic link."
    exit 1
fi
echo

echo "****** LOG: Link monster to visage ... ******"
ln -sf "$WORK_DIR/pyqcat-monster/pyQCat" "$WORK_DIR/pyqcat-visage/pyQCat"
if [ $? -ne 0 ]; then
    echo "Failed to create symbolic link."
    exit 1
fi
echo

echo "****** All commands executed successfully. ******"
read -p "Press any key to continue..." -n1 -s
echo
"""


# Generate Windows. bat and Linux. sh file content
def generate_files(versions, file_path):
    monster_version = versions["MONSTER"]

    # Generate Windows. bat file content
    bat_filename = os.path.join(file_path, f"pyqcat-release-{monster_version}.bat")
    with open(bat_filename, "w", newline="\r\n") as f:
        f.write(generate_bat_content(versions))
    print(f"Build SUC: {bat_filename}")

    # Generate Linux. sh file content
    sh_filename = os.path.join(file_path, f"pyqcat-release-{monster_version}.sh")
    with open(sh_filename, "w", newline="\n") as f:
        f.write(generate_sh_content(versions))

    # Add execution permission
    os.chmod(sh_filename, 0o755)
    print(f"Build SUC: {sh_filename}")


def build_release_bat_and_sh(versions):
    monster_version = versions["MONSTER"]
    file_path = os.path.join(PATH, "release", monster_version)
    os.makedirs(file_path, exist_ok=True)
    for device in MONSTER_DEVICE:
        device_monster_version = f"{device}-{monster_version}"
        versions["MONSTER"] = device_monster_version
        generate_files(versions, file_path)


def pyqcat_release(version: str):
    version_conf = os.path.join(PATH, "version_match.json")
    with open(version_conf, "r", encoding="utf-8") as file:
        version_data = json.load(file)
    if version in version_data:
        version_info = version_data[version]
        date = version_info.pop("DATE")
        print(f"Prepare build pyqcat project {version}, date {date}")
        build_release_bat_and_sh(version_info)


if __name__ == "__main__":
    # Create an ArgumentParser object
    parser = argparse.ArgumentParser(description="Build release bat and sh files.")
    
    # Add command-line arguments
    parser.add_argument("-A", "--APPS", type=str, help="Tag for APPS", default="")
    parser.add_argument("-M", "--MONSTER", type=str, help="Tag for MONSTER", default="")
    parser.add_argument("-I", "--INVOKER", type=str, help="Tag for INVOKER", default="")
    parser.add_argument("-V", "--VISAGE", type=str, help="Tag for VISAGE", default="")
    parser.add_argument("-R", "--RELEASE", type=str, help="Auto release from json", default="")
    
    # Parse command-line arguments
    args = parser.parse_args()
    
    if args.RELEASE:
        pyqcat_release(args.RELEASE)
    else:
        # Convert command-line arguments to a dictionary
        versions = {
            "APPS": args.APPS,
            "MONSTER": args.MONSTER,
            "INVOKER": args.INVOKER,
            "VISAGE": args.VISAGE,
        }
        
        # Call the function
        build_release_bat_and_sh(versions)
