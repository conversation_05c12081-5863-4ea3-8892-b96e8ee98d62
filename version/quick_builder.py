# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/31
# __author:       <PERSON><PERSON><PERSON>

import argparse
import os

PATH = os.path.dirname(__file__)


# Generate Windows. bat file content
def generate_bat_content(versions):
    return fr"""@echo off

net session >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo.
) else (
    echo Error: Please run this script as an administrator!
    pause
    exit /b 1
)

set WORK_DIR=%~dp0
set APPS={versions["APPS"]}
set MONSTER={versions["MONSTER"]}
set INVOKER={versions["INVOKER"]}
set VISAGE={versions["VISAGE"]}

echo ****** Start auto build pyqcat bucket ******
cd /d %WORK_DIR%
echo.

echo ****** LOG: delete history directory ... ******
rmdir /s /q pyqcat-apps
rmdir /s /q pyqcat-monster
rmdir /s /q pyqcat-invoker
rmdir /s /q pyqcat-visage
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Cloning pyqcat-apps repository ... ******
git clone https://gitlab.qpanda.cn/spirit/pyqcat-apps.git
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing directory to pyqcat-apps ... ******
cd pyqcat-apps
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Checking out %APPS% branch ... ******
git checkout -b %APPS% origin/%APPS%
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing back to the parent directory ... ******
cd ..
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Cloning pyqcat-invoker repository ... ******
git clone https://gitlab.qpanda.cn/spirit/pyqcat-invoker.git
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing directory to pyqcat-invoker ... ******
cd pyqcat-invoker
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Checking out %INVOKER% branch ... ******
git checkout -b %INVOKER% origin/%INVOKER%
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing back to the parent directory ... ******
cd ..
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Cloning pyqcat-monster repository ... ******
git clone https://gitlab.qpanda.cn/spirit/pyqcat-monster.git
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing directory to pyqcat-monster ... ******
cd pyqcat-monster
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Checking out %MONSTER% branch ... ******
git checkout -b %MONSTER% origin/%MONSTER%
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing back to the parent directory ... ******
cd ..
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Cloning pyqcat-visage repository ... ******
git clone https://gitlab.qpanda.cn/spirit/pyqcat-visage.git
if errorlevel 1 (
    echo Failed to clone repository.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing directory to pyqcat-visage ... ******
cd pyqcat-visage
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Checking out %VISAGE% branch ... ******
git checkout -b %VISAGE% origin/%VISAGE%
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** LOG: Changing back to the parent directory ... ******
cd ..
if errorlevel 1 (
    echo Failed to change directory.
    pause
    exit /b
)
echo.

echo ****** LOG: Link invoker to monster ... ******
mklink /d %WORK_DIR%pyqcat-monster\pyQCat\invoker\qmq %WORK_DIR%pyqcat-invoker\pyQCat\invoker\qmq
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** LOG: Link monster to apps ... ******
mklink /d %WORK_DIR%pyqcat-apps\pyQCat %WORK_DIR%pyqcat-monster\pyQCat
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** LOG: Link monster to visage ... ******
mklink /d %WORK_DIR%pyqcat-visage\pyQCat %WORK_DIR%pyqcat-monster\pyQCat
if errorlevel 1 (
    echo Failed to checkout branch.
    pause
    exit /b
)
echo.

echo ****** All commands executed successfully. ******
pause
"""


# Generate Linux. sh file content
def generate_sh_content(versions):
    return fr"""#!/bin/bash

WORK_DIR=$(dirname "$(readlink -f "$0")")
APPS="{versions["APPS"]}"
MONSTER="{versions["MONSTER"]}"
INVOKER="{versions["INVOKER"]}"
VISAGE="{versions["VISAGE"]}"

echo "****** Start auto build pyqcat bucket ******"
cd "$WORK_DIR" || exit
echo

echo "****** LOG: delete history directories ... ******"
rm -rf pyqcat-apps
rm -rf pyqcat-monster
rm -rf pyqcat-invoker
rm -rf pyqcat-visage
if [ $? -ne 0 ]; then
    echo "Failed to remove directories."
    exit 1
fi
echo

echo "****** LOG: Cloning pyqcat-apps repository ... ******"
git clone https://gitlab.qpanda.cn/spirit/pyqcat-apps.git
if [ $? -ne 0 ]; then
    echo "Failed to clone repository."
    exit 1
fi
echo

echo "****** LOG: Changing directory to pyqcat-apps ... ******"
cd pyqcat-apps || exit
if [ $? -ne 0 ]; then
    echo "Failed to change directory."
    exit 1
fi
echo

echo "****** LOG: Checking out $APPS branch ... ******"
git checkout -b "$APPS" "origin/$APPS"
if [ $? -ne 0 ]; then
    echo "Failed to checkout branch."
    exit 1
fi
echo

echo "****** LOG: Changing back to the parent directory ... ******"
cd .. || exit
if [ $? -ne 0 ]; then
    echo "Failed to change directory."
    exit 1
fi
echo

echo "****** LOG: Cloning pyqcat-invoker repository ... ******"
git clone https://gitlab.qpanda.cn/spirit/pyqcat-invoker.git
if [ $? -ne 0 ]; then
    echo "Failed to clone repository."
    exit 1
fi
echo

echo "****** LOG: Changing directory to pyqcat-invoker ... ******"
cd pyqcat-invoker || exit
if [ $? -ne 0 ]; then
    echo "Failed to change directory."
    exit 1
fi
echo

echo "****** LOG: Checking out $INVOKER branch ... ******"
git checkout -b "$INVOKER" "origin/$INVOKER"
if [ $? -ne 0 ]; then
    echo "Failed to checkout branch."
    exit 1
fi
echo

echo "****** LOG: Changing back to the parent directory ... ******"
cd .. || exit
if [ $? -ne 0 ]; then
    echo "Failed to change directory."
    exit 1
fi
echo

echo "****** LOG: Cloning pyqcat-monster repository ... ******"
git clone https://gitlab.qpanda.cn/spirit/pyqcat-monster.git
if [ $? -ne 0 ]; then
    echo "Failed to clone repository."
    exit 1
fi
echo

echo "****** LOG: Changing directory to pyqcat-monster ... ******"
cd pyqcat-monster || exit
if [ $? -ne 0 ]; then
    echo "Failed to change directory."
    exit 1
fi
echo

echo "****** LOG: Checking out $MONSTER branch ... ******"
git checkout -b "$MONSTER" "origin/$MONSTER"
if [ $? -ne 0 ]; then
    echo "Failed to checkout branch."
    exit 1
fi
echo

echo "****** LOG: Changing back to the parent directory ... ******"
cd .. || exit
if [ $? -ne 0 ]; then
    echo "Failed to change directory."
    exit 1
fi
echo

echo "****** LOG: Cloning pyqcat-visage repository ... ******"
git clone https://gitlab.qpanda.cn/spirit/pyqcat-visage.git
if [ $? -ne 0 ]; then
    echo "Failed to clone repository."
    exit 1
fi
echo

echo "****** LOG: Changing directory to pyqcat-visage ... ******"
cd pyqcat-visage || exit
if [ $? -ne 0 ]; then
    echo "Failed to change directory."
    exit 1
fi
echo

echo "****** LOG: Checking out $VISAGE branch ... ******"
git checkout -b "$VISAGE" "origin/$VISAGE"
if [ $? -ne 0 ]; then
    echo "Failed to checkout branch."
    exit 1
fi
echo

echo "****** LOG: Changing back to the parent directory ... ******"
cd .. || exit
if [ $? -ne 0 ]; then
    echo "Failed to change directory."
    exit 1
fi
echo

echo "****** LOG: Link invoker to monster ... ******"
ln -sf "$WORK_DIR/pyqcat-invoker/pyQCat/invoker/qmq" "$WORK_DIR/pyqcat-monster/pyQCat/invoker/qmq"
if [ $? -ne 0 ]; then
    echo "Failed to create symbolic link."
    exit 1
fi
echo

echo "****** LOG: Link monster to apps ... ******"
ln -sf "$WORK_DIR/pyqcat-monster/pyQCat" "$WORK_DIR/pyqcat-apps/pyQCat"
if [ $? -ne 0 ]; then
    echo "Failed to create symbolic link."
    exit 1
fi
echo

echo "****** LOG: Link monster to visage ... ******"
ln -sf "$WORK_DIR/pyqcat-monster/pyQCat" "$WORK_DIR/pyqcat-visage/pyQCat"
if [ $? -ne 0 ]; then
    echo "Failed to create symbolic link."
    exit 1
fi
echo

echo "****** All commands executed successfully. ******"
read -p "Press any key to continue..." -n1 -s
echo
"""


# Generate Windows. bat and Linux. sh file content
def generate_files(versions, file_path, branch_name):
    # Generate Windows. bat file content
    bat_filename = os.path.join(file_path, f"pyqcat-project-{branch_name}.bat")
    with open(bat_filename, "w", newline="\r\n") as f:
        f.write(generate_bat_content(versions))
    print(f"Build SUC: {bat_filename}")

    # Generate Linux. sh file content
    sh_filename = os.path.join(file_path, f"pyqcat-release-{branch_name}.sh")
    with open(sh_filename, "w", newline="\n") as f:
        f.write(generate_sh_content(versions))

    # Add execution permission
    os.chmod(sh_filename, 0o755)
    print(f"Build SUC: {sh_filename}")


def build_branch_bat_and_sh(brach_collections):
    monster_branch = brach_collections["MONSTER"]
    monster_branch = monster_branch.replace("/", "-")
    file_path = os.path.join(PATH, "branch", monster_branch)
    os.makedirs(file_path, exist_ok=True)
    generate_files(brach_collections, file_path, monster_branch)


if __name__ == "__main__":
    # Create an ArgumentParser object
    parser = argparse.ArgumentParser(description="Build project bat and sh files.")

    # Add command-line arguments
    parser.add_argument("-A", "--APPS", type=str, help="Branch for APPS")
    parser.add_argument("-M", "--MONSTER", type=str, help="Branch for MONSTER")
    parser.add_argument("-I", "--INVOKER", type=str, help="Branch for INVOKER")
    parser.add_argument("-V", "--VISAGE", type=str, help="Branch for VISAGE")

    # Parse command-line arguments
    args = parser.parse_args()

    # Convert command-line arguments to a dictionary
    versions = {
        "APPS": args.APPS,
        "MONSTER": args.MONSTER,
        "INVOKER": args.INVOKER,
        "VISAGE": args.VISAGE,
    }

    # Call the function
    build_branch_bat_and_sh(versions)
