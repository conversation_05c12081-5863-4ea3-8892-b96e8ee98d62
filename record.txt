[
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq4c3-4-CouplerOptimizeFirDicarlo-6895a2c2b950672a5e26e061\n--------------------------------------------------\nCan't pickle local object 'Amp2Freq.<locals>.<lambda>'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 449, in _sync_composite_run\n    await self._run_once(h_old_list, count, i)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 328, in _run_once\n    self._run_analysis([0, np.pi / 2] * (count + 1), DicarloAnalysis)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 217, in _run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 235, in run_analysis_process\n    serialized_data = pick_to_binary_data(experiment_data)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\tools\\serialization.py\", line 818, in pick_to_binary_data\n    binary_data = pickle.dumps(obj)\nAttributeError: Can't pickle local object 'Amp2Freq.<locals>.<lambda>'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-SingleShot_01-689599dc01f3bf39985c263d\n--------------------------------------------------\n<Instrument invalid value error> | intermediate_frequency(-250) is less than the minimum of 800 | Maybe bit parameters error, please check!"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq4c3-4-CouplerOptimizeFirDicarlo-689598ec96b322fb2cc7aac1\n--------------------------------------------------\n'NoneType' object cannot be interpreted as an integer\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 446, in _sync_composite_run\n    for count in range(self.experiment_options.repeat_times):\nTypeError: 'NoneType' object cannot be interpreted as an integer\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-SingleShot_01-68959585f1fd2448be02dafb\n--------------------------------------------------\n<Instrument invalid value error> | intermediate_frequency(-250) is less than the minimum of 800 | Maybe bit parameters error, please check!"},
{"level":15,"message":"DataCollector Error || send_execute_data_to_courier | {'code': 500, 'data': {}, 'msg': \"Internal Server Error: cannot encode object: array([0.175, 0.176, 0.177, 0.178, 0.179, 0.18 , 0.181, 0.182, 0.183,\\n       0.184, 0.185, 0.186, 0.187, 0.188, 0.189, 0.19 , 0.191, 0.192,\\n       0.193, 0.194, 0.195, 0.196, 0.197, 0.198, 0.199, 0.2  , 0.201,\\n       0.202, 0.203, 0.204, 0.205, 0.206, 0.207, 0.208, 0.209, 0.21 ,\\n       0.211, 0.212, 0.213, 0.214, 0.215]), of type: <class 'numpy.ndarray'>\"}"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq3q4c3-4-CouplerOptimizeFirDicarlo-6895946943c752d3ff4a99bf\n--------------------------------------------------\n'NoneType' object cannot be interpreted as an integer\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 446, in _sync_composite_run\n    for count in range(self.experiment_options.repeat_times):\nTypeError: 'NoneType' object cannot be interpreted as an integer\n"},
{"level":15,"message":"DataCollector Error || send_execute_data_to_courier | {'code': 500, 'data': {}, 'msg': \"Internal Server Error: cannot encode object: array([0.175, 0.176, 0.177, 0.178, 0.179, 0.18 , 0.181, 0.182, 0.183,\\n       0.184, 0.185, 0.186, 0.187, 0.188, 0.189, 0.19 , 0.191, 0.192,\\n       0.193, 0.194, 0.195, 0.196, 0.197, 0.198, 0.199, 0.2  , 0.201,\\n       0.202, 0.203, 0.204, 0.205, 0.206, 0.207, 0.208, 0.209, 0.21 ,\\n       0.211, 0.212, 0.213, 0.214, 0.215]), of type: <class 'numpy.ndarray'>\"}"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq3q4c3-4-CouplerOptimizeFirDicarlo-6895945743c752d3ff4a99bb\n--------------------------------------------------\nZ:\\Y8\\yxy\\data\\250805\\QubitcellV6.1\\Reset_LRU_test\\ACSpectrumByCoupler\\q4c3-4\\2025-08-08\\11.52.57ACSpectrumByCoupler.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 511, in _check_options\n    super()._check_options()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\analysis\\fit\\fit_models.py\", line 1330, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: Z:\\Y8\\yxy\\data\\250805\\QubitcellV6.1\\Reset_LRU_test\\ACSpectrumByCoupler\\q4c3-4\\2025-08-08\\11.52.57ACSpectrumByCoupler.dat not found.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq53-DetuneCalibration-68958ed2bdea78a7064b549b\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq53-DetuneCalibration-APEComposite(1-2)-RoughScan-68958ed2bdea78a7064b549f\n--------------------------------------------------\nmin() arg is an empty sequence\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 523, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 474, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 508, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(4973ba72-62a8-4d51-88ba-438e4ec589c0) | TID(890ca6fb-8e52-429d-a654-2d8fbd5dd332) | DataType(iq) | Loop(61)]"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq19-QubitSpectrum-689583c542d302e4067782bd\n--------------------------------------------------\nSpecified value for 'drive_power' is not a valid value, must be >=-40 or <=-10\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 605, in run_experiment\n    self._validate_options()\n  File \"D:\\code\\PYQCAT\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 362, in _validate_options\n    super()._validate_options()\n  File \"D:\\code\\PYQCAT\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 932, in _validate_options\n    self.experiment_options.validate_options()\n  File \"D:\\code\\PYQCAT\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 522, in validate_options\n    self._validate(key, value)\n  File \"D:\\code\\PYQCAT\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 483, in _validate\n    raise ValueError(\nValueError: Specified value for 'drive_power' is not a valid value, must be >=-40 or <=-10\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq11-QubitSpectrum-6895839642d302e4067782b9\n--------------------------------------------------\nSpecified value for 'drive_power' is not a valid value, must be >=-40 or <=-10\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 605, in run_experiment\n    self._validate_options()\n  File \"D:\\code\\PYQCAT\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 362, in _validate_options\n    super()._validate_options()\n  File \"D:\\code\\PYQCAT\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 932, in _validate_options\n    self.experiment_options.validate_options()\n  File \"D:\\code\\PYQCAT\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 522, in validate_options\n    self._validate(key, value)\n  File \"D:\\code\\PYQCAT\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 483, in _validate\n    raise ValueError(\nValueError: Specified value for 'drive_power' is not a valid value, must be >=-40 or <=-10\n"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(4973ba72-62a8-4d51-88ba-438e4ec589c0) | TID(890ca6fb-8e52-429d-a654-2d8fbd5dd332) | DataType(iq) | Loop(61)]"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq55-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(14-31)-changing_phase=0.7766666666666666 amp=None-689573adadd475bf4fee65a1\n--------------------------------------------------\n<Analysis Error> | User KeyboardInterrupt"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq4c3-4-ACSpectrumByCoupler-6895747a43c752d3ff4a9959\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\single_gate\\ac_spectrum.py\", line 246, in _sync_composite_run\n    osc_freq = result.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq55q56q57q58q59q60-BUS10-Channel2-ImpaOptiParams-68956f15973ee1f9ed36e66f\n--------------------------------------------------\nobject of type 'NoneType' has no len()\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 330, in run\n    self._run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_opt.py\", line 539, in _run_analysis\n    self._analysis = run_analysis_process(\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\analysis\\base_analysis.py\", line 144, in run_analysis\n    self._data_processing()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\analysis\\library\\impa_opt_analysis.py\", line 95, in _data_processing\n    x_data = list(range(len(mean_gain)))\nTypeError: object of type 'NoneType' has no len()\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-T2Ramsey-6895689ab0ee2c2a2f355048\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-T2Ramsey-Ramsey(1-unknow)-count=0-z_amp=None-fringe=1-6895689ab0ee2c2a2f35504a\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq25q26q27q28q29q30-BUS5-Channel5-ImpaGain-68955fdc973ee1f9ed36e636\n--------------------------------------------------\nVI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_gain.py\", line 210, in _run_experiment\n    data = self._run_open_mode()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_gain.py\", line 163, in _run_open_mode\n    _, amp_on, _ = self.net_analyzer.read_result(True, True, True)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 79, in read_result\n    phase = self.inst.getExtPhaseList()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\instrument\\E5071C.py\", line 186, in getExtPhaseList\n    self.inst_obj.write(':CALC1:FORM UPH\\n')\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 197, in write\n    count = self.write_raw(message.encode(enco))\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 157, in write_raw\n    return self.visalib.write(self.session, message)[0]\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 548, in write\n    return written, self.handle_return_value(session, status_code)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\highlevel.py\", line 251, in handle_return_value\n    raise errors.VisaIOError(rv)\npyvisa.errors.VisaIOError: VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nbus-16-ImpaGain-68956039c4eedb107ae68ea0\n--------------------------------------------------\nVI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_gain.py\", line 213, in _run_experiment\n    data = self._run_open_mode()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_gain.py\", line 159, in _run_open_mode\n    _, amp_off, _ = self.net_analyzer.read_result(True, True, True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 72, in read_result\n    self.inst.singleTrig()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 148, in singleTrig\n    while self.isMeasuring():\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 135, in isMeasuring\n    runState = self.inst_obj.query(':STAT:OPER:COND?')\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 648, in query\n    return self.read()\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 486, in read\n    message = self._read_raw().decode(enco)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 442, in _read_raw\n    chunk, status = self.visalib.read(self.session, size)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 520, in read\n    return data, self.handle_return_value(session, status_code)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 251, in handle_return_value\n    raise errors.VisaIOError(rv)\npyvisa.errors.VisaIOError: VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq19q20q21q22q23q24-BUS4-Channel4-ImpaGain-68955efa973ee1f9ed36e62a\n--------------------------------------------------\nVI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 271, in _initial\n    self.select_net_analyzer(network_analyzer_type)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 127, in select_net_analyzer\n    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 38, in __init__\n    self.inst.outputON()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\instrument\\E5071C.py\", line 16, in outputON\n    self.inst_obj.write(':OUTPUT ON')\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 197, in write\n    count = self.write_raw(message.encode(enco))\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 157, in write_raw\n    return self.visalib.write(self.session, message)[0]\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 548, in write\n    return written, self.handle_return_value(session, status_code)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\highlevel.py\", line 251, in handle_return_value\n    raise errors.VisaIOError(rv)\npyvisa.errors.VisaIOError: VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2c1-2-CouplerDistortionZZCompositeNew-68955b8343c752d3ff4a97b5\n--------------------------------------------------\n'dict' object has no attribute 't_offset'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 758, in _sync_composite_run\n    await self._run_once(dist_t1_exp)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 543, in _run_once\n    offset = new_dist_t1_exp.analysis.results.t_offset.value\nAttributeError: 'dict' object has no attribute 't_offset'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2c1-2-CouplerDistortionZZCompositeNew-CouplerDistortionZZ(2-unknow)-iter0_xy_delay=2100.0_err_count=0-68955b9043c752d3ff4a97b9\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq-circuit-QCloudPlusV1-6895569ed4d9ebbc58fa140d\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":15,"message":"DataCollector Error || send_execute_data_to_courier | {'code': 500, 'data': {}, 'msg': 'Internal Server Error: BSON document too large (41881431 bytes) - the connected server supports BSON document sizes up to 16793598 bytes.'}"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(75eaf41b-af6f-44d1-82b3-6ea419499ca1) | TID(858c212b-bfe5-4826-b531-478d8fe69d4a) | DataType(iq) | Loop(61)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(25b98a4a-6daa-4e93-9035-440d22369be0) | TID(84e5c32b-9457-4db5-b6d3-b7d7f6e437d3) | DataType(amp_phase) | Loop(101)]"},
{"level":15,"message":"DataCollector Error || send_execute_data_to_courier | {'code': 500, 'data': {}, 'msg': 'Internal Server Error: BSON document too large (41881431 bytes) - the connected server supports BSON document sizes up to 16793598 bytes.'}"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(a8436c44-7197-46c6-aae1-4f9bff936009) | TID(30d7e1d6-4e4b-4958-be23-3d616cfe2c91) | DataType(iq) | Loop(61)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(9c2a4c3c-1b5e-4ea3-b193-ff6960790e77) | TID(adef6b9d-b8d5-4920-9598-547d87dc17e2) | DataType(amp_phase) | Loop(101)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(b7f303c9-1992-4599-92f8-05dce2a6af8f) | TID(906f2214-3cfe-4e93-b660-49ee83b55e1e) | DataType(amp_phase) | Loop(61)]"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq4c3-4-CouplerDistortionZZCompositeNew-6894b57c43c752d3ff4a9691\n--------------------------------------------------\n'dict' object has no attribute 't_offset'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 758, in _sync_composite_run\n    await self._run_once(dist_t1_exp)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 543, in _run_once\n    offset = new_dist_t1_exp.analysis.results.t_offset.value\nAttributeError: 'dict' object has no attribute 't_offset'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2c1-2-CouplerDistortionZZCompositeNew-6894a0043879c2a5a096c64e\n--------------------------------------------------\n'dict' object has no attribute 't_offset'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 758, in _sync_composite_run\n    await self._run_once(dist_t1_exp)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 543, in _run_once\n    offset = new_dist_t1_exp.analysis.results.t_offset.value\nAttributeError: 'dict' object has no attribute 't_offset'\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CouplerDistortionZZ) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\nAssertionError: Inconsistent number of loops in sweep control, details:\n[80, 121]\n"},
{"level":15,"message":"DataCollector Error || send_execute_data_to_courier | {'code': 500, 'data': {}, 'msg': \"Internal Server Error: cannot encode object: array([0.   , 0.005, 0.01 , 0.015, 0.02 , 0.025, 0.03 , 0.035, 0.04 ,\\n       0.045, 0.05 , 0.055, 0.06 , 0.065, 0.07 , 0.075, 0.08 , 0.085,\\n       0.09 , 0.095, 0.1  , 0.105]), of type: <class 'numpy.ndarray'>\"}"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-DistortionT1CompositeNewV2-68949a03b0ee2c2a2f354faa\n--------------------------------------------------\n'dict' object has no attribute 't_offset'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new_v2.py\", line 851, in _sync_composite_run\n    await self._run_once(dist_t1_exp)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new_v2.py\", line 630, in _run_once\n    offset = new_dist_t1_exp.analysis.results.t_offset.value\nAttributeError: 'dict' object has no attribute 't_offset'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-DistortionT1CompositeNewV2-DistortionT1(4-unknow)-iter0_xy_delay=90_err_count=0-68949a17b0ee2c2a2f354fb0\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-DistortionT1CompositeNewV2-6894996db0ee2c2a2f354f9e\n--------------------------------------------------\n'dict' object has no attribute 't_offset'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new_v2.py\", line 851, in _sync_composite_run\n    await self._run_once(dist_t1_exp)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new_v2.py\", line 630, in _run_once\n    offset = new_dist_t1_exp.analysis.results.t_offset.value\nAttributeError: 'dict' object has no attribute 't_offset'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-DistortionT1CompositeNewV2-DistortionT1(7-unknow)-iter0_xy_delay=140_err_count=0-68949992b0ee2c2a2f354fa7\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-QubitSpectrum-689495c111740db8689a81cd\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(b7f303c9-1992-4599-92f8-05dce2a6af8f) | TID(906f2214-3cfe-4e93-b660-49ee83b55e1e) | DataType(amp_phase) | Loop(61)]"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-DistortionT1CompositeNewV2-DistortionT1(29-unknow)-iter0_xy_delay=650.0_err_count=0-6894909ab0ee2c2a2f354ece\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-DistortionT1CompositeNewV2-68948f98b0ee2c2a2f354ea0\n--------------------------------------------------\n'dict' object has no attribute 't_offset'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new_v2.py\", line 851, in _sync_composite_run\n    await self._run_once(dist_t1_exp)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new_v2.py\", line 630, in _run_once\n    offset = new_dist_t1_exp.analysis.results.t_offset.value\nAttributeError: 'dict' object has no attribute 't_offset'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-DistortionT1CompositeNewV2-DistortionT1(10-unknow)-iter0_xy_delay=5.625_err_count=0-68948fe7b0ee2c2a2f354eac\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nbus-5-ImpaOptiParams-68948f1ccdb9adbbae8f43ec\n--------------------------------------------------\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 325, in _run_experiment\n    self.optimize_params()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 245, in optimize_params\n    res = self.ea_optimize(opt_keys, opt_init_v, opt_params, bounds)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 492, in ea_optimize\n    res = ea.optimize(\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\optimize.py\", line 104, in optimize\n    [optPop, lastPop] = algorithm.run(prophetPop)\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\algorithms\\soeas\\DE\\DE_best_1_bin\\soea_DE_best_1_bin_templet.py\", line 63, in run\n    self.call_aimFunc(population)  # 计算种群的目标函数值\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\Algorithm.py\", line 209, in call_aimFunc\n    self.problem.evaluation(pop)  # 调用问题类的evaluation()\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\Problem.py\", line 218, in evaluation\n    return_object = self.evalVars(pop.Phen)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 459, in eval_vars\n    costi = self.cost_fun(vars[i, :])\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 350, in cost_fun\n    self.dc_source.set_dc(dc_channel, arg)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 109, in set_dc\n    super(QdcAIO, self).set_dc(channel, vol)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\qdc.py\", line 59, in set_dc\n    assert -10 <= vol <= 10\nAssertionError\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-CavityFluxScan-68948dc611740db8689a80f9\n--------------------------------------------------\n<Exp(CavityFluxScan) experiment options error> | key(device_conf_path) | value(Z:\\Y8\\zs\\impa.json) | file name does not exist! | Please check options!"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq11-VoltageDriftGradientCalibration-68948e470b16c31a4b2f38d4\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\calibration\\voltage_drift_calibration.py\", line 353, in _sync_composite_run\n    await self._calculate_gradient()\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\calibration\\voltage_drift_calibration.py\", line 241, in _calculate_gradient\n    delta_f = await self._run_child_experiment(self.run_options.guess_vol)\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\calibration\\voltage_drift_calibration.py\", line 217, in _run_child_experiment\n    self.run_options.guess_osc_freq = result.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq11-VoltageDriftGradientCalibration-Ramsey(3-unknow)-idx=2 fringe=20MHz,z_amp=-0.357706-68948e7a0b16c31a4b2f38d9\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq18-VoltageDriftGradientCalibration-68948e350b16c31a4b2f38cf\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\calibration\\voltage_drift_calibration.py\", line 343, in _sync_composite_run\n    is_drift = await self._check_drift()\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\calibration\\voltage_drift_calibration.py\", line 323, in _check_drift\n    delta_f = await self._run_child_experiment(\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\calibration\\voltage_drift_calibration.py\", line 217, in _run_child_experiment\n    self.run_options.guess_osc_freq = result.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq18-VoltageDriftGradientCalibration-Ramsey(1-unknow)-idx=0 fringe=20MHz,z_amp=0.32436target_osc=20MHz-68948e350b16c31a4b2f38d1\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-DistortionT1CompositeNewV2-68948d10b0ee2c2a2f354e79\n--------------------------------------------------\n'dict' object has no attribute 't_offset'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new_v2.py\", line 851, in _sync_composite_run\n    await self._run_once(dist_t1_exp)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new_v2.py\", line 630, in _run_once\n    offset = new_dist_t1_exp.analysis.results.t_offset.value\nAttributeError: 'dict' object has no attribute 't_offset'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-DistortionT1CompositeNewV2-DistortionT1(21-unknow)-iter0_xy_delay=15.0_err_count=0-68948d63b0ee2c2a2f354e90\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq98-VoltageDriftGradientCalibration-68948c8b0b16c31a4b2f38a3\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\calibration\\voltage_drift_calibration.py\", line 353, in _sync_composite_run\n    await self._calculate_gradient()\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\calibration\\voltage_drift_calibration.py\", line 241, in _calculate_gradient\n    delta_f = await self._run_child_experiment(self.run_options.guess_vol)\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\calibration\\voltage_drift_calibration.py\", line 217, in _run_child_experiment\n    self.run_options.guess_osc_freq = result.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq98-VoltageDriftGradientCalibration-Ramsey(2-unknow)-idx=1 fringe=25MHz,z_amp=-0.322677-68948cad0b16c31a4b2f38a7\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nbus-7-ImpaOptiParams-68948a81cdb9adbbae8f43c4\n--------------------------------------------------\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 325, in _run_experiment\n    self.optimize_params()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 245, in optimize_params\n    res = self.ea_optimize(opt_keys, opt_init_v, opt_params, bounds)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 492, in ea_optimize\n    res = ea.optimize(\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\optimize.py\", line 104, in optimize\n    [optPop, lastPop] = algorithm.run(prophetPop)\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\algorithms\\soeas\\DE\\DE_best_1_bin\\soea_DE_best_1_bin_templet.py\", line 63, in run\n    self.call_aimFunc(population)  # 计算种群的目标函数值\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\Algorithm.py\", line 209, in call_aimFunc\n    self.problem.evaluation(pop)  # 调用问题类的evaluation()\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\Problem.py\", line 218, in evaluation\n    return_object = self.evalVars(pop.Phen)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 459, in eval_vars\n    costi = self.cost_fun(vars[i, :])\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 350, in cost_fun\n    self.dc_source.set_dc(dc_channel, arg)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 109, in set_dc\n    super(QdcAIO, self).set_dc(channel, vol)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\qdc.py\", line 59, in set_dc\n    assert -10 <= vol <= 10\nAssertionError\n"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(b7f303c9-1992-4599-92f8-05dce2a6af8f) | TID(906f2214-3cfe-4e93-b660-49ee83b55e1e) | DataType(amp_phase) | Loop(61)]"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nbus-8-ImpaOptiParams-68947ec4cdb9adbbae8f435a\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n"},
vel":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-CoherentPumpExperiment-68947bb7da9558a3e77864d3\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(b7f303c9-1992-4599-92f8-05dce2a6af8f) | TID(906f2214-3cfe-4e93-b660-49ee83b55e1e) | DataType(amp_phase) | Loop(61)]"},
{"level":15,"message":"DataCollector Error || send_execute_data_to_courier | {'code': 500, 'data': {}, 'msg': \"Internal Server Error: cannot encode object: array([-0.14, -0.13, -0.12, -0.11, -0.1 , -0.09, -0.08, -0.07, -0.06,\\n       -0.05, -0.04, -0.03, -0.02, -0.01,  0.  ,  0.01,  0.02,  0.03,\\n        0.04,  0.05,  0.06,  0.07,  0.08,  0.09,  0.1 ,  0.11,  0.12,\\n        0.13,  0.14,  0.15,  0.16,  0.17]), of type: <class 'numpy.ndarray'>\"}"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2-CavityFreqSpectrum-689477beb0ee2c2a2f354e2c\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2-DistortionT1CompositeNew-68947095072c0c23f1186828\n--------------------------------------------------\n'dict' object has no attribute 't_offset'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 758, in _sync_composite_run\n    await self._run_once(dist_t1_exp)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 543, in _run_once\n    offset = new_dist_t1_exp.analysis.results.t_offset.value\nAttributeError: 'dict' object has no attribute 't_offset'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq4-DistortionT1CompositeNew-68947095072c0c23f1186826\n--------------------------------------------------\n'dict' object has no attribute 't_offset'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 758, in _sync_composite_run\n    await self._run_once(dist_t1_exp)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 543, in _run_once\n    offset = new_dist_t1_exp.analysis.results.t_offset.value\nAttributeError: 'dict' object has no attribute 't_offset'\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(DistortionT1) | default | Count-Identity(4)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\nAssertionError: Inconsistent number of loops in sweep control, details:\n[80, 81]\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-T1-6894707e11740db8689a80b7\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-QubitFreqCalibration-689470683879c2a5a096c400\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2-QubitFreqCalibration-689470683879c2a5a096c3fe\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(Ramsey) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\nAssertionError: Inconsistent number of loops in sweep control, details:\n[33, 33, 41, 41]\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(Ramsey) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\nAssertionError: Inconsistent number of loops in sweep control, details:\n[33, 33, 41, 41]\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2-DistortionT1CompositeNew-68946f3615f28a722ac411b6\n--------------------------------------------------\n'dict' object has no attribute 't_offset'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 758, in _sync_composite_run\n    await self._run_once(dist_t1_exp)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 543, in _run_once\n    offset = new_dist_t1_exp.analysis.results.t_offset.value\nAttributeError: 'dict' object has no attribute 't_offset'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq4-DistortionT1CompositeNew-68946f3615f28a722ac411b4\n--------------------------------------------------\n'dict' object has no attribute 't_offset'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 758, in _sync_composite_run\n    await self._run_once(dist_t1_exp)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 543, in _run_once\n    offset = new_dist_t1_exp.analysis.results.t_offset.value\nAttributeError: 'dict' object has no attribute 't_offset'\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(DistortionT1) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\nAssertionError: Inconsistent number of loops in sweep control, details:\n[81, 80]\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-DistortionT1CompositeNew-68946ea20785b601fb42cb88\n--------------------------------------------------\n<Exp(DistortionT1) experiment options error> | key(same_options) | value(False) | field same_options option must be defined in advance! | Please check options!"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SpinEcho-68946c7411740db8689a808a\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq4-ACSpectrum-68946be0b0ee2c2a2f354d33\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite\\single_gate\\ac_spectrum.py\", line 246, in _sync_composite_run\n    osc_freq = result.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-ACSpectrum-Ramsey(6-30)-z_amp=-0.03-68946bf7b0ee2c2a2f354d3a\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SpinEcho-68946bd711740db8689a8087\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(XYZTiming) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-2\nvalue:[4450.0, 4180.0]\nunit: ['q2', 'q4']\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2-DistortionT1CompositeNew-68946a3b49a5e29c29800b5e\n--------------------------------------------------\n'dict' object has no attribute 't_offset'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 758, in _sync_composite_run\n    await self._run_once(dist_t1_exp)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 543, in _run_once\n    offset = new_dist_t1_exp.analysis.results.t_offset.value\nAttributeError: 'dict' object has no attribute 't_offset'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq4-DistortionT1CompositeNew-68946a3b49a5e29c29800b5c\n--------------------------------------------------\n'dict' object has no attribute 't_offset'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 758, in _sync_composite_run\n    await self._run_once(dist_t1_exp)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\distortion_t1_com_new.py\", line 543, in _run_once\n    offset = new_dist_t1_exp.analysis.results.t_offset.value\nAttributeError: 'dict' object has no attribute 't_offset'\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(DistortionT1) | default | Count-Identity(4)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\nAssertionError: Inconsistent number of loops in sweep control, details:\n[81, 80]\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nbus-12-ImpaCavityFluxScan-689469a83d04340aef6a5cd0\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-T2Ramsey-Ramsey(2-unknow)-count=1-z_amp=None-fringe=0.333-6894668a11740db8689a805b\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nbus-4-ImpaCavityFluxScan-689462d63d04340aef6a5c81\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nbus-3-ImpaOptiParams-689462cc3d04340aef6a5c7e\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nbus-3-ImpaOptiParams-689462c83d04340aef6a5c7b\n--------------------------------------------------\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 325, in _run_experiment\n    self.optimize_params()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 245, in optimize_params\n    res = self.ea_optimize(opt_keys, opt_init_v, opt_params, bounds)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 492, in ea_optimize\n    res = ea.optimize(\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\optimize.py\", line 104, in optimize\n    [optPop, lastPop] = algorithm.run(prophetPop)\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\algorithms\\soeas\\DE\\DE_best_1_bin\\soea_DE_best_1_bin_templet.py\", line 72, in run\n    self.call_aimFunc(experimentPop)  # 计算目标函数值\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\Algorithm.py\", line 209, in call_aimFunc\n    self.problem.evaluation(pop)  # 调用问题类的evaluation()\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\Problem.py\", line 218, in evaluation\n    return_object = self.evalVars(pop.Phen)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 459, in eval_vars\n    costi = self.cost_fun(vars[i, :])\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 350, in cost_fun\n    self.dc_source.set_dc(dc_channel, arg)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 109, in set_dc\n    super(QdcAIO, self).set_dc(channel, vol)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\qdc.py\", line 59, in set_dc\n    assert -10 <= vol <= 10\nAssertionError\n"},
"level":10,"message":"Experiment Crash\n--------------------------------------------------\nBUS4-Channel4-ImpaCavityFluxScan-6894569caf54bbef0e3cb513\n--------------------------------------------------\nSpecified value for bus is not of required type <class 'int'>\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 328, in run\n    self._validate_options()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 966, in _validate_options\n    self.experiment_options.validate_options()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 522, in validate_options\n    self._validate(key, value)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 503, in _validate\n    raise TypeError(\nTypeError: Specified value for bus is not of required type <class 'int'>\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-T2Ramsey-6894557711740db8689a800c\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-T2Ramsey-Ramsey(4-unknow)-count=3-z_amp=None-fringe=0.333-6894560111740db8689a8011\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-SingleShot_01-6894545011740db8689a8006\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-T1-6894543a11740db8689a8003\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2-ACSpectrum-689450f9f7f717d707882e0b\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\ac_spectrum.py\", line 246, in _sync_composite_run\n    osc_freq = result.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2-ACSpectrum-Ramsey(1-40)-z_amp=0.0-689450f9f7f717d707882e0d\n--------------------------------------------------\n'NoneType' object is not iterable\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 604, in run_experiment\n    require_id = await self._async_compile()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 541, in _async_compile\n    experiment_file: ExperimentFile = build_experiment_message(exp_env)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 145, in build_experiment_message\n    builder.build()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 344, in build\n    self._prepare_measure_wrapper()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 1046, in _prepare_measure_wrapper\n    for control in self.inst.json_dict.get(\"Read_out_control\"):\nTypeError: 'NoneType' object is not iterable\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2-ACSpectrum-6894507116595f93a8f57dbc\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\ac_spectrum.py\", line 246, in _sync_composite_run\n    osc_freq = result.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2-ACSpectrum-Ramsey(1-40)-z_amp=0.0-6894507116595f93a8f57dbe\n--------------------------------------------------\n'NoneType' object is not iterable\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 604, in run_experiment\n    require_id = await self._async_compile()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 541, in _async_compile\n    experiment_file: ExperimentFile = build_experiment_message(exp_env)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 145, in build_experiment_message\n    builder.build()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 344, in build\n    self._prepare_measure_wrapper()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 1046, in _prepare_measure_wrapper\n    for control in self.inst.json_dict.get(\"Read_out_control\"):\nTypeError: 'NoneType' object is not iterable\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq5~q102-XYCrossPlusRabiWidth-68944bdf6ad6db967de093f6\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq8-XYCrossPlusRabiWidth-XYCrossRabiWidth(4-48)-Tq8-68944c796ad6db967de09419\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq19q8-XYCrossPlusRabiWidth-XYCrossRabiWidth(4-48)-Tq8-XYCrossRabiWidthOnce(1-unknow)-Tq8-Sq19-C0-68944cb06ad6db967de09423\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-T2Ramsey-68944a7dcd914f097738f7c5\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-T2Ramsey-Ramsey(1-unknow)-count=0-z_amp=None-fringe=2-68944a7dcd914f097738f7c7\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq49-XpiDetection-689447e06ad6db967de092ef\n--------------------------------------------------\n'dict' object has no attribute 'Xpi'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite\\single_gate\\xpi_detection.py\", line 134, in _sync_composite_run\n    exp_value = rabi_exp.analysis.results.Xpi.value\nAttributeError: 'dict' object has no attribute 'Xpi'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq49-XpiDetection-RabiScanAmp(1-unknow)-count=0-drive_power=-10-689447e06ad6db967de092f1\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2-ACSpectrum-689445eebdca65c0c82d86c2\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\ac_spectrum.py\", line 246, in _sync_composite_run\n    osc_freq = result.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2-ACSpectrum-Ramsey(1-40)-z_amp=0.0-689445eebdca65c0c82d86c4\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 494, in run\n    self._build_experiment_task()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 428, in _build_experiment_task\n    program_buf, program_pointer_buf = program_to_protobuf(self.program)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 475, in program_to_protobuf\n    program_buf.experiment_file.CopyFrom(experiment_file_to_protobuf(t_program.experiment_file))\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 402, in experiment_file_to_protobuf\n    comp_buf = once_compensate_to_protobuf(comp_dict)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 268, in once_compensate_to_protobuf\n    comp_buf.z_distortion_ab.ab_shape.append(len(arr_list))\nTypeError: object of type 'int' has no len()\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nBUS4-Channel4-ImpaOptiParams-689444e2f90276fec367159c\n--------------------------------------------------\ncannot reshape array of size 480 into shape (0,240)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 330, in run\n    self._run_analysis()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 542, in _run_analysis\n    self._analysis = run_analysis_process(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\base_analysis.py\", line 162, in run_analysis\n    self._visualization()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\standard_curve_analysis.py\", line 143, in _visualization\n    self.drawer.draw_color_map(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\visualization\\curve_drawer.py\", line 366, in draw_color_map\n    map_z = np.reshape(z_data, (len(y_data), len(x_data)), order=\"F\")\n  File \"D:\\miniconda3\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 285, in reshape\n    return _wrapfunc(a, 'reshape', newshape, order=order)\n  File \"D:\\miniconda3\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 59, in _wrapfunc\n    return bound(*args, **kwds)\nValueError: cannot reshape array of size 480 into shape (0,240)\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nBUS4-Channel4-ImpaOptiParams-6894449356d651370589580a\n--------------------------------------------------\n'<=' not supported between instances of 'float' and 'NoneType'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 321, in _run_experiment\n    self.optimize_params()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 241, in optimize_params\n    res = self.ea_optimize(opt_keys, opt_init_v, opt_params, bounds)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 488, in ea_optimize\n    res = ea.optimize(\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\optimize.py\", line 104, in optimize\n    [optPop, lastPop] = algorithm.run(prophetPop)\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\algorithms\\soeas\\DE\\DE_best_1_bin\\soea_DE_best_1_bin_templet.py\", line 63, in run\n    self.call_aimFunc(population)  # 计算种群的目标函数值\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\Algorithm.py\", line 209, in call_aimFunc\n    self.problem.evaluation(pop)  # 调用问题类的evaluation()\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\Problem.py\", line 218, in evaluation\n    return_object = self.evalVars(pop.Phen)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 455, in eval_vars\n    costi = self.cost_fun(vars[i, :])\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 391, in cost_fun\n    and std_gain <= std_gain_throld\nTypeError: '<=' not supported between instances of 'float' and 'NoneType'\n"},
level":10,"message":"Experiment Crash\n--------------------------------------------------\nq53-XpiDetection-689443b478ecdf1e064f67f9\n--------------------------------------------------\n'dict' object has no attribute 'Xpi'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\xpi_detection.py\", line 134, in _sync_composite_run\n    exp_value = rabi_exp.analysis.results.Xpi.value\nAttributeError: 'dict' object has no attribute 'Xpi'\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RabiScanAmp) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 482, in run\n    self._validate_and_merge()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 236, in _validate_and_merge\n    assert len(set(status_list)) == 1, (\nAssertionError: Different sub experiment status participating in parallel, details:\n[4, 4, 4, 4, 4, 1]\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nBUS4-Channel4-ImpaOptiParams-689441c47ed3c75e9b0ea511\n--------------------------------------------------\n'<=' not supported between instances of 'float' and 'NoneType'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 321, in _run_experiment\n    self.optimize_params()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 241, in optimize_params\n    res = self.ea_optimize(opt_keys, opt_init_v, opt_params, bounds)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 488, in ea_optimize\n    res = ea.optimize(\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\optimize.py\", line 104, in optimize\n    [optPop, lastPop] = algorithm.run(prophetPop)\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\algorithms\\soeas\\DE\\DE_best_1_bin\\soea_DE_best_1_bin_templet.py\", line 72, in run\n    self.call_aimFunc(experimentPop)  # 计算目标函数值\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\Algorithm.py\", line 209, in call_aimFunc\n    self.problem.evaluation(pop)  # 调用问题类的evaluation()\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\Problem.py\", line 218, in evaluation\n    return_object = self.evalVars(pop.Phen)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 455, in eval_vars\n    costi = self.cost_fun(vars[i, :])\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 391, in cost_fun\n    and std_gain <= std_gain_throld\nTypeError: '<=' not supported between instances of 'float' and 'NoneType'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nBUS4-Channel4-ImpaOptiParams-689441b07ed3c75e9b0ea50e\n--------------------------------------------------\n'<=' not supported between instances of 'float' and 'NoneType'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 321, in _run_experiment\n    self.optimize_params()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 241, in optimize_params\n    res = self.ea_optimize(opt_keys, opt_init_v, opt_params, bounds)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 488, in ea_optimize\n    res = ea.optimize(\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\optimize.py\", line 104, in optimize\n    [optPop, lastPop] = algorithm.run(prophetPop)\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\algorithms\\soeas\\DE\\DE_best_1_bin\\soea_DE_best_1_bin_templet.py\", line 72, in run\n    self.call_aimFunc(experimentPop)  # 计算目标函数值\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\Algorithm.py\", line 209, in call_aimFunc\n    self.problem.evaluation(pop)  # 调用问题类的evaluation()\n  File \"D:\\miniconda3\\lib\\site-packages\\geatpy\\Problem.py\", line 218, in evaluation\n    return_object = self.evalVars(pop.Phen)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 455, in eval_vars\n    costi = self.cost_fun(vars[i, :])\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 391, in cost_fun\n    and std_gain <= std_gain_throld\nTypeError: '<=' not supported between instances of 'float' and 'NoneType'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nBUS4-Channel4-ImpaOptiParams-689441767ed3c75e9b0ea50b\n--------------------------------------------------\ncannot reshape array of size 480 into shape (0,240)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 330, in run\n    self._run_analysis()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_opt.py\", line 541, in _run_analysis\n    self._analysis = run_analysis_process(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\base_analysis.py\", line 162, in run_analysis\n    self._visualization()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\standard_curve_analysis.py\", line 143, in _visualization\n    self.drawer.draw_color_map(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\visualization\\curve_drawer.py\", line 366, in draw_color_map\n    map_z = np.reshape(z_data, (len(y_data), len(x_data)), order=\"F\")\n  File \"D:\\miniconda3\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 285, in reshape\n    return _wrapfunc(a, 'reshape', newshape, order=order)\n  File \"D:\\miniconda3\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 59, in _wrapfunc\n    return bound(*args, **kwds)\nValueError: cannot reshape array of size 480 into shape (0,240)\n"},
{"level":15,"message":"DataCollector Error || batch_end_signal | {'code': 404, 'data': {}, 'msg': 'batch(68942434bcdfd6b32dd99c08) is not exist'}"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2-SingleShot_01-689422a945fe58ddaa4fe6d1\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 494, in run\n    self._build_experiment_task()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 428, in _build_experiment_task\n    program_buf, program_pointer_buf = program_to_protobuf(self.program)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\proto_utils.py\", line 475, in program_to_protobuf\n    program_buf.experiment_file.CopyFrom(experiment_file_to_protobuf(t_program.experiment_file))\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\proto_utils.py\", line 402, in experiment_file_to_protobuf\n    comp_buf = once_compensate_to_protobuf(comp_dict)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\proto_utils.py\", line 268, in once_compensate_to_protobuf\n    comp_buf.z_distortion_ab.ab_shape.append(len(arr_list))\nTypeError: object of type 'int' has no len()\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2-SingleShot_01-6894225745fe58ddaa4fe6ce\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 494, in run\n    self._build_experiment_task()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 428, in _build_experiment_task\n    program_buf, program_pointer_buf = program_to_protobuf(self.program)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\proto_utils.py\", line 475, in program_to_protobuf\n    program_buf.experiment_file.CopyFrom(experiment_file_to_protobuf(t_program.experiment_file))\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\proto_utils.py\", line 402, in experiment_file_to_protobuf\n    comp_buf = once_compensate_to_protobuf(comp_dict)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\proto_utils.py\", line 268, in once_compensate_to_protobuf\n    comp_buf.z_distortion_ab.ab_shape.append(len(arr_list))\nTypeError: object of type 'int' has no len()\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq19q20q21q22q23q24-BUS4-Channel4-ImpaOptiParams-689418aec8f7347e909bc1a2\n--------------------------------------------------\nindex 238 is out of bounds for axis 0 with size 7\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 330, in run\n    self._run_analysis()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_opt.py\", line 541, in _run_analysis\n    self._analysis = run_analysis_process(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\analysis\\base_analysis.py\", line 152, in run_analysis\n    self._extract_result()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\analysis\\library\\impa_opt_analysis.py\", line 119, in _extract_result\n    _, flux, freq, power, *_ = source_data_save[index]\nIndexError: index 238 is out of bounds for axis 0 with size 7\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq57-Ramsey-689412d9d6c1a0f0a3d84374\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 134, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 216, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq19q20q21q22q23q24-BUS4-Channel4-ImpaOptiParams-68940f098358ef09d7f08934\n--------------------------------------------------\n__init__() got an unexpected keyword argument 'metaditggigiasdasbzghxcasdihiata'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 330, in run\n    self._run_analysis()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_opt.py\", line 527, in _run_analysis\n    exp_data = ExperimentData(\nTypeError: __init__() got an unexpected keyword argument 'metaditggigiasdasbzghxcasdihiata'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq19q20q21q22q23q24-BUS4-Channel4-ImpaOptiParams-68940d2d3f9856d49bab7f15\n--------------------------------------------------\nobject of type 'NoneType' has no len()\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 330, in run\n    self._run_analysis()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_opt.py\", line 539, in _run_analysis\n    self._analysis = run_analysis_process(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\analysis\\base_analysis.py\", line 144, in run_analysis\n    self._data_processing()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-visage\\pyQCat\\analysis\\library\\impa_opt_analysis.py\", line 95, in _data_processing\n    x_data = list(range(len(mean_gain)))\nTypeError: object of type 'NoneType' has no len()\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq19-DetuneCalibration-6893923aa38f17f235b39223\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq19-DetuneCalibration-APEComposite(1-2)-RoughScan-6893923ba38f17f235b3922f\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq11-DetuneCalibration-68938ebea38f17f235b38f37\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq11-DetuneCalibration-APEComposite(1-2)-RoughScan-68938ebfa38f17f235b38f41\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq88-DetuneCalibration-6893862ca38f17f235b385dc\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq88-DetuneCalibration-APEComposite(1-2)-RoughScan-6893862da38f17f235b385fb\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq23-DetuneCalibration-68937ba9a38f17f235b37a01\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq23-DetuneCalibration-APEComposite(1-2)-RoughScan-68937baaa38f17f235b37a12\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-RabiScanAmp-6893699d45fe58ddaa4fe627\n--------------------------------------------------\n<Instrument invalid value error> | intermediate_frequency(782.129) is less than the minimum of 800 | Maybe bit parameters error, please check!"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq78-DetuneCalibration-689361f4a38f17f235b36456\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq36-DetuneCalibration-APEComposite(1-2)-RoughScan-68932de10780ecb62c0d47b1\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq29-DetuneCalibration-68932de00780ecb62c0d4777\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq5-DetuneCalibration-APEComposite(1-2)-RoughScan-68932de10780ecb62c0d47b3\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq81-DetuneCalibration-68932de00780ecb62c0d4783\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq77-DetuneCalibration-68932de00780ecb62c0d4792\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq97-DetuneCalibration-APEComposite(1-2)-RoughScan-68932de10780ecb62c0d47af\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-DetuneCalibration-APEComposite(1-2)-RoughScan-68932de00780ecb62c0d479b\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq43-DetuneCalibration-68932de00780ecb62c0d4780\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq57-DetuneCalibration-68932de00780ecb62c0d4774\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq84-DetuneCalibration-APEComposite(1-2)-RoughScan-68932de10780ecb62c0d47a7\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq97-DetuneCalibration-APEComposite(1-2)-RoughScan-APE(2-3)-N=7-68932de30780ecb62c0d47d1\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 134, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 216, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 189, in _initialize\n    self._data_filter()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 194, in _data_filter\n    smooth_y = savgol_filter(analysis_data.y, **self.data_filter)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\scipy\\signal\\_savitzky_golay.py\", line 345, in savgol_filter\n    raise ValueError(\"If mode is 'interp', window_length must be less \"\nValueError: If mode is 'interp', window_length must be less than or equal to the size of x.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: If mode is 'interp', window_length must be less than or equal to the size of x.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq92-DetuneCalibration-68932de00780ecb62c0d4795\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq36-DetuneCalibration-APEComposite(1-2)-RoughScan-APE(2-3)-N=7-68932de30780ecb62c0d47d2\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 134, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 216, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 189, in _initialize\n    self._data_filter()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 194, in _data_filter\n    smooth_y = savgol_filter(analysis_data.y, **self.data_filter)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\scipy\\signal\\_savitzky_golay.py\", line 345, in savgol_filter\n    raise ValueError(\"If mode is 'interp', window_length must be less \"\nValueError: If mode is 'interp', window_length must be less than or equal to the size of x.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: If mode is 'interp', window_length must be less than or equal to the size of x.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq32-DetuneCalibration-68932de00780ecb62c0d476e\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-DetuneCalibration-APEComposite(1-2)-RoughScan-APE(2-3)-N=7-68932de20780ecb62c0d47c7\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 134, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 216, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 189, in _initialize\n    self._data_filter()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 194, in _data_filter\n    smooth_y = savgol_filter(analysis_data.y, **self.data_filter)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\scipy\\signal\\_savitzky_golay.py\", line 345, in savgol_filter\n    raise ValueError(\"If mode is 'interp', window_length must be less \"\nValueError: If mode is 'interp', window_length must be less than or equal to the size of x.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: If mode is 'interp', window_length must be less than or equal to the size of x.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq32-DetuneCalibration-APEComposite(1-2)-RoughScan-APE(1-3)-N=6-68932de20780ecb62c0d47b9\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 134, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 216, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 189, in _initialize\n    self._data_filter()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 194, in _data_filter\n    smooth_y = savgol_filter(analysis_data.y, **self.data_filter)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\scipy\\signal\\_savitzky_golay.py\", line 345, in savgol_filter\n    raise ValueError(\"If mode is 'interp', window_length must be less \"\nValueError: If mode is 'interp', window_length must be less than or equal to the size of x.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: If mode is 'interp', window_length must be less than or equal to the size of x.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq92-RBSingle-68932c3d8cf64da1541d2a1a\n--------------------------------------------------\n'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 1680.0 MHz'"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq21-RBSingle-68932c3d8cf64da1541d29d3\n--------------------------------------------------\n'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 1680.0 MHz'"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq18-RBSingle-68932c3d8cf64da1541d29d0\n--------------------------------------------------\n'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 1680.0 MHz'"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq11-RBSingle-68932c3d8cf64da1541d29c9\n--------------------------------------------------\n'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 1680.0 MHz'"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq9-RBSingle-68932c3d8cf64da1541d29c7\n--------------------------------------------------\n'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 1450.0 MHz'"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-RBSingle-68932c3d8cf64da1541d29c5\n--------------------------------------------------\n'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 1450.0 MHz'"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RBSingle) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/project/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 495, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/project/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-5\nvalue:[3550.0, 3550.0, 3000.0, 3550.0, 3550.0, 3550.0]\nunit: ['q6', 'q13', 'q35', 'q37', 'q63', 'q85']\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RBSingle) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/project/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 495, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/project/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-5\nvalue:[3550.0, 3000.0, 3550.0, 3550.0, 3550.0, 3550.0]\nunit: ['q13', 'q35', 'q6', 'q37', 'q63', 'q85']\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq3-XYZTiming-689326a0179fc102a5a2de78\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-T2SpinEcho-689321cf179fc102a5a2de41\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-T2SpinEcho-SpinEcho(1-unknow)-count=0-z_amp=None-fringe=0.5-689321cf179fc102a5a2de43\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq3-T2Ramsey-68931affd07196db777be098\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq3-T2Ramsey-Ramsey(1-unknow)-count=0-z_amp=None-fringe=2-68931affd07196db777be09a\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq3-T2Ramsey-68931af1d07196db777be093\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq3-T2Ramsey-Ramsey(1-unknow)-count=0-z_amp=None-fringe=2.5-68931af1d07196db777be095\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq3-T2Ramsey-68931ae4d07196db777be08e\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq3-T2Ramsey-Ramsey(1-unknow)-count=0-z_amp=None-fringe=3-68931ae4d07196db777be090\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq5~q102-XYCrossPlusRabiWidth-689314af6ad6db967de08cf5\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq17-XYCrossPlusRabiWidth-XYCrossRabiWidth(8-49)-Tq17-689318f86ad6db967de08d50\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq17q23-XYCrossPlusRabiWidth-XYCrossRabiWidth(8-49)-Tq17-XYCrossRabiWidthOnce(1-unknow)-Tq17-Sq23-C0-6893197a6ad6db967de08d5d\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-T1-6893190011740db8689a7f72\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq-circuit-QCloudPlusV1-689316bcbc11c5a0646e4345\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-5\nvalue:[3550.0, 3550.0, 3550.0, 3000.0]\nunit: ['q37', 'q63', 'q85', 'q35']\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq85-DetuneCalibration-68931436e0884d7767b14527\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq85-DetuneCalibration-APEComposite(1-2)-RoughScan-68931437e0884d7767b1453b\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq85-DetuneCalibration-68931323e0884d7767b14436\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq85-DetuneCalibration-APEComposite(1-2)-RoughScan-68931323e0884d7767b14447\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-ReadoutFreqCalibrate-6893131fd07196db777bdf4b\n--------------------------------------------------\n'int' object has no attribute 'value'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 518, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 469, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 503, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 91, in _extract_result\n    self._quality = quality[np.argmin([q.value for q in quality])]\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 91, in <listcomp>\n    self._quality = quality[np.argmin([q.value for q in quality])]\nAttributeError: 'int' object has no attribute 'value'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-ReadoutFreqCalibrate-CavityFreqSpectrum(2-2)-xy_pulse amp=0.68-6893131fd07196db777bdf4e\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-XpiDetection-6893120b11740db8689a7dbc\n--------------------------------------------------\n'dict' object has no attribute 'Xpi'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite\\single_gate\\xpi_detection.py\", line 134, in _sync_composite_run\n    exp_value = rabi_exp.analysis.results.Xpi.value\nAttributeError: 'dict' object has no attribute 'Xpi'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-XpiDetection-RabiScanAmp(2-unknow)-count=1-drive_power=-11.6-6893121011740db8689a7dbf\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq78-DetuneCalibration-68931051e0884d7767b142ff\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq78-DetuneCalibration-APEComposite(1-2)-RoughScan-68931051e0884d7767b14303\n--------------------------------------------------\nmin() arg is an empty sequence\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 523, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 474, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 508, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-QubitSpectrum-6893100211740db8689a7d70\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq78-DetuneCalibration-68930ff7e0884d7767b142dd\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq78-DetuneCalibration-APEComposite(1-2)-RoughScan-68930ff7e0884d7767b142e1\n--------------------------------------------------\nmin() arg is an empty sequence\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 523, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 474, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 508, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-QubitSpectrum-68930ff111740db8689a7d6d\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-ReadoutFreqCalibrate-68930ef811740db8689a7d13\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-ReadoutFreqCalibrate-CavityFreqSpectrum(2-2)-xy_pulse amp=0.4825-68930ef811740db8689a7d16\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-ReadoutFreqCalibrate-CavityFreqSpectrum(1-2)-xy_pulse amp=0-68930ef811740db8689a7d15\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq74-DetuneCalibration-68930dffe0884d7767b14204\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq74-DetuneCalibration-APEComposite(1-2)-RoughScan-68930dffe0884d7767b14210\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq102-DetuneCalibration-68930bf7e0884d7767b140ec\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq102-DetuneCalibration-APEComposite(1-2)-RoughScan-68930bf7e0884d7767b140f6\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq102-DetuneCalibration-68930ad2e0884d7767b13fdd\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq102-DetuneCalibration-APEComposite(1-2)-RoughScan-68930ad2e0884d7767b13ff8\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq82-DetuneCalibration-6893087ce0884d7767b13d74\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq82-DetuneCalibration-APEComposite(1-2)-RoughScan-6893087ce0884d7767b13d91\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq28-DetuneCalibration-68930767e0884d7767b13c56\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq28-DetuneCalibration-APEComposite(1-2)-RoughScan-68930768e0884d7767b13c6c\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq30-DetuneCalibration-68930645e0884d7767b13b22\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq30-DetuneCalibration-APEComposite(1-2)-RoughScan-68930646e0884d7767b13b39\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq30-DetuneCalibration-68930519e0884d7767b139eb\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq30-DetuneCalibration-APEComposite(1-2)-RoughScan-6893051ae0884d7767b13a00\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-CavityFreqSpectrum-6893039c11740db8689a7cfe\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-CavityFreqSpectrum-6892fa05bf52d4032d759ce0\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 492, in run\n    self._transformer()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 364, in _transformer\n    transformer.run()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_transformer.py\", line 277, in run\n    self._correct_ac_bias()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_transformer.py\", line 62, in _correct_ac_bias\n    ac_crosstalk_inv = TransformToolAPI.get_ac_crosstalk(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_transformer.py\", line 311, in get_ac_crosstalk\n    ac_crosstalk = np.array(crosstalk_dict.get(\"ac_crosstalk\"))\nValueError: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (18,) + inhomogeneous part.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq7-DetuneCalibration-6892f63211740db8689a7cdc\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-DetuneCalibration-APEComposite(1-2)-RoughScan-6892f63211740db8689a7ce0\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-DetuneCalibration-APEComposite(1-2)-RoughScan-APE(3-3)-N=8-6892f63211740db8689a7ce3\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-DetuneCalibration-APEComposite(1-2)-RoughScan-APE(2-3)-N=7-6892f63211740db8689a7ce2\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-DetuneCalibration-APEComposite(1-2)-RoughScan-APE(1-3)-N=6-6892f63211740db8689a7ce1\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SingleShot_01-6892efe011740db8689a7c75\n--------------------------------------------------\n<Instrument invalid value error> | output_frequency(5996.5257)  is not a multiple of 0.001 | Maybe bit parameters error, please check!"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SingleShot_01-6892ed2c11740db8689a7bfe\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SingleShot_01-6892e69911740db8689a7bfb\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SingleShot_01-6892e66911740db8689a7bf8\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SingleShot_01-6892e5cc11740db8689a7bf5\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq28-DetuneCalibration-6892e5807881929c71495fa4\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq28-DetuneCalibration-APEComposite(1-2)-RoughScan-6892e5807881929c71495fc5\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-CavityFreqSpectrum-6892e56845fe58ddaa4fe564\n--------------------------------------------------\n<Instrument invalid value error> | intermediate_frequency(1700.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SingleShot_01-6892e55fb54c52f2bf9557cf\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4c3-4-ACBiasTunable-6892e55645fe58ddaa4fe500\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2c1-2-ACBiasTunable-6892e55645fe58ddaa4fe502\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4c3-4-ACBiasTunable-CavityFreqSpectrum(27-46)-ac_bias = 0.07v-6892e55645fe58ddaa4fe53a\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2c1-2-ACBiasTunable-CavityFreqSpectrum(1-46)-ac_bias = -0.45v-6892e55645fe58ddaa4fe507\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4c3-4-ACBiasTunable-CavityFreqSpectrum(1-46)-ac_bias = -0.45v-6892e55645fe58ddaa4fe506\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SingleShot_01-6892e54d5c9926f87002a983\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SingleShot_01-6892e53b152616ef22fdd9bb\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SingleShot_01-6892e4b3152616ef22fdd9b8\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SingleShot_01-6892e4a5510cfc99cf1232b1\n--------------------------------------------------\n<Analysis Error> | User KeyboardInterrupt"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2c1-2-ACBiasTunable-6892e51445fe58ddaa4fe49e\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4c3-4-ACBiasTunable-6892e51445fe58ddaa4fe49c\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2c1-2-ACBiasTunable-CavityFreqSpectrum(23-46)-ac_bias = -0.01v-6892e51445fe58ddaa4fe4cf\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2c1-2-ACBiasTunable-CavityFreqSpectrum(30-46)-ac_bias = 0.13v-6892e51445fe58ddaa4fe4dd\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2c1-2-ACBiasTunable-CavityFreqSpectrum(37-46)-ac_bias = 0.27v-6892e51445fe58ddaa4fe4eb\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2c1-2-ACBiasTunable-6892e4ea45fe58ddaa4fe43a\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4c3-4-ACBiasTunable-6892e4ea45fe58ddaa4fe438\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2c1-2-ACBiasTunable-CavityFreqSpectrum(15-46)-ac_bias = -0.17v-6892e4ea45fe58ddaa4fe45b\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2c1-2-ACBiasTunable-CavityFreqSpectrum(17-46)-ac_bias = -0.13v-6892e4ea45fe58ddaa4fe45f\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2c1-2-ACBiasTunable-CavityFreqSpectrum(29-46)-ac_bias = 0.11v-6892e4ea45fe58ddaa4fe477\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4c3-4-ACBiasTunable-CavityFreqSpectrum(9-46)-ac_bias = -0.29v-6892e4ea45fe58ddaa4fe44e\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4c3-4-ACBiasTunable-CavityFreqSpectrum(7-46)-ac_bias = -0.33v-6892e4ea45fe58ddaa4fe44a\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4c3-4-ACBiasTunable-CavityFreqSpectrum(8-46)-ac_bias = -0.31v-6892e4ea45fe58ddaa4fe44c\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4c3-4-ACBiasTunable-CavityFreqSpectrum(6-46)-ac_bias = -0.35v-6892e4ea45fe58ddaa4fe448\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2c1-2-ACBiasTunable-CavityFreqSpectrum(3-46)-ac_bias = -0.41v-6892e4ea45fe58ddaa4fe443\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4c3-4-ACBiasTunable-CavityFreqSpectrum(5-46)-ac_bias = -0.37v-6892e4ea45fe58ddaa4fe446\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4c3-4-ACBiasTunable-CavityFreqSpectrum(1-46)-ac_bias = -0.45v-6892e4ea45fe58ddaa4fe43e\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2c1-2-ACBiasTunable-CavityFreqSpectrum(2-46)-ac_bias = -0.43v-6892e4ea45fe58ddaa4fe441\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2c1-2-ACBiasTunable-CavityFreqSpectrum(1-46)-ac_bias = -0.45v-6892e4ea45fe58ddaa4fe43f\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1560.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4c3-4-ACBiasTunable-CavityFreqSpectrum(2-46)-ac_bias = -0.43v-6892e4ea45fe58ddaa4fe440\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 137, in build_experiment_message\n    builder.initial()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 340, in initial\n    self._initialize_experiment()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 539, in _initialize_experiment\n    self._bind_qubits_inst()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 460, in _bind_qubits_inst\n    self._bind_qubit_drive(qubit)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_builder.py\", line 528, in _bind_qubit_drive\n    self.inst.set_intermediate_freq(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 490, in set_intermediate_freq\n    self._validate_value(param_name, value, *param_range)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\instrument_v1.py\", line 1312, in _validate_value\n    raise InstrumentInvalidValueError(\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.InstrumentInvalidValueError: <Instrument invalid value error> | intermediate_frequency(1890.0) is more than the maximum of 1300 | Maybe bit parameters error, please check!\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SingleShot_01-6892e464c26e81862bd1b792\n--------------------------------------------------\n<Analysis Error> | User KeyboardInterrupt"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq7-DetuneCalibration-6892e3a8880c5dfcd2ccd020\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\zs\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\zs\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\zs\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq7-DetuneCalibration-APEComposite(1-2)-RoughScan-6892e3a8880c5dfcd2ccd024\n--------------------------------------------------\nmin() arg is an empty sequence\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\zs\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\zs\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 518, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\zs\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 469, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\zs\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 503, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\zs\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\zs\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\zs\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\zs\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq7-F12Calibration-6892e217152616ef22fdd9a5\n--------------------------------------------------\n'int' object has no attribute 'items'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 518, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 469, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 503, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\analysis\\curve_analysis.py\", line 431, in run_analysis\n    self._visualization()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\analysis\\library\\qubit_freq_cali_analysis.py\", line 117, in _visualization\n    for index2, item in enumerate(datas.items()):\nAttributeError: 'int' object has no attribute 'items'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-F12Calibration-RamseyF12(2-2)-fringe=-10MHz-6892e217152616ef22fdd9a8\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-RabiScanWidthF12-6892e1f2152616ef22fdd99f\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-RabiScanAmp-6892e1af152616ef22fdd996\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq7-SweepAmpRabiWidth-6892e132152616ef22fdd927\n--------------------------------------------------\nlist index out of range\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 518, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 469, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 503, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\analysis\\library\\detune_width_analysis.py\", line 97, in run_analysis\n    super().run_analysis()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\analysis\\curve_analysis.py\", line 431, in run_analysis\n    self._visualization()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\analysis\\library\\detune_width_analysis.py\", line 75, in _visualization\n    child_data = self.experiment_data.child_data(index=i)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\structures.py\", line 726, in child_data\n    return list(self._child_data.values())[index]\nIndexError: list index out of range\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepAmpRabiWidth-RabiScanWidthAmp(2-101)-Amp=0.01-6892e132152616ef22fdd92a\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepAmpRabiWidth-RabiScanWidthAmp(3-101)-Amp=0.02-6892e132152616ef22fdd92b\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepAmpRabiWidth-RabiScanWidthAmp(10-101)-Amp=0.09-6892e132152616ef22fdd932\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepAmpRabiWidth-RabiScanWidthAmp(9-101)-Amp=0.08-6892e132152616ef22fdd931\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepAmpRabiWidth-RabiScanWidthAmp(6-101)-Amp=0.05-6892e132152616ef22fdd92e\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepAmpRabiWidth-RabiScanWidthAmp(4-101)-Amp=0.03-6892e132152616ef22fdd92c\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepAmpRabiWidth-RabiScanWidthAmp(7-101)-Amp=0.06-6892e132152616ef22fdd92f\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepAmpRabiWidth-RabiScanWidthAmp(8-101)-Amp=0.07-6892e132152616ef22fdd930\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepAmpRabiWidth-RabiScanWidthAmp(5-101)-Amp=0.04-6892e132152616ef22fdd92d\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq7-SweepDetuneRabiWidth-6892e124152616ef22fdd90e\n--------------------------------------------------\nlist index out of range\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 518, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 469, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 503, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\analysis\\library\\detune_width_analysis.py\", line 97, in run_analysis\n    super().run_analysis()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\analysis\\curve_analysis.py\", line 431, in run_analysis\n    self._visualization()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\analysis\\library\\detune_width_analysis.py\", line 75, in _visualization\n    child_data = self.experiment_data.child_data(index=i)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\structures.py\", line 726, in child_data\n    return list(self._child_data.values())[index]\nIndexError: list index out of range\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepDetuneRabiWidth-RabiScanWidthDetune(3-21)-Detune=-236-6892e125152616ef22fdd912\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepDetuneRabiWidth-RabiScanWidthDetune(6-21)-Detune=-230-6892e125152616ef22fdd915\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepDetuneRabiWidth-RabiScanWidthDetune(7-21)-Detune=-228-6892e125152616ef22fdd916\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepDetuneRabiWidth-RabiScanWidthDetune(4-21)-Detune=-234-6892e125152616ef22fdd913\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepDetuneRabiWidth-RabiScanWidthDetune(2-21)-Detune=-238-6892e124152616ef22fdd911\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepDetuneRabiWidth-RabiScanWidthDetune(8-21)-Detune=-226-6892e125152616ef22fdd917\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepDetuneRabiWidth-RabiScanWidthDetune(10-21)-Detune=-222-6892e125152616ef22fdd919\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepDetuneRabiWidth-RabiScanWidthDetune(5-21)-Detune=-232-6892e125152616ef22fdd914\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-F12Calibration-6892e061152616ef22fdd8f9\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-F12Calibration-RamseyF12(2-2)-fringe=-50MHz-6892e061152616ef22fdd8fc\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-F12Calibration-RamseyF12(1-2)-fringe=50MHz-6892e061152616ef22fdd8fb\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-RabiScanAmpF12-6892e01e152616ef22fdd8ea\n--------------------------------------------------\n<Base Qubit type error> | F12 drive freq is null"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-RabiScanAmpF12-6892e010152616ef22fdd8e7\n--------------------------------------------------\n<Base Qubit type error> | F12 drive freq is null"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-RabiScanAmp-6892e00d152616ef22fdd8e4\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq84-DetuneCalibration-6892da917881929c7149528a\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq84-DetuneCalibration-APEComposite(1-2)-RoughScan-6892da927881929c71495299\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq84-DetuneCalibration-6892d9917881929c714951d2\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq84-DetuneCalibration-APEComposite(1-2)-RoughScan-6892d9917881929c714951e7\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq29-DetuneCalibration-6892d86e7881929c714950bf\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq29-DetuneCalibration-APEComposite(1-2)-RoughScan-6892d86f7881929c714950d5\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq84-DetuneCalibration-6892d86e7881929c714950b0\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq84-DetuneCalibration-APEComposite(1-2)-RoughScan-6892d86e7881929c714950cb\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq36-DetuneCalibration-6892d74e7881929c71494fa0\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq36-DetuneCalibration-APEComposite(1-2)-RoughScan-6892d74e7881929c71494fb7\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq36-DetuneCalibration-6892d4037881929c71494cf6\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq36-DetuneCalibration-APEComposite(1-2)-RoughScan-6892d4037881929c71494d04\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq7-XpiDetection-6892d25a450cc0599a91e9fe\n--------------------------------------------------\n'dict' object has no attribute 'Xpi'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite\\single_gate\\xpi_detection.py\", line 134, in _sync_composite_run\n    exp_value = rabi_exp.analysis.results.Xpi.value\nAttributeError: 'dict' object has no attribute 'Xpi'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-XpiDetection-RabiScanAmp(1-unknow)-count=0-drive_power=-10-6892d25a450cc0599a91ea00\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq43-DetuneCalibration-6892d1c37881929c71494b15\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq43-DetuneCalibration-APEComposite(1-2)-RoughScan-6892d1c47881929c71494b27\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq7-ReadoutFreqCalibrate-6892c8a8450cc0599a91e897\n--------------------------------------------------\nzero-size array to reduction operation minimum which has no identity\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 518, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 469, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 503, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 114, in _extract_result\n    min_fr_index, max_fr_index = np.amin(indices), np.amax(indices)\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2970, in amin\n    return _wrapreduction(a, np.minimum, 'min', axis, None, out,\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation minimum which has no identity\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq7-F12CavityShift-6892c89d450cc0599a91e890\n--------------------------------------------------\nlist index out of range\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 518, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 469, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 503, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\analysis\\library\\f012_cavity_shift_analysis.py\", line 82, in _extract_result\n    child_data = self.experiment_data.child_data(index=i)\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\structures.py\", line 726, in child_data\n    return list(self._child_data.values())[index]\nIndexError: list index out of range\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-F12CavityShift-CavityFreqSpectrum(1-3)-xy_pulse amp=0-6892c89d450cc0599a91e892\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-F12CavityShift-CavityFreqSpectrum(3-3)-xy_pulse amp=0.78-6892c89d450cc0599a91e894\n--------------------------------------------------\n<Base Qubit type error> | F12 drive freq is null"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-DetuneCalibration-APEComposite(1-2)-RoughScan-APE(3-3)-N=8-6892c822b59393c78b98c28e\n--------------------------------------------------\n<Analysis Error> | User KeyboardInterrupt"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq19-VoltageDriftGradientCalibration-6892c7a821c5e7f185d136de\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite\\calibration\\voltage_drift_calibration.py\", line 353, in _sync_composite_run\n    await self._calculate_gradient()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite\\calibration\\voltage_drift_calibration.py\", line 241, in _calculate_gradient\n    delta_f = await self._run_child_experiment(self.run_options.guess_vol)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite\\calibration\\voltage_drift_calibration.py\", line 217, in _run_child_experiment\n    self.run_options.guess_osc_freq = result.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq19-VoltageDriftGradientCalibration-Ramsey(2-unknow)-idx=1 fringe=25MHz,z_amp=0.014738-6892c7b321c5e7f185d136e2\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq19-QubitFreqCalibration-6892c6ed21c5e7f185d136c9\n--------------------------------------------------\nindex 1 is out of bounds for axis 0 with size 1\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 523, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 474, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 508, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\analysis\\library\\qubit_freq_cali_analysis.py\", line 77, in _extract_result\n    df_2nd = x[1] - y[1] if x[1] > 0 else x[1] + y[1]\nIndexError: index 1 is out of bounds for axis 0 with size 1\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq19-QubitFreqCalibration-Ramsey(2-2)-fringe=-41MHz-6892c6ed21c5e7f185d136cc\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SingleShot_01-6892c5b9450cc0599a91e88a\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 483, in run\n    self._transformer()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 355, in _transformer\n    transformer.run()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_transformer.py\", line 280, in run\n    self._get_ctrl_ac_crosstalk_inv()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_transformer.py\", line 98, in _get_ctrl_ac_crosstalk_inv\n    self._ac_crosstalk_inv = TransformToolAPI.get_ac_crosstalk(\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_transformer.py\", line 312, in get_ac_crosstalk\n    ac_crosstalk = np.array(crosstalk_dict.get(\"ac_crosstalk\"))\nValueError: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (620,) + inhomogeneous part.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-ACBiasTunable-CavityFreqSpectrum(28-54)-ac_bias = 0.005v-68929b314b9402e9faf2dcf2\n--------------------------------------------------\n<Analysis Error> | User KeyboardInterrupt"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-ACBiasTunable-CavityFreqSpectrum(22-54)-ac_bias = -0.085v-68929b314b9402e9faf2dcec\n--------------------------------------------------\n<Analysis Error> | User KeyboardInterrupt"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-ACBiasTunable-CavityFreqSpectrum(16-54)-ac_bias = -0.175v-68929b314b9402e9faf2dce6\n--------------------------------------------------\n<Analysis Error> | User KeyboardInterrupt"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-ACBiasTunable-CavityFreqSpectrum(19-54)-ac_bias = -0.13v-68929b314b9402e9faf2dce9\n--------------------------------------------------\n<Analysis Error> | User KeyboardInterrupt"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq32-DetuneCalibration-6892bf0f7881929c71493ad2\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq32-DetuneCalibration-APEComposite(1-2)-RoughScan-6892bf107881929c71493aea\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-CavityFreqSpectrum-6892bcd8c7ba9bd28a8da4d3\n--------------------------------------------------\n<Instrument invalid value error> | output_frequency(465937) is more than the maximum of 8250 | Maybe bit parameters error, please check!"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-SingleShot_01-6892b7fc46d2c290e6f7a396\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(bc6343a0-e90d-4780-94ff-2780a3012ea4) | TID(f8dafdae-00d2-45d4-a76c-c2a93221174c) | DataType(amp_phase) | Loop(101)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(0c8a3f30-d07e-408a-8dfe-2917b931e1fa) | TID(1ab3f543-c8ec-4c4f-8965-2d63bbdc60c5) | DataType(amp_phase) | Loop(101)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(fc492af2-5497-4e79-bcc9-4622f169fd62) | TID(56655402-e6d6-4c98-90ba-e8506b5377c6) | DataType(amp_phase) | Loop(101)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(f59f50c2-0d02-41bb-b1bb-0395cb1f03e6) | TID(37604c5e-ba47-4850-bc45-afda5a632133) | DataType(amp_phase) | Loop(101)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(8f3cff65-6e1b-44fb-8ec8-40c1e110b921) | TID(d364beb7-37e5-4447-9b8c-72761c03d3f4) | DataType(amp_phase) | Loop(101)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(36be162e-6f28-4698-94e1-ac777a8a5555) | TID(2cbf9b01-80a1-4020-9c65-4b50b86041a7) | DataType(amp_phase) | Loop(101)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(5d8639a0-92c5-4148-abee-d8258415b694) | TID(75a2b36f-2c8b-4133-8447-67bd0a9fb458) | DataType(amp_phase) | Loop(101)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(e5c64453-7460-44d5-aa02-7321b8ed0703) | TID(2aa26612-7396-4c80-a61c-77ff0b795ce1) | DataType(amp_phase) | Loop(101)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(af6380dd-a47c-422b-a372-5c1d753b8349) | TID(ed112c51-1da7-4711-b85f-5762c0a8120d) | DataType(amp_phase) | Loop(101)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(fe1418f4-a1a9-42b9-b0ea-9fed9b6994a2) | TID(2a1e914a-ecd2-4efa-846f-5d17d8f22a05) | DataType(amp_phase) | Loop(101)]"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq32-DetuneCalibration-6892b5227881929c71492eff\n--------------------------------------------------\n'dict' object has no attribute 'detune'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\detune_calibration.py\", line 155, in _sync_composite_run\n    coincident_point = child_ape_composite_exp.analysis.results.detune.value\nAttributeError: 'dict' object has no attribute 'detune'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq32-DetuneCalibration-APEComposite(1-2)-RoughScan-6892b5227881929c71492f24\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 74, in _extract_result\n    coincident_point_list = find_coincident_point(all_points, times)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\find_peak.py\", line 446, in find_coincident_point\n    min_dis = min(distance_list)\nValueError: min() arg is an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: min() arg is an empty sequence\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-SingleShot_01-6892b37d46d2c290e6f7a390\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 483, in run\n    self._transformer()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 355, in _transformer\n    transformer.run()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_transformer.py\", line 280, in run\n    self._get_ctrl_ac_crosstalk_inv()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_transformer.py\", line 98, in _get_ctrl_ac_crosstalk_inv\n    self._ac_crosstalk_inv = TransformToolAPI.get_ac_crosstalk(\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_transformer.py\", line 312, in get_ac_crosstalk\n    ac_crosstalk = np.array(crosstalk_dict.get(\"ac_crosstalk\"))\nValueError: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (620,) + inhomogeneous part.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq77-XpiDetection-6892ac56f5d7f91d773526da\n--------------------------------------------------\n'dict' object has no attribute 'Xpi'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\xpi_detection.py\", line 134, in _sync_composite_run\n    exp_value = rabi_exp.analysis.results.Xpi.value\nAttributeError: 'dict' object has no attribute 'Xpi'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq7-XpiDetection-6892ac56f5d7f91d773526ea\n--------------------------------------------------\n'dict' object has no attribute 'Xpi'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\xpi_detection.py\", line 134, in _sync_composite_run\n    exp_value = rabi_exp.analysis.results.Xpi.value\nAttributeError: 'dict' object has no attribute 'Xpi'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq92-XpiDetection-6892ac56f5d7f91d773526e2\n--------------------------------------------------\n'dict' object has no attribute 'Xpi'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\xpi_detection.py\", line 134, in _sync_composite_run\n    exp_value = rabi_exp.analysis.results.Xpi.value\nAttributeError: 'dict' object has no attribute 'Xpi'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq32-XpiDetection-6892ac56f5d7f91d773526e8\n--------------------------------------------------\n'dict' object has no attribute 'Xpi'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\xpi_detection.py\", line 134, in _sync_composite_run\n    exp_value = rabi_exp.analysis.results.Xpi.value\nAttributeError: 'dict' object has no attribute 'Xpi'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq81-XpiDetection-6892ac56f5d7f91d773526de\n--------------------------------------------------\n'dict' object has no attribute 'Xpi'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\xpi_detection.py\", line 134, in _sync_composite_run\n    exp_value = rabi_exp.analysis.results.Xpi.value\nAttributeError: 'dict' object has no attribute 'Xpi'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq36-XpiDetection-6892ac56f5d7f91d773526ec\n--------------------------------------------------\n'dict' object has no attribute 'Xpi'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\xpi_detection.py\", line 134, in _sync_composite_run\n    exp_value = rabi_exp.analysis.results.Xpi.value\nAttributeError: 'dict' object has no attribute 'Xpi'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq29-XpiDetection-6892ac56f5d7f91d773526d8\n--------------------------------------------------\n'dict' object has no attribute 'Xpi'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\xpi_detection.py\", line 134, in _sync_composite_run\n    exp_value = rabi_exp.analysis.results.Xpi.value\nAttributeError: 'dict' object has no attribute 'Xpi'\n"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(bc6343a0-e90d-4780-94ff-2780a3012ea4) | TID(f8dafdae-00d2-45d4-a76c-c2a93221174c) | DataType(amp_phase) | Loop(101)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(101fb9db-306f-4072-b904-f85110e42d1b) | TID(7901fb1a-b902-4d20-9fb8-aac0f4e6cdd6) | DataType(iq) | Loop(360)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(101fb9db-306f-4072-b904-f85110e42d1b) | TID(7901fb1a-b902-4d20-9fb8-aac0f4e6cdd6) | DataType(iq) | Loop(360)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(101fb9db-306f-4072-b904-f85110e42d1b) | TID(7901fb1a-b902-4d20-9fb8-aac0f4e6cdd6) | DataType(iq) | Loop(360)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(101fb9db-306f-4072-b904-f85110e42d1b) | TID(7901fb1a-b902-4d20-9fb8-aac0f4e6cdd6) | DataType(iq) | Loop(360)]"},
{"level":15,"message":"Task Timeout\n--------------------------------------------------\nTask[RID(101fb9db-306f-4072-b904-f85110e42d1b) | TID(7901fb1a-b902-4d20-9fb8-aac0f4e6cdd6) | DataType(iq) | Loop(360)]"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq27-ReadoutFreqCalibrate-6891fae821c5e7f185d133d4\n--------------------------------------------------\nzero-size array to reduction operation minimum which has no identity\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 523, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 474, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 508, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 114, in _extract_result\n    min_fr_index, max_fr_index = np.amin(indices), np.amax(indices)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2970, in amin\n    return _wrapreduction(a, np.minimum, 'min', axis, None, out,\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation minimum which has no identity\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq27-ReadoutFreqCalibrate-6891fab621c5e7f185d133ce\n--------------------------------------------------\nzero-size array to reduction operation minimum which has no identity\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 523, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 474, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 508, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 114, in _extract_result\n    min_fr_index, max_fr_index = np.amin(indices), np.amax(indices)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2970, in amin\n    return _wrapreduction(a, np.minimum, 'min', axis, None, out,\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation minimum which has no identity\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq80-QubitFreqCalibration-6891f23521c5e7f185d13209\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq80-QubitFreqCalibration-Ramsey(2-2)-fringe=-41MHz-6891f23521c5e7f185d1320c\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq80-QubitFreqCalibration-Ramsey(1-2)-fringe=41MHz-6891f23521c5e7f185d1320b\n--------------------------------------------------\n<Analysis fit init/bound data error> | The first value is greater than the second value 0 >= 0.0."},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq68-ReadoutFreqCalibrate-6891eed0fc7053b57c77fddb\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 123, in _extract_result\n    max_index = np.argmax(distance_arr[min_fr_index: max_fr_index])\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 1229, in argmax\n    return _wrapfunc(a, 'argmax', axis=axis, out=out, **kwds)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 59, in _wrapfunc\n    return bound(*args, **kwds)\nValueError: attempt to get argmax of an empty sequence\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: attempt to get argmax of an empty sequence\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq28-ReadoutFreqCalibrate-6891ee42fc7053b57c77fd47\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 114, in _extract_result\n    min_fr_index, max_fr_index = np.amin(indices), np.amax(indices)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2970, in amin\n    return _wrapreduction(a, np.minimum, 'min', axis, None, out,\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation minimum which has no identity\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: zero-size array to reduction operation minimum which has no identity\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq58-ReadoutFreqCalibrate-6891edcffc7053b57c77fcc2\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 114, in _extract_result\n    min_fr_index, max_fr_index = np.amin(indices), np.amax(indices)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2970, in amin\n    return _wrapreduction(a, np.minimum, 'min', axis, None, out,\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation minimum which has no identity\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: zero-size array to reduction operation minimum which has no identity\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq32-ReadoutFreqCalibrate-6891ed283abb1842ea37ac9c\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 114, in _extract_result\n    min_fr_index, max_fr_index = np.amin(indices), np.amax(indices)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2970, in amin\n    return _wrapreduction(a, np.minimum, 'min', axis, None, out,\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation minimum which has no identity\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: zero-size array to reduction operation minimum which has no identity\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq16-ReadoutFreqCalibrate-6891ed263abb1842ea37ac8f\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq34-ReadoutFreqCalibrate-6891ed263abb1842ea37ac91\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq31-ReadoutFreqCalibrate-6891ed263abb1842ea37ac8d\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 482, in run\n    self._validate_and_merge()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 255, in _validate_and_merge\n    assert len(set(same_bus_powers)) == 1, (\nAssertionError: Same bus power validate error, details:\nbus: 6\nvalue: [-27.0, -28.0]\nunit: ['q31', 'q34']\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 482, in run\n    self._validate_and_merge()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 255, in _validate_and_merge\n    assert len(set(same_bus_powers)) == 1, (\nAssertionError: Same bus power validate error, details:\nbus: 6\nvalue: [-27.0, -28.0]\nunit: ['q31', 'q34']\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq27-ReadoutFreqCalibrate-6891ed053abb1842ea37ac76\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 114, in _extract_result\n    min_fr_index, max_fr_index = np.amin(indices), np.amax(indices)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2970, in amin\n    return _wrapreduction(a, np.minimum, 'min', axis, None, out,\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation minimum which has no identity\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: zero-size array to reduction operation minimum which has no identity\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq27-ReadoutFreqCalibrate-6891ebe7f2f6b6c6d6c3b813\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 114, in _extract_result\n    min_fr_index, max_fr_index = np.amin(indices), np.amax(indices)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2970, in amin\n    return _wrapreduction(a, np.minimum, 'min', axis, None, out,\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation minimum which has no identity\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: zero-size array to reduction operation minimum which has no identity\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq58-ReadoutFreqCalibrate-6891e9d0f2f6b6c6d6c3b67a\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 114, in _extract_result\n    min_fr_index, max_fr_index = np.amin(indices), np.amax(indices)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2970, in amin\n    return _wrapreduction(a, np.minimum, 'min', axis, None, out,\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation minimum which has no identity\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: zero-size array to reduction operation minimum which has no identity\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq94-ReadoutFreqCalibrate-6891e9d0f2f6b6c6d6c3b676\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 114, in _extract_result\n    min_fr_index, max_fr_index = np.amin(indices), np.amax(indices)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2970, in amin\n    return _wrapreduction(a, np.minimum, 'min', axis, None, out,\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation minimum which has no identity\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: zero-size array to reduction operation minimum which has no identity\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-SingleShot_01-6891e6d91f204a37dcdb7e1e\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 483, in run\n    self._transformer()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 355, in _transformer\n    transformer.run()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_transformer.py\", line 280, in run\n    self._get_ctrl_ac_crosstalk_inv()\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_transformer.py\", line 98, in _get_ctrl_ac_crosstalk_inv\n    self._ac_crosstalk_inv = TransformToolAPI.get_ac_crosstalk(\n  File \"D:\\code\\zs\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_transformer.py\", line 312, in get_ac_crosstalk\n    ac_crosstalk = np.array(crosstalk_dict.get(\"ac_crosstalk\"))\nValueError: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (620,) + inhomogeneous part.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq47-QubitFreqCalibration-6891ce10be85e8d842865262\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq74-QubitFreqCalibration-6891cb1987ebf8173ba15ad0\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq92-QubitFreqCalibration-6891cb1987ebf8173ba15ace\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq94-DetuneCalibration-APEComposite(1-2)-RoughScan-6891ca55f0505ca18866614f\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq37-DetuneCalibration-APEComposite(1-2)-RoughScan-6891ca56f0505ca188666157\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq40-DetuneCalibration-APEComposite(1-2)-RoughScan-6891ca56f0505ca188666155\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq55-DetuneCalibration-APEComposite(1-2)-RoughScan-6891ca55f0505ca18866614d\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq22-DetuneCalibration-APEComposite(1-2)-RoughScan-6891ca56f0505ca188666153\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-DetuneCalibration-APEComposite(1-2)-RoughScan-6891ca55f0505ca18866614b\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq58-DetuneCalibration-APEComposite(1-2)-RoughScan-6891ca56f0505ca188666151\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq91-QubitFreqCalibration-6891ca2bf0505ca1886660f8\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq91-QubitFreqCalibration-Ramsey(2-2)-fringe=-50MHz-6891ca2df0505ca18866610f\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 134, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 216, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq91-QubitFreqCalibration-Ramsey(1-2)-fringe=50MHz-6891ca2cf0505ca188666106\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 134, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 216, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-ACBiasTunable-6891c69621c5e7f185d130e4\n--------------------------------------------------\n'NoneType' object has no attribute 'metadata'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 521, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 485, in _sync_composite_run\n    self._handle_child_experiment(child_exp)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 418, in _handle_child_experiment\n    self._handle_child_result(exp)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\composite\\readout\\cavity_tunable.py\", line 191, in _handle_child_result\n    elif cs_exp.analysis.analysis_state(\"result\"):\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\analysis\\top_analysis.py\", line 219, in analysis_state\n    if self.experiment_data.metadata.process_meta:\nAttributeError: 'NoneType' object has no attribute 'metadata'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-ACBiasTunable-CavityFreqSpectrum(8-33)-ac_bias = -0.27v-6891c6ae21c5e7f185d130ed\n--------------------------------------------------\n<Acq Task State Error> | 4 | Task execution failed, detail in pyqcat-venus"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 482, in run\n    self._validate_and_merge()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 255, in _validate_and_merge\n    assert len(set(same_bus_powers)) == 1, (\nAssertionError: Same bus power validate error, details:\nbus: 1\nvalue: [-26, -21, -27, -27, -24, -12]\nunit: ['q3', 'q5', 'q4', 'q1', 'q2', 'q6']\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 482, in run\n    self._validate_and_merge()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 255, in _validate_and_merge\n    assert len(set(same_bus_powers)) == 1, (\nAssertionError: Same bus power validate error, details:\nbus: 1\nvalue: [-30, -24, -18, -12, -30, -17]\nunit: ['q5', 'q3', 'q2', 'q4', 'q1', 'q6']\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 482, in run\n    self._validate_and_merge()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 255, in _validate_and_merge\n    assert len(set(same_bus_powers)) == 1, (\nAssertionError: Same bus power validate error, details:\nbus: 1\nvalue: [-14, -23, -11, -13, -21, -10]\nunit: ['q5', 'q3', 'q4', 'q6', 'q2', 'q1']\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 482, in run\n    self._validate_and_merge()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 255, in _validate_and_merge\n    assert len(set(same_bus_powers)) == 1, (\nAssertionError: Same bus power validate error, details:\nbus: 1\nvalue: [-19, -25, -19, -21, -13, -13]\nunit: ['q3', 'q2', 'q4', 'q6', 'q1', 'q5']\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq8-McmQubitDePhaseComposite-6891be4394a082d218d9fa01\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq5-McmQubitDePhaseComposite-6891be4394a082d218d9f9fb\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-McmQubitDePhaseComposite-6891be4394a082d218d9f9ff\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq3-McmQubitDePhaseComposite-6891be4394a082d218d9f9f7\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2-McmQubitDePhaseComposite-6891be4394a082d218d9f9f5\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-McmQubitDePhaseComposite-6891be4394a082d218d9f9f3\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq6-McmQubitDePhaseComposite-6891be4394a082d218d9f9fd\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-McmQubitDePhaseComposite-6891be4394a082d218d9f9f9\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(McmQubitDePhase) | default | Count-Identity(4)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 485, in run\n    self._build_experiment_task()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 419, in _build_experiment_task\n    program_buf, program_pointer_buf = program_to_protobuf(self.program)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 475, in program_to_protobuf\n    program_buf.experiment_file.CopyFrom(experiment_file_to_protobuf(t_program.experiment_file))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 391, in experiment_file_to_protobuf\n    ms_buf = measure_aio_to_protobuf(exp_file.measure_aio)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 199, in measure_aio_to_protobuf\n    ctrl_buf = ctrl_to_protobuf(module, ctrl_obj)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 167, in ctrl_to_protobuf\n    pulse_buf = pulse_to_protobuf_v2(val)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 313, in pulse_to_protobuf_v2\n    op_buf = PROTOBUF_PULSE_CLASS_DICT[op_cls_name](**op_params)\nValueError: Protocol message AcquirePhase has no \"rd_time\" field.\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(McmQubitDePhase) | default | Count-Identity(3)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 485, in run\n    self._build_experiment_task()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 419, in _build_experiment_task\n    program_buf, program_pointer_buf = program_to_protobuf(self.program)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 475, in program_to_protobuf\n    program_buf.experiment_file.CopyFrom(experiment_file_to_protobuf(t_program.experiment_file))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 391, in experiment_file_to_protobuf\n    ms_buf = measure_aio_to_protobuf(exp_file.measure_aio)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 199, in measure_aio_to_protobuf\n    ctrl_buf = ctrl_to_protobuf(module, ctrl_obj)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 167, in ctrl_to_protobuf\n    pulse_buf = pulse_to_protobuf_v2(val)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 313, in pulse_to_protobuf_v2\n    op_buf = PROTOBUF_PULSE_CLASS_DICT[op_cls_name](**op_params)\nValueError: Protocol message AcquirePhase has no \"rd_time\" field.\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(McmQubitDePhase) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 485, in run\n    self._build_experiment_task()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 419, in _build_experiment_task\n    program_buf, program_pointer_buf = program_to_protobuf(self.program)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 475, in program_to_protobuf\n    program_buf.experiment_file.CopyFrom(experiment_file_to_protobuf(t_program.experiment_file))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 391, in experiment_file_to_protobuf\n    ms_buf = measure_aio_to_protobuf(exp_file.measure_aio)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 199, in measure_aio_to_protobuf\n    ctrl_buf = ctrl_to_protobuf(module, ctrl_obj)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 167, in ctrl_to_protobuf\n    pulse_buf = pulse_to_protobuf_v2(val)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 313, in pulse_to_protobuf_v2\n    op_buf = PROTOBUF_PULSE_CLASS_DICT[op_cls_name](**op_params)\nValueError: Protocol message AcquirePhase has no \"rd_time\" field.\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(McmQubitDePhase) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 485, in run\n    self._build_experiment_task()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 419, in _build_experiment_task\n    program_buf, program_pointer_buf = program_to_protobuf(self.program)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 475, in program_to_protobuf\n    program_buf.experiment_file.CopyFrom(experiment_file_to_protobuf(t_program.experiment_file))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 391, in experiment_file_to_protobuf\n    ms_buf = measure_aio_to_protobuf(exp_file.measure_aio)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 199, in measure_aio_to_protobuf\n    ctrl_buf = ctrl_to_protobuf(module, ctrl_obj)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 167, in ctrl_to_protobuf\n    pulse_buf = pulse_to_protobuf_v2(val)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 313, in pulse_to_protobuf_v2\n    op_buf = PROTOBUF_PULSE_CLASS_DICT[op_cls_name](**op_params)\nValueError: Protocol message AcquirePhase has no \"rd_time\" field.\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(McmQubitDePhase) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 485, in run\n    self._build_experiment_task()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 419, in _build_experiment_task\n    program_buf, program_pointer_buf = program_to_protobuf(self.program)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 475, in program_to_protobuf\n    program_buf.experiment_file.CopyFrom(experiment_file_to_protobuf(t_program.experiment_file))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 391, in experiment_file_to_protobuf\n    ms_buf = measure_aio_to_protobuf(exp_file.measure_aio)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 199, in measure_aio_to_protobuf\n    ctrl_buf = ctrl_to_protobuf(module, ctrl_obj)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 167, in ctrl_to_protobuf\n    pulse_buf = pulse_to_protobuf_v2(val)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 313, in pulse_to_protobuf_v2\n    op_buf = PROTOBUF_PULSE_CLASS_DICT[op_cls_name](**op_params)\nValueError: Protocol message AcquirePhase has no \"rd_time\" field.\n"},
{"level":5,"message":"Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(McmQubitPopulation) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 485, in run\n    self._build_experiment_task()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 419, in _build_experiment_task\n    program_buf, program_pointer_buf = program_to_protobuf(self.program)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 475, in program_to_protobuf\n    program_buf.experiment_file.CopyFrom(experiment_file_to_protobuf(t_program.experiment_file))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 391, in experiment_file_to_protobuf\n    ms_buf = measure_aio_to_protobuf(exp_file.measure_aio)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 199, in measure_aio_to_protobuf\n    ctrl_buf = ctrl_to_protobuf(module, ctrl_obj)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 167, in ctrl_to_protobuf\n    pulse_buf = pulse_to_protobuf_v2(val)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\proto_utils.py\", line 313, in pulse_to_protobuf_v2\n    op_buf = PROTOBUF_PULSE_CLASS_DICT[op_cls_name](**op_params)\nValueError: Protocol message AcquirePhase has no \"rd_time\" field.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq69-ReadoutFreqCalibrate-6891bc98c9a1560de48e553d\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 114, in _extract_result\n    min_fr_index, max_fr_index = np.amin(indices), np.amax(indices)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2970, in amin\n    return _wrapreduction(a, np.minimum, 'min', axis, None, out,\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation minimum which has no identity\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: zero-size array to reduction operation minimum which has no identity\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq9-ReadoutFreqCalibrate-6891bb30c9a1560de48e5476\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 114, in _extract_result\n    min_fr_index, max_fr_index = np.amin(indices), np.amax(indices)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2970, in amin\n    return _wrapreduction(a, np.minimum, 'min', axis, None, out,\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation minimum which has no identity\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: zero-size array to reduction operation minimum which has no identity\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq27-ReadoutFreqCalibrate-6891bb30c9a1560de48e5474\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 114, in _extract_result\n    min_fr_index, max_fr_index = np.amin(indices), np.amax(indices)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2970, in amin\n    return _wrapreduction(a, np.minimum, 'min', axis, None, out,\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation minimum which has no identity\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: zero-size array to reduction operation minimum which has no identity\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq4-CPMGComposite-6891bac994a082d218d9f9bb\n--------------------------------------------------\n'dict' object has no attribute 'tau'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 480, in _sync_composite_run\n    self._handle_child_experiment(child_exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 413, in _handle_child_experiment\n    self._handle_child_result(exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\error_quantification\\cpmg_compensite.py\", line 84, in _handle_child_result\n    dict(tau=exp.analysis.results.tau.value, omega=exp.analysis.results.omega.value)\nAttributeError: 'dict' object has no attribute 'tau'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-CPMGComposite-CP(1-9)-N=1dBm-6891bacb94a082d218d9f9ca\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq5-CPMGComposite-6891bac994a082d218d9f9bd\n--------------------------------------------------\n'dict' object has no attribute 'tau'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 480, in _sync_composite_run\n    self._handle_child_experiment(child_exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 413, in _handle_child_experiment\n    self._handle_child_result(exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\error_quantification\\cpmg_compensite.py\", line 84, in _handle_child_result\n    dict(tau=exp.analysis.results.tau.value, omega=exp.analysis.results.omega.value)\nAttributeError: 'dict' object has no attribute 'tau'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-CPMGComposite-6891bac894a082d218d9f9b5\n--------------------------------------------------\n'dict' object has no attribute 'tau'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 480, in _sync_composite_run\n    self._handle_child_experiment(child_exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 413, in _handle_child_experiment\n    self._handle_child_result(exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\error_quantification\\cpmg_compensite.py\", line 84, in _handle_child_result\n    dict(tau=exp.analysis.results.tau.value, omega=exp.analysis.results.omega.value)\nAttributeError: 'dict' object has no attribute 'tau'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq6-CPMGComposite-6891baca94a082d218d9f9bf\n--------------------------------------------------\n'dict' object has no attribute 'tau'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 480, in _sync_composite_run\n    self._handle_child_experiment(child_exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 413, in _handle_child_experiment\n    self._handle_child_result(exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\error_quantification\\cpmg_compensite.py\", line 84, in _handle_child_result\n    dict(tau=exp.analysis.results.tau.value, omega=exp.analysis.results.omega.value)\nAttributeError: 'dict' object has no attribute 'tau'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq5-CPMGComposite-CP(1-9)-N=1dBm-6891bacb94a082d218d9f9cb\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq3-CPMGComposite-6891bac994a082d218d9f9b9\n--------------------------------------------------\n'dict' object has no attribute 'tau'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 480, in _sync_composite_run\n    self._handle_child_experiment(child_exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 413, in _handle_child_experiment\n    self._handle_child_result(exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\error_quantification\\cpmg_compensite.py\", line 84, in _handle_child_result\n    dict(tau=exp.analysis.results.tau.value, omega=exp.analysis.results.omega.value)\nAttributeError: 'dict' object has no attribute 'tau'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2-CPMGComposite-6891bac894a082d218d9f9b7\n--------------------------------------------------\n'dict' object has no attribute 'tau'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 480, in _sync_composite_run\n    self._handle_child_experiment(child_exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 413, in _handle_child_experiment\n    self._handle_child_result(exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\error_quantification\\cpmg_compensite.py\", line 84, in _handle_child_result\n    dict(tau=exp.analysis.results.tau.value, omega=exp.analysis.results.omega.value)\nAttributeError: 'dict' object has no attribute 'tau'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-CPMGComposite-CP(1-9)-N=1dBm-6891bacb94a082d218d9f9c7\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq6-CPMGComposite-CP(1-9)-N=1dBm-6891bacb94a082d218d9f9cc\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2-CPMGComposite-CP(1-9)-N=1dBm-6891bacb94a082d218d9f9c8\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq3-CPMGComposite-CP(1-9)-N=1dBm-6891bacb94a082d218d9f9c9\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq5-CP-6891ba5894a082d218d9f9af\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2-CP-6891ba5894a082d218d9f9ac\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq3-CP-6891ba5894a082d218d9f9ad\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-CP-6891ba5894a082d218d9f9ab\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-CP-6891ba5894a082d218d9f9ae\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq6-CP-6891ba5994a082d218d9f9b0\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq5-PurityXEBSingle-6891b97594a082d218d9f99f\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq3-PurityXEBSingle-6891b97594a082d218d9f99d\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-PurityXEBSingle-6891b97594a082d218d9f99b\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-PurityXEBSingle-6891b97594a082d218d9f99e\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2-PurityXEBSingle-6891b97594a082d218d9f99c\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq6-PurityXEBSingle-6891b97594a082d218d9f9a0\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq6-SweepDetuneRabiZamp-6891b88b94a082d218d9f96d\n--------------------------------------------------\n<Exp(validate_ac_spectrum) field error> | freq=4500 or z_amp=None is out of scope!"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq8-SweepDetuneRabiZamp-6891b88b94a082d218d9f973\n--------------------------------------------------\n<Exp(validate_ac_spectrum) field error> | freq=4500 or z_amp=None is out of scope!"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-SweepDetuneRabiZamp-6891b88b94a082d218d9f970\n--------------------------------------------------\n<Exp(validate_ac_spectrum) field error> | freq=4500 or z_amp=None is out of scope!"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq5-SweepDetuneRabiZamp-6891b88b94a082d218d9f96a\n--------------------------------------------------\n<Exp(validate_ac_spectrum) field error> | freq=4500 or z_amp=None is out of scope!"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq3-SweepDetuneRabiZamp-6891b88b94a082d218d9f964\n--------------------------------------------------\n<Exp(validate_ac_spectrum) field error> | freq=4500 or z_amp=None is out of scope!"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2-SweepDetuneRabiZamp-6891b88b94a082d218d9f961\n--------------------------------------------------\n<Exp(validate_ac_spectrum) field error> | freq=4500 or z_amp=None is out of scope!"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-SweepDetuneRabiZamp-6891b88b94a082d218d9f967\n--------------------------------------------------\n<Exp(validate_ac_spectrum) field error> | freq=4500 or z_amp=None is out of scope!"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-SweepDetuneRabiZamp-6891b88b94a082d218d9f95e\n--------------------------------------------------\n<Exp(validate_ac_spectrum) field error> | freq=4500 or z_amp=None is out of scope!"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq8-OptimizeFirDicarlo-6891b88a94a082d218d9f959\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq7-OptimizeFirDicarlo-6891b88a94a082d218d9f957\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq6-OptimizeFirDicarlo-6891b88a94a082d218d9f955\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq5-OptimizeFirDicarlo-6891b88a94a082d218d9f953\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq4-OptimizeFirDicarlo-6891b88a94a082d218d9f951\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq3-OptimizeFirDicarlo-6891b88a94a082d218d9f94f\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2-OptimizeFirDicarlo-6891b88a94a082d218d9f94d\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-OptimizeFirDicarlo-6891b88a94a082d218d9f94b\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-DistortionPolesOpt-6891b88994a082d218d9f945\n--------------------------------------------------\n<Exp(DistortionPolesOpt) experiment options error> | key(poles_bounds_path) | value(F:\\2024-01\\monster_dev\\pyqcat-monster\\conf\\poles_bounds_p0.json) | F:\\2024-01\\monster_dev\\pyqcat-monster\\conf\\poles_bounds_p0.json does not exist! | Please check options!"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-DistortionPolesOpt-6891b88994a082d218d9f939\n--------------------------------------------------\n<Exp(DistortionPolesOpt) experiment options error> | key(poles_bounds_path) | value(F:\\2024-01\\monster_dev\\pyqcat-monster\\conf\\poles_bounds_p0.json) | F:\\2024-01\\monster_dev\\pyqcat-monster\\conf\\poles_bounds_p0.json does not exist! | Please check options!"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq18-SingleShot_01-6891b819a64d966d6103ed15\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 189, in _validate_and_merge\n    lo_num = self._get_lo_from_channel(ctrl.channel)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 140, in _get_lo_from_channel\n    lo = self.common.q_component_resources.get(unit).get(f\"{mode}_lo\")\nAttributeError: 'NoneType' object has no attribute 'get'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq3-RabiWidthComposite-6891b7ed94a082d218d9f90d\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\rabi_width_composite.py\", line 97, in _sync_composite_run\n    osc_freq = rabi_width_exp.analysis.results.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2-RabiWidthComposite-6891b7ed94a082d218d9f90b\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\rabi_width_composite.py\", line 97, in _sync_composite_run\n    osc_freq = rabi_width_exp.analysis.results.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq5-RabiWidthComposite-6891b7ed94a082d218d9f911\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\rabi_width_composite.py\", line 97, in _sync_composite_run\n    osc_freq = rabi_width_exp.analysis.results.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq4-RabiWidthComposite-6891b7ed94a082d218d9f90f\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\rabi_width_composite.py\", line 97, in _sync_composite_run\n    osc_freq = rabi_width_exp.analysis.results.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq3-RabiWidthComposite-RabiScanWidth(1-unknow)-count=0-drive_power=None-6891b7ed94a082d218d9f91d\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq2-RabiWidthComposite-RabiScanWidth(1-unknow)-count=0-drive_power=None-6891b7ed94a082d218d9f91c\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq6-RabiWidthComposite-6891b7ed94a082d218d9f913\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\rabi_width_composite.py\", line 97, in _sync_composite_run\n    osc_freq = rabi_width_exp.analysis.results.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-RabiWidthComposite-6891b7ed94a082d218d9f909\n--------------------------------------------------\n'dict' object has no attribute 'freq'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\rabi_width_composite.py\", line 97, in _sync_composite_run\n    osc_freq = rabi_width_exp.analysis.results.freq.value\nAttributeError: 'dict' object has no attribute 'freq'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq5-RabiWidthComposite-RabiScanWidth(1-unknow)-count=0-drive_power=None-6891b7ed94a082d218d9f91f\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-RabiWidthComposite-RabiScanWidth(1-unknow)-count=0-drive_power=None-6891b7ed94a082d218d9f91e\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq6-RabiWidthComposite-RabiScanWidth(1-unknow)-count=0-drive_power=None-6891b7ed94a082d218d9f920\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-RabiWidthComposite-RabiScanWidth(1-unknow)-count=0-drive_power=None-6891b7ed94a082d218d9f91b\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq8-MicSourceRamseySpectrum-6891b7ec94a082d218d9f905\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq7-MicSourceRamseySpectrum-6891b7ec94a082d218d9f903\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq6-MicSourceRamseySpectrum-6891b7ec94a082d218d9f901\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq5-MicSourceRamseySpectrum-6891b7ec94a082d218d9f8ff\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq4-MicSourceRamseySpectrum-6891b7ec94a082d218d9f8fd\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq3-MicSourceRamseySpectrum-6891b7ec94a082d218d9f8fb\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2-MicSourceRamseySpectrum-6891b7ec94a082d218d9f8f9\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-MicSourceRamseySpectrum-6891b7ec94a082d218d9f8f7\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq5-StabilityT2Ramsey-6891b55594a082d218d9f84b\n--------------------------------------------------\n'dict' object has no attribute 'tau'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 103, in _sync_composite_run\n    self.add_records(child_exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 180, in add_records\n    super().add_records(child)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 68, in add_records\n    data = self._extra_data(child)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 207, in _extra_data\n    t2 = child.analysis.results.tau.value\nAttributeError: 'dict' object has no attribute 'tau'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq4-StabilityT2Ramsey-6891b55594a082d218d9f848\n--------------------------------------------------\n'dict' object has no attribute 'tau'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 103, in _sync_composite_run\n    self.add_records(child_exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 180, in add_records\n    super().add_records(child)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 68, in add_records\n    data = self._extra_data(child)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 207, in _extra_data\n    t2 = child.analysis.results.tau.value\nAttributeError: 'dict' object has no attribute 'tau'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq5-StabilityT2Ramsey-T2Ramsey(1-unknow)-Loop-0-6891b55594a082d218d9f866\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-StabilityT2Ramsey-6891b55594a082d218d9f83f\n--------------------------------------------------\n'dict' object has no attribute 'tau'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 103, in _sync_composite_run\n    self.add_records(child_exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 180, in add_records\n    super().add_records(child)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 68, in add_records\n    data = self._extra_data(child)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 207, in _extra_data\n    t2 = child.analysis.results.tau.value\nAttributeError: 'dict' object has no attribute 'tau'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-StabilityT2Ramsey-T2Ramsey(1-unknow)-Loop-0-6891b55594a082d218d9f863\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq1-StabilityT2Ramsey-T2Ramsey(1-unknow)-Loop-0-6891b55594a082d218d9f85a\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq5-StabilityT2Ramsey-T2Ramsey(1-unknow)-Loop-0-Ramsey(1-unknow)-count=0-z_amp=None-fringe=0.5-6891b55594a082d218d9f867\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2-StabilityT2Ramsey-6891b55594a082d218d9f842\n--------------------------------------------------\n'dict' object has no attribute 'tau'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 103, in _sync_composite_run\n    self.add_records(child_exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 180, in add_records\n    super().add_records(child)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 68, in add_records\n    data = self._extra_data(child)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 207, in _extra_data\n    t2 = child.analysis.results.tau.value\nAttributeError: 'dict' object has no attribute 'tau'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq6-StabilityT2Ramsey-6891b55594a082d218d9f84e\n--------------------------------------------------\n'dict' object has no attribute 'tau'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 103, in _sync_composite_run\n    self.add_records(child_exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 180, in add_records\n    super().add_records(child)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 68, in add_records\n    data = self._extra_data(child)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\stability.py\", line 207, in _extra_data\n    t2 = child.analysis.results.tau.value\nAttributeError: 'dict' object has no attribute 'tau'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq6-StabilityT2Ramsey-T2Ramsey(1-unknow)-Loop-0-Ramsey(1-unknow)-count=0-z_amp=None-fringe=0.5-6891b55594a082d218d9f86a\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\oscillation_analysis.py\", line 139, in _guess_fit_param\n    fit_opt.bounds.set_if_empty(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 311, in set_if_empty\n    self.__setitem__(key, value)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 298, in __setitem__\n    super().__setitem__(key, self.format(value))\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\specification.py\", line 377, in format\n    raise AnalysisFitDataError(\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.AnalysisFitDataError: <Analysis fit init/bound data error> | The first value is greater than the second value 1.0 >= 1.0.\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq69-ReadoutFreqCalibrate-6891b521ee736158854b7456\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\analysis\\library\\readout_freq_cali_analysis.py\", line 114, in _extract_result\n    min_fr_index, max_fr_index = np.amin(indices), np.amax(indices)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2970, in amin\n    return _wrapreduction(a, np.minimum, 'min', axis, None, out,\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation minimum which has no identity\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nValueError: zero-size array to reduction operation minimum which has no identity\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq3-CheckFreqRabiWidth-6891b11594a082d218d9f596\n--------------------------------------------------\n'dict' object has no attribute 'oscillating'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 480, in _sync_composite_run\n    self._handle_child_experiment(child_exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 413, in _handle_child_experiment\n    self._handle_child_result(exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\rabi_width_amp.py\", line 145, in _handle_child_result\n    if child_exp.analysis and child_exp.analysis.results.oscillating.value is True:\nAttributeError: 'dict' object has no attribute 'oscillating'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-CheckFreqRabiWidth-6891b11594a082d218d9f592\n--------------------------------------------------\n'dict' object has no attribute 'oscillating'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 480, in _sync_composite_run\n    self._handle_child_experiment(child_exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 413, in _handle_child_experiment\n    self._handle_child_result(exp)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\rabi_width_amp.py\", line 145, in _handle_child_result\n    if child_exp.analysis and child_exp.analysis.results.oscillating.value is True:\nAttributeError: 'dict' object has no attribute 'oscillating'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq8-CheckFreqRabiWidth-RabiScanWidth(1-3)-fq=4300-6891b11694a082d218d9f5ab\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 482, in run\n    self._validate_and_merge()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 188, in _validate_and_merge\n    unit_name = self.common.channel_bit_map.get(f\"xy-{ctrl.channel}\")\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\qm_protocol.py\", line 340, in channel_bit_map\n    return self.env_bit_resource.channel_bit_map\nAttributeError: 'NoneType' object has no attribute 'channel_bit_map'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq2-DERBSingle-6891af6694a082d218d9f246\n--------------------------------------------------\n'NoneType' object has no attribute 'Population'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\error_quantification\\nm_base.py\", line 171, in _sync_composite_run\n    gea.Population(Encoding=\"RI\", NIND=opti_opts[\"NIND\"]),\nAttributeError: 'NoneType' object has no attribute 'Population'\n"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-DERBSingle-6891af6694a082d218d9f244\n--------------------------------------------------\n'NoneType' object has no attribute 'Population'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\error_quantification\\nm_base.py\", line 171, in _sync_composite_run\n    gea.Population(Encoding=\"RI\", NIND=opti_opts[\"NIND\"]),\nAttributeError: 'NoneType' object has no attribute 'Population'\n"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq5-ReadoutPowerF02Calibrate-6891af5f94a082d218d9f191\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq7-ReadoutPowerF02Calibrate-6891af5f94a082d218d9f195\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":5,"message":"Experiment Fail\n--------------------------------------------------\nq4-ReadoutPowerF02Calibrate-6891af5f94a082d218d9f18f\n--------------------------------------------------\nCause by Child Experiment Error"},
{"level":10,"message":"Experiment Crash\n--------------------------------------------------\nq1-XYZTimingComposite-XYZTiming(1-unknow)-count-0-xy_delay-0.0-z_delay-0.0-68748b9797cdb725f3079cc6\n--------------------------------------------------\nzero-size array to reduction operation maximum which has no identity\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\mycode\\gitlab\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\mycode\\gitlab\\apps\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 610, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"F:\\mycode\\gitlab\\apps\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"F:\\mycode\\gitlab\\apps\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 171, in top_experiment_analysis\n    return base_analysis_process(\n  File \"F:\\mycode\\gitlab\\apps\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"F:\\mycode\\gitlab\\apps\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"F:\\mycode\\gitlab\\apps\\pyqcat-apps\\pyQCat\\analysis\\library\\xyztiming_analysis.py\", line 269, in run_analysis\n    super().run_analysis()\n  File \"F:\\mycode\\gitlab\\apps\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 410, in run_analysis\n    res = self._run_fitting()\n  File \"F:\\mycode\\gitlab\\apps\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 213, in _run_fitting\n    guess_fit_options = self._guess_fit_param(new_fit_opt, analysis_data)\n  File \"F:\\mycode\\gitlab\\apps\\pyqcat-apps\\pyQCat\\analysis\\library\\xyztiming_analysis.py\", line 130, in _guess_fit_param\n    idx_half = np.max(np.argwhere(y < (np.max(y) + np.min(y)) / 2))\n  File \"C:\\Users\\<USER>\\AppData\\Local\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 2810, in max\n    return _wrapreduction(a, np.maximum, 'max', axis, None, out,\n  File \"C:\\Users\\<USER>\\AppData\\Local\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 88, in _wrapreduction\n    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)\nValueError: zero-size array to reduction operation maximum which has no identity\n"}
]
