# Replace default Base URL of the Python Package Index with the one provided by Alibaba Cloud so that our users don't need to specify it again when using `pip install`.
# FYI: https://pip.pypa.io/en/stable/reference/requirements-file-format/
# --index-url https://mirrors.qpanda.cn/repository/pip-public/simple

# Database related
mongoengine~=0.29.0
minio~=7.2.15
orjson==3.9.10
pymongo~=3.13.0

# Core of Scientific Computing
numpy==1.26.3
scipy==1.11.4
scikit-learn==1.3.2
pandas==2.1.4
sympy==1.12
qutip==4.7.3
cvxpy==1.4.2
cma==3.3.0
lmfit==1.2.2

# Data visualization
matplotlib==3.7.0
brokenaxes==0.5.0
networkx==3.2.1

# instrument control
PyVISA==1.13.0
pyzmq==26.0.3

# Data Processing and Format
openpyxl==3.1.2
PyYAML==6.0.1
jsonschema==4.19.2
prettytable==3.9.0

# Systems and Tools
loguru==0.7.2
tqdm==4.66.1
psutil~=7.0.0

# Web and Network
requests==2.32.3
tornado==6.3.3
Jinja2==3.1.3

# Other
protobuf==5.27.1
pydantic-settings~=2.7.1
# geatpy==2.7.0
# rb_generator==0.0.3
