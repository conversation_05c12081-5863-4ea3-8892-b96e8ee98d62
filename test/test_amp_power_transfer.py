# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/05
# __author:       <PERSON><PERSON><PERSON>


import random
import unittest

from app.config import init_backend
from pyQCat.qubit.qubit import (
    ModuleEnum,
    XEnum,
    use_amp_refresh_power,
    use_power_refresh_amp,
)


class AmpPowerToolTest(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.backend = init_backend()

    def test_xy_use_power_refresh_amp(self):
        cache_qubit = self.backend.chip_data.cache_qubit
        random_items = random.sample(list(cache_qubit.items()), 5)
        qubits = [item[1] for item in random_items]
        use_power_refresh_amp(qubits, -10, ModuleEnum.XY)

    def test_m_use_power_refresh_amp(self):
        cache_qubit = self.backend.chip_data.cache_qubit
        random_items = random.sample(list(cache_qubit.items()), 5)
        qubits = [item[1] for item in random_items]
        use_power_refresh_amp(qubits, -15, ModuleEnum.M)

    def test_xy_use_amp_refresh_power(self):
        cache_qubit = self.backend.chip_data.cache_qubit
        random_items = random.sample(list(cache_qubit.items()), 5)
        qubits = [item[1] for item in random_items]
        use_amp_refresh_power(qubits, 0.6, ModuleEnum.XY, XEnum.XPI2)

    def test_m_use_amp_refresh_power(self):
        cache_qubit = self.backend.chip_data.cache_qubit
        qubits = list(cache_qubit.values())
        use_amp_refresh_power(qubits, 1, ModuleEnum.M)

