# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/09/10
# __author:       <PERSON><PERSON><PERSON>

import os
from pathlib import Path

CUR_PATH = str(Path(os.path.dirname(__file__), "data"))
os.makedirs(CUR_PATH, exist_ok=True)