# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/09/10
# __author:       <PERSON><PERSON><PERSON>

import unittest

import matplotlib.pyplot as plt

from pyQCat.processor.topology import (
    BrickBlock,
    DiamondLattice,
    HexagonLattice,
    LineTopology,
    SquareLattice,
)

from . import CUR_PATH, Path


class TopologyTest(unittest.TestCase):
    def test_line_topology(self):
        topology = LineTopology(6)
        self.assertEqual(len(topology.bit_map), 6)
        self.assertEqual(len(topology.c_map), 5)
        fig = plt.figure()
        ax = fig.add_subplot(111)
        topology.draw(ax=ax)
        fig.savefig(str(Path(CUR_PATH, "line_topology.png")))
        plt.close(fig)

    def test_square_topology(self):
        topology = SquareLattice(12, 6, node_names=[f"q{i}" for i in range(1, 73)])
        fig = plt.figure(figsize=(12, 16))
        ax = fig.add_subplot(111)
        topology.draw(ax=ax)
        fig.savefig(str(Path(CUR_PATH, "square_topology.png")))
        plt.close(fig)

    def test_diamond_topology(self):
        topology = DiamondLattice(18, 6, node_names=[f"q{i}" for i in range(1, 109)])
        fig = plt.figure(figsize=(16, 16))
        ax = fig.add_subplot(111)
        topology.draw(ax=ax)
        fig.savefig(str(Path(CUR_PATH, "diamond_topology.png")))
        plt.close(fig)

    def test_hexagon_topology(self):
        topology = HexagonLattice(18, 9, node_names=[f"q{i}" for i in range(1, 163)])
        fig = plt.figure(figsize=(16, 16))
        ax = fig.add_subplot(111)
        topology.draw(ax=ax)
        fig.savefig(str(Path(CUR_PATH, "hexagon_topology.png")))
        plt.close(fig)

    def test_brick_block_topologry(self):
        topology = BrickBlock(node_names=[f"q{i}" for i in range(1, 209)])
        fig = plt.figure(figsize=(16, 20))
        ax = fig.add_subplot(111)
        topology.draw(ax=ax)
        fig.savefig(str(Path(CUR_PATH, "brick_block_topology.png")))
        plt.close(fig)

    def test_bit_distance(self):
        topology = DiamondLattice(17, 6, node_names=[f"q{i}" for i in range(1, 103)])
        self.assertEqual(topology.bit_distance("q1", "q6"), 10)
        self.assertEqual(topology.bit_distance("q1", "q2"), 2)
        self.assertEqual(topology.bit_distance("q1", "q7"), 1)
        self.assertEqual(topology.bit_distance("c1-7", "c2-7"), 1)
        self.assertEqual(topology.bit_distance("c1-7", "c20-27"), 3)

        topology2 = SquareLattice(17, 6, node_names=[f"q{i}" for i in range(1, 103)])
        self.assertEqual(topology2.bit_distance("q1", "q6"), 5)
        self.assertEqual(topology2.bit_distance("q1", "q2"), 1)
        self.assertEqual(topology2.bit_distance("q1", "q7"), 1)
        self.assertEqual(topology2.bit_distance("c1-7", "c2-8"), 2)
        self.assertEqual(topology2.bit_distance("c1-7", "c20-21"), 4)

    def test_neighbor(self):
        topology = DiamondLattice(17, 6, node_names=[f"q{i}" for i in range(1, 103)])
        self.assertEqual(topology.bit_neighbor("q1", 1), {"q1", "q7"})
        self.assertEqual(
            topology.bit_neighbor("q1", 2), {"q1", "q2", "q7", "q13", "q14"}
        )
        self.assertEqual(
            topology.bit_neighbor("c1-7", 1), {"c1-7", "c2-7", "c7-13", "c7-14"}
        )
        self.assertEqual(
            topology.bit_neighbor("c1-7", 2),
            {
                "c1-7",
                "c2-7",
                "c7-13",
                "c7-14",
                "c2-8",
                "c8-14",
                "c14-20",
                "c14-19",
                "c13-19",
            },
        )

    def test_bit_cell_collections(self):
        topology = DiamondLattice(17, 6, node_names=[f"q{i}" for i in range(1, 103)])
        self.assertEqual(
            topology.bit_cell_collections("Q1"),
            set(
                [
                    f"Q{i}"
                    for i in [1, 3, 5, 25, 27, 29, 49, 51, 53, 73, 75, 77, 97, 99, 101]
                ]
            ),
        )
        self.assertEqual(
            topology.bit_cell_collections("Q2"),
            set(
                [
                    f"Q{i}"
                    for i in [2, 4, 6, 26, 28, 30, 50, 52, 54, 74, 76, 78, 98, 100, 102]
                ]
            ),
        )

    def test_bit_grouping(self):
        topology = DiamondLattice(18, 6, node_names=[f"q{i}" for i in range(1, 109)])
        for name, collect in topology.bit_cell_grouping().items():
            print("DiamondLattice-102", name, collect)

        topology = SquareLattice(12, 6, node_names=[f"q{i}" for i in range(1, 73)])
        for name, collect in topology.bit_cell_grouping().items():
            print("SquareLattice-72", name, collect)

        topology = SquareLattice(17, 6, node_names=[f"q{i}" for i in range(1, 103)])
        for name, collect in topology.bit_cell_grouping().items():
            print("SquareLattice-102", name, collect)


if __name__ == "__main__":
    unittest.main()
