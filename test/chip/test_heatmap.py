# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/09/10
# __author:       <PERSON><PERSON><PERSON>

import os
import unittest
from pathlib import Path

from app.config import init_backend
from pyQCat.processor.heatmap import HeatMap, TwoQubitInteractionHeatMap

CUR_PATH = str(Path(os.path.dirname(__file__), "data"))
os.makedirs(CUR_PATH, exist_ok=True)


class HeatMapTest(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.backend = init_backend()

    def test_qubit_heatmap(self):
        field = "XYwave.time + XYwave.offset * 2"
        _, value_map = self.backend.chip_data.physical_unit_heatmap(field)
        heatmap = HeatMap(
            value_map,
            layout_style=self.backend.chip_data.chip_layout_style,
            show_value=True,
            edit_units={},
            annotation_format=".5g",
        )
        _, fig, _ = heatmap.plot(title=f"HeatMap Qubit.{field}")

        save_path = str(
            Path(
                CUR_PATH,
                f"{self.backend.chip_data.chip_layout_style}-qubit-{field}.png",
            )
        )
        fig.savefig(save_path)

    def test_coupler_heatmap(self):
        field = "abs(dc_max - dc_min) * 2"
        _, value_map = self.backend.chip_data.physical_unit_heatmap(
            field, bit_type="Coupler"
        )
        heatmap = TwoQubitInteractionHeatMap(
            value_map,
            layout_style=self.backend.chip_data.chip_layout_style,
            show_value=False,
            edit_units={},
            style="Coupler",
            annotation_format=".5g",
        )
        _, fig, _ = heatmap.plot(title=f"HeatMap Coupler.{field}")
        save_path = str(
            Path(
                CUR_PATH,
                f"{self.backend.chip_data.chip_layout_style}-coupler-{field}.png",
            )
        )
        fig.savefig(save_path)

    def test_qubit_pair_heatmap(self):
        field = "metadata.std.cz.params.qc.sigma"
        _, value_map = self.backend.chip_data.physical_unit_heatmap(
            field, bit_type="QubitPair"
        )
        heatmap = TwoQubitInteractionHeatMap(
            value_map,
            layout_style=self.backend.chip_data.chip_layout_style,
            show_value=True,
            edit_units={},
            style="QubitPair",
            annotation_format=".5g",
        )
        _, fig, _ = heatmap.plot(title=f"HeatMap QubitPair.{field}")
        save_path = str(
            Path(
                CUR_PATH,
                f"{self.backend.chip_data.chip_layout_style}-qubit-pair-{field}.png",
            )
        )
        fig.savefig(save_path)

    def test_heatmap_export_csv(self):
        name_of_data = "Qubit"
        name_of_attribute = "bit * 2"
        file_name = name_of_attribute.replace("*", "X")
        file_name = file_name.replace("/", "C")
        save_path = str(
            Path(
                CUR_PATH,
                f"{self.backend.chip_data.chip_layout_style}-{name_of_data}.{file_name}.csv",
            )
        )
        self.backend.chip_data.to_xlsx(
            name_of_data=name_of_data,
            name_of_attribute=name_of_attribute,
            color_scheme="info",
            path_of_file=save_path,
            exist_ok=True,
        )

    def test_chip_topology(self):
        # DiamondLattice struct
        topology = self.backend.chip_data.topology
        # self.assertEqual(topology.bit_distance("q1", "q6"), 10)
        # self.assertEqual(topology.bit_distance("q1", "q2"), 2)
        # self.assertEqual(topology.bit_distance("q1", "q7"), 1)
        # self.assertEqual(topology.bit_distance("c1-7", "c2-7"), 1)
        # self.assertEqual(topology.bit_distance("c1-7", "c20-27"), 3)

        # brick block struct
        self.assertEqual(topology.bit_distance("q1", "q6"), 5)
        self.assertEqual(topology.bit_distance("q1", "q14"), 1)
        self.assertEqual(topology.bit_distance("q1", "q29"), 4)
        self.assertEqual(topology.bit_distance("c1-14", "c5-18"), 5)


if __name__ == "__main__":
    unittest.main()
