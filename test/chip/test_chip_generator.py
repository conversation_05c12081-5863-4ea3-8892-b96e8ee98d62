# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/09/10
# __author:       <PERSON><PERSON><PERSON>

import unittest

from pyQCat.processor.chip_generator import (
    ChipGenerator72Bit,
    ChipGenerator102BitV1_0,
    ChipGenerator102BitV1_1,
    ChipGenerator102BitV2_0,
    ChipGenerator208BitV1_0,
    ChipGenerator162BitV1_0,
    LayoutTypeEnum,
)


class ChipGeneratorTest(unittest.TestCase):
    def test_chip_72bit(self):
        chip = ChipGenerator72Bit()
        chip.generator()
        self.assertEqual(chip.result.qubit_count, 72)
        self.assertEqual(chip.result.coupler_count, 126)
        self.assertEqual(chip.result.layout_style, LayoutTypeEnum.RECTANGLE.value)
        self.assertEqual(chip.result.shape, (12, 6))
        self.assertEqual(chip.result.qubit_names, [f"q{i + 1}" for i in range(72)])

    def test_chip_102bit_v1_0(self):
        chip = ChipGenerator102BitV1_0()
        chip.generator()
        self.assertEqual(chip.result.qubit_count, 102)
        self.assertEqual(chip.result.coupler_count, 17 * 5 + 16 * 6)
        self.assertEqual(chip.result.layout_style, LayoutTypeEnum.RECTANGLE.value)
        self.assertEqual(chip.result.shape, (17, 6))
        self.assertEqual(chip.result.qubit_names, [f"q{i + 1}" for i in range(102)])

    def test_chip_102bit_v1_1(self):
        chip = ChipGenerator102BitV1_1()
        chip.generator()
        self.assertEqual(chip.result.qubit_count, 102)
        self.assertEqual(chip.result.coupler_count, 17 * 5 + 16 * 6)
        self.assertEqual(chip.result.layout_style, LayoutTypeEnum.RECTANGLE.value)
        self.assertEqual(chip.result.shape, (17, 6))
        self.assertEqual(chip.result.qubit_names, [f"q{i + 1}" for i in range(102)])

    def test_chip_102bit_v2_0(self):
        chip = ChipGenerator102BitV2_0()
        chip.generator()
        self.assertEqual(chip.result.qubit_count, 108)
        self.assertEqual(chip.result.coupler_count, 17 * 11)
        self.assertEqual(chip.result.layout_style, LayoutTypeEnum.RHOMBUS.value)
        self.assertEqual(chip.result.shape, (18, 6))
        self.assertEqual(chip.result.qubit_names, [f"q{i + 1}" for i in range(108)])

    def test_chip_208bit_v1_0(self):
        chip = ChipGenerator208BitV1_0()
        chip.generator()
        self.assertEqual(chip.result.qubit_count, 208)
        self.assertEqual(chip.result.coupler_count, 16 * 12 + 4 * 8 + 3 * 7)
        self.assertEqual(chip.result.layout_style, LayoutTypeEnum.BRICK.value)
        self.assertEqual(chip.result.shape, (16, 13))
        self.assertEqual(chip.result.qubit_names, [f"q{i + 1}" for i in range(208)])

    def test_chip_162bit_v1_0(self):
        chip = ChipGenerator162BitV1_0()
        chip.generator()
        self.assertEqual(chip.result.qubit_count, 162)
        self.assertEqual(chip.result.coupler_count, 225)
        self.assertEqual(chip.result.layout_style, LayoutTypeEnum.HEXAGON.value)
        self.assertEqual(chip.result.shape, (18, 9))
        self.assertEqual(chip.result.qubit_names, [f"q{i + 1}" for i in range(162)])
