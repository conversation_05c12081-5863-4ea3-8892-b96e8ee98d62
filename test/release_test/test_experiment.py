# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/04/09
# __author:       <PERSON><PERSON><PERSON>


import unittest

from app.tool.regression_test.worker import FakeExperimentExecutor


class ExperimentTest(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.worker = FakeExperimentExecutor(
            config_path="/home/<USER>/code/pyqcat-apps/.settings/config/s251/nix/config.conf",
            is_record=False,
        )

    def test_from_record_id(self):
        self.worker.regression_test_from_ids("682183729f27ed3fc61bbcc9")

    def test_experiment_library(self):
        self.worker.regression_test_long_time()
