# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/09/06
# __author:       <PERSON><PERSON><PERSON>

import unittest

from pyQCat.processor.hardware_manager import HardwareOffsetManager
from pyQCat.qaio_property import QAIO


def fake_chip() -> dict:
    chip_data = {}
    for i in range(1, 73):
        chip_data[f"q{i}"] = {"xy": i, "z": i}
    for j, coupler in enumerate(
        [f"c{i}-{i + 1}" for i in range(1, 72, 1) if (i % 6) != 0]
        + [f"c{i}-{i + 6}" for i in range(1, 67, 1)]
    ):
        chip_data[coupler] = {"z": 73 + j}
    return chip_data


class HardwareOffsetManagerTest(unittest.TestCase):
    manager = HardwareOffsetManager()
    chip = fake_chip()

    @classmethod
    def setUpClass(cls) -> None:
        QAIO.type = 72

    def test_log_state(self):
        self.manager.log_state()

    def test_insert_xyz_timing(self):
        self.manager.insert_xyz_timing(1, 1, 10.2)
        self.manager.log_state()

    def test_insert_zz_timing(self):
        self.manager.insert_xyz_timing(1, 1, 10.2)
        self.manager.insert_xyz_timing(2, 2, 3)
        self.manager.insert_zz_timing(1, 2, 73, 0, 4, 8)
        self.manager.log_state()
        print(self.manager._insert_records)
