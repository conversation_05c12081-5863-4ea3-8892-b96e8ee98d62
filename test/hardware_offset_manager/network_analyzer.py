import os

import numpy as np
import matplotlib.pyplot as plt

from pyQCat.instrument.E5071C import E5071C


class Cavity:
    def __init__(self, inst_ena, file_manager):
        self.inst_ena = inst_ena
        self.file = file_manager

        self.freq_begin = 4e9
        self.freq_end = 8e9
        self.freq_center = (self.freq_begin + self.freq_end) / 2
        self.span = self.freq_end - self.freq_begin
        self.ena_power = -20
        self.sweep_point = 1001
        self.ifbw = 1000
        self.ave = 1

        self._initialize_instrument()

    def _initialize_instrument(self):
        self.inst_ena.setFreqStart(self.freq_begin)
        self.inst_ena.setFreqStop(self.freq_end)
        self.inst_ena.setPower(self.ena_power)
        self.inst_ena.setSweepPoints(self.sweep_point)
        self.inst_ena.setIFBW(self.ifbw)
        self.inst_ena.AverageCount(self.ave)
        if self.ave <= 1:
            self.inst_ena.AverageOFF()
        else:
            self.inst_ena.AverageON()

    def get_s_curve_be(self, freq_begin, freq_end, ena_power, sweep_point, ifbw, ave):
        # Begin-End 模式获取S曲线数据
        if not np.isclose(self.freq_begin, freq_begin):
            self.freq_begin = freq_begin
            self.inst_ena.setFreqStart(self.freq_begin)

        if not np.isclose(self.freq_end, freq_end):
            self.freq_end = freq_end
            self.inst_ena.setFreqStop(self.freq_end)

        if not np.isclose(self.ena_power, ena_power):
            self.ena_power = ena_power
            self.inst_ena.setPower(self.ena_power)

        if not np.isclose(self.sweep_point, sweep_point):
            self.sweep_point = sweep_point
            self.inst_ena.setSweepPoints(self.sweep_point)

        if not np.isclose(self.ifbw, ifbw):
            self.ifbw = ifbw
            self.inst_ena.setIFBW(self.ifbw)

        if not np.isclose(self.ave, ave):
            self.ave = ave
            self.inst_ena.AverageCount(self.ave)
            if self.ave <= 1:
                self.inst_ena.AverageOFF()
            else:
                self.inst_ena.AverageON()

        self.inst_ena.singleTrig()
        freq_list = self.inst_ena.getFreqList()
        amp_list = self.inst_ena.getAmpList()
        phase_list = self.inst_ena.getExtPhaseList()

        return {"FreqList": freq_list, "AmpList": amp_list, "ExtPhaseList": phase_list}

    def plot_s_curve_be(self, freq_begin, freq_end, ena_power, sweep_point, ifbw, ave):
        data = self.get_s_curve_be(freq_begin, freq_end, ena_power, sweep_point, ifbw, ave)
        freq_list, amp_list, phase_list = data["FreqList"], data["AmpList"], data["ExtPhaseList"]

        plt.figure()
        plt.subplot(2, 1, 1)
        plt.plot(freq_list, amp_list)
        plt.grid(True)
        plt.title("AMP")

        plt.subplot(2, 1, 2)
        plt.plot(freq_list, phase_list)
        plt.grid(True)
        plt.title("Phase")
        plt.show()

    def sweep_bus(self, sweep_params):
        freq_list_all = []
        amp_list_all = []
        phase_list_all = []

        for freq_center in np.arange(
                sweep_params["freq_begin"] + sweep_params["freq_span"] / 2,
                sweep_params["freq_end"] - sweep_params["freq_span"] / 2,
                sweep_params["freq_span"],
        ):
            data = self.get_s_curve_be(
                freq_center - sweep_params["freq_span"] / 2,
                freq_center + sweep_params["freq_span"] / 2,
                sweep_params["ena_power"],
                sweep_params["sweep_point"],
                sweep_params["ifbw"],
                sweep_params["ave"],
            )
            freq_list_all.extend(data["FreqList"])
            amp_list_all.extend(data["AmpList"])
            phase_list_all.extend(data["ExtPhaseList"])

            plt.plot(data["FreqList"], data["AmpList"], label=f"FreqCenter={freq_center}")
            plt.pause(0.1)

        # Save results
        self.file.save(np.column_stack((freq_list_all, amp_list_all)), "AMP")
        self.file.save(np.column_stack((freq_list_all, phase_list_all)), "PHASE")
        plt.legend()
        plt.show()

    def sweep_cavities(self, sweep_params):
        freq_list_all = []
        amp_list_all = []
        phase_list_all = []

        for freq_center in sweep_params["freq_center_list"]:
            data = self.get_s_curve_be(
                freq_center - sweep_params["freq_span"] / 2,
                freq_center + sweep_params["freq_span"] / 2,
                sweep_params["ena_power"],
                sweep_params["sweep_point"],
                sweep_params["ifbw"],
                sweep_params["ave"],
            )
            freq_list_all.extend(data["FreqList"])
            amp_list_all.extend(data["AmpList"])
            phase_list_all.extend(data["ExtPhaseList"])

            self.file.save(np.column_stack((data["FreqList"], data["AmpList"])), f"AMP_{freq_center}.dat")
            self.file.save(np.column_stack((data["FreqList"], data["ExtPhaseList"])), f"PHASE_{freq_center}.dat")

        plt.plot(freq_list_all, amp_list_all, "k")
        plt.grid(True)
        plt.xlabel("Frequency (Hz)")
        plt.ylabel("S21")
        plt.title(f"Power = {sweep_params['probe_power']} dBm")
        plt.show()


class FileManager:
    def __init__(self, file_path):
        self.file_path = file_path

    def save(self, data, name):
        file_path = self.file_path
        if not os.path.exists(file_path):
            os.makedirs(file_path)
        save_data = np.vstack(data)
        f_name = os.path.join(file_path, name)
        np.savetxt(f_name, save_data, fmt="%.7e", delimiter="   ")


# 示例
inst = E5071C('TCPIP0::*************::inst0::INSTR')
file_path = "./data"
file_mgr = FileManager(file_path)
cavity = Cavity(inst, file_mgr)

sweep_params = {
    "freq_begin": 4e9,
    "freq_end": 8e9,
    "freq_span": 1e8,
    "ena_power": -20,
    "sweep_point": 1001,
    "ifbw": 1000,
    "ave": 1,
    "probe_power": -10,
    "freq_center_list": np.linspace(4e9, 8e9, 5),
}

if __name__ == "__main__":
    cavity.sweep_cavities(sweep_params)
