# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

.vscode
.settings
.pipe
# C extensions
*.so

# Distribution / packaging
/.idea
.Python
build/
.idea/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit qubit_test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Translations
*.mo
*.pot

# Django stuff:
*.log
.static_storage/
.media/
local_settings.py

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site
/config/

# log
/log-monster/

# result-test
/result-monster/
shot.png


# pyQCat
/pyQCat/
pyQCat

/scripts/
bits_cache.*
pyqcat_developer/*
.build_pyd/
*.c

# Ignore all symlinks created by mklink command
*/lnk/*

# version bat and sh
/version/release
/version/branch
app/analysis_test/StandardCurveAnalysis-q43-QubitSpectrumScanPower.png
app/analysis_test/T1SpectrumAnalysis-q7c1-7c2-7c7-13c7-14-T1SpectrumWithBiasCoupler.png
app/analysis_test/CavityPowerScan/*
app/analysis_test/QubitSpectrumScanPower/*
.VSCodeCounter/*
app/config.py
app/analysis_test/CavityPowerScanAnalysis-q1-CavityPowerScan.png
app/analysis_test/q1-CavityPowerScan.epd
app/analysis_test/q1-T1Spectrum-T1(1-23)-z_amp=0.08638 freq=4453.163.epd
app/analysis_test/q7c1-7-T1Extend.epd
app/analysis_test/q7c1-7-T1WithBiasCoupler.epd
app/analysis_test/q7c1-7c2-7c7-13c7-14-T1SpectrumWithBiasCoupler-T1WithBiasCoupler(1-11)-z_amp=-1.0.epd
app/analysis_test/q7c1-7c2-7c7-13c7-14-T1SpectrumWithBiasCoupler.epd

Program/naga-ParallelCount-1/*
Monster/*
app/config.py
app/analysis_test/standard_analysis_demo.py
app/batch_test/json_data/CavityPowerScan.json
app/batch_test/libs/batch_runner.py
app/temp_need/batch_T1SpectrumWithBiasCoupler/t1_spectrum_with_bias_coupler.json
app/temp_need/batch_T1SpectrumWithBiasCoupler/batch_t1_spectrum_with_bias_coupler_setup.py
app/tool/program_compare/test.py

app/config.py
